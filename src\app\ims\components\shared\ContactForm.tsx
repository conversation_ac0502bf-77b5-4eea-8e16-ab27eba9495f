/**
 * 統一的 Contact 表單組件
 * 
 * 提供標準的 Contact 表單功能，包括：
 * - 響應式佈局
 * - 統一的驗證規則
 * - 可配置的欄位顯示
 * - 多種表單模式（創建/編輯/查看）
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

"use client";

import React, { useEffect, useMemo } from 'react';
import { 
  Form, 
  Input, 
  Select, 
  Switch, 
  Row, 
  Col, 
  Space,
  Typography
} from 'antd';
import { 
  UserOutlined, 
  MailOutlined, 
  PhoneOutlined,
  BankOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { Contact } from '@/services/ims/ContactService';
import { 
  CONTACT_TYPES, 
  CONTACT_FORM_FIELDS,
  CONTACT_DEFAULTS 
} from '@/app/ims/components/contact/shared/contactConstants';
import { 
  getContactFieldValidationRules 
} from '@/app/ims/components/contact/shared/contactValidation';
import { useScreenSize } from './ResponsiveModalConfig';

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;

export interface ContactFormProps {
  // 表單實例
  form: any;
  
  // 數據
  initialData?: Contact | null;
  
  // 模式
  mode?: 'create' | 'edit' | 'view';
  
  // 欄位配置
  fieldSet?: 'basic' | 'extended' | 'all' | 'quick';
  visibleFields?: string[];
  
  // 佈局配置
  layout?: 'horizontal' | 'vertical';
  labelCol?: any;
  wrapperCol?: any;
  
  // 事件回調
  onValuesChange?: (changedValues: any, allValues: any) => void;
}

const ContactForm: React.FC<ContactFormProps> = ({
  form,
  initialData,
  mode = 'create',
  fieldSet = 'all',
  visibleFields,
  layout,
  labelCol,
  wrapperCol,
  onValuesChange
}) => {
  const { isMobile, isTablet } = useScreenSize();
  
  // 響應式佈局配置
  const responsiveLayout = useMemo(() => {
    if (layout) return layout;
    return isMobile ? 'vertical' : 'horizontal';
  }, [layout, isMobile]);

  const responsiveLabelCol = useMemo(() => {
    if (labelCol) return labelCol;
    if (isMobile) return { span: 24 };
    return { span: 6 };
  }, [labelCol, isMobile]);

  const responsiveWrapperCol = useMemo(() => {
    if (wrapperCol) return wrapperCol;
    if (isMobile) return { span: 24 };
    return { span: 18 };
  }, [wrapperCol, isMobile]);

  // 確定要顯示的欄位
  const fieldsToShow = useMemo(() => {
    if (visibleFields) return visibleFields;
    return CONTACT_FORM_FIELDS[fieldSet];
  }, [visibleFields, fieldSet]);

  // 是否為只讀模式
  const isReadOnly = mode === 'view';

  // 初始化表單數據
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(initialData);
    } else if (mode === 'create') {
      form.setFieldsValue(CONTACT_DEFAULTS);
    }
  }, [form, initialData, mode]);

  // 欄位渲染函數
  const renderField = (fieldName: string) => {
    const validationRules = getContactFieldValidationRules(fieldName);
    
    switch (fieldName) {
      case 'name':
        return (
          <Form.Item
            name="name"
            label="姓名"
            rules={validationRules}
            required
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="請輸入聯絡人姓名"
              disabled={isReadOnly}
              maxLength={100}
              showCount
            />
          </Form.Item>
        );

      case 'position':
        return (
          <Form.Item
            name="position"
            label="職位"
            rules={validationRules}
          >
            <Input
              prefix={<TeamOutlined />}
              placeholder="請輸入職位"
              disabled={isReadOnly}
              maxLength={50}
              showCount
            />
          </Form.Item>
        );

      case 'email':
        return (
          <Form.Item
            name="email"
            label="電子郵件"
            rules={validationRules}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder="請輸入電子郵件"
              disabled={isReadOnly}
              maxLength={100}
              showCount
            />
          </Form.Item>
        );

      case 'phone':
        return (
          <Form.Item
            name="phone"
            label="電話"
            rules={validationRules}
          >
            <Input
              prefix={<PhoneOutlined />}
              placeholder="請輸入電話號碼"
              disabled={isReadOnly}
              maxLength={20}
              showCount
            />
          </Form.Item>
        );

      case 'department':
        return (
          <Form.Item
            name="department"
            label="部門"
            rules={validationRules}
          >
            <Input
              prefix={<BankOutlined />}
              placeholder="請輸入部門"
              disabled={isReadOnly}
              maxLength={50}
              showCount
            />
          </Form.Item>
        );

      case 'company':
        return (
          <Form.Item
            name="company"
            label="公司"
            rules={validationRules}
          >
            <Input
              prefix={<BankOutlined />}
              placeholder="請輸入公司名稱"
              disabled={isReadOnly}
              maxLength={100}
              showCount
            />
          </Form.Item>
        );

      case 'contactType':
        return (
          <Form.Item
            name="contactType"
            label="聯絡人類型"
            rules={[{ required: true, message: '請選擇聯絡人類型' }]}
          >
            <Select
              placeholder="請選擇聯絡人類型"
              disabled={isReadOnly}
            >
              {CONTACT_TYPES.map(type => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        );

      case 'isActive':
        return (
          <Form.Item
            name="isActive"
            label="狀態"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="啟用"
              unCheckedChildren="停用"
              disabled={isReadOnly}
            />
          </Form.Item>
        );

      default:
        return null;
    }
  };

  // 響應式欄位佈局
  const renderFieldsInGrid = () => {
    const colSpan = isMobile ? 24 : isTablet ? 12 : 8;
    
    return (
      <Row gutter={[16, 0]}>
        {fieldsToShow.map(fieldName => (
          <Col key={fieldName} span={colSpan}>
            {renderField(fieldName)}
          </Col>
        ))}
      </Row>
    );
  };

  // 簡單佈局（垂直排列）
  const renderFieldsSimple = () => {
    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        {fieldsToShow.map(fieldName => renderField(fieldName))}
      </Space>
    );
  };

  return (
    <Form
      form={form}
      layout={responsiveLayout}
      labelCol={responsiveLabelCol}
      wrapperCol={responsiveWrapperCol}
      onValuesChange={onValuesChange}
      preserve={false}
      className="contact-form"
    >
      {/* 根據螢幕大小選擇佈局方式 */}
      {isMobile || fieldSet === 'quick' 
        ? renderFieldsSimple() 
        : renderFieldsInGrid()
      }
      
      {/* 只讀模式提示 */}
      {isReadOnly && (
        <div style={{ 
          marginTop: 16, 
          padding: 8, 
          background: '#f5f5f5', 
          borderRadius: 4 
        }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            * 當前為查看模式，所有欄位均為只讀
          </Text>
        </div>
      )}
    </Form>
  );
};

export default ContactForm;
