using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 折舊單
    /// </summary>
    public class DepreciationForm : ModelBaseEntity
    {
        [Key]
        [Comment("折舊單編號")]
        [Column(TypeName = "nvarchar(100)")]
        public Guid DepreciationFormId { get; set; } // 折舊單編號

        [Comment("折舊單號")]
        [Column(TypeName = "nvarchar(50)")]
        public string DepreciationFormNo { get; set; } // 折舊單號

        [Comment("折舊紀錄編號")]
        [Column(TypeName = "nvarchar(100)")]
        [ForeignKey("DepreciationId")]
        public Guid DepreciationId { get; set; } // 折舊紀錄編號

        [Comment("折舊年度")]
        [Column(TypeName = "int")]
        public int DepreciationYear { get; set; } // 折舊年度

        [Comment("折舊月份")]
        [Column(TypeName = "int")]
        public int DepreciationMonth { get; set; } // 折舊月份

        [Comment("折舊日期")]
        [Column(TypeName = "bigint")]
        public long DepreciationDate { get; set; } // 折舊日期

        [Comment("折舊狀態")]
        [Column(TypeName = "nvarchar(20)")]
        public string Status { get; set; } // 折舊狀態

        [Comment("審核人員")]
        [Column(TypeName = "nvarchar(50)")]
        [ForeignKey("UserId")]
        public string? ApproverId { get; set; } // 審核人員

        [Comment("審核日期")]
        [Column(TypeName = "bigint")]
        public long? ApprovalDate { get; set; } // 審核日期

        [Comment("審核意見")]
        [Column(TypeName = "nvarchar(500)")]
        public string? ApprovalComment { get; set; } // 審核意見

        [Comment("備註")]
        [Column(TypeName = "nvarchar(500)")]
        public string Notes { get; set; } // 備註

        public DepreciationForm()
        {
            DepreciationFormId = Guid.NewGuid();
            DepreciationFormNo = "";
            DepreciationId = Guid.Empty;
            DepreciationDate = 0;
            DepreciationYear = 0;
            DepreciationMonth = 0;
            Status = DepreciationFormStatus.PENDING;
            ApproverId = null;
            ApprovalDate = null;
            ApprovalComment = null;
            Notes = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class DepreciationFormDTO : ModelBaseEntityDTO
    {
        public string DepreciationFormId { get; set; } // 折舊單編號
        public string DepreciationFormNo { get; set; } // 折舊單號
        public string DepreciationId { get; set; } // 折舊紀錄編號
        public long DepreciationDate { get; set; } // 折舊日期
        public int DepreciationYear { get; set; } // 折舊年度
        public int DepreciationMonth { get; set; } // 折舊月份
        public string Status { get; set; } // 折舊狀態
        public string StatusName { get; set; } // 折舊狀態名稱
        public string? ApproverId { get; set; } // 審核人員
        public string? ApproverName { get; set; } // 審核人員姓名
        public long? ApprovalDate { get; set; } // 審核日期
        public string? ApprovalComment { get; set; } // 審核意見
        public string Notes { get; set; } // 備註
        public ICollection<DepreciationFormDetailDTO> DepreciationFormDetail { get; set; } //折舊紀錄資料
        public string? CreateUserName { get; set; } // 建立者姓名

        public DepreciationFormDTO()
        {
            DepreciationFormId = "";
            DepreciationFormNo = "";
            DepreciationId = "";
            DepreciationDate = 0;
            DepreciationYear = 0;
            DepreciationMonth = 0;
            Status = DepreciationFormStatus.PENDING;
            StatusName = "待審核";
            ApproverId = null;
            ApproverName = null;
            ApprovalDate = null;
            ApprovalComment = null;
            Notes = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
            DepreciationFormDetail = new List<DepreciationFormDetailDTO>();
            CreateUserName = null;
        }
    }

    /// <summary>
    /// 折舊單狀態枚舉
    /// </summary>
    public static class DepreciationFormStatus
    {
        public const string PENDING = "PENDING";        // 待審核
        public const string APPROVED = "APPROVED";      // 已核准
        public const string REJECTED = "REJECTED";      // 已駁回
        public const string EXECUTED = "EXECUTED";      // 已執行

        public static Dictionary<string, string> GetStatusNames()
        {
            return new Dictionary<string, string>
            {
                { PENDING, "待審核" },
                { APPROVED, "已核准" },
                { REJECTED, "已駁回" },
                { EXECUTED, "已執行" }
            };
        }
    }

    /// <summary>
    /// 折舊單批次處理DTO
    /// </summary>
    public class DepreciationFormBatchDTO
    {
        public List<Guid> DepreciationFormIds { get; set; }
        public string Action { get; set; } // APPROVE, REJECT, EXECUTE
        public string? ApprovalComment { get; set; }
        public string OperatorId { get; set; }

        public DepreciationFormBatchDTO()
        {
            DepreciationFormIds = new List<Guid>();
            Action = "";
            OperatorId = "";
        }
    }
}