"use client";

/* 攤提來源
  /app/pms/parameter_settings/amortization_source/page.tsx
  功能說明
    1. 選擇攤提來源
    2. 新增攤提來源
    3. 編輯攤提來源
    4. 刪除攤提來源
*/
import React, { useEffect, useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Tag,
  List,
  Typography,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import {
  AmortizationSource,
  getAmortizationSources,
  createAmortizationSource,
  updateAmortizationSource,
  deleteAmortizationSource,
} from "@/services/pms/amortizationSourceService";
import { notifySuccess, notifyError } from "@/utils/notification";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import {
  getDepartments,
  Department,
} from "@/services/common/departmentService";
import { useAuth } from "@/contexts/AuthContext";

const { Text } = Typography;

const AmortizationSourcePage: React.FC = () => {
  const [amortizationSources, setAmortizationSources] = useState<
    AmortizationSource[]
  >([]);
  const [filteredSources, setFilteredSources] = useState<AmortizationSource[]>(
    []
  );
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingSource, setEditingSource] = useState<AmortizationSource | null>(
    null
  );
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();
  const [searchText, setSearchText] = useState("");
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const { user } = useAuth();
  const [isMobile, setIsMobile] = useState(false);

  // 加載攤提來源列表
  const loadAmortizationSources = async () => {
    setLoading(true);
    try {
      const response = await getAmortizationSources();
      if (response.success && response.data) {
        setAmortizationSources(response.data);
        setFilteredSources(response.data);
      } else {
        notifyError("獲取攤提來源列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取攤提來源列表失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  // 加載部門列表
  const loadDepartments = async () => {
    try {
      const response = await getDepartments();
      if (response.success && response.data) {
        setDepartments(response.data);
      } else {
        notifyError("獲取部門列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取部門列表失敗", "請稍後再試");
    }
  };

  useEffect(() => {
    loadAmortizationSources();
    loadDepartments();
  }, []);

  // 處理搜尋
  useEffect(() => {
    const filtered = amortizationSources.filter((source) =>
      source.sourceName
        ? source.sourceName.toLowerCase().includes(searchText.toLowerCase())
        : false
    );
    setFilteredSources(filtered);
  }, [searchText, amortizationSources]);

  // 檢查手機版
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // 表格列定義
  const columns: ColumnsType<AmortizationSource> = [
    {
      key: "index",
      width: 80,
      render: (_, __, index) => <span>{index + 1}</span>,
    },
    {
      title: "來源名稱",
      dataIndex: "sourceName",
      key: "sourceName",
    },
    {
      title: "描述",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "部門名稱",
      dataIndex: "departmentId",
      key: "departmentId",
      render: (departmentId) => {
        const department = departments.find(
          (d) => d.departmentId === departmentId
        );
        return department?.name || departmentId;
      },
    },
    {
      title: "金額",
      dataIndex: "amount",
      key: "amount",
      render: (text) => (
        <Tag color="red">
          {new Intl.NumberFormat("zh-TW", {
            style: "currency",
            currency: "TWD",
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
          }).format(text)}
        </Tag>
      ),
    },
    {
      title: "建立時間",
      dataIndex: "createTime",
      key: "createTime",
      render: (text) => (
        <span>{DateTimeExtensions.formatFromTimestamp(text)}</span>
      ),
    },
    {
      title: "建立者",
      dataIndex: "createUserName",
      key: "createUserName",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
        </Space>
      ),
    },
  ];

  // 處理新增/編輯表單提交
  const handleSubmit = async (values: any) => {
    try {
      if (editingSource) {
        // 更新攤提來源
        const response = await updateAmortizationSource({
          ...values,
          amortizationSourceId: editingSource.amortizationSourceId,
        });
        if (response.success) {
          notifySuccess("更新成功", "攤提來源已更新");
          loadAmortizationSources();
        } else {
          notifyError("更新失敗", response.message);
        }
      } else {
        // 新增攤提來源
        const response = await createAmortizationSource(values);
        if (response.success) {
          notifySuccess("新增成功", "攤提來源已新增");
          loadAmortizationSources();
        } else {
          notifyError("新增失敗", response.message);
        }
      }
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      notifyError("操作失敗", "請稍後再試");
    }
  };

  // 處理編輯
  const handleEdit = (source: AmortizationSource) => {
    setEditingSource(source);
    form.setFieldsValue(source);
    setIsModalVisible(true);
  };

  // 打開新增攤提來源的表單
  const showAddModal = () => {
    setIsAddModalVisible(true);
    addForm.resetFields();
  };

  // 新增攤提來源表單提交
  const handleAddSubmit = async (values: any) => {
    try {
      const response = await createAmortizationSource({
        ...values,
        createUserId: user?.userId,
      });
      if (response.success) {
        notifySuccess("新增成功", "攤提來源已新增");
        loadAmortizationSources();
        setIsAddModalVisible(false);
        addForm.resetFields();
      } else {
        notifyError("新增失敗", response.message);
      }
    } catch (error) {
      notifyError("操作失敗", "請稍後再試");
    }
  };

  // 手機版列表
  const renderMobileList = () => {
    return (
      <List
        loading={loading}
        dataSource={filteredSources}
        renderItem={(source, index) => (
          <List.Item
            key={source.amortizationSourceId}
            style={{
              padding: "12px",
              borderBottom: "1px solid #f0f0f0",
            }}
          >
            <div style={{ width: "100%" }}>
              {/* 標題列 */}
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "8px",
                  gap: "8px",
                }}
              >
                <div
                  style={{
                    width: 24,
                    height: 24,
                    background: "#f0f0f0",
                    borderRadius: "50%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    fontSize: "14px",
                    flexShrink: 0,
                  }}
                >
                  {index + 1}
                </div>
                <Text strong style={{ fontSize: "16px" }}>
                  {source.sourceName}
                </Text>
              </div>

              {/* 描述列 */}
              <div style={{ marginBottom: "8px" }}>
                <Text style={{ fontSize: "14px", color: "#666" }}>
                  {source.description}
                </Text>
              </div>

              {/* 部門和金額列 */}
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  marginBottom: "8px",
                  alignItems: "center",
                }}
              >
                <Text style={{ fontSize: "14px" }}>
                  部門：
                  {departments.find(
                    (d) => d.departmentId === source.departmentId
                  )?.name || source.departmentId}
                </Text>
                <Tag color="red" style={{ margin: 0 }}>
                  {new Intl.NumberFormat("zh-TW", {
                    style: "currency",
                    currency: "TWD",
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  }).format(source.amount)}
                </Tag>
              </div>

              {/* 時間和建立者列 */}
              <div style={{ marginBottom: "8px" }}>
                <Text type="secondary" style={{ fontSize: "13px" }}>
                  建立時間：
                  {DateTimeExtensions.formatFromTimestamp(source.createTime)}
                </Text>
              </div>
              <div style={{ marginBottom: "8px" }}>
                <Text type="secondary" style={{ fontSize: "13px" }}>
                  建立者：{source.createUserName}
                </Text>
              </div>

              {/* 操作按鈕列 */}
              <div
                style={{
                  display: "flex",
                  gap: "16px",
                  marginTop: "12px",
                }}
              >
                <Button
                  type="link"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(source)}
                  style={{ padding: 0 }}
                >
                  編輯
                </Button>
              </div>
            </div>
          </List.Item>
        )}
        pagination={{
          onChange: (page) => {
            console.log(page);
          },
          pageSize: 10,
          size: "small",
          style: { marginTop: "16px" },
        }}
      />
    );
  };

  return (
    <Card
      title="攤提來源"
      styles={{
        body: { padding: isMobile ? "12px" : "24px" },
      }}
    >
      <Space
        style={{
          marginBottom: 16,
          width: "100%",
          flexDirection: isMobile ? "column" : "row",
        }}
        wrap
      >
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={showAddModal}
          style={{ width: isMobile ? "100%" : "auto" }}
        >
          新增攤提來源
        </Button>
        <Input
          placeholder="搜尋攤提來源名稱"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: isMobile ? "100%" : 200 }}
        />
      </Space>

      {isMobile ? (
        renderMobileList()
      ) : (
        <Table
          columns={columns}
          dataSource={filteredSources}
          rowKey="amortizationSourceId"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
            showTotal: (total) => `共 ${total} 筆資料`,
          }}
        />
      )}

      <Modal
        title={editingSource ? "編輯攤提來源" : "新增攤提來源"}
        open={isModalVisible}
        onOk={form.submit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        okText="確認"
        cancelText="取消"
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item name="amortizationSourceId" label="來源編號：">
            {editingSource ? (
              <span>{editingSource.amortizationSourceId}</span>
            ) : (
              <Input placeholder="請輸入來源編號" />
            )}
          </Form.Item>

          <Form.Item
            name="sourceName"
            label="來源名稱："
            rules={[{ required: true, message: "請輸入來源名稱" }]}
          >
            <Input placeholder="請輸入來源名稱" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述："
            rules={[{ required: true, message: "請輸入描述" }]}
          >
            <Input.TextArea
              placeholder="請輸入描述"
              maxLength={100}
              autoSize={{ minRows: 3, maxRows: 6 }}
              showCount
            />
          </Form.Item>

          <Form.Item
            name="amount"
            label="金額："
            rules={[
              { required: true, message: "請輸入金額" },
              {
                pattern: /^[0-9]+(\.[0-9]{1,2})?$/,
                message: "請輸入有效的數字金額",
              },
            ]}
          >
            <Input placeholder="請輸入金額" type="number" min={0} step={0.01} />
          </Form.Item>

          <Form.Item
            name="departmentId"
            label="部門名稱："
            rules={[{ required: true, message: "請選擇部門" }]}
          >
            <Select placeholder="請選擇部門">
              {departments.map((dept) => (
                <Select.Option
                  key={dept.departmentId}
                  value={dept.departmentId}
                >
                  {dept.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="createTime" label="建立時間：">
            <span>
              {DateTimeExtensions.formatFromTimestamp(
                editingSource?.createTime
              )}
            </span>
          </Form.Item>

          <Form.Item name="createUserName" label="建立者：">
            <span>{editingSource?.createUserName}</span>
          </Form.Item>

          <Form.Item
            name="createUserId"
            label="建立者編號："
            style={{ display: "none" }}
          >
            <span>{editingSource?.createUserId}</span>
          </Form.Item>

          <Form.Item name="updateTime" label="更新時間：">
            <span>
              {DateTimeExtensions.formatFromTimestamp(
                editingSource?.updateTime
              )}
            </span>
          </Form.Item>

          {editingSource?.updateUserName && (
            <Form.Item name="updateUserName" label="更新者：">
              <span>{editingSource?.updateUserName}</span>
            </Form.Item>
          )}

          {editingSource?.updateUserId && (
            <Form.Item
              name="updateUserId"
              label="更新者編號："
              style={{ display: "none" }}
            >
              <span>{editingSource?.updateUserId}</span>
            </Form.Item>
          )}
        </Form>
      </Modal>

      <Modal
        title="新增攤提來源"
        open={isAddModalVisible}
        onOk={addForm.submit}
        onCancel={() => {
          setIsAddModalVisible(false);
          addForm.resetFields();
        }}
        okText="確認"
        cancelText="取消"
      >
        <Form form={addForm} layout="vertical" onFinish={handleAddSubmit}>
          <Form.Item
            name="sourceName"
            label="來源名稱："
            rules={[{ required: true, message: "請輸入來源名稱" }]}
          >
            <Input placeholder="請輸入來源名稱" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述："
            rules={[{ required: true, message: "請輸入描述" }]}
          >
            <Input.TextArea
              placeholder="請輸入描述"
              maxLength={100}
              autoSize={{ minRows: 3, maxRows: 6 }}
              showCount
            />
          </Form.Item>

          <Form.Item
            name="amount"
            label="金額："
            rules={[
              { required: true, message: "請輸入金額" },
              {
                pattern: /^[0-9]+(\.[0-9]{1,2})?$/,
                message: "請輸入有效的數字金額",
              },
            ]}
          >
            <Input placeholder="請輸入金額" type="number" min={0} step={0.01} />
          </Form.Item>

          <Form.Item
            name="departmentId"
            label="部門名稱："
            rules={[{ required: true, message: "請選擇部門" }]}
          >
            <Select placeholder="請選擇部門">
              {departments.map((dept) => (
                <Select.Option
                  key={dept.departmentId}
                  value={dept.departmentId}
                >
                  {dept.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default AmortizationSourcePage;
