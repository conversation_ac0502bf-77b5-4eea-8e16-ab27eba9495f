import { AssetSubAccount } from './assetSubAccountService';
import { apiEndpoints, API_BASE_URL } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";
import { getCookie } from '@/utils/cookies';

// 附屬設備
export interface AccessoryEquipment {
    accessoryEquipmentId: string;
    equipmentNo: string;
    equipmentName: string;
    equipmentType: string;
    specification: string;
    purchaseDate: number;
    purchasePrice: number;
    assetId: string;
    asset?: any;
    usageStatus: string;
    remarks: string;
    createTime: number;
    createUserId: string;
    updateTime?: number;
    updateUserId?: string;
    deleteTime?: number;
    deleteUserId?: string;
}

// 保險單位
export interface InsuranceUnit {
    insuranceUnitId: string;
    name: string;
    companyNo: string;
    contactPerson: string;
    contactPhone: string;
    contactEmail: string;
    address: string;
    website: string;
    description: string;
    sortCode: number;
    insuranceAmount: number;
    insuranceStartDate: number;
    insuranceExpiryDate: number;
    createTime: number;
    createUserId: string;
    updateTime?: number;
    updateUserId?: string;
    deleteTime?: number;
    deleteUserId?: string;
    assetInsuranceUnits: any[];
}

// 攤提來源
export interface AmortizationSource {
    amortizationSourceId: string;
    departmentId: string;
    departmentName?: string;
    sourceName: string;
    description: string;
    amount: number;
    createTime: number;
    createUserId: string;
    createUserName?: string;
    updateTime?: number;
    updateUserId?: string;
    updateUserName?: string;
    deleteTime?: number;
    deleteUserId?: string;
    deleteUserName?: string;
    assetAmortizationSources: any[];
}

// 財產來源
export interface AssetSource {
    assetSourceId: string;
    assetSourceName: string;
    sortCode: number;
    createTime: number;
    createUserId: string;
    createUserName?: string;
    updateTime?: number;
    updateUserId?: string;
    updateUserName?: string;
    deleteTime?: number;
    deleteUserId?: string;
    deleteUserName?: string;
    assetAssetSources: any[];
}

// 財產
export interface Asset {
    assetId: string;
    assetNo: string;
    assetName: string;
    assetShortName?: string;
    acquisitionDate: number;
    quantity: number;
    unitId: string;
    purchaseAmount: number;
    subsidyAmount: number;
    estimatedResidualValue: number;
    departmentId: string;
    divisionId: string;
    custodianId: string;
    userId: string;
    username?: string;
    assetStatusId: string;
    statusChangeDate: number;
    usage: string;
    notes?: string;
    storageLocationId: string;
    serviceLife: number;
    depreciationAmount: number;
    accumulatedDepreciationAmount: number;
    insurancePeriod: number;
    manufacturerId: string;
    specification?: string;
    buildingAddress?: string;
    buildingStructure?: string;
    constructionDate?: number;
    floorArea?: number;
    publicArea?: number;
    usageExpiryDate?: number;
    usageLicenseNo?: string;
    buildingTaxItem?: string;
    publicValue?: number;
    landSection?: string;
    landLocation?: string;
    landNumber?: string;
    landArea?: number;
    certificateNo?: string;
    assetAccountId: string;
    assetSubAccountId: string;
    assetCategoryId: string;
    equipmentTypeId: string;
    unitPrice: number;
    customAssetNo1?: string;
    customAssetNo2?: string;
    scrapReason?: string;
    scrapDate: number;
    estimatedScrapYear?: number;
    usableAfterScrap?: string;
    createTime: number;
    createUserId: string;
    updateTime?: number;
    updateUserId?: string;
    deleteTime?: number;
    deleteUserId?: string;
    accessoryEquipments: AccessoryEquipment[];
    assetInsuranceUnits: any[];
    assetAmortizationSources: any[];
    assetAssetSources: any[];
}

// 財產詳細資料 
export interface AssetDetail {
    asset: Asset;
    accessoryEquipments: AccessoryEquipment[];
    insuranceUnits: InsuranceUnit[];
    amortizationSources: AmortizationSource[];
    assetSources: AssetSource[];
}

// 獲取所有財產
export async function getAssets(): Promise<ApiResponse<AssetDetail[]>> {
    try {
        const response = await httpClient(apiEndpoints.getAssets, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取財產列表失敗",
            data: [],
        };
    }
}

// 獲取單一財產詳細資料
export async function getAssetById(id: string): Promise<ApiResponse<AssetDetail>> {
    try {
        const response = await httpClient(`${apiEndpoints.getAssetDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取財產詳細資料失敗",
            data: {} as AssetDetail,
        };
    }
}

// 新增財產
export async function addAsset(data: AssetDetail): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.addAsset, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增財產失敗",
            data: null,
        };
    }
}

// 編輯財產
export async function editAsset(data: AssetDetail): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.editAsset, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        console.log(JSON.stringify(data));
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯財產失敗",
            data: null,
        };
    }
}

// 刪除財產
export async function deleteAsset(data: AssetDetail): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.deleteAsset, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除財產失敗",
            data: null,
        };
    }
}

interface NewAssetNoResponse {
    assetNo: string;
    message: string;
    success: boolean;
}

// 獲取新財產編號
export async function getNewAssetNo(subject?: string, subSubject?: string, category?: string): Promise<ApiResponse<NewAssetNoResponse>> {
    try {
        const queryParams = new URLSearchParams();
        if (subject) queryParams.append('subject', subject);
        if (subSubject) queryParams.append('subSubject', subSubject);
        if (category) queryParams.append('category', category);

        const url = `${apiEndpoints.getNewAssetNo}?${queryParams.toString()}`;

        const response = await httpClient(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取新財產編號失敗",
            data: { assetNo: "", message: "", success: false },
        };
    }
}

// 驗證批次轉檔Excel檔案
export async function validateExcelFile(file: File): Promise<ApiResponse<any>> {
    try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await httpClient(apiEndpoints.validateExcelFile, {
            method: "POST",
            body: formData,
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "驗證Excel檔案失敗",
            data: null,
        };
    }
}

// 批次匯入資產資料
export const batchImport = async (file: File, userId: string = "system") => {
    try {
        const formData = new FormData();
        formData.append("file", file);

        const response = await httpClient(`${apiEndpoints.batchImport}?userId=${userId}`, {
            method: "POST",
            body: formData,
        });

        return response;
    } catch (error) {
        console.error("批次匯入資產資料失敗:", error);
        return {
            success: false,
            message: error instanceof Error ? error.message : "批次匯入時發生未知錯誤",
        };
    }
};

// 取消批次匯入
export const cancelBatchImport = async (userId: string = "system") => {
    try {
        const response = await httpClient(`${apiEndpoints.cancelBatchImport}?userId=${userId}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({ userId }),
        });
        return response;
    } catch (error) {
        console.error("取消批次匯入失敗:", error);
        return {
            success: false,
            message: error instanceof Error ? error.message : "取消批次匯入時發生未知錯誤",
        };
    }
};

// 下載批次轉檔範本
export async function downloadBatchTemplate(): Promise<Blob | null> {
    try {
        // 從cookies獲取token
        const token = getCookie('token') || '';

        // 使用原生fetch但確保有正確的認證頭
        const response = await fetch(apiEndpoints.downloadBatchTemplate, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${token}`
            },
            credentials: 'include'  // 包含cookies
        });

        if (!response.ok) {
            const errorText = await response.text().catch(() => "未知錯誤");
            throw new Error(`下載失敗 (${response.status}): ${errorText}`);
        }

        // 檢查Content-Type是否為excel
        const contentType = response.headers.get("content-type");
        if (!contentType || !contentType.includes("spreadsheet")) {
            throw new Error(`非預期的檔案類型: ${contentType}`);
        }

        return await response.blob();
    } catch (error: any) {
        console.error("下載範本失敗:", error);
        throw error;
    }
}

// ========== 統計相關介面定義 ==========

// 財產統計總覽
export interface AssetStatisticsOverview {
    totalAssets: number;
    totalValue: number;
    totalDepreciation: number;
    totalNetValue: number;
    thisYearNewAssets: number;
    thisMonthNewAssets: number;
    pendingScrapAssets: number;
    underMaintenanceAssets: number;
    idleAssets: number;
    reusableAfterScrapAssets: number;
    generatedTime: string;
}

// 部門統計
export interface DepartmentStatistics {
    departmentId: string;
    departmentName: string;
    assetCount: number;
    totalValue: number;
    totalDepreciation: number;
    netValue: number;
    percentage: number;
}

// 狀態統計
export interface StatusStatistics {
    statusId: string;
    statusName: string;
    assetCount: number;
    totalValue: number;
    percentage: number;
}

// 科目統計
export interface AccountStatistics {
    accountId: string;
    accountNo: string;
    accountName: string;
    assetCount: number;
    totalValue: number;
    totalDepreciation: number;
    netValue: number;
    percentage: number;
}

// 年度統計
export interface YearStatistics {
    year: number;
    newAssets: number;
    newAssetsValue: number;
    scrappedAssets: number;
    scrappedAssetsValue: number;
    yearDepreciation: number;
    yearEndTotalAssets: number;
    yearEndTotalValue: number;
}

// 月度統計
export interface MonthStatistics {
    year: number;
    month: number;
    yearMonth: string;
    newAssets: number;
    newAssetsValue: number;
    scrappedAssets: number;
    scrappedAssetsValue: number;
    monthDepreciation: number;
}

// 廠牌統計
export interface ManufacturerStatistics {
    manufacturerId: string;
    manufacturerName: string;
    assetCount: number;
    totalValue: number;
    percentage: number;
}

// 設備類型統計
export interface EquipmentTypeStatistics {
    equipmentTypeId: string;
    equipmentTypeName: string;
    assetCount: number;
    totalValue: number;
    percentage: number;
}

// 使用年限統計
export interface AgeStatistics {
    ageRange: string;
    assetCount: number;
    totalValue: number;
    percentage: number;
}

// 折舊統計
export interface DepreciationStatistics {
    year: number;
    month: number;
    yearMonth: string;
    currentDepreciation: number;
    accumulatedDepreciation: number;
    depreciatedAssetsCount: number;
}

// 價值區間統計
export interface ValueRangeStatistics {
    valueRange: string;
    minValue: number;
    maxValue: number;
    assetCount: number;
    totalValue: number;
    percentage: number;
}

// ========== 統計API調用函數 ==========

// 取得財產統計總覽
export async function getAssetStatisticsOverview(): Promise<ApiResponse<AssetStatisticsOverview>> {
    try {
        const response = await httpClient(`${API_BASE_URL}/pms/Asset/statistics/overview`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取統計總覽失敗",
            data: {} as AssetStatisticsOverview,
        };
    }
}

// 按部門取得統計
export async function getAssetStatisticsByDepartment(departmentIds?: string[]): Promise<ApiResponse<DepartmentStatistics[]>> {
    try {
        let url = `${API_BASE_URL}/pms/Asset/statistics/by-department`;
        if (departmentIds && departmentIds.length > 0) {
            const params = new URLSearchParams();
            departmentIds.forEach(id => params.append('departmentIds', id));
            url += `?${params.toString()}`;
        }

        const response = await httpClient(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取部門統計失敗",
            data: [],
        };
    }
}

// 按狀態取得統計
export async function getAssetStatisticsByStatus(statusIds?: string[]): Promise<ApiResponse<StatusStatistics[]>> {
    try {
        let url = `${API_BASE_URL}/pms/Asset/statistics/by-status`;
        if (statusIds && statusIds.length > 0) {
            const params = new URLSearchParams();
            statusIds.forEach(id => params.append('statusIds', id));
            url += `?${params.toString()}`;
        }

        const response = await httpClient(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取狀態統計失敗",
            data: [],
        };
    }
}

// 按科目取得統計
export async function getAssetStatisticsByAccount(accountIds?: string[]): Promise<ApiResponse<AccountStatistics[]>> {
    try {
        let url = `${API_BASE_URL}/pms/Asset/statistics/by-account`;
        if (accountIds && accountIds.length > 0) {
            const params = new URLSearchParams();
            accountIds.forEach(id => params.append('accountIds', id));
            url += `?${params.toString()}`;
        }

        const response = await httpClient(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取科目統計失敗",
            data: [],
        };
    }
}

// 按年度取得統計
export async function getAssetStatisticsByYear(startYear?: number, endYear?: number): Promise<ApiResponse<YearStatistics[]>> {
    try {
        const params = new URLSearchParams();
        if (startYear) params.append('startYear', startYear.toString());
        if (endYear) params.append('endYear', endYear.toString());

        const url = `${API_BASE_URL}/pms/Asset/statistics/by-year${params.toString() ? '?' + params.toString() : ''}`;

        const response = await httpClient(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取年度統計失敗",
            data: [],
        };
    }
}

// 按月度取得統計
export async function getAssetStatisticsByMonth(year?: number, months?: number): Promise<ApiResponse<MonthStatistics[]>> {
    try {
        const params = new URLSearchParams();
        if (year) params.append('year', year.toString());
        if (months) params.append('months', months.toString());

        const url = `${API_BASE_URL}/pms/Asset/statistics/by-month${params.toString() ? '?' + params.toString() : ''}`;

        const response = await httpClient(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取月度統計失敗",
            data: [],
        };
    }
}

// 按廠牌取得統計
export async function getAssetStatisticsByManufacturer(): Promise<ApiResponse<ManufacturerStatistics[]>> {
    try {
        const response = await httpClient(`${API_BASE_URL}/pms/Asset/statistics/by-manufacturer`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取廠牌統計失敗",
            data: [],
        };
    }
}

// 按設備類型取得統計
export async function getAssetStatisticsByEquipmentType(): Promise<ApiResponse<EquipmentTypeStatistics[]>> {
    try {
        const response = await httpClient(`${API_BASE_URL}/pms/Asset/statistics/by-equipment-type`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取設備類型統計失敗",
            data: [],
        };
    }
}

// 按使用年限取得統計
export async function getAssetStatisticsByAge(): Promise<ApiResponse<AgeStatistics[]>> {
    try {
        const response = await httpClient(`${API_BASE_URL}/pms/Asset/statistics/by-age`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取使用年限統計失敗",
            data: [],
        };
    }
}

// 取得折舊統計
export async function getAssetStatisticsDepreciation(year?: number, months?: number): Promise<ApiResponse<DepreciationStatistics[]>> {
    try {
        const params = new URLSearchParams();
        if (year) params.append('year', year.toString());
        if (months) params.append('months', months.toString());

        const url = `${API_BASE_URL}/pms/Asset/statistics/depreciation${params.toString() ? '?' + params.toString() : ''}`;

        const response = await httpClient(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取折舊統計失敗",
            data: [],
        };
    }
}

// 按價值區間取得統計
export async function getAssetStatisticsByValueRange(): Promise<ApiResponse<ValueRangeStatistics[]>> {
    try {
        const response = await httpClient(`${API_BASE_URL}/pms/Asset/statistics/by-value-range`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取價值區間統計失敗",
            data: [],
        };
    }
}

// ========== 固定資產明細表相關 ==========

// 固定資產明細表查詢條件
export interface FixedAssetDetailReportQuery {
    assetAccountId?: string;
    assetSubAccountId?: string;
    departmentId?: string;
    assetStatusId?: string;
    assetNo?: string;
    assetName?: string;
    acquisitionDateStart?: number;
    acquisitionDateEnd?: number;
    includeScrapAssets?: boolean;
    includeSoldAssets?: boolean;
    includeDonatedAssets?: boolean;
}

// 固定資產明細表項目
export interface FixedAssetDetailReportItem {
    assetId: string;
    assetNo: string;
    assetName: string;
    acquisitionDate: number;
    acquisitionDateFormatted: string;
    specification: string;
    quantity: number;
    unitName: string;
    unitPrice: number;
    acquisitionValue: number;
    accumulatedDepreciation: number;
    currentYearAccumulatedDepreciation: number;
    netBookValue: number;
    notes: string;
    assetAccountName: string;
    assetSubAccountName: string;
    departmentName: string;
    custodianName: string;
    userName: string;
    storageLocationName: string;
    serviceLife: number;
    usage: string;
    assetStatusName: string;
}

// 產生固定資產明細表
export async function generateFixedAssetDetailReport(query: FixedAssetDetailReportQuery): Promise<ApiResponse<FixedAssetDetailReportItem[]>> {
    try {
        const response = await httpClient(apiEndpoints.generateFixedAssetDetailReport, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(query),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "產生固定資產明細表失敗",
            data: [],
        };
    }
}


