'use client';

import { Card, Typography, Button, Flex } from 'antd';
import { ArrowLeftOutlined, SafetyOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import InsuranceGradeManagement from './module/InsuranceGradeManagement';
import AnnualInsuranceRateManagement from './module/AnnualInsuranceRateManagement';

const { Title, Text } = Typography;

export default function InsuranceGradePage() {
    const router = useRouter();

    return (
        <div>
            <Card
                title={
                    <Flex align="center" gap="middle">
                        <div style={{
                            padding: '12px',
                            borderRadius: '12px',
                            background: 'linear-gradient(135deg, rgba(52, 199, 89, 0.15), rgba(52, 199, 89, 0.25))',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                            <SafetyOutlined style={{ fontSize: '24px', color: '#34c759' }} />
                        </div>
                        <div>
                            <Title level={3} style={{ margin: 0, color: '#262626' }}>
                                勞健保級距等級
                            </Title>
                            <Text type="secondary" style={{ fontSize: '14px' }}>
                                設定與管理勞保、健保費用級距表
                            </Text>
                        </div>
                    </Flex>
                }
                extra={
                    <Button
                        icon={<ArrowLeftOutlined />}
                        onClick={() => router.back()}
                        style={{
                            borderRadius: '8px',
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            borderColor: 'transparent',
                            color: 'white'
                        }}
                    >
                        返回
                    </Button>
                }
                style={{
                    borderRadius: '16px',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                    background: 'rgba(255, 255, 255, 0.9)',
                    backdropFilter: 'blur(10px)',
                    border: 'none'
                }}
                bodyStyle={{
                    padding: '32px'
                }}
            >
                <AnnualInsuranceRateManagement />
                <InsuranceGradeManagement />
            </Card>
        </div>
    );
} 