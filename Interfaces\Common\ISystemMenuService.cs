using FAST_ERP_Backend.Models.Common;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface ISystemMenuService
    {
        Task<List<SystemMenuDTO>> GetSystemMenuAsync(string systemMenuId = "");
        Task<List<SystemGroupsNode>> GetGroupMenuAsync(string systemGroupId = "",string rolesId = "");
        Task<(bool, string)> AddSystemMenuAsync(SystemMenuDTO systemMenu,String tokenUid="");
        Task<(bool, string)> EditSystemMenuAsync(SystemMenuDTO systemMenu,String tokenUid="");
        Task<(bool, string)> DeleteSystemMenuAsync(SystemMenuDTO systemMenu,String tokenUid="");
        Task<bool> VerifyMenuAsync(string verifyMenuName, string tokenUid);

        /// <summary>
        /// 識別選單樹中的末節點（葉子節點）
        /// 用於支援末節點權限儲存策略
        /// </summary>
        /// <returns>所有末節點的ID列表</returns>
        Task<List<string>> GetLeafMenuIdsAsync();
    }
}
