using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
using FAST_ERP_Backend.Attributes;
using FAST_ERP_Backend.Models.Rms.Enums;

namespace FAST_ERP_Backend.Models.Rms
{
    /// <summary>
    /// 房源資料表
    /// </summary>
    public class Property : ModelBaseEntity
    {
        /// <summary>
        /// 房源編號
        /// </summary>
        [Key]
        [Comment("房源編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string PropertyId { get; set; }

        /// <summary>
        /// 房源名稱
        /// </summary>
        [Comment("房源名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string PropertyName { get; set; }

        /// <summary>
        /// 房源地址
        /// </summary>
        [Comment("房源地址")]
        [Column(TypeName = "nvarchar(200)")]
        public string Address { get; set; }

        /// <summary>
        /// 房源狀態（空置/出租/維修等）
        /// </summary>
        [Comment("房源狀態")]
        [Column(TypeName = "nvarchar(50)")]
        public string Status { get; set; }

        /// <summary>
        /// 房屋面積(m²)
        /// </summary>
        [Comment("房屋面積")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Area { get; set; }

        /// <summary>
        /// 樓層
        /// </summary>
        [Comment("樓層")]
        [Column(TypeName = "nvarchar(20)")]
        public string Floor { get; set; }

        /// <summary>
        /// 房型（辦公/住宅/店面等）
        /// </summary>
        [Comment("房型")]
        [Column(TypeName = "nvarchar(50)")]
        public string Type { get; set; }

        /// <summary>
        /// 所有權人
        /// </summary>
        [Comment("所有權人")]
        [Column(TypeName = "nvarchar(100)")]
        public string Owner { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Note { get; set; }

        /// <summary>
        /// 房源合約集合
        /// </summary>
        public virtual ICollection<Contract> Contracts { get; set; }

        /// <summary>
        /// 初始化房源實體
        /// </summary>
        public Property()
        {
            Contracts = new HashSet<Contract>();
        }

        /// <summary>
        /// 是否已出租（計算屬性）
        /// </summary>
        [NotMapped]
        public bool IsRented => Status == PropertyStatusInfo.Rented.Code;
    }

    /// <summary>
    /// 房源資料傳輸物件
    /// </summary>
    public class PropertyDTO : ModelBaseEntityDTO
    {
        /// <summary>
        /// 房源編號
        /// </summary>
        public string PropertyId { get; set; }

        /// <summary>
        /// 房源名稱
        /// </summary>
        public string PropertyName { get; set; }

        /// <summary>
        /// 房源地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 房源狀態
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 房屋面積
        /// </summary>
        public decimal Area { get; set; }

        /// <summary>
        /// 樓層
        /// </summary>
        public string Floor { get; set; }

        /// <summary>
        /// 房型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 所有權人
        /// </summary>
        public string Owner { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// 初始化房源資料傳輸物件
        /// </summary>
        public PropertyDTO()
        {
        }
    }

    /// <summary>
    /// 房源查詢請求 DTO
    /// </summary>
    public class PropertyQueryRequestDTO
    {
        /// <summary>
        /// 房源編號（可選）
        /// </summary>
        public string? PropertyId { get; set; }

        /// <summary>
        /// 房源名稱（可選）
        /// </summary>
        public string? PropertyName { get; set; }

        /// <summary>
        /// 房源地址（可選）
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// 房源狀態（可選）
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 房型（可選）
        /// </summary>
        public string? Type { get; set; }

        /// <summary>
        /// 所有權人（可選）
        /// </summary>
        public string? Owner { get; set; }
    }

    /// <summary>
    /// 房源新增請求 DTO
    /// </summary>
    public class PropertyCreateRequestDTO
    {
        /// <summary>
        /// 房源名稱
        /// </summary>
        [Required(ErrorMessage = "房源名稱不能為空")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "房源名稱長度必須在2-100字元之間")]
        [RegularExpression(@"^[\u4e00-\u9fa5a-zA-Z0-9_\s]+$", ErrorMessage = "房源名稱只能包含中文、英文、數字、底線和空格")]
        [PropertyCreateValidation]
        public string PropertyName { get; set; }

        /// <summary>
        /// 房源地址
        /// </summary>
        [Required(ErrorMessage = "房源地址不能為空")]
        [StringLength(200, MinimumLength = 5, ErrorMessage = "房源地址長度必須在5-200字元之間")]
        [RegularExpression(@"^[\u4e00-\u9fa5a-zA-Z0-9_\s\-\(\)]+$", ErrorMessage = "房源地址格式不正確")]
        public string Address { get; set; }

        /// <summary>
        /// 房源狀態
        /// </summary>
        [Required(ErrorMessage = "房源狀態不能為空")]
        public string Status { get; set; }

        /// <summary>
        /// 房屋面積
        /// </summary>
        [Required(ErrorMessage = "房屋面積不能為空")]
        public decimal Area { get; set; }

        /// <summary>
        /// 樓層
        /// </summary>
        [StringLength(20, ErrorMessage = "樓層長度不能超過20字元")]
        public string Floor { get; set; }

        /// <summary>
        /// 房型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 所有權人
        /// </summary>
        [StringLength(100, ErrorMessage = "所有權人長度不能超過100字元")]
        public string Owner { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        [StringLength(500, ErrorMessage = "備註長度不能超過500字元")]
        public string Note { get; set; }
    }

    /// <summary>
    /// 房源更新請求 DTO
    /// </summary>
    public class PropertyUpdateRequestDTO
    {
        /// <summary>
        /// 房源編號
        /// </summary>
        [Required(ErrorMessage = "房源編號不能為空")]
        [PropertyUpdateValidation]
        public string PropertyId { get; set; }

        /// <summary>
        /// 房源名稱
        /// </summary>
        [Required(ErrorMessage = "房源名稱不能為空")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "房源名稱長度必須在2-100字元之間")]
        [RegularExpression(@"^[\u4e00-\u9fa5a-zA-Z0-9_\s]+$", ErrorMessage = "房源名稱只能包含中文、英文、數字、底線和空格")]
        public string PropertyName { get; set; }

        /// <summary>
        /// 房源地址
        /// </summary>
        [Required(ErrorMessage = "房源地址不能為空")]
        [StringLength(200, MinimumLength = 5, ErrorMessage = "房源地址長度必須在5-200字元之間")]
        [RegularExpression(@"^[\u4e00-\u9fa5a-zA-Z0-9_\s\-\(\)]+$", ErrorMessage = "房源地址格式不正確")]
        public string Address { get; set; }

        /// <summary>
        /// 房源狀態
        /// </summary>
        [Required(ErrorMessage = "房源狀態不能為空")]
        [PropertyStatusTransition]
        [PropertyStatusValid]
        public string Status { get; set; }

        /// <summary>
        /// 房屋面積
        /// </summary>
        [Required(ErrorMessage = "房屋面積不能為空")]
        public decimal Area { get; set; }

        /// <summary>
        /// 樓層
        /// </summary>
        [StringLength(20, ErrorMessage = "樓層長度不能超過20字元")]
        public string Floor { get; set; }

        /// <summary>
        /// 房型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 所有權人
        /// </summary>
        [StringLength(100, ErrorMessage = "所有權人長度不能超過100字元")]
        public string Owner { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        [StringLength(500, ErrorMessage = "備註長度不能超過500字元")]
        public string Note { get; set; }
    }

    /// <summary>
    /// 房源刪除請求 DTO
    /// </summary>
    public class PropertyDeleteRequestDTO
    {
        /// <summary>
        /// 房源編號
        /// </summary>
        [Required(ErrorMessage = "房源編號不能為空")]
        [PropertyExists]
        [PropertyCanDelete]
        public string PropertyId { get; set; }
    }

    /// <summary>
    /// 房源可刪除驗證屬性（檢查是否有相關合約）
    /// </summary>
    public class PropertyCanDeleteAttribute : ValidationAttribute
    {
        /// <summary>
        /// 初始化房源可刪除驗證屬性
        /// </summary>
        public PropertyCanDeleteAttribute()
        {
            ErrorMessage = "該房源有相關合約，無法刪除";
        }

        /// <summary>
        /// 驗證房源是否可以刪除
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            // 嘗試從服務容器中取得 DbContext
            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            var propertyId = value.ToString();
            
            // 檢查是否有相關合約（全域查詢過濾器會自動過濾軟刪除的合約）
            var hasContracts = context.Rms_Contracts
                .Any(c => c.PropertyId == propertyId);

            if (hasContracts)
            {
                return new ValidationResult(ErrorMessage);
            }

            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 房源存在驗證屬性
    /// </summary>
    public class PropertyExistsAttribute : ValidationAttribute
    {
        /// <summary>
        /// 初始化房源存在驗證屬性
        /// </summary>
        public PropertyExistsAttribute()
        {
            ErrorMessage = "房源不存在";
        }

        /// <summary>
        /// 驗證房源是否存在
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            // 這裡只做基本的 null/empty 檢查
            // 實際的資料庫檢查會在 Service 層處理
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return new ValidationResult("房源編號不能為空");
            }

            // 嘗試從服務容器中取得 DbContext
            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            var propertyId = value.ToString();
            
            // 檢查房源是否存在（全域查詢過濾器會自動過濾軟刪除的房源）
            var exists = context.Rms_Properties
                .Any(p => p.PropertyId == propertyId);

            if (!exists)
            {
                return new ValidationResult(ErrorMessage);
            }

            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 房源狀態值驗證屬性（檢查是否為允許的手動調整狀態）
    /// </summary>
    public class PropertyStatusValidAttribute : ValidationAttribute
    {
        /// <summary>
        /// 初始化房源狀態值驗證屬性
        /// </summary>
        public PropertyStatusValidAttribute()
        {
            ErrorMessage = "房源狀態僅允許在空置、維修、停用間調整";
        }

        /// <summary>
        /// 驗證房源狀態值是否有效
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var status = value.ToString();
            var allowStatus = new[] { PropertyStatusInfo.Vacant.Code, PropertyStatusInfo.Maintenance.Code, PropertyStatusInfo.Disabled.Code }; // 空置、維修、停用

            if (!allowStatus.Contains(status))
            {
                return new ValidationResult(ErrorMessage);
            }

            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 房源狀態轉換驗證屬性（禁止手動設為出租）
    /// </summary>
    public class PropertyStatusTransitionAttribute : ValidationAttribute
    {
        /// <summary>
        /// 初始化房源狀態轉換驗證屬性
        /// </summary>
        public PropertyStatusTransitionAttribute()
        {
            ErrorMessage = "房源狀態不可手動設為出租，請透過合約啟用流程進行";
        }

        /// <summary>
        /// 驗證房源狀態轉換是否有效
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var status = value.ToString();
            
            if (status == PropertyStatusInfo.Rented.Code) // 出租狀態
            {
                return new ValidationResult(ErrorMessage);
            }

            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 房源建立複合驗證屬性（優化 SQL 查詢）
    /// </summary>
    public class PropertyCreateValidationAttribute : ValidationAttribute
    {
        /// <summary>
        /// 初始化房源建立驗證屬性
        /// </summary>
        public PropertyCreateValidationAttribute()
        {
            ErrorMessage = "房源建立驗證失敗";
        }

        /// <summary>
        /// 驗證房源建立資料
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            // 嘗試從服務容器中取得 DbContext
            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            // 獲取當前請求的 DTO 實例以進行額外的業務邏輯驗證
            var instance = validationContext.ObjectInstance;
            if (instance is PropertyCreateRequestDTO createRequest)
            {
                // 檢查房源名稱是否重複
                var existingProperty = context.Rms_Properties
                    .FirstOrDefault(p => p.PropertyName == createRequest.PropertyName);

                if (existingProperty != null)
                {
                    return new ValidationResult("房源名稱已存在");
                }

                // 檢查地址是否重複
                var existingAddress = context.Rms_Properties
                    .FirstOrDefault(p => p.Address == createRequest.Address);

                if (existingAddress != null)
                {
                    return new ValidationResult("房源地址已存在");
                }
            }

            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 房源更新複合驗證屬性（優化 SQL 查詢）
    /// </summary>
    public class PropertyUpdateValidationAttribute : ValidationAttribute
    {
        /// <summary>
        /// 初始化房源更新驗證屬性
        /// </summary>
        public PropertyUpdateValidationAttribute()
        {
            ErrorMessage = "房源更新驗證失敗";
        }

        /// <summary>
        /// 驗證房源更新資料
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            // 嘗試從服務容器中取得 DbContext
            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            var propertyId = value.ToString();

            // 一次查詢取得房源資料
            var property = context.Rms_Properties
                .FirstOrDefault(p => p.PropertyId == propertyId);

            if (property == null)
            {
                return new ValidationResult("房源不存在");
            }

            if (property.Status == PropertyStatusInfo.Rented.Code)
            {
                return new ValidationResult("該房源已出租，無法更新");
            }

            // 獲取當前請求的 DTO 實例以進行額外的業務邏輯驗證
            var instance = validationContext.ObjectInstance;
            if (instance is PropertyUpdateRequestDTO updateRequest)
            {
                // 檢查房源名稱是否重複（排除自己）
                var existingProperty = context.Rms_Properties
                    .FirstOrDefault(p => p.PropertyName == updateRequest.PropertyName && p.PropertyId != propertyId);

                if (existingProperty != null)
                {
                    return new ValidationResult("房源名稱已存在");
                }

                // 檢查地址是否重複（排除自己）
                var existingAddress = context.Rms_Properties
                    .FirstOrDefault(p => p.Address == updateRequest.Address && p.PropertyId != propertyId);

                if (existingAddress != null)
                {
                    return new ValidationResult("房源地址已存在");
                }
            }

            return ValidationResult.Success;
        }
    }
} 