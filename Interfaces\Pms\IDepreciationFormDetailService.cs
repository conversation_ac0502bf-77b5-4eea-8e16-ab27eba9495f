using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IDepreciationFormDetailService
    {
        /// <summary>
        /// 取得折舊紀錄資料
        /// </summary>
        /// <returns>折舊紀錄資料列表</returns>
        Task<List<DepreciationFormDetailDTO>> GetDepreciationAsync();

        /// <summary>
        /// 依年月取得折舊紀錄資料
        /// </summary>
        /// <param name="year">西元年(可選)</param>
        /// <param name="month">月份(可選)</param>
        /// <param name="hasCreateDepreciationFormDate">是否有新增折舊單日期(可選): true=有日期, false=無日期, null=全部</param>
        /// <returns>折舊紀錄資料列表</returns>
        Task<List<DepreciationFormDetailDTO>> GetDepreciationByYearMonthAsync(int? year = null, int? month = null, bool? hasCreateDepreciationFormDate = null);

        /// <summary>
        /// 新增折舊紀錄
        /// </summary>
        /// <param name="_data">折舊紀錄資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddDepreciationAsync(DepreciationFormDetailDTO _data);

        /// <summary>
        /// 編輯折舊紀錄
        /// </summary>
        /// <param name="_data">折舊紀錄資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditDepreciationAsync(DepreciationFormDetailDTO _data);

        /// <summary>
        /// 刪除折舊紀錄
        /// </summary>
        /// <param name="_data">折舊紀錄資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteDepreciationAsync(DepreciationFormDetailDTO _data);


        /// <summary>
        /// 依財產編號取得折舊紀錄
        /// </summary>
        /// <param name="_assetId">財產編號</param>
        /// <returns>折舊紀錄資料列表</returns>
        Task<List<DepreciationFormDetailDTO>> GetDepreciationByAssetAsync(string _assetId);

        /// <summary>
        /// 取得可用折舊方法
        /// </summary>
        /// <returns>折舊方法列表</returns>
        Task<(bool success, List<object> methods, string message)> GetAvailableDepreciationMethodsAsync();

        /// <summary>
        /// 取得指定財產科目餘額遞減法折舊率
        /// </summary>
        /// <param name="assetAccountId">財產科目ID</param>
        /// <returns>折舊率及訊息</returns>
        Task<(bool success, decimal rate, string message)> GetDecliningBalanceRateForAssetAccountAsync(string assetAccountId);

        /// <summary>
        /// 產生整期折舊紀錄
        /// </summary>
        /// <param name="assetId">財產編號</param>
        /// <param name="userId">使用者編號</param>
        /// <returns>成功與否、訊息</returns>
        Task<(bool success, string message)> GenerateDepreciationScheduleAsync(string assetId, string userId);

        /// <summary>
        /// 產生固定資產明細表
        /// </summary>
        /// <param name="request">查詢條件</param>
        /// <returns>固定資產明細表資料</returns>
        Task<List<FixedAssetDetailReportDTO>> GenerateFixedAssetDetailReportAsync(FixedAssetDetailReportRequestDTO request);
    }
}