using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Rms;

namespace FAST_ERP_Backend.Interfaces.Rms
{
    /// <summary>
    /// 繳費建立請求
    /// </summary>
    public class PaymentCreateRequest
    {
        /// <summary>
        /// 合約編號
        /// </summary>
        public string ContractId { get; set; }
        
        /// <summary>
        /// 繳費日期（Unix 時間戳）
        /// </summary>
        public long PaymentDate { get; set; }
        
        /// <summary>
        /// 繳費金額（後端可覆蓋為 allocations 加總）
        /// </summary>
        public decimal Amount { get; set; }
        
        /// <summary>
        /// 繳費類型
        /// </summary>
        public string PaymentType { get; set; }
        
        /// <summary>
        /// 備註
        /// </summary>
        public string? Note { get; set; }
        
        /// <summary>
        /// 分配項目列表
        /// </summary>
        public List<PaymentAllocationItem> Allocations { get; set; } = new();
    }

    /// <summary>
    /// 繳費分配項目
    /// </summary>
    public class PaymentAllocationItem
    {
        /// <summary>
        /// 費用編號
        /// </summary>
        public string FeeId { get; set; }
        
        /// <summary>
        /// 分配金額
        /// </summary>
        public decimal Amount { get; set; }
    }

    /// <summary>
    /// 繳費管理服務介面
    /// </summary>
    public interface IPaymentService
    {
        /// <summary>
        /// 建立繳費記錄
        /// </summary>
        /// <param name="request">繳費建立請求</param>
        /// <param name="tokenUid">使用者識別碼</param>
        /// <returns>建立結果</returns>
        Task<ApiResponse<string>> CreatePaymentAsync(PaymentCreateRequest request, string tokenUid);
        
        /// <summary>
        /// 取得繳費記錄列表
        /// </summary>
        /// <param name="contractId">合約編號</param>
        /// <returns>繳費記錄列表</returns>
        Task<ApiResponse<List<PaymentDTO>>> GetPaymentsAsync(string contractId);
    }
}


