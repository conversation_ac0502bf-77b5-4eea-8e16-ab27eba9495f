using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using FAST_ERP_Backend.Server.Tools;

namespace FAST_ERP_Backend.Services.Pms
{
    public class DepreciationFormService : IDepreciationFormService
    {
        private readonly ERPDbContext _context;
        private readonly IMapper _mapper;
        private readonly Baseform _baseform;

        public DepreciationFormService(ERPDbContext context, IMapper mapper, Baseform baseform)
        {
            _context = context;
            _mapper = mapper;
            _baseform = baseform;
        }

        /// <summary>
        /// 取得折舊單資料
        /// </summary>
        /// <returns>折舊單資料列表</returns>
        public async Task<List<DepreciationFormDTO>> GetDepreciationFormsAsync()
        {
            // 查詢折舊主檔
            var query = from d in _context.Set<DepreciationForm>().Where(d => !d.IsDeleted)
                        select d;

            var depreciationForms = await query
                .OrderBy(item => item.DepreciationYear)
                .ThenBy(item => item.DepreciationMonth)
                .ToListAsync();

            // 批次查詢建立者名稱，避免 N+1 查詢
            var createUserIds = depreciationForms
                .Select(f => f.CreateUserId)
                .Where(id => !string.IsNullOrEmpty(id))
                .Distinct()
                .ToList();

            // 批次查詢審核人員名稱，避免 N+1 查詢
            var approverIds = depreciationForms
                .Select(f => f.ApproverId)
                .Where(id => !string.IsNullOrEmpty(id))
                .Distinct()
                .ToList();

            var allUserIds = createUserIds.Union(approverIds).Distinct().ToList();

            var userNameDict = await _context.Common_Users
                .Where(u => allUserIds.Contains(u.UserId))
                .Select(u => new { u.UserId, u.Name })
                .ToDictionaryAsync(x => x.UserId, x => x.Name);

            var result = new List<DepreciationFormDTO>();

            foreach (var form in depreciationForms)
            {
                // 映射主檔資料
                var formDto = _mapper.Map<DepreciationFormDTO>(form);

                // 設定狀態名稱
                var statusNames = DepreciationFormStatus.GetStatusNames();
                formDto.StatusName = statusNames.GetValueOrDefault(form.Status, form.Status);

                // 使用批次查詢結果設定建立者名稱
                if (!string.IsNullOrEmpty(form.CreateUserId) && userNameDict.TryGetValue(form.CreateUserId, out var createUserName))
                {
                    formDto.CreateUserName = createUserName;
                }

                // 使用批次查詢結果設定審核人員名稱
                if (!string.IsNullOrEmpty(form.ApproverId) && userNameDict.TryGetValue(form.ApproverId, out var approverName))
                {
                    formDto.ApproverName = approverName;
                }

                // 查詢相關的折舊明細
                var detailsQuery = from detail in _context.Set<DepreciationFormDetail>().Where(d => !d.IsDeleted)
                                   join asset in _context.Pms_Assets on detail.AssetId equals asset.AssetId into assetGroup
                                   from asset in assetGroup.DefaultIfEmpty()
                                   join account in _context.Pms_AssetAccounts on asset.AssetAccountId equals account.AssetAccountId into accountGroup
                                   from account in accountGroup.DefaultIfEmpty()
                                   orderby detail.DepreciationYear, detail.DepreciationMonth, account.AssetAccountNo
                                   where detail.DepreciationYear == form.DepreciationYear
                                         && detail.DepreciationMonth == form.DepreciationMonth
                                   select new { Detail = detail, Asset = asset, Account = account };

                var details = await detailsQuery.ToListAsync();

                // 批次查詢明細建立者名稱，避免 N+1 查詢
                var detailCreateUserIds = details
                    .Where(d => !string.IsNullOrEmpty(d.Detail.CreateUserId))
                    .Select(d => d.Detail.CreateUserId)
                    .Distinct()
                    .ToList();

                var detailUserNameDict = await _context.Common_Users
                    .Where(u => detailCreateUserIds.Contains(u.UserId))
                    .Select(u => new { u.UserId, u.Name })
                    .ToDictionaryAsync(x => x.UserId, x => x.Name);

                // 映射明細資料
                formDto.DepreciationFormDetail = details.Select(d =>
                {
                    var detailDto = _mapper.Map<DepreciationFormDetailDTO>(d.Detail);
                    if (d.Asset != null)
                    {
                        detailDto.AssetNo = d.Asset.AssetNo;
                        detailDto.AssetName = d.Asset.AssetName;
                    }

                    // 使用批次查詢結果設定明細建立者名稱
                    if (!string.IsNullOrEmpty(d.Detail.CreateUserId) &&
                        detailUserNameDict.TryGetValue(d.Detail.CreateUserId, out var detailCreateUserName))
                    {
                        detailDto.CreateUserName = detailCreateUserName ?? "";
                    }
                    else
                    {
                        detailDto.CreateUserName = "";
                    }

                    return detailDto;
                }).ToList();

                result.Add(formDto);
            }

            return result;
        }

        /// <summary>
        /// 取得折舊單明細
        /// </summary>
        /// <param name="depreciationFormId">折舊單編號</param>
        /// <returns>折舊單明細資料</returns>
        public async Task<DepreciationFormDTO> GetDepreciationFormDetailsAsync(string depreciationFormId)
        {
            if (!Guid.TryParse(depreciationFormId, out Guid formId))
            {
                throw new ArgumentException("無效的折舊單編號格式");
            }

            // 查詢折舊主檔
            var form = await _context.Set<DepreciationForm>()
                .FirstOrDefaultAsync(d => d.DepreciationFormId == formId && !d.IsDeleted);

            if (form == null)
            {
                throw new InvalidOperationException("找不到指定的折舊單");
            }

            // 映射主檔資料
            var formDto = _mapper.Map<DepreciationFormDTO>(form);

            // 設定狀態名稱
            var statusNames = DepreciationFormStatus.GetStatusNames();
            formDto.StatusName = statusNames.GetValueOrDefault(form.Status, form.Status);

            // 取得建立者名稱
            if (!string.IsNullOrEmpty(form.CreateUserId))
            {
                var createUser = await _context.Common_Users
                    .Where(u => u.UserId == form.CreateUserId)
                    .Select(u => new { u.Name })
                    .FirstOrDefaultAsync();
                formDto.CreateUserName = createUser?.Name;
            }

            // 取得審核人員名稱
            if (!string.IsNullOrEmpty(form.ApproverId))
            {
                var approver = await _context.Common_Users
                    .Where(u => u.UserId == form.ApproverId)
                    .Select(u => new { u.Name })
                    .FirstOrDefaultAsync();
                formDto.ApproverName = approver?.Name;
            }

            // 查詢相關的折舊明細
            var detailsQuery = from detail in _context.Set<DepreciationFormDetail>().Where(d => !d.IsDeleted)
                               join asset in _context.Pms_Assets on detail.AssetId equals asset.AssetId into assetGroup
                               from asset in assetGroup.DefaultIfEmpty()
                               orderby detail.DepreciationYear, detail.DepreciationMonth
                               where detail.DepreciationYear == form.DepreciationYear
                                     && detail.DepreciationMonth == form.DepreciationMonth
                                     && detail.DepreciationDate > 0
                               select new { Detail = detail, Asset = asset };

            var details = await detailsQuery.ToListAsync();

            // 映射明細資料
            formDto.DepreciationFormDetail = details.Select(d =>
            {
                var detailDto = _mapper.Map<DepreciationFormDetailDTO>(d.Detail);
                if (d.Asset != null)
                {
                    detailDto.AssetNo = d.Asset.AssetNo;
                    detailDto.AssetName = d.Asset.AssetName;
                }
                return detailDto;
            }).ToList();

            return formDto;
        }

        /// <summary>
        /// 新增固定資產折舊單
        /// </summary>
        /// <param name="_data">固定資產折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        public async Task<(bool, string)> AddDepreciationFormAsync(DepreciationFormDTO _data)
        {
            try
            {
                DepreciationFormDetail? foundDetail = null;
                // 檢查折舊紀錄是否已被使用
                if (await IsDepreciationUsedAsync(_data.DepreciationId))
                {
                    return (false, "此折舊紀錄已被使用");
                }

                // 取得對應的折舊明細資料
                if (Guid.TryParse(_data.DepreciationId, out Guid depreciationId))
                {
                    var detail = await _context.Set<DepreciationFormDetail>()
                        .FirstOrDefaultAsync(d => d.DepreciationId == depreciationId && !d.IsDeleted);

                    if (detail != null)
                    {
                        foundDetail = detail;
                    }
                }

                // 依折舊年月產生折舊單號（優先使用傳入的年/月，否則落回明細或當前年月）
                var year = _data.DepreciationYear != 0 ? _data.DepreciationYear : (foundDetail?.DepreciationYear ?? DateTime.UtcNow.Year);
                var month = _data.DepreciationMonth != 0 ? _data.DepreciationMonth : (foundDetail?.DepreciationMonth ?? DateTime.UtcNow.Month);
                var depreciationFormNo = await GenerateDepreciationFormNoAsync(year, month);

                var entity = _mapper.Map<DepreciationForm>(_data);
                entity.DepreciationFormNo = depreciationFormNo;
                entity.Status = DepreciationFormStatus.PENDING; // 新增時狀態為待審核
                entity.DepreciationDate = 0; // 未審核通過前不設定折舊日期
                entity.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.IsDeleted = false;

                Console.WriteLine(DateTimeOffset.FromUnixTimeSeconds(entity.DepreciationDate).ToString("yyyy-MM-dd"));

                _context.Add(entity);
                await _context.SaveChangesAsync();

                // 更新前端傳來的折舊明細的新增折舊單日期
                if (_data.DepreciationFormDetail != null && _data.DepreciationFormDetail.Any())
                {
                    var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                    // 收集所有有效的折舊明細ID
                    var validDetailIds = new List<Guid>();
                    foreach (var detailDto in _data.DepreciationFormDetail)
                    {
                        if (Guid.TryParse(detailDto.DepreciationId, out Guid detailId))
                        {
                            validDetailIds.Add(detailId);
                        }
                        else
                        {
                            Console.WriteLine($"無效的折舊明細ID格式: {detailDto.DepreciationId}");
                        }
                    }

                    if (validDetailIds.Any())
                    {
                        // 批次查詢需要更新的折舊明細，避免 N+1 查詢
                        var detailsToUpdate = await _context.Set<DepreciationFormDetail>()
                            .Where(d => validDetailIds.Contains(d.DepreciationId) && !d.IsDeleted)
                            .ToListAsync();

                        var updatedCount = 0;
                        foreach (var detail in detailsToUpdate)
                        {
                            detail.CreateDepreciationFormDate = currentTime;
                            detail.UpdateTime = currentTime;
                            detail.UpdateUserId = _data.CreateUserId;
                            updatedCount++;

                            Console.WriteLine($"更新折舊明細 {detail.DepreciationId} 的新增折舊單日期為 {currentTime}");
                        }

                        if (updatedCount > 0)
                        {
                            await _context.SaveChangesAsync();
                            Console.WriteLine($"成功更新 {updatedCount} 筆折舊明細的新增折舊單日期");
                        }

                        // 檢查是否有找不到的記錄
                        var foundIds = detailsToUpdate.Select(d => d.DepreciationId).ToList();
                        var missingIds = validDetailIds.Except(foundIds).ToList();
                        foreach (var missingId in missingIds)
                        {
                            Console.WriteLine($"找不到對應的折舊明細記錄: {missingId}");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("沒有折舊明細需要更新 CreateDepreciationFormDate");
                }

                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯固定資產折舊單
        /// </summary>
        /// <param name="_data">固定資產折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        public async Task<(bool, string)> EditDepreciationFormAsync(DepreciationFormDTO _data)
        {
            try
            {
                var entity = await _context.Set<DepreciationForm>()
                    .FindAsync(Guid.Parse(_data.DepreciationFormId));
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                // 如果折舊紀錄編號有變更，檢查新的折舊紀錄是否已被使用
                if (entity.DepreciationId != Guid.Parse(_data.DepreciationId))
                {
                    if (await IsDepreciationUsedAsync(_data.DepreciationId))
                    {
                        return (false, "此折舊紀錄已被使用");
                    }
                }

                // 只有待審核狀態才能修改
                if (entity.Status != DepreciationFormStatus.PENDING)
                {
                    return (false, "只有待審核狀態的折舊單才能修改");
                }

                _mapper.Map(_data, entity);
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除固定資產折舊單
        /// </summary>
        /// <param name="_data">固定資產折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        public async Task<(bool, string)> DeleteDepreciationFormAsync(DepreciationFormDTO _data)
        {
            try
            {
                using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    var entity = await _context.Set<DepreciationForm>()
                        .FindAsync(Guid.Parse(_data.DepreciationFormId));
                    if (entity == null)
                    {
                        return (false, "找不到資料");
                    }

                    // 只有待審核或已駁回狀態才能刪除
                    if (entity.Status != DepreciationFormStatus.PENDING && entity.Status != DepreciationFormStatus.REJECTED)
                    {
                        return (false, "只有待審核或已駁回狀態的折舊單才能刪除");
                    }

                    // 更新折舊明細的折舊日期為0
                    var details = await _context.Set<DepreciationFormDetail>()
                        .Where(d => d.DepreciationYear == entity.DepreciationYear &&
                               d.DepreciationMonth == entity.DepreciationMonth &&
                               !d.IsDeleted)
                        .ToListAsync();

                    var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                    // 使用各筆明細的「前期累計折舊金額」回寫資產的累計折舊金額

                    foreach (var detail in details)
                    {
                        // 更新折舊明細
                        detail.DepreciationDate = 0;
                        detail.CreateDepreciationFormDate = 0;
                        detail.UpdateTime = currentTime;
                        detail.UpdateUserId = _data.DeleteUserId;
                        _context.Update(detail);

                        // 更新資產的累計折舊金額（使用前期累計折舊金額）
                        var asset = await _context.Pms_Assets
                            .FirstOrDefaultAsync(a => a.AssetId == detail.AssetId && !a.IsDeleted);
                        if (asset != null)
                        {
                            asset.AccumulatedDepreciationAmount = detail.PriorAccumulatedDepreciation;
                            asset.UpdateTime = currentTime;
                            asset.UpdateUserId = _data.DeleteUserId;
                            _context.Update(asset);
                        }
                    }

                    entity.DeleteTime = currentTime;
                    entity.DeleteUserId = _data.DeleteUserId;
                    entity.IsDeleted = true;

                    _context.Update(entity);
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();
                    return (true, "刪除成功");
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw new Exception($"刪除失敗: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 檢查折舊紀錄是否已被使用
        /// </summary>
        /// <param name="_depreciationId">折舊紀錄編號</param>
        /// <returns>是否已被使用</returns>
        public async Task<bool> IsDepreciationUsedAsync(string _depreciationId)
        {
            if (!Guid.TryParse(_depreciationId, out Guid depreciationId))
            {
                return false;
            }

            return await _context.Set<DepreciationForm>()
                .AnyAsync(d => d.DepreciationId == depreciationId && !d.IsDeleted && d.Status == DepreciationFormStatus.EXECUTED);
        }

        /// <summary>
        /// 產生財產折舊表
        /// </summary>
        public async Task<AssetDepreciationReportResponseDTO> GenerateAssetDepreciationReportAsync(AssetDepreciationReportRequestDTO request)
        {
            var response = new AssetDepreciationReportResponseDTO
            {
                ReportTitle = "財產折舊表",
                ReportPeriod = $"{request.StartDate} ~ {request.EndDate}",
                GeneratedTime = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")
            };

            // 日期轉為 timestamp（含日界）
            var startTimestamp = _baseform.DateStrToTimestamp(request.StartDate) ?? 0;
            var endTimestamp = _baseform.DateStrToTimestamp(request.EndDate) ?? 0;

            // 取得折舊紀錄，連結資產以便篩選與輸出欄位
            var query = from d in _context.Set<DepreciationFormDetail>().Where(x => !x.IsDeleted)
                        join a in _context.Pms_Assets on d.AssetId equals a.AssetId into assetGroup
                        from asset in assetGroup.DefaultIfEmpty()
                        select new { d, asset };

            // 套用篩選：部門/類別/科目/子目
            if (!string.IsNullOrEmpty(request.DepartmentId))
                query = query.Where(x => x.asset != null && x.asset.DepartmentId == request.DepartmentId);

            if (!string.IsNullOrEmpty(request.AssetCategoryId))
                query = query.Where(x => x.asset != null && x.asset.AssetCategoryId == request.AssetCategoryId);

            if (request.AssetAccountId.HasValue)
                query = query.Where(x => x.asset != null && x.asset.AssetAccountId == request.AssetAccountId.Value);

            if (request.AssetSubAccountId.HasValue)
                query = query.Where(x => x.asset != null && x.asset.AssetSubAccountId == request.AssetSubAccountId.Value);

            // 財產狀態篩選：排除已報廢、已出售、已捐贈的財產
            query = query.Where(x => x.asset != null &&
                                   (x.asset.ScrapDate == null || x.asset.ScrapDate == 0) && // 排除已報廢財產
                                   (x.asset.SoldDate == null || x.asset.SoldDate == 0) && // 排除已出售財產
                                   (x.asset.DonatedDate == null || x.asset.DonatedDate == 0)); // 排除已捐贈財產

            // 財產狀態篩選：只包含正常使用中的財產（排除已報廢、已出售、已捐贈等狀態）
            // 根據預設資料，正常使用的狀態編號為 "N"
            query = query.Where(x => x.asset != null &&
                                   x.asset.AssetStatusId != Guid.Empty); // 確保有有效的狀態

            // 耐用年限篩選：只包含有耐用年限的財產（排除土地等不折舊財產）
            query = query.Where(x => x.asset != null &&
                                   x.asset.ServiceLife > 0);

            // 期間過濾：使用 DepreciationYear/Month 推導月份第一天的 timestamp
            query = query.Where(x => x.d.DepreciationYear * 100 + x.d.DepreciationMonth >=
                                      YearMonthIntFromTs(startTimestamp)
                                  && x.d.DepreciationYear * 100 + x.d.DepreciationMonth <=
                                      YearMonthIntFromTs(endTimestamp));

            var list = await query
                .OrderBy(x => x.d.DepreciationYear)
                .ThenBy(x => x.d.DepreciationMonth)
                .ThenBy(x => x.asset.AssetNo)
                .ToListAsync();

            // 組裝報表項目
            response.Items = list.Select(x => new AssetDepreciationReportItemDTO
            {
                AssetId = x.asset?.AssetId ?? Guid.Empty,
                AssetNo = x.asset?.AssetNo ?? string.Empty,
                AssetName = x.asset?.AssetName ?? string.Empty,
                DepreciationYear = x.d.DepreciationYear,
                DepreciationMonth = x.d.DepreciationMonth,
                OriginalAmount = x.d.OriginalAmount,
                BeginningBookValue = x.d.BeginningBookValue,
                CurrentDepreciation = x.d.CurrentDepreciation,
                AccumulatedDepreciation = x.d.AccumulatedDepreciation,
                EndingBookValue = x.d.EndingBookValue,
                DepreciationMethod = x.d.DepreciationMethod ?? string.Empty
            }).ToList();

            // 合計
            response.Summary = new AssetDepreciationReportSummaryDTO
            {
                TotalOriginalAmount = response.Items.Sum(i => i.OriginalAmount),
                TotalCurrentDepreciation = response.Items.Sum(i => i.CurrentDepreciation),
                TotalAccumulatedDepreciation = response.Items.Sum(i => i.AccumulatedDepreciation),
                TotalEndingBookValue = response.Items.Sum(i => i.EndingBookValue)
            };

            return response;
        }

        private static int YearMonthIntFromTs(long ts)
        {
            if (ts <= 0) return 0;
            var dt = DateTimeOffset.FromUnixTimeSeconds(ts).UtcDateTime;
            return dt.Year * 100 + dt.Month;
        }

        /// <summary>
        /// 依折舊年月產生折舊單號
        /// 規則：DFyyyyMM + 三碼序號（001 起）
        /// </summary>
        private async Task<string> GenerateDepreciationFormNoAsync(int year, int month)
        {
            var prefix = $"DF{year}{month:00}";
            var last = await _context.Set<DepreciationForm>()
                .Where(x => !x.IsDeleted && x.DepreciationFormNo.StartsWith(prefix))
                .OrderByDescending(x => x.DepreciationFormNo)
                .FirstOrDefaultAsync();

            int seq = 1;
            if (last != null && !string.IsNullOrEmpty(last.DepreciationFormNo) && last.DepreciationFormNo.Length > prefix.Length)
            {
                var lastSeqStr = last.DepreciationFormNo.Substring(prefix.Length);
                if (int.TryParse(lastSeqStr, out int lastSeq)) seq = lastSeq + 1;
            }
            return $"{prefix}{seq:D3}";
        }

        /// <summary>
        /// 審核折舊單
        /// </summary>
        /// <param name="depreciationFormId">折舊單編號</param>
        /// <param name="isApproved">是否核准</param>
        /// <param name="approverId">審核人員</param>
        /// <param name="comment">審核意見</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool success, string message)> ApproveDepreciationFormAsync(string depreciationFormId, bool isApproved, string approverId, string? comment = null)
        {
            try
            {
                if (!Guid.TryParse(depreciationFormId, out Guid formId))
                {
                    return (false, "無效的折舊單編號格式");
                }

                var entity = await _context.Set<DepreciationForm>()
                    .FirstOrDefaultAsync(d => d.DepreciationFormId == formId && !d.IsDeleted);

                if (entity == null)
                {
                    return (false, "找不到指定的折舊單");
                }

                if (entity.Status != DepreciationFormStatus.PENDING)
                {
                    return (false, "只有待審核狀態的折舊單才能進行審核");
                }

                entity.Status = isApproved ? DepreciationFormStatus.APPROVED : DepreciationFormStatus.REJECTED;
                entity.ApproverId = approverId;
                entity.ApprovalDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.ApprovalComment = comment;
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.UpdateUserId = approverId;

                await _context.SaveChangesAsync();

                string statusText = isApproved ? "核准" : "駁回";
                return (true, $"折舊單{statusText}成功");
            }
            catch (Exception ex)
            {
                return (false, $"審核折舊單失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 執行折舊單（審核通過後執行資產累計折舊金額更新）
        /// </summary>
        /// <param name="depreciationFormId">折舊單編號</param>
        /// <param name="operatorId">操作人員</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool success, string message)> ExecuteDepreciationFormAsync(string depreciationFormId, string operatorId)
        {
            try
            {
                using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    if (!Guid.TryParse(depreciationFormId, out Guid formId))
                    {
                        return (false, "無效的折舊單編號格式");
                    }

                    var entity = await _context.Set<DepreciationForm>()
                        .FirstOrDefaultAsync(d => d.DepreciationFormId == formId && !d.IsDeleted);

                    if (entity == null)
                    {
                        return (false, "找不到指定的折舊單");
                    }

                    if (entity.Status != DepreciationFormStatus.APPROVED)
                    {
                        return (false, "只有已核准的折舊單才能執行");
                    }

                    var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                    // 查詢相關的折舊明細
                    var details = await _context.Set<DepreciationFormDetail>()
                        .Where(d => d.DepreciationYear == entity.DepreciationYear &&
                               d.DepreciationMonth == entity.DepreciationMonth &&
                               !d.IsDeleted)
                        .ToListAsync();

                    foreach (var detail in details)
                    {
                        // 如果新增折舊單日期為0，則跳過
                        if (detail.CreateDepreciationFormDate == 0)
                        {
                            continue;
                        }

                        // 更新折舊明細的折舊日期
                        detail.DepreciationDate = currentTime;
                        detail.UpdateTime = currentTime;
                        detail.UpdateUserId = operatorId;
                        _context.Update(detail);

                        // 更新資產的累計折舊金額
                        var asset = await _context.Pms_Assets
                            .FirstOrDefaultAsync(a => a.AssetId == detail.AssetId && !a.IsDeleted);
                        if (asset != null)
                        {
                            asset.AccumulatedDepreciationAmount = detail.AccumulatedDepreciation;
                            asset.UpdateTime = currentTime;
                            asset.UpdateUserId = operatorId;
                            _context.Update(asset);
                        }
                    }

                    // 更新折舊單狀態和執行時間
                    entity.Status = DepreciationFormStatus.EXECUTED;
                    entity.DepreciationDate = currentTime;
                    entity.UpdateTime = currentTime;
                    entity.UpdateUserId = operatorId;
                    _context.Update(entity);

                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    return (true, "折舊單執行成功");
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw new Exception($"執行折舊單失敗: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                return (false, $"執行折舊單失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 批次處理折舊單
        /// </summary>
        /// <param name="batchDto">批次處理資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool success, string message)> BatchProcessDepreciationFormsAsync(DepreciationFormBatchDTO batchDto)
        {
            try
            {
                var depreciationForms = await _context.Set<DepreciationForm>()
                    .Where(d => batchDto.DepreciationFormIds.Contains(d.DepreciationFormId) && !d.IsDeleted)
                    .ToListAsync();

                if (depreciationForms.Count != batchDto.DepreciationFormIds.Count)
                {
                    return (false, "部分折舊單不存在");
                }

                int successCount = 0;
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                foreach (var form in depreciationForms)
                {
                    switch (batchDto.Action.ToUpper())
                    {
                        case "APPROVE":
                            if (form.Status == DepreciationFormStatus.PENDING)
                            {
                                form.Status = DepreciationFormStatus.APPROVED;
                                form.ApproverId = batchDto.OperatorId;
                                form.ApprovalDate = currentTime;
                                form.ApprovalComment = batchDto.ApprovalComment;
                                successCount++;
                            }
                            break;

                        case "REJECT":
                            if (form.Status == DepreciationFormStatus.PENDING)
                            {
                                form.Status = DepreciationFormStatus.REJECTED;
                                form.ApproverId = batchDto.OperatorId;
                                form.ApprovalDate = currentTime;
                                form.ApprovalComment = batchDto.ApprovalComment;
                                successCount++;
                            }
                            break;

                        case "EXECUTE":
                            if (form.Status == DepreciationFormStatus.APPROVED)
                            {
                                // 執行折舊計算邏輯
                                var executeResult = await ExecuteDepreciationFormAsync(form.DepreciationFormId.ToString(), batchDto.OperatorId);
                                if (executeResult.success)
                                {
                                    successCount++;
                                }
                            }
                            break;
                    }

                    form.UpdateTime = currentTime;
                    form.UpdateUserId = batchDto.OperatorId;
                }

                await _context.SaveChangesAsync();

                return (true, $"批次處理完成，成功處理 {successCount} 筆記錄");
            }
            catch (Exception ex)
            {
                return (false, $"批次處理失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 取得折舊單統計資料
        /// </summary>
        /// <param name="userId">使用者ID (可選，用於個人統計)</param>
        /// <returns>統計資料</returns>
        public async Task<object> GetDepreciationFormStatisticsAsync(string? userId = null)
        {
            try
            {
                var query = _context.Set<DepreciationForm>()
                    .Where(d => !d.IsDeleted);

                if (!string.IsNullOrEmpty(userId))
                {
                    query = query.Where(d => d.CreateUserId == userId);
                }

                var allForms = await query.ToListAsync();

                var statistics = new
                {
                    TotalForms = allForms.Count,
                    PendingForms = allForms.Count(d => d.Status == DepreciationFormStatus.PENDING),
                    ApprovedForms = allForms.Count(d => d.Status == DepreciationFormStatus.APPROVED),
                    RejectedForms = allForms.Count(d => d.Status == DepreciationFormStatus.REJECTED),
                    ExecutedForms = allForms.Count(d => d.Status == DepreciationFormStatus.EXECUTED)
                };

                return statistics;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得折舊單統計資料時發生錯誤: {ex.Message}");
                return new { };
            }
        }
    }
}