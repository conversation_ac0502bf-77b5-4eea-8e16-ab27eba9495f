﻿using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models.Pms;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("折舊紀錄管理")]
    public class DepreciationFormDetailController : ControllerBase
    {
        private readonly IDepreciationFormDetailService _depreciationService;

        public DepreciationFormDetailController(IDepreciationFormDetailService depreciationService)
        {
            _depreciationService = depreciationService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得折舊紀錄", Description = "取得所有財產折舊紀錄")]
        public async Task<IActionResult> GetDepreciation()
        {
            var result = await _depreciationService.GetDepreciationAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetByYearMonth")]
        [SwaggerOperation(Summary = "依年月取得折舊紀錄", Description = "依指定年月篩選取得財產折舊紀錄")]
        public async Task<IActionResult> GetDepreciationByYearMonth([FromQuery] int? year, [FromQuery] int? month, [FromQuery] bool? hasCreateDepreciationFormDate)
        {
            // 驗證月份範圍
            if (month.HasValue && (month.Value < 1 || month.Value > 12))
            {
                return BadRequest(new { message = "月份必須在1-12之間" });
            }

            var result = await _depreciationService.GetDepreciationByYearMonthAsync(year, month, hasCreateDepreciationFormDate);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得折舊紀錄", Description = "依資產ID取得財產折舊紀錄")]
        public async Task<IActionResult> GetDepreciationDetail(string id)
        {
            var result = await _depreciationService.GetDepreciationByAssetAsync(id);
            if (result == null)
            {
                return NotFound();
            }
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增折舊紀錄", Description = "新增財產折舊紀錄")]
        public async Task<IActionResult> AddDepreciation([FromBody] DepreciationFormDetailDTO data)
        {
            var (success, message) = await _depreciationService.AddDepreciationAsync(data);
            if (success)
            {
                return Ok(new { message });
            }
            return BadRequest(new { message });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯折舊紀錄", Description = "修改已存在之財產折舊紀錄")]
        public async Task<IActionResult> EditDepreciation([FromBody] DepreciationFormDetailDTO data)
        {
            var (success, message) = await _depreciationService.EditDepreciationAsync(data);
            if (success)
            {
                return Ok(new { message });
            }
            return BadRequest(new { message });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除折舊紀錄", Description = "刪除已存在之財產折舊紀錄")]
        public async Task<IActionResult> DeleteDepreciation([FromBody] DepreciationFormDetailDTO data)
        {
            var (success, message) = await _depreciationService.DeleteDepreciationAsync(data);
            if (success)
            {
                return Ok(new { message });
            }
            return BadRequest(new { message });
        }

        [HttpGet]
        [Route("Methods")]
        [SwaggerOperation(Summary = "取得折舊方法", Description = "取得系統支援的折舊方法列表")]
        public async Task<IActionResult> GetDepreciationMethods()
        {
            var (success, methods, message) = await _depreciationService.GetAvailableDepreciationMethodsAsync();
            if (success)
            {
                return Ok(methods);
            }
            return BadRequest(new { message });
        }

        [HttpGet]
        [Route("DecliningBalanceRate/{assetAccountId}")]
        [SwaggerOperation(Summary = "取得餘額遞減法折舊率", Description = "取得指定財產科目的餘額遞減法折舊率")]
        public async Task<IActionResult> GetDecliningBalanceRate(string assetAccountId)
        {
            var (success, rate, message) = await _depreciationService.GetDecliningBalanceRateForAssetAccountAsync(assetAccountId);
            if (success)
            {
                return Ok(new { rate, message });
            }
            return BadRequest(new { message });
        }

        /// <summary>
        /// 產生固定資產明細表
        /// </summary>
        /// <param name="request">查詢條件</param>
        /// <returns>固定資產明細表資料</returns>
        [HttpPost]
        [Route("FixedAssetDetailReport")]
        [SwaggerOperation(Summary = "產生固定資產明細表", Description = "根據查詢條件產生固定資產明細表報告，包含本年度累計折舊")]
        public async Task<IActionResult> GenerateFixedAssetDetailReport([FromBody] FixedAssetDetailReportRequestDTO request)
        {
            try
            {
                // 如果 request 為 null，使用預設值
                if (request == null)
                {
                    request = new FixedAssetDetailReportRequestDTO();
                }

                var result = await _depreciationService.GenerateFixedAssetDetailReportAsync(request);
                return Ok(new
                {
                    success = true,
                    data = result,
                    message = $"成功產生固定資產明細表，共 {result.Count} 筆記錄"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    success = false,
                    message = $"產生固定資產明細表失敗：{ex.Message}"
                });
            }
        }

        /// <summary>
        /// 產生固定資產明細表（GET 方式，使用查詢參數）
        /// </summary>
        /// <param name="assetAccountId">財產科目編號（可選）</param>
        /// <param name="assetSubAccountId">財產子目編號（可選）</param>
        /// <param name="departmentId">部門編號（可選）</param>
        /// <param name="assetStatusId">財產狀態編號（可選）</param>
        /// <param name="assetNo">財產編號（模糊搜尋，可選）</param>
        /// <param name="assetName">財產名稱（模糊搜尋，可選）</param>
        /// <param name="acquisitionDateStart">取得日期起始（可選）</param>
        /// <param name="acquisitionDateEnd">取得日期結束（可選）</param>
        /// <param name="year">指定年度（用於計算本年度累計折舊，預設為當前年度）</param>
        /// <param name="includeScrapAssets">是否包含報廢財產（預設 false）</param>
        /// <param name="includeSoldAssets">是否包含出售財產（預設 false）</param>
        /// <param name="includeDonatedAssets">是否包含捐贈財產（預設 false）</param>
        /// <returns>固定資產明細表資料</returns>
        [HttpGet]
        [Route("FixedAssetDetailReport")]
        [SwaggerOperation(Summary = "產生固定資產明細表（查詢參數）", Description = "使用查詢參數產生固定資產明細表報告，包含本年度累計折舊")]
        public async Task<IActionResult> GenerateFixedAssetDetailReportByQuery(
            [FromQuery] Guid? assetAccountId,
            [FromQuery] Guid? assetSubAccountId,
            [FromQuery] string? departmentId,
            [FromQuery] Guid? assetStatusId,
            [FromQuery] string? assetNo,
            [FromQuery] string? assetName,
            [FromQuery] long? acquisitionDateStart,
            [FromQuery] long? acquisitionDateEnd,
            [FromQuery] int? year,
            [FromQuery] bool includeScrapAssets = false,
            [FromQuery] bool includeSoldAssets = false,
            [FromQuery] bool includeDonatedAssets = false)
        {
            try
            {
                var request = new FixedAssetDetailReportRequestDTO
                {
                    AssetAccountId = assetAccountId,
                    AssetSubAccountId = assetSubAccountId,
                    DepartmentId = departmentId ?? "",
                    AssetStatusId = assetStatusId,
                    AssetNo = assetNo ?? "",
                    AssetName = assetName ?? "",
                    AcquisitionDateStart = acquisitionDateStart,
                    AcquisitionDateEnd = acquisitionDateEnd,
                    Year = year,
                    IncludeScrapAssets = includeScrapAssets,
                    IncludeSoldAssets = includeSoldAssets,
                    IncludeDonatedAssets = includeDonatedAssets
                };

                var result = await _depreciationService.GenerateFixedAssetDetailReportAsync(request);
                return Ok(new
                {
                    success = true,
                    data = result,
                    message = $"成功產生固定資產明細表，共 {result.Count} 筆記錄"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    success = false,
                    message = $"產生固定資產明細表失敗：{ex.Message}"
                });
            }
        }

    }

    /// <summary>
    /// 折舊計算請求參數
    /// </summary>
    public class DepreciationCalculationRequest
    {
        /// <summary>
        /// 資產ID
        /// </summary>
        public string AssetId { get; set; }

        /// <summary>
        /// 折舊年度
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 折舊月份
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// 操作用戶ID
        /// </summary>
        public string UserId { get; set; }
    }

    /// <summary>
    /// 批次折舊計算請求參數
    /// </summary>
    public class BatchDepreciationCalculationRequest
    {
        /// <summary>
        /// 資產ID列表
        /// </summary>
        public List<string> AssetIds { get; set; }

        /// <summary>
        /// 折舊年度
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 折舊月份
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// 操作用戶ID
        /// </summary>
        public string UserId { get; set; }
    }

    /// <summary>
    /// 折舊試算請求參數
    /// </summary>
    public class DepreciationSimulationRequest
    {
        /// <summary>
        /// 資產ID
        /// </summary>
        public string AssetId { get; set; }

        /// <summary>
        /// 折舊年度
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 折舊月份
        /// </summary>
        public int Month { get; set; }
    }

    /// <summary>
    /// 批次折舊試算請求參數
    /// </summary>
    public class BatchDepreciationSimulationRequest
    {
        /// <summary>
        /// 資產ID列表
        /// </summary>
        public List<string> AssetIds { get; set; }

        /// <summary>
        /// 折舊年度
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 折舊月份
        /// </summary>
        public int Month { get; set; }
    }
}

