/**
 * 聯絡人表格組件
 * 
 * 基於 ResponsiveTable 構建的專用聯絡人表格
 * 支援多種操作模式和響應式設計
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

"use client";

import React from 'react';
import { Space, Tag, Tooltip, Button, Popconfirm } from 'antd';
import { 
  EditOutlined, 
  DeleteOutlined, 
  UserOutlined, 
  MailOutlined, 
  PhoneOutlined 
} from '@ant-design/icons';

// 共享組件
import ResponsiveTable, { SmartColumnType } from '../shared/ResponsiveTable';

// 聯絡人相關
import { Contact } from '@/services/ims/ContactService';
import { CONTACT_STATUS_OPTIONS } from './shared/contactConstants';

// 響應式工具
import { useScreenSize } from '../shared/ResponsiveModalConfig';

export interface ContactTableProps {
  /** 聯絡人資料 */
  contacts: Contact[];
  /** 載入狀態 */
  loading?: boolean;
  /** 選擇模式 */
  selectMode?: boolean;
  /** 編輯回調 */
  onEdit?: (contact: Contact) => void;
  /** 刪除回調 */
  onDelete?: (contactId: string) => void;
  /** 選擇回調（選擇模式下） */
  onSelect?: (contact: Contact) => void;
  /** 行選擇配置 */
  rowSelection?: any;
  /** 自定義操作列 */
  customActions?: (record: Contact) => React.ReactNode;
  /** 是否顯示分頁 */
  showPagination?: boolean;
  /** 每頁數量 */
  pageSize?: number;
  /** 表格大小 */
  size?: 'small' | 'middle' | 'large';
  /** 自定義樣式類名 */
  className?: string;
}

/**
 * 聯絡人表格組件
 * 
 * 特性：
 * - 基於 ResponsiveTable 的統一表格體驗
 * - 支援編輯、刪除、選擇等操作模式
 * - 響應式設計，移動端優化
 * - 統一的狀態顏色和圖示
 */
const ContactTable: React.FC<ContactTableProps> = ({
  contacts = [],
  loading = false,
  selectMode = false,
  onEdit,
  onDelete,
  onSelect,
  rowSelection,
  customActions,
  showPagination = true,
  pageSize = 10,
  size = 'middle',
  className = ''
}) => {
  const { isMobile } = useScreenSize();

  // 表格欄位定義（使用 SmartColumnType 啟用智能篩選器）
  const columns: SmartColumnType<Contact>[] = [
    {
      title: "姓名",
      dataIndex: "name",
      key: "name",
      fixed: 'left',
      width: 120,
      smartFilter: true,
      filterType: 'text',
      filterPlaceholder: '搜尋聯絡人姓名',
      sorter: (a: Contact, b: Contact) => (a.name || '').localeCompare(b.name || ''),
      render: (text: string, record: Contact) => (
        <Space>
          <UserOutlined style={{ color: "#1890ff" }} />
          <span
            style={{
              color: selectMode ? "#1890ff" : "inherit",
              cursor: selectMode ? "pointer" : "default",
              fontWeight: 500
            }}
            onClick={selectMode && onSelect ? () => onSelect(record) : undefined}
          >
            {text}
          </span>
        </Space>
      ),
    },
    {
      title: "職位",
      dataIndex: "position",
      key: "position",
      width: 100,
      smartFilter: true,
      filterType: 'text',
      filterPlaceholder: '搜尋職位',
      render: (text: string) => text || "-",
      responsive: ['md'],
    },
    {
      title: "公司",
      dataIndex: "company",
      key: "company",
      width: 120,
      smartFilter: true,
      filterType: 'text',
      filterPlaceholder: '搜尋公司名稱',
      sorter: (a: Contact, b: Contact) => (a.company || '').localeCompare(b.company || ''),
      render: (text: string) => text || "-",
      responsive: ['lg'],
    },
    {
      title: "電子郵件",
      dataIndex: "email",
      key: "email",
      width: 200,
      smartFilter: true,
      filterType: 'text',
      filterPlaceholder: '搜尋電子郵件',
      sorter: (a: Contact, b: Contact) => (a.email || '').localeCompare(b.email || ''),
      render: (text: string) => text ? (
        <Space>
          <MailOutlined style={{ color: "#52c41a" }} />
          <span style={{ fontSize: '12px' }}>{text}</span>
        </Space>
      ) : "-",
      responsive: ['md'],
    },
    {
      title: "電話",
      dataIndex: "phone",
      key: "phone",
      width: 120,
      smartFilter: true,
      filterType: 'text',
      filterPlaceholder: '搜尋電話號碼',
      render: (text: string) => text ? (
        <Space>
          <PhoneOutlined style={{ color: "#fa8c16" }} />
          <span>{text}</span>
        </Space>
      ) : "-",
      responsive: ['sm'],
    },
    {
      title: "類型",
      dataIndex: "contactType",
      key: "contactType",
      width: 80,
      smartFilter: true,
      filterType: 'select',
      sorter: (a: Contact, b: Contact) => (a.contactType || '').localeCompare(b.contactType || ''),
      render: (type: string) => {
        const colorMap: Record<string, string> = {
          '客戶': 'blue',
          '供應商': 'green',
          '合作夥伴': 'orange',
          '其他': 'default'
        };
        return type ? (
          <Tag color={colorMap[type] || 'default'}>{type}</Tag>
        ) : "-";
      },
      responsive: ['lg'],
    },
    {
      title: "狀態",
      dataIndex: "isActive",
      key: "isActive",
      width: 80,
      smartFilter: true,
      filterType: 'select',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'success' : 'error'}>
          {isActive ? "啟用" : "停用"}
        </Tag>
      ),
    },
    {
      title: "操作",
      key: "actions",
      fixed: 'right',
      width: selectMode ? 80 : 120,
      render: (_: any, record: Contact) => (
        <Space size="small">
          {/* 選擇模式 */}
          {selectMode && onSelect && (
            <Button
              type="primary"
              size="small"
              onClick={() => onSelect(record)}
            >
              選擇
            </Button>
          )}
          
          {/* 編輯模式 */}
          {!selectMode && (
            <>
              {onEdit && (
                <Tooltip title="編輯">
                  <Button
                    type="primary"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => onEdit(record)}
                  />
                </Tooltip>
              )}
              
              {onDelete && (
                <Popconfirm
                  title="確定要刪除此聯絡人嗎？"
                  description="此操作無法復原"
                  onConfirm={() => onDelete(record.contactID)}
                  okText="確定"
                  cancelText="取消"
                  okType="danger"
                >
                  <Tooltip title="刪除">
                    <Button
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                    />
                  </Tooltip>
                </Popconfirm>
              )}
              
              {/* 自定義操作 */}
              {customActions && customActions(record)}
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className={`contact-table ${className}`}>
      <ResponsiveTable<Contact>
        columns={columns}
        dataSource={contacts}
        rowKey="contactID"
        loading={loading}
        rowSelection={rowSelection}
        pagination={showPagination ? {
          total: contacts.length,
          pageSize: pageSize,
          showSizeChanger: true,
          showQuickJumper: !isMobile,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
          size: isMobile ? 'small' : 'default'
        } : false}
        scroll={{ x: 800 }}
        size={isMobile ? 'small' : size}
      />

      {/* 開發模式指示器 */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ 
          marginTop: 8, 
          padding: 8, 
          backgroundColor: '#f0f0f0', 
          borderRadius: 4,
          fontSize: 12,
          color: '#666'
        }}>
          聯絡人數: {contacts.length} | 模式: {selectMode ? '選擇' : '編輯'} | 響應式: {isMobile ? '移動端' : '桌面端'}
        </div>
      )}
    </div>
  );
};

export default ContactTable;
