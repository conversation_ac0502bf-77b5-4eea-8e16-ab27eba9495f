import React, { createContext, useContext, useEffect, useState } from "react";
import { getAssetAccounts } from "@/services/pms/assetAccountService";
import { getAssetSubAccounts } from "@/services/pms/assetSubAccountService";
import { getAssetCategories } from "@/services/pms/assetCategoryService";
import { getAssetStatuses } from "@/services/pms/assetStatusService";
import { getDepartments } from "@/services/common/departmentService";
import { getStorageLocations } from "@/services/pms/storageLocationService";
import { getUnits } from "@/services/common/unitService";
import { getUsers } from "@/services/common/userService";
import { getPositions } from "@/services/common/positionService";
import { getReportSignatureTemplates } from "@/services/common/reportSignatureTemplateService";
import { siteConfig } from "@/config/site";
import SignalRExecuter from "@/app/components/common/SignalRExecuter";
import { useAuth } from "@/contexts/AuthContext";

interface OptionsContextType {
  assetAccounts: any[];
  assetSubAccounts: any[];
  assetCategories: any[];
  assetStatuses: any[];
  departments: any[];
  storageLocations: any[];
  units: any[];
  users: any[];
  positions: any[];
  signatureTemplates: any[];
  refreshOptions: () => Promise<void>;
}

const OptionsContext = createContext<OptionsContextType | null>(null);

export const OptionsProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [assetAccounts, setAssetAccounts] = useState<any[]>([]);
  const [assetSubAccounts, setAssetSubAccounts] = useState<any[]>([]);
  const [assetCategories, setAssetCategories] = useState<any[]>([]);
  const [assetStatuses, setAssetStatuses] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [storageLocations, setStorageLocations] = useState<any[]>([]);
  const [units, setUnits] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [positions, setPositions] = useState<any[]>([]);
  const [signatureTemplates, setSignatureTemplates] = useState<any[]>([]);

  const { isAuthenticated, user } = useAuth();

  const loadOptions = async () => {
    try {
      // 從 sessionStorage 讀取快取資料
      const cachedData = sessionStorage.getItem("optionsCache");
      if (cachedData) {
        const {
          assetAccounts: cachedAssetAccounts,
          assetSubAccounts: cachedAssetSubAccounts,
          assetCategories: cachedAssetCategories,
          assetStatuses: cachedAssetStatuses,
          departments: cachedDepartments,
          storageLocations: cachedStorageLocations,
          units: cachedUnits,
          users: cachedUsers,
          positions: cachedPositions,
          signatureTemplates: cachedSignatureTemplates,
          timestamp,
        } = JSON.parse(cachedData);

        // 檢查快取是否過期
        const now = new Date().getTime();
        if (now - timestamp < siteConfig.optionsCacheDurationMs) {
          setAssetAccounts(cachedAssetAccounts);
          setAssetSubAccounts(cachedAssetSubAccounts);
          setAssetCategories(cachedAssetCategories);
          setAssetStatuses(cachedAssetStatuses);
          setDepartments(cachedDepartments);
          setStorageLocations(cachedStorageLocations);
          setUnits(cachedUnits);
          setUsers(cachedUsers);
          setPositions(cachedPositions);
          setSignatureTemplates(cachedSignatureTemplates);
          return;
        }
      }

      // 快取不存在或已過期，重新請求API
      const [
        accountsResult,
        subAccountsResult,
        categoriesResult,
        statusesResult,
        departmentsResult,
        locationsResult,
        unitsResult,
        usersResult,
        positionsResult,
        templatesResult,
      ] = await Promise.all([
        getAssetAccounts(),
        getAssetSubAccounts(),
        getAssetCategories(),
        getAssetStatuses(),
        getDepartments(),
        getStorageLocations(),
        getUnits(),
        getUsers(),
        getPositions(),
        getReportSignatureTemplates(),
      ]);

      // 更新狀態
      if (accountsResult.success) setAssetAccounts(accountsResult.data || []);
      if (subAccountsResult.success)
        setAssetSubAccounts(subAccountsResult.data || []);
      if (categoriesResult.success)
        setAssetCategories(categoriesResult.data || []);
      if (statusesResult.success) setAssetStatuses(statusesResult.data || []);
      if (departmentsResult.success)
        setDepartments(departmentsResult.data || []);
      if (locationsResult.success)
        setStorageLocations(locationsResult.data || []);
      if (unitsResult.success) setUnits(unitsResult.data || []);
      if (usersResult.success) setUsers(usersResult.data || []);
      if (positionsResult.success) setPositions(positionsResult.data || []);
      if (templatesResult.success)
        setSignatureTemplates(templatesResult.data || []);

      // 儲存到 sessionStorage
      const cacheData = {
        assetAccounts: accountsResult.data || [],
        assetSubAccounts: subAccountsResult.data || [],
        assetCategories: categoriesResult.data || [],
        assetStatuses: statusesResult.data || [],
        departments: departmentsResult.data || [],
        storageLocations: locationsResult.data || [],
        units: unitsResult.data || [],
        users: usersResult.data || [],
        positions: positionsResult.data || [],
        signatureTemplates: templatesResult.data || [],
        timestamp: new Date().getTime(),
      };
      sessionStorage.setItem("optionsCache", JSON.stringify(cacheData));
    } catch (error) {
      console.error("載入選項資料錯誤:", error);
    }
  };

  // 提供重新整理方法
  const refreshOptions = async () => {
    sessionStorage.removeItem("optionsCache");
    await loadOptions();
  };

  // 監聽登入狀態或 user 變化，自動 refresh options
  useEffect(() => {
    sessionStorage.removeItem("optionsCache");
    if (isAuthenticated) {
      refreshOptions();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, user]);

  return (
    <>
      <OptionsContext.Provider
        value={{
          assetAccounts,
          assetSubAccounts,
          assetCategories,
          assetStatuses,
          departments,
          storageLocations,
          units,
          users,
          positions,
          signatureTemplates,
          refreshOptions,
        }}
      >
        {children}
      </OptionsContext.Provider>
      <SignalRExecuter
        doFunction={() => {
          refreshOptions();
        }}
        eventName="DepartmentUI"
        action="refresh"
      />
    </>
  );
};

export const useOptions = () => {
  const context = useContext(OptionsContext);
  if (!context) {
    throw new Error("useOptions must be used within an OptionsProvider");
  }
  return context;
};
