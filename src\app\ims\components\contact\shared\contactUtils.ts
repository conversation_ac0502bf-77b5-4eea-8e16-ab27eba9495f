/**
 * 聯絡人工具函數
 * 
 * 統一的聯絡人處理工具，對應後端 Tools/ 中的工具類別
 * 包含資料正規化、搜尋、篩選等功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Contact } from '@/services/ims/ContactService';
import { PartnerContact } from '@/services/ims/partner';
import { searchContacts } from '@/services/ims/ContactService';
import { CONTACT_DEFAULTS } from './contactConstants';

/**
 * 創建空的聯絡人物件
 * 對應後端 ContactService.createEmptyContact()
 */
export const createEmptyContact = (): Partial<Contact> => ({
  contactID: '',
  name: '',
  position: '',
  email: '',
  phone: '',
  department: '',
  company: '',
  contactType: CONTACT_DEFAULTS.contactType,
  isActive: CONTACT_DEFAULTS.isActive,
  createTime: null,
  createUserId: null,
  updateTime: null,
  updateUserId: null,
  deleteTime: null,
  deleteUserId: null,
  isDeleted: false
});

/**
 * 創建聯絡人預設值（支援覆蓋）
 * 
 * @param overrides 要覆蓋的欄位值
 * @returns 合併後的聯絡人物件
 */
export const makeContactDefaults = (overrides: Partial<Contact> = {}): Partial<Contact> => ({
  ...createEmptyContact(),
  ...overrides
});

/**
 * 正規化聯絡人資料
 * 處理後端回傳的資料格式不一致問題（如 ContactID vs contactID）
 * 
 * @param contact 原始聯絡人資料
 * @returns 正規化後的聯絡人資料
 */
export const normalizeContact = (contact: any): Contact => {
  if (!contact) return createEmptyContact() as Contact;

  return {
    contactID: contact.contactID || contact.ContactID || '',
    name: contact.name || contact.Name || '',
    position: contact.position || contact.Position || '',
    email: contact.email || contact.Email || '',
    phone: contact.phone || contact.Phone || '',
    isActive: typeof contact.isActive === 'boolean' ? contact.isActive : (typeof contact.IsActive === 'boolean' ? contact.IsActive : true),
    contactType: contact.contactType || contact.ContactType || CONTACT_DEFAULTS.contactType,
    department: contact.department || contact.Department || '',
    company: contact.company || contact.Company || '',
    createTime: contact.createTime || contact.CreateTime || null,
    createUserId: contact.createUserId || contact.CreateUserId || null,
    updateTime: contact.updateTime || contact.UpdateTime || null,
    updateUserId: contact.updateUserId || contact.UpdateUserId || null,
    deleteTime: contact.deleteTime || contact.DeleteTime || null,
    deleteUserId: contact.deleteUserId || contact.DeleteUserId || null,
    isDeleted: typeof contact.isDeleted === 'boolean' ? contact.isDeleted : (typeof contact.IsDeleted === 'boolean' ? contact.IsDeleted : false),
  };
};

/**
 * 正規化夥伴聯絡人關聯資料
 * 確保資料一致性：去重、單一主聯絡人、優先順序處理
 * 
 * @param contacts 夥伴聯絡人陣列
 * @param partnerID 夥伴ID（可選）
 * @returns 正規化後的夥伴聯絡人陣列
 */
export const normalizePartnerContacts = (contacts: PartnerContact[], partnerID?: string): PartnerContact[] => {
  if (!contacts || contacts.length === 0) return [];

  // 1. 去重（基於 contactID）
  const uniqueMap = new Map<string, PartnerContact>();
  for (const contact of contacts) {
    if (!contact?.contactID) continue;
    uniqueMap.set(contact.contactID, contact);
  }

  let list = Array.from(uniqueMap.values());

  // 2. 確保只有一個主聯絡人
  const primaries = list.filter(c => c.isPrimary);
  if (primaries.length > 1) {
    // 如果有多個主聯絡人，選擇優先順序最低的作為主聯絡人
    const primary = primaries.sort((a, b) => (a.priority ?? 99) - (b.priority ?? 99))[0];
    list = list.map(c => ({ ...c, isPrimary: c.contactID === primary.contactID }));
  }

  // 3. 如果沒有主聯絡人，將優先順序最低的設為主聯絡人
  if (list.length > 0 && !list.some(c => c.isPrimary)) {
    const first = [...list].sort((a, b) => (a.priority ?? 99) - (b.priority ?? 99))[0];
    list = list.map(c => ({ ...c, isPrimary: c.contactID === first.contactID }));
  }

  // 4. 設定優先順序和夥伴ID
  list = list.map(c => ({
    ...c,
    priority: c.isPrimary ? 0 : (typeof c.priority === 'number' ? c.priority : 99),
    partnerID: partnerID || c.partnerID || ('' as any),
  }));

  return list;
};

/**
 * 後援查找聯絡人
 * 當 addContact API 回傳 data: null 時，透過搜尋找回新建的聯絡人
 * 
 * @param searchCriteria 搜尋條件（email 或 name）
 * @returns 找到的聯絡人或 null
 */
export const resolveCreatedContact = async (searchCriteria: {
  email?: string;
  name?: string;
}): Promise<Contact | null> => {
  try {
    const keyword = searchCriteria.email?.trim() || searchCriteria.name?.trim();
    if (!keyword) return null;

    const response = await searchContacts(keyword);
    if (!response.success || !response.data || response.data.length === 0) {
      return null;
    }

    // 篩選候選者
    const candidates = response.data.filter((contact: Contact) => {
      if (searchCriteria.email?.trim()) {
        return (contact.email || '').toLowerCase() === searchCriteria.email.trim().toLowerCase();
      }
      return (contact.name || '').trim() === searchCriteria.name?.trim();
    });

    // 如果沒有精確匹配，使用所有搜尋結果
    const list = candidates.length > 0 ? candidates : response.data;

    // 按建立時間排序，取最新的
    const sorted = [...list].sort((a, b) => (b.createTime ?? 0) - (a.createTime ?? 0));
    
    return normalizeContact(sorted[0]) || null;
  } catch (error) {
    console.error('resolveCreatedContact 失敗:', error);
    return null;
  }
};

/**
 * 聯絡人搜尋篩選邏輯
 * 統一的前端搜尋邏輯，支援多欄位搜尋和篩選
 *
 * @param contacts 聯絡人陣列
 * @param searchText 搜尋關鍵字
 * @param filters 篩選條件
 * @returns 篩選後的聯絡人陣列
 */
export const filterContacts = (
  contacts: Contact[],
  searchText: string = '',
  filters: {
    name?: string;
    email?: string;
    contactType?: string;
    status?: boolean;
    company?: string;
    department?: string;
  } = {}
): Contact[] => {
  return contacts.filter(contact => {
    // 搜尋條件：多欄位模糊搜尋
    const matchesSearch = !searchText ||
      contact.name?.toLowerCase().includes(searchText.toLowerCase()) ||
      contact.email?.toLowerCase().includes(searchText.toLowerCase()) ||
      contact.phone?.toLowerCase().includes(searchText.toLowerCase()) ||
      contact.company?.toLowerCase().includes(searchText.toLowerCase()) ||
      contact.position?.toLowerCase().includes(searchText.toLowerCase()) ||
      contact.department?.toLowerCase().includes(searchText.toLowerCase());

    // 姓名篩選
    const matchesName = !filters.name ||
      contact.name?.toLowerCase().includes(filters.name.toLowerCase());

    // 電子郵件篩選
    const matchesEmail = !filters.email ||
      contact.email?.toLowerCase().includes(filters.email.toLowerCase());

    // 類型篩選
    const matchesType = !filters.contactType || contact.contactType === filters.contactType;

    // 狀態篩選
    const matchesStatus = filters.status === undefined || contact.isActive === filters.status;

    // 公司篩選
    const matchesCompany = !filters.company ||
      contact.company?.toLowerCase().includes(filters.company.toLowerCase());

    // 部門篩選
    const matchesDepartment = !filters.department ||
      contact.department?.toLowerCase().includes(filters.department.toLowerCase());

    return matchesSearch && matchesName && matchesEmail && matchesType &&
           matchesStatus && matchesCompany && matchesDepartment;
  });
};

/**
 * 應用聯絡人篩選器（配合 FilterSearchContainer 使用）
 *
 * @param contacts 聯絡人陣列
 * @param searchText 搜尋文字
 * @param filterValues 篩選器值
 * @returns 篩選後的聯絡人陣列
 */
export const applyContactFilters = (
  contacts: Contact[],
  searchText: string,
  filterValues: Record<string, any>
): Contact[] => {
  return filterContacts(contacts, searchText, {
    name: filterValues.name,
    email: filterValues.email,
    contactType: filterValues.contactType,
    status: filterValues.isActive === 'true' ? true :
            filterValues.isActive === 'false' ? false : undefined,
    company: filterValues.company,
    department: filterValues.department
  });
};

/**
 * 獲取聯絡人統計資訊
 * 
 * @param contacts 聯絡人陣列
 * @returns 統計資訊
 */
export const getContactStats = (contacts: Contact[]) => {
  const total = contacts.length;
  const active = contacts.filter(c => c.isActive).length;
  const inactive = total - active;
  
  const typeStats = contacts.reduce((acc, contact) => {
    const type = contact.contactType || '未分類';
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    total,
    active,
    inactive,
    typeStats
  };
};

/**
 * 格式化聯絡人顯示名稱
 * 
 * @param contact 聯絡人資料
 * @returns 格式化的顯示名稱
 */
export const formatContactDisplayName = (contact: Contact): string => {
  const parts = [];
  
  if (contact.name) {
    parts.push(contact.name);
  }
  
  if (contact.position) {
    parts.push(`(${contact.position})`);
  }
  
  if (contact.company && parts.length > 0) {
    parts.push(`- ${contact.company}`);
  }
  
  return parts.join(' ') || '未命名聯絡人';
};
