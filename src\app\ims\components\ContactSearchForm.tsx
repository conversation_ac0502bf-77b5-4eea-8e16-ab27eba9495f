"use client";

import React, { useState } from "react";
import {
  Form,
  Input,
  Select,
  Button,
  Space,
  Row,
  Col,
  Card,
  Typography,
} from "antd";
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  UserOutlined,
} from "@ant-design/icons";

// Types
interface ContactSearchParams {
  keyword?: string;
  contactType?: string;
  isActive?: boolean;
  company?: string;
  department?: string;
}

interface ContactSearchFormProps {
  onSearch: (params: ContactSearchParams) => void;
  onFilter: (filters: ContactSearchParams) => void;
  loading?: boolean;
  isMobile?: boolean;
  compact?: boolean;
}

const { Search } = Input;
const { Option } = Select;
const { Text } = Typography;

const ContactSearchForm: React.FC<ContactSearchFormProps> = ({
  onSearch,
  onFilter,
  loading = false,
  isMobile = false,
  compact = false,
}) => {
  const [form] = Form.useForm();
  const [activeFilters, setActiveFilters] = useState<string[]>([]);

  // 處理搜尋
  const handleSearch = (value: string) => {
    const params: ContactSearchParams = {
      keyword: value,
    };
    onSearch(params);
  };

  // 處理篩選變更
  const handleFilterChange = (changedValues: any, allValues: any) => {
    const filters: ContactSearchParams = {};
    
    if (allValues.contactType) {
      filters.contactType = allValues.contactType;
    }
    if (allValues.isActive !== undefined) {
      filters.isActive = allValues.isActive;
    }
    if (allValues.company) {
      filters.company = allValues.company;
    }
    if (allValues.department) {
      filters.department = allValues.department;
    }

    // 更新活躍篩選器
    const newActiveFilters = Object.keys(filters).filter(key => filters[key as keyof ContactSearchParams] !== undefined);
    setActiveFilters(newActiveFilters);

    onFilter(filters);
  };

  // 清除所有篩選
  const clearAllFilters = () => {
    form.resetFields();
    setActiveFilters([]);
    onFilter({});
  };

  // 聯絡人類型選項
  const contactTypeOptions = [
    { label: "客戶", value: "客戶" },
    { label: "供應商", value: "供應商" },
    { label: "合作夥伴", value: "合作夥伴" },
    { label: "其他", value: "其他" },
  ];

  if (compact) {
    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[8, 8]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜尋聯絡人..."
              onSearch={handleSearch}
              enterButton={<SearchOutlined />}
              size={isMobile ? "large" : "middle"}
              loading={loading}
            />
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Select
              placeholder="選擇類型"
              style={{ width: "100%" }}
              size={isMobile ? "large" : "middle"}
              onChange={(value) => handleFilterChange({ contactType: value }, { contactType: value })}
              allowClear
            >
              {contactTypeOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Space>
              <Button
                icon={<ClearOutlined />}
                onClick={clearAllFilters}
                size={isMobile ? "large" : "middle"}
              >
                清除
              </Button>
              {activeFilters.length > 0 && (
                <Text type="secondary">
                  已套用 {activeFilters.length} 個篩選條件
                </Text>
              )}
            </Space>
          </Col>
        </Row>
      </Card>
    );
  }

  return (
    <Card
      title={
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <FilterOutlined style={{ color: "#1890ff" }} />
          <span>聯絡人搜尋與篩選</span>
        </div>
      }
      size="small"
      style={{ marginBottom: 16 }}
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFilterChange}
        initialValues={{
          isActive: undefined,
        }}
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8}>
            <Form.Item label="關鍵字搜尋" name="keyword">
              <Search
                placeholder="搜尋姓名、郵件、電話、公司..."
                onSearch={handleSearch}
                enterButton={<SearchOutlined />}
                size={isMobile ? "large" : "middle"}
                loading={loading}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item label="聯絡人類型" name="contactType">
              <Select
                placeholder="選擇聯絡人類型"
                size={isMobile ? "large" : "middle"}
                allowClear
              >
                {contactTypeOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item label="狀態" name="isActive">
              <Select
                placeholder="選擇狀態"
                size={isMobile ? "large" : "middle"}
                allowClear
              >
                <Option value={true}>啟用</Option>
                <Option value={false}>停用</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8}>
            <Form.Item label="公司" name="company">
              <Input
                placeholder="搜尋公司名稱"
                size={isMobile ? "large" : "middle"}
                prefix={<UserOutlined />}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item label="部門" name="department">
              <Input
                placeholder="搜尋部門名稱"
                size={isMobile ? "large" : "middle"}
                prefix={<UserOutlined />}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item label="操作">
              <Space>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={() => {
                    const values = form.getFieldsValue();
                    onSearch(values);
                  }}
                  loading={loading}
                  size={isMobile ? "large" : "middle"}
                >
                  搜尋
                </Button>
                <Button
                  icon={<ClearOutlined />}
                  onClick={clearAllFilters}
                  size={isMobile ? "large" : "middle"}
                >
                  清除篩選
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        {activeFilters.length > 0 && (
          <Row>
            <Col span={24}>
              <Space wrap>
                <Text type="secondary">已套用篩選條件：</Text>
                {activeFilters.map((filter) => (
                  <Text key={filter} code>
                    {filter}
                  </Text>
                ))}
                <Button
                  type="link"
                  size="small"
                  onClick={clearAllFilters}
                  style={{ padding: 0 }}
                >
                  清除全部
                </Button>
              </Space>
            </Col>
          </Row>
        )}
      </Form>
    </Card>
  );
};

export default ContactSearchForm;
