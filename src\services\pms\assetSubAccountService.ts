import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 財產子目
export interface AssetSubAccount {
    assetSubAccountId: string;
    assetSubAccountNo: string;
    assetAccountId: string;
    assetAccountNo: string;
    assetSubAccountName: string;
    sortCode: number;
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    createUserName?: string;
    updateUserName?: string;
    deleteUserName?: string;
    assetAccountName?: string;
}

// 獲取財產子目列表
export async function getAssetSubAccounts(): Promise<ApiResponse<AssetSubAccount[]>> {
    try {
        const response = await httpClient(apiEndpoints.getAssetSubAccounts, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取財產子目列表失敗",
            data: [],
        };
    }
}

// 獲取財產子目詳情
export async function getAssetSubAccountDetail(id: string): Promise<ApiResponse<AssetSubAccount>> {
    try {
        const response = await httpClient(apiEndpoints.getAssetSubAccountDetail, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取財產子目詳情失敗",
            data: undefined,
        };
    }
}

// 依資產科目編號獲取財產子目列表
export async function getAssetSubAccountsByAssetAccountId(assetAccountId: string): Promise<ApiResponse<AssetSubAccount[]>> {
    try {
        const response = await httpClient(apiEndpoints.getAccessoryEquipmentDetail, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取財產子目列表失敗",
            data: [],
        };
    }
}

// 新增財產子目
export async function createAssetSubAccount(data: Partial<AssetSubAccount>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addAssetSubAccount, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增財產子目失敗",
        };
    }
}

// 更新財產子目
export async function updateAssetSubAccount(data: Partial<AssetSubAccount>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editAssetSubAccount, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log(JSON.stringify(data));
        console.log(response);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新財產子目失敗",
        };
    }
}

// 刪除財產子目
export async function deleteAssetSubAccount(data: Partial<AssetSubAccount>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteAssetSubAccount, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除財產子目失敗",
        };
    }
} 