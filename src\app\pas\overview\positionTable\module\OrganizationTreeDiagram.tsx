"use client";

import React, { useMemo, useRef, useEffect, useState, useCallback } from "react";
import { Card, Typography, Space, Tag, Empty, Modal, Button, Tooltip, InputNumber, Divider, Spin } from "antd";
import { ZoomInOutlined, ZoomOutOutlined, ReloadOutlined, DragOutlined, DownloadOutlined, SettingOutlined } from "@ant-design/icons";
import { Employee } from "@/services/pas/EmployeeService";
import { Division } from "@/services/common/divisionService";

const { Text } = Typography;

/**
 * 樹狀節點介面 - 組織架構圖的核心資料結構
 * 支援部門、組別、員工三種節點類型的統一表示
 */
interface TreeNode {
    id: string;                         // 節點唯一識別碼
    name: string;                       // 節點顯示名稱
    type: 'department' | 'division' | 'employee';  // 節點類型：部門/組別/員工
    level: number;                      // 節點在樹狀結構中的層級
    allowanceRank?: number;             // 加給等級（用於員工排序和樣式）
    employees?: Employee[];             // 員工資料（僅員工節點使用）
    children: TreeNode[];               // 子節點陣列
    parent?: TreeNode;                  // 父節點引用

    // 節點位置和尺寸資訊（用於 SVG 渲染）
    x?: number;                         // 節點 X 座標
    y?: number;                         // 節點 Y 座標
    width?: number;                     // 節點寬度
    height?: number;                    // 節點高度

    // 容器位置和尺寸資訊（部門節點專用）
    containerX?: number;                // 容器 X 座標
    containerY?: number;                // 容器 Y 座標
    containerWidth?: number;            // 容器寬度
    containerHeight?: number;           // 容器高度

    // 組別容器相關屬性（支援組別層級顯示）
    divisionContainers?: DivisionContainer[];
}

/**
 * 組別容器介面 - 用於在部門內顯示組別分組
 * 當啟用組別層級顯示時，員工會按組別分組顯示在獨立容器中
 */
interface DivisionContainer {
    id: string;                         // 組別容器唯一識別碼
    name: string;                       // 組別顯示名稱
    employees: Employee[];              // 該組別的員工清單
    x: number;                          // 容器相對於部門的 X 座標
    y: number;                          // 容器相對於部門的 Y 座標
    width: number;                      // 容器寬度
    height: number;                     // 容器高度
}

/**
 * 組織架構圖元件屬性介面
 */
interface OrganizationTreeDiagramProps {
    employees: Employee[];              // 員工資料清單
    departments: any[];                 // 部門資料清單
    divisions: Division[];              // 組別資料清單
    allowanceRank: (allowanceTypeName?: string, allowanceType?: string) => number;  // 加給等級計算函數
    sortEmployees: (list: Employee[]) => Employee[];  // 員工排序函數
    showDivisionLevel: boolean;         // 是否顯示組別層級
    onEmployeeClick?: (employee: Employee) => void;  // 員工點擊回調函數
    loading?: boolean;                  // 是否顯示載入狀態
}

/**
 * 組織架構圖主元件
 * 提供互動式的組織架構視覺化，支援拖拽、縮放、員工詳情查看等功能
 */
const OrganizationTreeDiagram: React.FC<OrganizationTreeDiagramProps> = ({
    employees,
    departments,
    divisions,
    allowanceRank,
    sortEmployees,
    showDivisionLevel,
    onEmployeeClick,
    loading = false
}) => {
    // 是否顯示「未分配部門」容器於 SVG 中
    const SHOW_UNKNOWN_DEPARTMENT = true;
    // === DOM 引用 ===
    const svgRef = useRef<SVGSVGElement>(null);           // SVG 元素引用
    const containerRef = useRef<HTMLDivElement>(null);    // 容器元素引用

    // === 基本狀態管理 ===
    const [svgSize, setSvgSize] = useState({ width: 1200, height: 800 });  // SVG 畫布尺寸
    const [employeesPerRow, setEmployeesPerRow] = useState(5);  // 每行員工數量
    const [exportModalVisible, setExportModalVisible] = useState(false);  // 匯出選項彈窗

    // === 視圖變換狀態（拖拽和縮放） ===
    const [transform, setTransform] = useState({
        x: 0,           // 水平偏移量
        y: 0,           // 垂直偏移量
        scale: 1        // 縮放比例
    });
    const [isDragging, setIsDragging] = useState(false);              // 是否正在拖拽
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });       // 拖拽起始點
    const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 }); // 上次平移位置

    // === 事件處理函數 ===
    /**
     * 處理員工點擊事件
     * 調用父組件傳入的回調函數
     */
    const handleEmployeeClick = (employee: Employee) => {
        if (onEmployeeClick) {
            onEmployeeClick(employee);
        }
    };

    // === 拖拽功能相關處理函數 ===
    /**
     * 處理滑鼠按下事件 - 開始拖拽
     * 只處理左鍵點擊，並排除員工節點上的點擊
     */
    const handleMouseDown = useCallback((e: React.MouseEvent) => {
        if (e.button !== 0) return; // 只處理左鍵

        // 檢查是否點擊在員工節點上，如果是則不開始拖拽
        const target = e.target as SVGElement;
        if (target.getAttribute('data-employee-clickable') === 'true') {
            return;
        }

        setIsDragging(true);
        setDragStart({ x: e.clientX, y: e.clientY });
        setLastPanPoint({ x: transform.x, y: transform.y });
        e.preventDefault();
    }, [transform.x, transform.y]);

    /**
     * 處理滑鼠移動事件 - 執行拖拽
     * 計算新位置並應用邊界限制，防止內容拖拽到可視範圍外
     */
    const handleMouseMove = useCallback((e: React.MouseEvent) => {
        if (!isDragging) return;

        // 計算滑鼠移動距離
        const deltaX = e.clientX - dragStart.x;
        const deltaY = e.clientY - dragStart.y;

        // 計算新的平移位置
        const newX = lastPanPoint.x + deltaX;
        const newY = lastPanPoint.y + deltaY;

        // 獲取容器和SVG的尺寸來計算邊界
        const container = containerRef.current;
        if (!container) return;

        const containerRect = container.getBoundingClientRect();
        const scaledSvgWidth = svgSize.width * transform.scale;
        const scaledSvgHeight = svgSize.height * transform.scale;

        // 計算邊界限制 - 防止內容拖拽到容器外
        const minX = Math.min(0, containerRect.width - scaledSvgWidth);
        const maxX = Math.max(0, containerRect.width - scaledSvgWidth);
        const minY = Math.min(0, containerRect.height - scaledSvgHeight);
        const maxY = Math.max(0, containerRect.height - scaledSvgHeight);

        // 應用邊界限制
        const clampedX = Math.max(minX, Math.min(maxX, newX));
        const clampedY = Math.max(minY, Math.min(maxY, newY));

        setTransform(prev => ({
            ...prev,
            x: clampedX,
            y: clampedY
        }));
    }, [isDragging, dragStart.x, dragStart.y, lastPanPoint.x, lastPanPoint.y, transform.scale, svgSize.width, svgSize.height]);

    /**
     * 處理滑鼠釋放事件 - 結束拖拽
     */
    const handleMouseUp = useCallback(() => {
        setIsDragging(false);
    }, []);


    // === 縮放控制函數 ===
    /**
     * 處理放大操作
     * 放大比例上限為 3倍，並調整位置以符合邊界限制
     */
    const handleZoomIn = useCallback(() => {
        setTransform(prev => {
            const newScale = Math.min(3, prev.scale * 1.2);  // 放大 1.2 倍，最大 3 倍

            // 獲取容器尺寸來計算縮放後的邊界限制
            const container = containerRef.current;
            if (!container) return { ...prev, scale: newScale };

            const containerRect = container.getBoundingClientRect();
            const scaledSvgWidth = svgSize.width * newScale;
            const scaledSvgHeight = svgSize.height * newScale;

            // 計算邊界限制
            const minX = Math.min(0, containerRect.width - scaledSvgWidth);
            const maxX = Math.max(0, containerRect.width - scaledSvgWidth);
            const minY = Math.min(0, containerRect.height - scaledSvgHeight);
            const maxY = Math.max(0, containerRect.height - scaledSvgHeight);

            // 應用邊界限制
            const clampedX = Math.max(minX, Math.min(maxX, prev.x));
            const clampedY = Math.max(minY, Math.min(maxY, prev.y));

            return {
                x: clampedX,
                y: clampedY,
                scale: newScale
            };
        });
    }, [svgSize.width, svgSize.height]);

    /**
     * 處理縮小操作
     * 縮小比例下限為 0.1倍，並調整位置以符合邊界限制
     */
    const handleZoomOut = useCallback(() => {
        setTransform(prev => {
            const newScale = Math.max(0.1, prev.scale / 1.2);  // 縮小 1.2 倍，最小 0.1 倍

            // 獲取容器尺寸來計算縮放後的邊界限制
            const container = containerRef.current;
            if (!container) return { ...prev, scale: newScale };

            const containerRect = container.getBoundingClientRect();
            const scaledSvgWidth = svgSize.width * newScale;
            const scaledSvgHeight = svgSize.height * newScale;

            // 計算邊界限制
            const minX = Math.min(0, containerRect.width - scaledSvgWidth);
            const maxX = Math.max(0, containerRect.width - scaledSvgWidth);
            const minY = Math.min(0, containerRect.height - scaledSvgHeight);
            const maxY = Math.max(0, containerRect.height - scaledSvgHeight);

            // 應用邊界限制
            const clampedX = Math.max(minX, Math.min(maxX, prev.x));
            const clampedY = Math.max(minY, Math.min(maxY, prev.y));

            return {
                x: clampedX,
                y: clampedY,
                scale: newScale
            };
        });
    }, [svgSize.width, svgSize.height]);

    /**
     * 重設視圖位置和縮放比例
     * 將視圖恢復到初始狀態
     */
    const handleResetView = useCallback(() => {
        setTransform({
            x: 0,     // 水平位置歸零
            y: 0,     // 垂直位置歸零
            scale: 1  // 縮放比例歸一
        });
    }, []);

    /**
     * 匯出為 SVG 格式
     */
    const exportToSVG = useCallback(() => {
        if (!svgRef.current) return;

        // 克隆 SVG 元素
        const svgElement = svgRef.current.cloneNode(true) as SVGSVGElement;

        // 移除變換效果，保持原始尺寸
        svgElement.style.transform = 'none';
        svgElement.style.transition = 'none';

        // 添加 XML 命名空間
        svgElement.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
        svgElement.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');

        // 序列化 SVG
        const serializer = new XMLSerializer();
        const svgString = serializer.serializeToString(svgElement);

        // 創建下載連結
        const blob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `組織架構圖_${new Date().toISOString().slice(0, 10)}.svg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }, []);

    /**
     * 匯出為 PNG 格式
     */
    const exportToPNG = useCallback(() => {
        if (!svgRef.current) return;

        // 克隆 SVG 元素
        const svgElement = svgRef.current.cloneNode(true) as SVGSVGElement;

        // 移除變換效果，保持原始尺寸
        svgElement.style.transform = 'none';
        svgElement.style.transition = 'none';

        // 添加 XML 命名空間
        svgElement.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
        svgElement.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');

        // 序列化 SVG
        const serializer = new XMLSerializer();
        const svgString = serializer.serializeToString(svgElement);

        // 創建 canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = () => {
            canvas.width = svgSize.width;
            canvas.height = svgSize.height;

            // 設定白色背景
            if (ctx) {
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0);
            }

            // 下載 PNG
            canvas.toBlob((blob) => {
                if (blob) {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `組織架構圖_${new Date().toISOString().slice(0, 10)}.png`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                }
            }, 'image/png');
        };

        img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgString)));
    }, [svgSize]);

    /**
     * 匯出為 HTML 格式
     */
    const exportToHTML = useCallback(() => {
        if (!svgRef.current) return;

        // 克隆 SVG 元素
        const svgElement = svgRef.current.cloneNode(true) as SVGSVGElement;

        // 移除變換效果，保持原始尺寸
        svgElement.style.transform = 'none';
        svgElement.style.transition = 'none';

        // 添加 XML 命名空間
        svgElement.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
        svgElement.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');

        // 創建完整的 HTML 文件
        const htmlContent = `
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>組織架構圖 - ${new Date().toLocaleDateString('zh-TW')}</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #1890ff;
            margin-bottom: 30px;
        }
        .export-info {
            text-align: center;
            color: #666;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .svg-container {
            text-align: center;
            overflow-x: auto;
        }
        svg {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
        }
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>組織架構圖</h1>
        <div class="export-info">
            匯出時間：${new Date().toLocaleString('zh-TW')}<br>
            每行員工數：${employeesPerRow} 人
        </div>
        <div class="svg-container">
            ${svgElement.outerHTML}
        </div>
    </div>
</body>
</html>`;

        // 創建下載連結
        const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `組織架構圖_${new Date().toISOString().slice(0, 10)}.html`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }, [employeesPerRow]);

    /**
     * 監聽容器尺寸變化的 Effect
     * 當窗口大小或 SVG 尺寸變化時，自動調整視圖位置以符合新的邊界限制
     */
    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        /**
         * 檢查並調整視圖邊界
         * 確保內容不會超出可視範圍
         */
        const checkBounds = () => {
            setTransform(prev => {
                const containerRect = container.getBoundingClientRect();
                const scaledSvgWidth = svgSize.width * prev.scale;
                const scaledSvgHeight = svgSize.height * prev.scale;

                // 計算邊界限制
                const minX = Math.min(0, containerRect.width - scaledSvgWidth);
                const maxX = Math.max(0, containerRect.width - scaledSvgWidth);
                const minY = Math.min(0, containerRect.height - scaledSvgHeight);
                const maxY = Math.max(0, containerRect.height - scaledSvgHeight);

                // 應用邊界限制
                const clampedX = Math.max(minX, Math.min(maxX, prev.x));
                const clampedY = Math.max(minY, Math.min(maxY, prev.y));

                // 只有在需要調整時才更新狀態（優化性能）
                if (clampedX !== prev.x || clampedY !== prev.y) {
                    return {
                        ...prev,
                        x: clampedX,
                        y: clampedY
                    };
                }
                return prev;
            });
        };

        // 使用 ResizeObserver 監聽容器尺寸變化
        const resizeObserver = new ResizeObserver(checkBounds);
        resizeObserver.observe(container);

        // 初始檢查
        checkBounds();

        // 清理函數
        return () => {
            resizeObserver.disconnect();
        };
    }, [svgSize.width, svgSize.height]);

    // === 資料處理和樹狀結構建立 ===
    /**
     * 建立組別 ID 到組別資料的對應關係
     * 用於快速查找組別資訊
     */
    const divisionIdToDivision = useMemo(() => {
        const map = new Map<string, Division>();
        divisions.forEach((d) => map.set(d.divisionId, d));
        return map;
    }, [divisions]);

    /**
     * 獲取部門的父部門 ID
     * 支援多種不同的父部門欄位命名方式
     * @param dept 部門資料物件
     * @returns 父部門 ID 或 null
     */
    const getDeptParentId = (dept: any): string | null => {
        const parent =
            dept?.parentId ??                    // 標準父部門 ID
            dept?.parentDepartmentId ??          // 父部門 ID 變形
            dept?.upperDepartmentId ??           // 上級部門 ID
            dept?.parent_id ??                   // 底線式命名
            dept?.parent ??                      // 簡化命名
            null;
        if (parent === "") return null;          // 空字串視為 null
        return parent ?? null;
    };

    /**
     * 建立樹狀結構的核心邏輯
     * 將部門、組別、員工資料組織成層級化的樹狀結構
     * 支援組別層級的開關和加給等級排序
     */
    const treeStructure = useMemo(() => {
        // === 第一階段：建立部門樹狀結構 ===
        const deptById = new Map<string, TreeNode>();  // 部門 ID 到節點的對應
        const rootDepts: TreeNode[] = [];               // 根部門清單（無父部門）

        // 為每個部門創建樹狀節點
        (departments || []).forEach((d: any) => {
            if (!d?.departmentId) return;  // 跳過無效的部門資料
            const node: TreeNode = {
                id: d.departmentId,
                name: d.name,
                type: 'department',
                level: 0,               // 初始層級為 0，稍後會重新計算
                children: []
            };
            deptById.set(d.departmentId, node);
        });

        // 建立部門層級關係（父子關係）
        (departments || []).forEach((d: any) => {
            if (!d?.departmentId) return;
            const node = deptById.get(d.departmentId)!;
            const parentId = getDeptParentId(d);

            if (parentId && deptById.has(parentId)) {
                // 有父部門且父部門存在
                const parent = deptById.get(parentId)!;
                parent.children.push(node);         // 加入父部門的子節點
                node.parent = parent;                // 設定父節點引用
                node.level = parent.level + 1;       // 層級 = 父層級 + 1
            } else {
                // 無父部門或父部門不存在，視為根部門
                rootDepts.push(node);
                node.level = 0;                     // 根部門層級為 0
            }
        });

        // === 第二階段：整理員工資料並按部門和組別分組 ===
        const deptIdToEmployees = new Map<string, Employee[]>();                    // 部門 -> 員工清單
        const deptIdToDivIdToEmployees = new Map<string, Map<string, Employee[]>>(); // 部門 -> (組別 -> 員工清單)

        // 遍歷所有員工，按部門和組別進行分組
        for (const emp of employees) {
            const deptId = emp.currentServiceDepartment?.serviceDepartmentId || "";  // 員工所屬部門
            const divId = emp.currentServiceDepartment?.serviceDivisionId || "";     // 員工所屬組別

            // 建立部門到員工的對應
            if (!deptIdToEmployees.has(deptId)) deptIdToEmployees.set(deptId, []);
            deptIdToEmployees.get(deptId)!.push(emp);

            // 建立部門 -> 組別 -> 員工的層級對應
            if (!deptIdToDivIdToEmployees.has(deptId)) deptIdToDivIdToEmployees.set(deptId, new Map());
            const divMap = deptIdToDivIdToEmployees.get(deptId)!;
            if (!divMap.has(divId)) divMap.set(divId, []);
            divMap.get(divId)!.push(emp);
        }

        // === 第三階段：為每個部門添加子節點（組別和員工） ===
        /**
         * 為指定部門添加員工和組別節點
         * 根據 showDivisionLevel 參數決定是否顯示組別層級
         */
        const addEmployeesToDept = (deptNode: TreeNode) => {
            const deptEmployees = deptIdToEmployees.get(deptNode.id) || [];           // 該部門的所有員工
            const divMap = deptIdToDivIdToEmployees.get(deptNode.id) || new Map<string, Employee[]>(); // 該部門的組別分組

            if (showDivisionLevel) {
                // 啟用組別層級顯示時的處理邏輯
                const normalDivIds: string[] = [];        // 正常組別 ID 清單
                const ungroupedEmployees: Employee[] = []; // 未分組員工清單

                // 遍歷所有組別，區分正常組別和未分組員工
                for (const [divId, empList] of divMap.entries()) {
                    const isEmpty = !divId || divId.trim() === "";     // 組別 ID 是否為空
                    const knownDivision = divisionIdToDivision.get(divId); // 組別是否存在於系統中

                    if (isEmpty || !knownDivision) {
                        // 組別 ID 為空或不存在，視為未分組員工
                        ungroupedEmployees.push(...empList);
                    } else {
                        // 正常的組別
                        normalDivIds.push(divId);
                    }
                }

                // 為未分組員工創建組別節點
                if (ungroupedEmployees.length > 0) {
                    const divNode: TreeNode = {
                        id: `${deptNode.id}_div_none`,
                        name: "未分組",
                        type: 'division',
                        level: deptNode.level + 1,
                        children: [],
                        parent: deptNode
                    };
                    addEmployeesToNode(divNode, ungroupedEmployees);  // 添加員工節點
                    deptNode.children.push(divNode);                  // 加入部門的子節點
                }

                // 為每個正常組別創建組別節點
                normalDivIds.forEach(divId => {
                    const empList = divMap.get(divId) || [];
                    if (empList.length === 0) return;  // 跳過空組別

                    const divName = divisionIdToDivision.get(divId)?.name || `未知組別(${divId})`;
                    const divNode: TreeNode = {
                        id: `${deptNode.id}_div_${divId}`,
                        name: divName,
                        type: 'division',
                        level: deptNode.level + 1,
                        children: [],
                        parent: deptNode
                    };
                    addEmployeesToNode(divNode, empList);  // 添加員工節點
                    deptNode.children.push(divNode);       // 加入部門的子節點
                });
            } else {
                // 直接添加員工到部門
                addEmployeesToNode(deptNode, deptEmployees);
            }

            // 遞歸處理子部門
            deptNode.children.forEach(child => {
                if (child.type === 'department') {
                    addEmployeesToDept(child);
                }
            });
        };

        const addEmployeesToNode = (parentNode: TreeNode, empList: Employee[]) => {
            // 按加給類型分組
            const allowanceGroups = new Map<string, Employee[]>();

            for (const emp of empList) {
                const allowanceType = emp.currentPromotion?.allowanceTypeName?.trim() || "未分類";


                if (!allowanceGroups.has(allowanceType)) allowanceGroups.set(allowanceType, []);
                allowanceGroups.get(allowanceType)!.push(emp);
            }

            // 按加給等級排序組別
            const sortedAllowanceTypes = Array.from(allowanceGroups.keys()).sort((a, b) => {
                const aRank = allowanceRank(a);
                const bRank = allowanceRank(b);
                if (aRank !== bRank) return aRank - bRank;
                return a.localeCompare(b);
            });

            // 為每個加給類型創建員工節點
            sortedAllowanceTypes.forEach(allowanceType => {
                const empList = allowanceGroups.get(allowanceType)!;
                const sortedEmps = sortEmployees(empList);

                sortedEmps.forEach(emp => {
                    const empNode: TreeNode = {
                        id: `emp_${emp.usersDTO?.userId || emp.userId}`,
                        name: emp.usersDTO?.name || "(未命名)",
                        type: 'employee',
                        level: parentNode.level + 1,
                        allowanceRank: allowanceRank(emp.currentPromotion?.allowanceTypeName, emp.currentPromotion?.allowanceType),
                        employees: [emp],
                        children: [],
                        parent: parentNode
                    };
                    parentNode.children.push(empNode);
                });
            });
        };

        // 處理所有部門
        rootDepts.forEach(addEmployeesToDept);

        // 處理未分配部門的員工
        const knownDeptIds = new Set((departments || []).map((d: any) => d.departmentId));
        const unknownEmployees = employees.filter(
            (e) => !(e.currentServiceDepartment?.serviceDepartmentId && knownDeptIds.has(e.currentServiceDepartment.serviceDepartmentId))
        );

        if (unknownEmployees.length > 0 && SHOW_UNKNOWN_DEPARTMENT) {
            // 計算現有正常部門的最大層級
            const computeMaxDeptLevel = (nodes: TreeNode[]): number => {
                let maxLevel = -1;
                const traverse = (node: TreeNode) => {
                    if (node.type === 'department') {
                        if (typeof node.level === 'number') {
                            maxLevel = Math.max(maxLevel, node.level) + 1;
                        }
                        node.children.forEach(traverse);
                    }
                };
                nodes.forEach(traverse);
                return maxLevel;
            };

            const maxExistingLevel = computeMaxDeptLevel(rootDepts);

            const unknownDept: TreeNode = {
                id: "dept_unknown",
                name: "未分配部門",
                type: 'department',
                level: (maxExistingLevel >= 0 ? maxExistingLevel + 1 : 0),
                children: []
            };
            addEmployeesToNode(unknownDept, unknownEmployees);
            rootDepts.push(unknownDept);
        }

        return rootDepts;
    }, [employees, departments, divisions, divisionIdToDivision, showDivisionLevel, allowanceRank, sortEmployees]);

    // 全新的佈局計算系統 - 分步驟清晰計算
    const calculateLayout = useMemo(() => {
        // 佈局常數定義
        const CONSTANTS = {
            // 員工節點相關設定
            NODE_WIDTH: 140,                    // 員工卡片寬度 (px)
            NODE_HEIGHT: 40,                    // 員工卡片高度 (px)

            // 間距設定
            HORIZONTAL_SPACING: 15,             // 員工卡片間的水平間距 (px)
            VERTICAL_SPACING: 12,               // 員工卡片間的垂直間距 (px)
            CONTAINER_PADDING: 25,              // 部門容器內邊距 (px)
            DEPARTMENT_SPACING: 80,             // 同層級部門間的間距 (px)
            LEVEL_SPACING: 80,                  // 不同層級部門間的垂直間距 (px)

            // 容器尺寸限制
            MIN_CONTAINER_WIDTH: 200,           // 部門容器最小寬度 (px)
            MIN_CONTAINER_HEIGHT: 100,          // 部門容器最小高度 (px)

            // 佈局控制 - 使用動態值
            EMPLOYEES_PER_ROW_MAX: employeesPerRow            // 每行最大員工數量，超過此數量會自動換行
        };

        // 第一步：清除所有位置信息
        const clearAllPositions = (nodes: TreeNode[]) => {
            nodes.forEach(node => {
                node.x = undefined;
                node.y = undefined;
                node.width = undefined;
                node.height = undefined;
                node.containerX = undefined;
                node.containerY = undefined;
                node.containerWidth = undefined;
                node.containerHeight = undefined;
                clearAllPositions(node.children);
            });
        };
        clearAllPositions(treeStructure);

        // 第二步：收集每個部門的直屬員工（不包括子部門的員工）
        const departmentDirectEmployeeMap = new Map<string, Employee[]>();

        const collectDirectEmployees = (node: TreeNode) => {
            if (node.type === 'department') {
                const directEmployees: Employee[] = [];

                // 只收集直接子節點中的員工，不遞歸到子部門
                node.children.forEach(child => {
                    if (child.type === 'employee' && child.employees && child.employees.length > 0) {
                        directEmployees.push(...child.employees);
                    } else if (child.type === 'division') {
                        // 如果有組別層級，收集組別下的員工
                        child.children.forEach(grandChild => {
                            if (grandChild.type === 'employee' && grandChild.employees && grandChild.employees.length > 0) {
                                directEmployees.push(...grandChild.employees);
                            }
                        });
                    }
                });


                departmentDirectEmployeeMap.set(node.id, directEmployees);
            }

            // 遞歸處理子部門
            node.children.forEach(child => {
                if (child.type === 'department') {
                    collectDirectEmployees(child);
                }
            });
        };

        treeStructure.forEach(collectDirectEmployees);

        // 簡單員工佈局（不顯示組別層級時使用）
        const calculateSimpleEmployeeLayout = (employees: Employee[]): { width: number; height: number; employeeNodes: TreeNode[]; divisionContainers: DivisionContainer[] } => {
            if (employees.length === 0) {
                return {
                    width: CONSTANTS.MIN_CONTAINER_WIDTH,
                    height: CONSTANTS.MIN_CONTAINER_HEIGHT,
                    employeeNodes: [],
                    divisionContainers: []
                };
            }

            // 按加給等級分組
            const rankGroups = new Map<number, Employee[]>();
            employees.forEach(emp => {
                const allowanceTypeName = emp.currentPromotion?.allowanceTypeName?.trim();
                const allowanceType = emp.currentPromotion?.allowanceType;
                const rank = allowanceRank(allowanceTypeName, allowanceType) ?? 2;

                if (!rankGroups.has(rank)) {
                    rankGroups.set(rank, []);
                }
                rankGroups.get(rank)!.push(emp);
            });

            // 按等級排序
            const sortedRanks = Array.from(rankGroups.keys()).sort((a, b) => a - b);

            let currentY = CONSTANTS.CONTAINER_PADDING;
            let maxWidth = 0;
            const employeeNodes: TreeNode[] = [];

            sortedRanks.forEach(rank => {
                const rankEmployees = sortEmployees(rankGroups.get(rank)!);

                // 計算這個等級需要多少行
                const employeesPerRow = Math.min(CONSTANTS.EMPLOYEES_PER_ROW_MAX, rankEmployees.length);
                const rows = Math.ceil(rankEmployees.length / employeesPerRow);

                for (let row = 0; row < rows; row++) {
                    const startIdx = row * employeesPerRow;
                    const endIdx = Math.min(startIdx + employeesPerRow, rankEmployees.length);
                    const rowEmployees = rankEmployees.slice(startIdx, endIdx);

                    // 計算這一行的寬度
                    const rowWidth = rowEmployees.length * CONSTANTS.NODE_WIDTH +
                        (rowEmployees.length - 1) * CONSTANTS.HORIZONTAL_SPACING;
                    maxWidth = Math.max(maxWidth, rowWidth);

                    // 為每個員工創建節點
                    rowEmployees.forEach((emp, empIndex) => {
                        const empNode: TreeNode = {
                            id: `emp_${emp.usersDTO?.userId || emp.userId}`,
                            name: emp.usersDTO?.name || "(未命名)",
                            type: 'employee',
                            level: 0,
                            allowanceRank: allowanceRank(emp.currentPromotion?.allowanceTypeName, emp.currentPromotion?.allowanceType),
                            employees: [emp],
                            children: [],
                            x: empIndex * (CONSTANTS.NODE_WIDTH + CONSTANTS.HORIZONTAL_SPACING),
                            y: currentY,
                            width: CONSTANTS.NODE_WIDTH,
                            height: CONSTANTS.NODE_HEIGHT
                        };
                        employeeNodes.push(empNode);
                    });

                    currentY += CONSTANTS.NODE_HEIGHT + CONSTANTS.VERTICAL_SPACING;
                }

                // 等級間額外間距
                if (rank !== sortedRanks[sortedRanks.length - 1]) {
                    currentY += CONSTANTS.VERTICAL_SPACING;
                }
            });

            // 調整員工位置使其在容器內居中
            const containerWidth = Math.max(CONSTANTS.MIN_CONTAINER_WIDTH, maxWidth + CONSTANTS.CONTAINER_PADDING * 2);
            employeeNodes.forEach(empNode => {
                // 找到這個員工所在行的所有員工
                const sameRowEmployees = employeeNodes.filter(n => n.y === empNode.y);
                const rowWidth = sameRowEmployees.length * CONSTANTS.NODE_WIDTH +
                    (sameRowEmployees.length - 1) * CONSTANTS.HORIZONTAL_SPACING;
                const rowStartX = (containerWidth - CONSTANTS.CONTAINER_PADDING * 2 - rowWidth) / 2;

                const empIndexInRow = sameRowEmployees.findIndex(n => n.id === empNode.id);
                if (empNode.x !== undefined) {
                    empNode.x = rowStartX + empIndexInRow * (CONSTANTS.NODE_WIDTH + CONSTANTS.HORIZONTAL_SPACING);
                }
            });

            const containerHeight = Math.max(CONSTANTS.MIN_CONTAINER_HEIGHT, currentY + CONSTANTS.CONTAINER_PADDING);

            return {
                width: containerWidth,
                height: containerHeight,
                employeeNodes,
                divisionContainers: []
            };
        };

        // 第三步：為每個部門計算直屬員工佈局（支援組別容器）
        const calculateEmployeeLayout = (departmentId: string): { width: number; height: number; employeeNodes: TreeNode[]; divisionContainers: DivisionContainer[] } => {
            const employees = departmentDirectEmployeeMap.get(departmentId) || [];

            if (employees.length === 0) {
                return {
                    width: CONSTANTS.MIN_CONTAINER_WIDTH,
                    height: CONSTANTS.MIN_CONTAINER_HEIGHT,
                    employeeNodes: [],
                    divisionContainers: []
                };
            }

            // 如果未啟用組別層級，使用原來的排法
            if (!showDivisionLevel) {
                return calculateSimpleEmployeeLayout(employees);
            }

            // 按組別分組員工
            const divisionGroups = new Map<string, Employee[]>();
            const ungroupedEmployees: Employee[] = [];

            employees.forEach(emp => {
                const divId = emp.currentServiceDepartment?.serviceDivisionId || "";
                const isEmpty = !divId || divId.trim() === "";
                const knownDivision = divisionIdToDivision.get(divId);

                if (isEmpty || !knownDivision) {
                    ungroupedEmployees.push(emp);
                } else {
                    if (!divisionGroups.has(divId)) {
                        divisionGroups.set(divId, []);
                    }
                    divisionGroups.get(divId)!.push(emp);
                }
            });

            let currentY = CONSTANTS.CONTAINER_PADDING;
            let maxWidth = 0;
            const employeeNodes: TreeNode[] = [];
            const divisionContainers: DivisionContainer[] = [];

            // 計算單個組別的佈局
            const calculateDivisionLayout = (divEmployees: Employee[], divisionName: string) => {
                // 按加給等級分組
                const rankGroups = new Map<number, Employee[]>();
                divEmployees.forEach(emp => {
                    const allowanceTypeName = emp.currentPromotion?.allowanceTypeName?.trim();
                    const allowanceType = emp.currentPromotion?.allowanceType;
                    const rank = allowanceRank(allowanceTypeName, allowanceType) ?? 2;

                    if (!rankGroups.has(rank)) {
                        rankGroups.set(rank, []);
                    }
                    rankGroups.get(rank)!.push(emp);
                });

                // 按等級排序
                const sortedRanks = Array.from(rankGroups.keys()).sort((a, b) => a - b);

                let divCurrentY = 0;
                let divMaxWidth = 0;
                const divEmployeeNodes: TreeNode[] = [];

                sortedRanks.forEach(rank => {
                    const rankEmployees = sortEmployees(rankGroups.get(rank)!);

                    // 計算這個等級需要多少行
                    const employeesPerRow = Math.min(CONSTANTS.EMPLOYEES_PER_ROW_MAX, rankEmployees.length);
                    const rows = Math.ceil(rankEmployees.length / employeesPerRow);

                    for (let row = 0; row < rows; row++) {
                        const startIdx = row * employeesPerRow;
                        const endIdx = Math.min(startIdx + employeesPerRow, rankEmployees.length);
                        const rowEmployees = rankEmployees.slice(startIdx, endIdx);

                        // 計算這一行的寬度
                        const rowWidth = rowEmployees.length * CONSTANTS.NODE_WIDTH +
                            (rowEmployees.length - 1) * CONSTANTS.HORIZONTAL_SPACING;
                        divMaxWidth = Math.max(divMaxWidth, rowWidth);

                        // 為每個員工創建節點
                        rowEmployees.forEach((emp, empIndex) => {
                            const empNode: TreeNode = {
                                id: `emp_${emp.usersDTO?.userId || emp.userId}`,
                                name: emp.usersDTO?.name || "(未命名)",
                                type: 'employee',
                                level: 0,
                                allowanceRank: allowanceRank(emp.currentPromotion?.allowanceTypeName, emp.currentPromotion?.allowanceType),
                                employees: [emp],
                                children: [],
                                x: empIndex * (CONSTANTS.NODE_WIDTH + CONSTANTS.HORIZONTAL_SPACING),
                                y: divCurrentY,
                                width: CONSTANTS.NODE_WIDTH,
                                height: CONSTANTS.NODE_HEIGHT
                            };
                            divEmployeeNodes.push(empNode);
                        });

                        divCurrentY += CONSTANTS.NODE_HEIGHT + CONSTANTS.VERTICAL_SPACING;
                    }

                    // 等級間額外間距
                    if (rank !== sortedRanks[sortedRanks.length - 1]) {
                        divCurrentY += CONSTANTS.VERTICAL_SPACING;
                    }
                });

                // 調整員工位置使其在組別容器內居中
                const divContainerWidth = Math.max(200, divMaxWidth + 20);
                const divContainerHeight = Math.max(60, divCurrentY + 20);

                divEmployeeNodes.forEach(empNode => {
                    const sameRowEmployees = divEmployeeNodes.filter(n => n.y === empNode.y);
                    const rowWidth = sameRowEmployees.length * CONSTANTS.NODE_WIDTH +
                        (sameRowEmployees.length - 1) * CONSTANTS.HORIZONTAL_SPACING;
                    const rowStartX = (divContainerWidth - rowWidth) / 2;

                    const empIndexInRow = sameRowEmployees.findIndex(n => n.id === empNode.id);
                    empNode.x = rowStartX + empIndexInRow * (CONSTANTS.NODE_WIDTH + CONSTANTS.HORIZONTAL_SPACING);
                });

                return {
                    width: divContainerWidth,
                    height: divContainerHeight,
                    employeeNodes: divEmployeeNodes
                };
            };

            // 處理未分組員工
            if (ungroupedEmployees.length > 0) {
                const layout = calculateDivisionLayout(ungroupedEmployees, "未分組");

                divisionContainers.push({
                    id: `${departmentId}_div_none`,
                    name: "未分組",
                    employees: ungroupedEmployees,
                    x: 0,
                    y: currentY,
                    width: layout.width,
                    height: layout.height
                });

                // 保持員工節點位置為相對位置，稍後統一轉換
                layout.employeeNodes.forEach(empNode => {
                    // 直接使用組別內的相對位置，不添加額外偏移
                    employeeNodes.push(empNode);
                });

                maxWidth = Math.max(maxWidth, layout.width);
                currentY += layout.height + CONSTANTS.VERTICAL_SPACING + 30;
            }

            // 處理正常組別
            Array.from(divisionGroups.keys())
                .sort((a, b) => {
                    const an = divisionIdToDivision.get(a)?.name || a;
                    const bn = divisionIdToDivision.get(b)?.name || b;
                    return an.localeCompare(bn, 'zh-TW');
                })
                .forEach(divId => {
                    const divEmployees = divisionGroups.get(divId)!;
                    const divName = divisionIdToDivision.get(divId)?.name || `未知組別(${divId})`;
                    const layout = calculateDivisionLayout(divEmployees, divName);

                    divisionContainers.push({
                        id: `${departmentId}_div_${divId}`,
                        name: divName,
                        employees: divEmployees,
                        x: 0,
                        y: currentY,
                        width: layout.width,
                        height: layout.height
                    });

                    // 保持員工節點位置為相對位置，稍後統一轉換
                    layout.employeeNodes.forEach(empNode => {
                        // 直接使用組別內的相對位置，不添加額外偏移
                        employeeNodes.push(empNode);
                    });

                    maxWidth = Math.max(maxWidth, layout.width);
                    currentY += layout.height + CONSTANTS.VERTICAL_SPACING + 30;
                });

            const containerWidth = Math.max(CONSTANTS.MIN_CONTAINER_WIDTH, maxWidth + CONSTANTS.CONTAINER_PADDING * 2);
            const containerHeight = Math.max(CONSTANTS.MIN_CONTAINER_HEIGHT, currentY + CONSTANTS.CONTAINER_PADDING);

            // 調整所有組別容器的X位置使其居中
            divisionContainers.forEach(divContainer => {
                divContainer.x = (containerWidth - divContainer.width) / 2;
            });

            return {
                width: containerWidth,
                height: containerHeight,
                employeeNodes,
                divisionContainers
            };
        };

        // 第四步：計算每個部門的容器尺寸並重新組織節點結構
        const departmentLayouts = new Map<string, { width: number; height: number; employeeNodes: TreeNode[]; divisionContainers: DivisionContainer[] }>();

        const calculateDepartmentSizes = (node: TreeNode) => {
            if (node.type === 'department') {
                const layout = calculateEmployeeLayout(node.id);
                departmentLayouts.set(node.id, layout);

                node.containerWidth = layout.width;
                node.containerHeight = layout.height;
                node.divisionContainers = layout.divisionContainers;

                // 清除現有的員工和組別節點，只保留子部門
                const childDepartments = node.children.filter(child => child.type === 'department');

                // 確保子部門的父子關係和層級正確
                childDepartments.forEach(childDept => {
                    childDept.parent = node;
                    childDept.level = node.level + 1;
                });

                node.children = [...childDepartments];

                // 添加新計算的員工節點
                layout.employeeNodes.forEach(empNode => {
                    empNode.level = node.level + 1;
                    empNode.parent = node;
                    node.children.push(empNode);
                });
            }

            // 遞歸處理子部門
            node.children.forEach(child => {
                if (child.type === 'department') {
                    calculateDepartmentSizes(child);
                }
            });
        };

        treeStructure.forEach(calculateDepartmentSizes);

        // 第五步：按層級排列部門
        const departmentsByLevel = new Map<number, TreeNode[]>();
        const collectDepartmentsByLevel = (nodes: TreeNode[]) => {
            nodes.forEach(node => {
                if (node.type === 'department') {
                    console.log(`收集部門 "${node.name}" 到層級 ${node.level}`);
                    if (!departmentsByLevel.has(node.level)) {
                        departmentsByLevel.set(node.level, []);
                    }
                    departmentsByLevel.get(node.level)!.push(node);
                    // 遞歸收集子部門
                    collectDepartmentsByLevel(node.children.filter(child => child.type === 'department'));
                }
            });
        };
        collectDepartmentsByLevel(treeStructure);

        // 輸出層級統計
        console.log('部門層級統計:');
        Array.from(departmentsByLevel.keys()).sort((a, b) => a - b).forEach(level => {
            const depts = departmentsByLevel.get(level)!;
            console.log(`  層級 ${level}: ${depts.map(d => d.name).join(', ')}`);
        });

        // 第六步：計算每層的總寬度並設置部門位置
        const sortedLevels = Array.from(departmentsByLevel.keys()).sort((a, b) => a - b);
        let totalMaxWidth = 0;

        // 先計算總體最大寬度
        sortedLevels.forEach(level => {
            const departments = departmentsByLevel.get(level)!;
            const levelWidth = departments.reduce((sum, dept) => sum + (dept.containerWidth || 0), 0) +
                (departments.length - 1) * CONSTANTS.DEPARTMENT_SPACING;
            totalMaxWidth = Math.max(totalMaxWidth, levelWidth);
        });

        // 設置部門的絕對位置 - 確保每個層級都在獨立的橫排
        let currentY = 50;
        console.log(`開始佈局，共 ${sortedLevels.length} 個層級: [${sortedLevels.join(', ')}]`);
        console.log(`總最大寬度: ${totalMaxWidth}`);

        sortedLevels.forEach((level, levelIndex) => {
            const departments = departmentsByLevel.get(level)!;
            const levelWidth = departments.reduce((sum, dept) => sum + (dept.containerWidth || 0), 0) +
                (departments.length - 1) * CONSTANTS.DEPARTMENT_SPACING;

            // 計算這一層的起始X位置（居中）
            let currentX = Math.max(50, (totalMaxWidth - levelWidth) / 2 + 50);
            let levelMaxHeight = 0;

            console.log(`\n層級 ${level} (索引 ${levelIndex}):`);
            console.log(`  部門數量: ${departments.length}`);
            console.log(`  層級寬度: ${levelWidth}`);
            console.log(`  起始X: ${currentX}, Y: ${currentY}`);

            departments.forEach((dept, deptIndex) => {
                // 設置部門容器位置
                dept.containerX = currentX;
                dept.containerY = currentY;

                // 設置部門標題位置
                dept.x = currentX + (dept.containerWidth || 0) / 2 - 80;
                dept.y = currentY - 30;
                dept.width = 160;
                dept.height = 25;

                console.log(`    部門 "${dept.name}" (層級 ${dept.level}): X=${currentX}, Y=${currentY}, 容器寬=${dept.containerWidth}, 容器高=${dept.containerHeight}`);

                // 轉換當前部門的直屬員工為絕對位置
                dept.children.forEach(child => {
                    if (child.type === 'employee' && child.x !== undefined && child.y !== undefined) {
                        if (showDivisionLevel) {
                            // 組別模式：需要根據員工所屬的組別容器來計算絕對位置
                            const empDivisionId = child.employees?.[0]?.currentServiceDepartment?.serviceDivisionId || "";
                            const isEmpty = !empDivisionId || empDivisionId.trim() === "";
                            const knownDivision = divisionIdToDivision.get(empDivisionId);

                            // 找到對應的組別容器
                            let targetDivContainer = null;
                            if (isEmpty || !knownDivision) {
                                // 未分組員工
                                targetDivContainer = dept.divisionContainers?.find(dc => dc.id === `${dept.id}_div_none`);
                            } else {
                                // 正常組別員工
                                targetDivContainer = dept.divisionContainers?.find(dc => dc.id === `${dept.id}_div_${empDivisionId}`);
                            }

                            if (targetDivContainer) {
                                // 員工絕對位置 = 部門容器位置 + 組別容器位置 + 組別內邊距 + 員工相對位置
                                child.x = currentX + targetDivContainer.x + child.x;
                                child.y = currentY + targetDivContainer.y + 15 + child.y;
                            } else {
                                // 找不到對應組別容器時的後備方案
                                child.x = currentX + CONSTANTS.CONTAINER_PADDING + child.x;
                                child.y = currentY + CONSTANTS.CONTAINER_PADDING + child.y;
                            }
                        } else {
                            // 簡單模式：轉換為絕對位置：部門容器位置 + 容器內邊距 + 員工相對位置
                            child.x = currentX + CONSTANTS.CONTAINER_PADDING + child.x;
                            child.y = currentY + child.y;
                        }
                    }
                });

                levelMaxHeight = Math.max(levelMaxHeight, dept.containerHeight || 0);
                currentX += (dept.containerWidth || 0) + CONSTANTS.DEPARTMENT_SPACING;
            });

            // 確保每個層級之間有足夠的間距
            currentY += levelMaxHeight + CONSTANTS.LEVEL_SPACING;
            console.log(`  層級 ${level} 完成，最大高度: ${levelMaxHeight}，下一層Y座標: ${currentY}`);
        });

        // 第七步：計算最終SVG尺寸
        const finalWidth = Math.max(1000, totalMaxWidth + 100);
        const finalHeight = Math.max(600, currentY + 50);

        setSvgSize({ width: finalWidth, height: finalHeight });


        return { maxWidth: finalWidth, maxHeight: finalHeight };
    }, [treeStructure, allowanceRank, sortEmployees, employeesPerRow, showDivisionLevel, divisionIdToDivision]);

    // 執行佈局計算 - 確保所有節點位置和尺寸都被計算
    const layoutResult = calculateLayout;

    // 確保佈局計算被執行
    layoutResult;

    // === 渲染功能區塊 ===
    /**
     * 渲染部門間的連接線
     * 在父子部門之間繪製帶箭頭的連接線，支援直線和 L 型連接
     * @returns SVG 連接線元素陣列
     */
    const renderConnections = () => {
        const lines: React.ReactElement[] = [];

        /**
         * 遞迴為每個部門添加與其子部門的連接線
         */
        const addDepartmentConnections = (node: TreeNode) => {
            if (node.type === 'department') {
                // 找到所有子部門
                const childDepartments = node.children.filter(child => child.type === 'department');

                // 為每個子部門繪製連接線
                childDepartments.forEach(child => {
                    // 確保兩個部門都有有效的位置信息
                    if (node.containerX !== undefined && node.containerY !== undefined &&
                        child.containerX !== undefined && child.containerY !== undefined &&
                        node.containerWidth !== undefined && node.containerHeight !== undefined &&
                        child.containerWidth !== undefined) {

                        // 計算連接點座標
                        const startX = node.containerX + node.containerWidth / 2;   // 父部門底部中心
                        const startY = node.containerY + node.containerHeight;
                        const endX = child.containerX + child.containerWidth / 2;   // 子部門頂部中心
                        const endY = child.containerY - 15;

                        const connectionKey = `connection-${node.id}-${child.id}`;
                        const strokeColor = "#1890ff";
                        const strokeWidth = "2";

                        // 根據父子部門的水平位置決定連接線類型
                        if (Math.abs(startX - endX) < 10) {
                            // 直線連接（父子部門在同一垂直線上）
                            lines.push(
                                <g key={connectionKey}>
                                    <line
                                        x1={startX}
                                        y1={startY}
                                        x2={endX}
                                        y2={endY}
                                        stroke={strokeColor}
                                        strokeWidth={strokeWidth}
                                        markerEnd="url(#arrowhead)"
                                    />
                                </g>
                            );
                        } else {
                            // L型連接線（父子部門不在同一垂直線上）
                            const midY = startY + (endY - startY) / 2;
                            lines.push(
                                <g key={connectionKey}>
                                    <path
                                        d={`M ${startX} ${startY} L ${startX} ${midY} L ${endX} ${midY} L ${endX} ${endY}`}
                                        stroke={strokeColor}
                                        strokeWidth={strokeWidth}
                                        fill="none"
                                        markerEnd="url(#arrowhead)"
                                    />
                                </g>
                            );
                        }
                    }

                    // 遞迴處理子部門的連接線
                    addDepartmentConnections(child);
                });
            }
        };

        treeStructure.forEach(addDepartmentConnections);
        return lines;
    };

    /**
     * 渲染所有節點和容器
     * 包括部門容器、組別容器、員工節點的完整渲染邏輯
     * @returns SVG 節點元素陣列
     */
    const renderNodes = () => {
        const nodes: React.ReactElement[] = [];

        /**
         * 遞迴渲染每個節點及其子節點
         * 支援部門容器、組別容器、員工節點三種類型
         */
        const renderNodeRecursive = (node: TreeNode) => {
            // === 渲染部門容器 ===
            if (node.type === 'department' &&
                node.containerX !== undefined && node.containerY !== undefined &&
                node.containerWidth !== undefined && node.containerHeight !== undefined) {

                const containerKey = `dept-container-${node.id}`;
                const departmentElements: React.ReactElement[] = [];

                // 部門容器背景（虛線邊框矩形）
                departmentElements.push(
                    <rect
                        key={`${containerKey}-bg`}
                        x={node.containerX}
                        y={node.containerY}
                        width={node.containerWidth}
                        height={node.containerHeight}
                        fill="#f8f9fa"           // 淺灰背景
                        stroke="#1890ff"         // 藍色邊框
                        strokeWidth="2"
                        rx="12"                  // 圓角
                        strokeDasharray="5,5"    // 虛線樣式
                    />
                );

                // 部門標題背景（藍色標籤）
                departmentElements.push(
                    <rect
                        key={`${containerKey}-title-bg`}
                        x={node.containerX + node.containerWidth / 2 - 100}  // 水平置中
                        y={node.containerY - 30}                            // 位於容器上方
                        width={200}
                        height={30}
                        fill="#1890ff"                                       // 藍色背景
                        rx="8"                                               // 圓角
                    />
                );

                // 部門標題文字（白色粗體）
                departmentElements.push(
                    <text
                        key={`${containerKey}-title-text`}
                        x={node.containerX + node.containerWidth / 2}  // 水平置中
                        y={node.containerY - 10}                      // 垂直置中於標題背景
                        textAnchor="middle"
                        fill="white"                                   // 白色文字
                        fontSize="14"
                        fontWeight="bold"
                    >
                        {node.name}
                    </text>
                );

                // 渲染組別容器（如果啟用組別層級顯示）
                if (showDivisionLevel && node.divisionContainers && node.divisionContainers.length > 0) {
                    node.divisionContainers.forEach(divContainer => {
                        const divKey = `div-container-${divContainer.id}`;
                        const absoluteX = node.containerX! + divContainer.x;
                        const absoluteY = node.containerY! + divContainer.y;

                        // 組別容器背景
                        departmentElements.push(
                            <rect
                                key={`${divKey}-bg`}
                                x={absoluteX}
                                y={absoluteY}
                                width={divContainer.width}
                                height={divContainer.height}
                                fill="#fff"
                                stroke="#722ed1"
                                strokeWidth="1.5"
                                rx="8"
                                strokeDasharray="3,3"
                                opacity="0.8"
                            />
                        );

                        // 組別標題背景
                        departmentElements.push(
                            <rect
                                key={`${divKey}-title-bg`}
                                x={absoluteX + divContainer.width / 2 - 40}
                                y={absoluteY - 20}
                                width={80}
                                height={20}
                                fill="#722ed1"
                                rx="4"
                            />
                        );

                        // 組別標題文字
                        departmentElements.push(
                            <text
                                key={`${divKey}-title-text`}
                                x={absoluteX + divContainer.width / 2}
                                y={absoluteY - 6}
                                textAnchor="middle"
                                fill="white"
                                fontSize="11"
                                fontWeight="600"
                            >
                                {divContainer.name}
                            </text>
                        );

                        // 組別員工數量標籤
                        departmentElements.push(
                            <text
                                key={`${divKey}-count`}
                                x={absoluteX + divContainer.width - 10}
                                y={absoluteY + 15}
                                textAnchor="end"
                                fill="#722ed1"
                                fontSize="10"
                                fontWeight="bold"
                            >
                                {divContainer.employees.length}人
                            </text>
                        );
                    });
                }

                nodes.push(<g key={containerKey}>{departmentElements}</g>);
            }

            // 渲染員工節點
            if (node.type === 'employee' &&
                node.x !== undefined && node.y !== undefined &&
                node.width !== undefined && node.height !== undefined) {

                // 根據加給等級設定顏色
                const rank = node.allowanceRank ?? 2;
                const employee = node.employees?.[0];

                // 檢查是否為未設定狀態
                const isUnsetStatus = !employee?.currentPromotion?.jobTitleName ||
                    !employee?.currentPromotion?.allowanceTypeName?.trim() ||
                    !employee?.currentPromotion?.jobLevelName ||
                    !employee?.currentPromotion?.jobRankName;

                let bgColor = "#fff";
                let borderColor = "#d9d9d9";
                let textColor = "#000";

                if (isUnsetStatus) {
                    // 未設定狀態 - 使用灰色系
                    bgColor = "#f5f5f5";
                    borderColor = "#bfbfbf";
                    textColor = "#8c8c8c";
                } else {
                    switch (rank) {
                        case 0: // 總幹事
                            bgColor = "#fff7e6";
                            borderColor = "#fa8c16";
                            break;
                        case 1: // 主管
                            bgColor = "#f9f0ff";
                            borderColor = "#722ed1";
                            break;
                        default: // 一般員工
                            bgColor = "#fff";
                            borderColor = "#d9d9d9";
                    }
                }

                const employeeKey = `employee-${node.id}`;

                nodes.push(
                    <g
                        key={employeeKey}
                        style={{ cursor: 'pointer' }}
                        onClick={(e) => {
                            e.stopPropagation();
                            employee && handleEmployeeClick(employee);
                        }}
                    >
                        {/* 員工卡片背景 */}
                        <rect
                            x={node.x}
                            y={node.y}
                            width={node.width}
                            height={node.height}
                            fill={bgColor}
                            stroke={borderColor}
                            strokeWidth="2"
                            rx="6"
                        />
                        {/* 懸停效果覆蓋層 */}
                        <rect
                            x={node.x}
                            y={node.y}
                            width={node.width}
                            height={node.height}
                            fill="transparent"
                            rx="6"
                            style={{ cursor: 'pointer' }}
                            data-employee-clickable="true"
                            onMouseEnter={(e) => {
                                const rect = e.currentTarget.previousElementSibling as SVGRectElement;
                                if (rect) {
                                    let hoverColor = '#f0f8ff'; // 預設懸停顏色
                                    if (isUnsetStatus) {
                                        hoverColor = '#e8e8e8'; // 未設定狀態的懸停顏色
                                    } else if (rank === 0) {
                                        hoverColor = '#fff2d9'; // 總幹事懸停顏色
                                    } else if (rank === 1) {
                                        hoverColor = '#f0e6ff'; // 主管懸停顏色
                                    }
                                    rect.setAttribute('fill', hoverColor);
                                }
                            }}
                            onMouseLeave={(e) => {
                                const rect = e.currentTarget.previousElementSibling as SVGRectElement;
                                if (rect) {
                                    rect.setAttribute('fill', bgColor);
                                }
                            }}
                        />

                        {/* 員工姓名 */}
                        <text
                            x={node.x + node.width / 2}
                            y={node.y + 16}
                            textAnchor="middle"
                            fill={textColor}
                            fontSize="12"
                            fontWeight="600"
                            style={{ pointerEvents: 'none' }}
                        >
                            {node.name}
                        </text>

                        {/* 員工編號 */}
                        {employee?.empNo && (
                            <text
                                x={node.x + node.width / 2}
                                y={node.y + 28}
                                textAnchor="middle"
                                fill={isUnsetStatus ? "#bfbfbf" : "#666"}
                                fontSize="10"
                                style={{ pointerEvents: 'none' }}
                            >
                                {employee.empNo}
                            </text>
                        )}
                    </g>
                );
            }

            // 遞歸處理子節點
            node.children.forEach(renderNodeRecursive);
        };

        treeStructure.forEach(renderNodeRecursive);
        return nodes;
    };

    if (treeStructure.length === 0) {
        return <Empty description="無組織架構資料" />;
    }

    return (
        <div style={{ width: '100%' }}>
            {/* 圖例 */}
            <Card size="small" style={{ marginBottom: 16 }}>
                <Space wrap>
                    <Space size={4}>
                        <div style={{
                            width: 24, height: 16,
                            backgroundColor: '#f8f9fa',
                            border: '2px dashed #1890ff',
                            borderRadius: 4
                        }}></div>
                        <Text>部門容器</Text>
                    </Space>
                    <Space size={4}>
                        <div style={{
                            width: 16, height: 16,
                            backgroundColor: '#1890ff',
                            borderRadius: 4
                        }}></div>
                        <Text>部門標題</Text>
                    </Space>
                    {showDivisionLevel && (
                        <>
                            <Space size={4}>
                                <div style={{
                                    width: 20, height: 14,
                                    backgroundColor: '#fff',
                                    border: '1.5px dashed #722ed1',
                                    borderRadius: 4,
                                    opacity: 0.8
                                }}></div>
                                <Text>組別容器</Text>
                            </Space>
                            <Space size={4}>
                                <div style={{
                                    width: 14, height: 14,
                                    backgroundColor: '#722ed1',
                                    borderRadius: 4
                                }}></div>
                                <Text>組別標題</Text>
                            </Space>
                        </>
                    )}
                    <Space size={4}>
                        <div style={{ width: 16, height: 16, backgroundColor: '#fff7e6', border: '2px solid #fa8c16', borderRadius: 4 }}></div>
                        <Text>總幹事</Text>
                    </Space>
                    <Space size={4}>
                        <div style={{ width: 16, height: 16, backgroundColor: '#f9f0ff', border: '2px solid #722ed1', borderRadius: 4 }}></div>
                        <Text>主管</Text>
                    </Space>
                    <Space size={4}>
                        <div style={{ width: 16, height: 16, backgroundColor: '#fff', border: '2px solid #d9d9d9', borderRadius: 4 }}></div>
                        <Text>一般員工</Text>
                    </Space>
                    <Space size={4}>
                        <div style={{
                            width: 20, height: 2,
                            backgroundColor: '#1890ff',
                            marginTop: 7
                        }}></div>
                        <Text>部門階層連線</Text>
                    </Space>
                </Space>
            </Card>

            {/* 控制按鈕 */}
            <Card size="small" style={{ marginBottom: 16 }}>
                <Space wrap>
                    {/* 視圖控制 */}
                    <Space>
                        <Tooltip title="放大">
                            <Button
                                icon={<ZoomInOutlined />}
                                size="small"
                                onClick={handleZoomIn}
                            />
                        </Tooltip>
                        <Tooltip title="縮小">
                            <Button
                                icon={<ZoomOutOutlined />}
                                size="small"
                                onClick={handleZoomOut}
                            />
                        </Tooltip>
                        <Tooltip title="重置視圖">
                            <Button
                                icon={<ReloadOutlined />}
                                size="small"
                                onClick={handleResetView}
                            />
                        </Tooltip>
                    </Space>

                    <Divider type="vertical" />

                    {/* 佈局設定 */}
                    <Space size={4}>
                        <SettingOutlined style={{ color: '#1890ff' }} />
                        <Text type="secondary" style={{ fontSize: 12 }}>每行員工數：</Text>
                        <InputNumber
                            size="small"
                            min={1}
                            max={10}
                            value={employeesPerRow}
                            onChange={(value) => setEmployeesPerRow(value || 5)}
                            style={{ width: 60 }}
                        />
                        <Text type="secondary" style={{ fontSize: 12 }}>人</Text>
                    </Space>

                    <Divider type="vertical" />

                    {/* 匯出功能 */}
                    <Space>
                        <Tooltip title="匯出選項">
                            <Button
                                icon={<DownloadOutlined />}
                                size="small"
                                onClick={() => setExportModalVisible(true)}
                            >
                                匯出
                            </Button>
                        </Tooltip>
                    </Space>

                    <Divider type="vertical" />

                    {/* 狀態資訊 */}
                    <Space size={4}>
                        <DragOutlined style={{ color: '#1890ff' }} />
                        <Text type="secondary" style={{ fontSize: 12 }}>
                            拖拽平移 | 按鈕縮放 | 縮放: {(transform.scale * 100).toFixed(0)}%
                        </Text>
                    </Space>
                </Space>
            </Card>

            {/* 樹狀圖 */}
            <Spin spinning={loading} tip="載入組織架構圖中...">
                <div
                    ref={containerRef}
                    style={{
                        width: '100%',
                        height: '70vh',
                        overflow: 'hidden',
                        border: '1px solid #d9d9d9',
                        borderRadius: 6,
                        cursor: isDragging ? 'grabbing' : 'grab',
                        position: 'relative'
                    }}
                    onMouseDown={handleMouseDown}
                    onMouseMove={handleMouseMove}
                    onMouseUp={handleMouseUp}
                    onMouseLeave={handleMouseUp}
                >
                    <svg
                        ref={svgRef}
                        width={svgSize.width}
                        height={svgSize.height}
                        style={{
                            background: '#fafafa',
                            transform: `translate(${transform.x}px, ${transform.y}px) scale(${transform.scale})`,
                            transformOrigin: '0 0',
                            transition: isDragging ? 'none' : 'transform 0.2s ease-out'
                        }}
                    >
                        {/* 定義箭頭標記 */}
                        <defs>
                            <marker
                                id="arrowhead"
                                markerWidth="10"
                                markerHeight="7"
                                refX="9"
                                refY="3.5"
                                orient="auto"
                            >
                                <polygon
                                    points="0 0, 10 3.5, 0 7"
                                    fill="#1890ff"
                                />
                            </marker>
                        </defs>

                        {/* 渲染連接線 */}
                        {renderConnections()}

                        {/* 渲染節點 */}
                        {renderNodes()}
                    </svg>
                </div>
            </Spin>


            {/* 匯出選項 Modal */}
            <Modal
                title="匯出組織架構圖"
                open={exportModalVisible}
                onCancel={() => setExportModalVisible(false)}
                footer={null}
                width={400}
            >
                <Space direction="vertical" style={{ width: '100%' }} size="large">
                    <div>
                        <Text strong style={{ fontSize: 16, color: '#1890ff' }}>選擇匯出格式</Text>
                        <Text type="secondary" style={{ display: 'block', marginTop: 8 }}>
                            當前設定：每行 {employeesPerRow} 個員工
                        </Text>
                    </div>

                    <Space direction="vertical" style={{ width: '100%' }} size="middle">
                        <Button
                            type="primary"
                            icon={<DownloadOutlined />}
                            size="large"
                            block
                            onClick={() => {
                                exportToPNG();
                                setExportModalVisible(false);
                            }}
                        >
                            匯出為 PNG 圖片
                            <Text type="secondary" style={{ fontSize: 12 }}>（適合插入文件或簡報）</Text>
                        </Button>

                        <Button
                            icon={<DownloadOutlined />}
                            size="large"
                            block
                            onClick={() => {
                                exportToSVG();
                                setExportModalVisible(false);
                            }}
                        >
                            匯出為 SVG 向量圖
                            <Text type="secondary" style={{ fontSize: 12 }}>（可無損縮放）</Text>
                        </Button>

                        <Button
                            icon={<DownloadOutlined />}
                            size="large"
                            block
                            onClick={() => {
                                exportToHTML();
                                setExportModalVisible(false);
                            }}
                        >
                            匯出為 HTML 網頁
                            <Text type="secondary" style={{ fontSize: 12 }}>（可在瀏覽器中開啟或列印）</Text>
                        </Button>
                    </Space>

                    <div style={{ textAlign: 'center', marginTop: 16 }}>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                            匯出的檔案將包含當前的佈局設定和所有可見的員工資訊
                        </Text>
                    </div>
                </Space>
            </Modal>
        </div>
    );
};

export default OrganizationTreeDiagram;
