using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Rms;

namespace FAST_ERP_Backend.Interfaces.Rms
{
    /// <summary>
    /// 費用管理服務介面
    /// </summary>
    public interface IFeeService
    {
        /// <summary>
        /// 取得費用列表
        /// </summary>
        /// <param name="contractId">合約編號</param>
        /// <param name="status">費用狀態</param>
        /// <param name="feeType">費用類型</param>
        /// <param name="billingPeriod">計費期間</param>
        /// <returns>費用列表回應</returns>
        Task<ApiResponse<List<FeeDTO>>> GetFeeListAsync(string contractId, string? status, string? feeType, string? billingPeriod);
        
        /// <summary>
        /// 建立費用
        /// </summary>
        /// <param name="request">費用建立請求</param>
        /// <param name="tokenUid">使用者識別碼</param>
        /// <returns>費用建立回應</returns>
        Task<ApiResponse<FeeDTO>> CreateFeeAsync(FeeCreateRequestDTO request, string tokenUid);
        
        /// <summary>
        /// 更新費用
        /// </summary>
        /// <param name="request">費用更新請求</param>
        /// <param name="tokenUid">使用者識別碼</param>
        /// <returns>更新結果</returns>
        Task<ApiResponse<string>> UpdateFeeAsync(FeeUpdateRequestDTO request, string tokenUid);
        
        /// <summary>
        /// 刪除費用
        /// </summary>
        /// <param name="request">費用刪除請求</param>
        /// <param name="tokenUid">使用者識別碼</param>
        /// <returns>刪除結果</returns>
        Task<ApiResponse<string>> DeleteFeeAsync(FeeDeleteRequestDTO request, string tokenUid);

    }
}


