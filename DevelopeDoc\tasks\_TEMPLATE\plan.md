# 實作規劃（Plan）

## 目標與範圍
- 問題背景：
- 成功條件：
- 非目標（Out of Scope）：

## 開發流程檢查清單

### 階段一：需求分析（必須完成）
- [ ] **使用者案例分析**
  - [ ] 分析目標使用者的使用情境
  - [ ] 確認使用者的痛點和需求
  - [ ] 定義使用者故事和驗收標準

- [ ] **現有功能分析**
  - [ ] 檢查是否有相關的現有功能
  - [ ] 分析現有功能的完整性和可用性
  - [ ] 確認需要補足的功能缺口

- [ ] **後端 API 分析**
  - [ ] 檢查現有的 API 端點
  - [ ] 確認 API 的實作狀況和完整性
  - [ ] 識別需要新增或修改的 API
  - [ ] 檢查 API 端點命名一致性
  - [ ] 驗證 HTTP 方法使用正確性

- [ ] **資料庫結構分析**
  - [ ] 檢查相關的資料表設計
  - [ ] 確認資料關聯和約束
  - [ ] 識別需要新增或修改的資料結構

- [ ] **權限需求分析**
  - [ ] 確認功能所需的權限
  - [ ] 分析權限的粒度和範圍
  - [ ] 設計權限檢查機制

- [ ] **架構一致性分析**
  - [ ] 檢查前後端介面屬性一致性
  - [ ] 驗證 API 端點命名一致性
  - [ ] 確認資料驗證邏輯與介面同步
  - [ ] 檢查服務層函數命名與 API 對應

### 階段二：規格設計（必須完成）
- [ ] **業務規格定義**
  - [ ] 完成 specs/ 目錄下的規格文件
  - [ ] 定義清楚的業務規則和驗證邏輯
  - [ ] 設計資料結構和 API 介面

- [ ] **API 設計**
  - [ ] 完成 apis/ 目錄下的 API 文件
  - [ ] 定義端點、參數、回應格式
  - [ ] 設計錯誤處理和狀態碼

### 階段三：實作規劃（必須完成）
- [ ] **任務切分**
  - [ ] 將功能切分為可管理的任務
  - [ ] 定義任務的依賴關係
  - [ ] 估算任務的複雜度和時間

- [ ] **里程碑定義**
  - [ ] 設定明確的里程碑
  - [ ] 定義每個里程碑的驗收條件
  - [ ] 規劃里程碑之間的依賴關係

## 任務切分
- 里程碑 M1：
- 里程碑 M2：
- 里程碑 M3：

## 驗收條件（Definition of Done）
- 功能驗收：
- 文件驗收：
- 效能/穩定性：

## 風險與緩解
- 風險：
- 影響：
- 緩解：

## 相關連結
- 需求單：
- 設計稿：
- 相關 PR：

## ⚠️ 開發注意事項

### 必須遵循的開發順序
1. **先分析後實作**：完成所有分析階段後才能開始實作
2. **先後端後前端**：確保後端 API 完整後再實作前端
3. **先核心後周邊**：先實作核心功能，再處理周邊功能
4. **先單一後複合**：先實作單一功能，再處理複合業務邏輯

### 禁止的開發方式
- ❌ 跳過需求分析直接開始實作
- ❌ 沒有確認後端 API 就開始前端開發
- ❌ 沒有任務管理就開始修改程式碼
- ❌ 沒有使用者案例分析就設計功能
- ❌ 沒有權限分析就實作功能
- ❌ 沒有資料庫分析就設計 API
