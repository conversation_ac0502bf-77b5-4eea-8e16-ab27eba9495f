import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 折舊紀錄介面
export interface Depreciation {
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
    depreciationId: string;
    assetId: string;
    assetNo: string;
    assetName: string;
    depreciationYear: number;
    depreciationMonth: number;
    originalAmount: number;
    accumulatedDepreciation: number;
    currentDepreciation: number;
    depreciationRate: number;
    depreciationMethod: string;
    serviceLifeRemaining: number;
    isAdjustment: boolean;
    adjustmentReason: string;
    notes: string;
    beginningBookValue: number;
    endingBookValue: number;
    depreciationDate: number;
}

// 折舊單介面
export interface DepreciationForm {
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
    depreciationFormId: string;
    depreciationFormNo: string;
    depreciationId: string;
    depreciationDate: number;
    depreciationYear: number;
    depreciationMonth: number;
    status?: string;
    statusName?: string;
    approverId?: string;
    approverName?: string;
    approvalDate?: number;
    approvalComment?: string;
    notes: string;
    depreciationFormDetail?: Depreciation[];
    createUserName?: string;
    updateUserName?: string;
    deleteUserName?: string;
}

// 折舊單統計介面
export interface DepreciationFormStatistics {
    totalCount: number;
    pendingCount: number;
    approvedCount: number;
    rejectedCount: number;
    executedCount: number;
    draftCount: number;
}

// API 回應統計介面 (對應後端實際回應格式)
export interface DepreciationFormStatisticsResponse {
    totalForms: number;
    pendingForms: number;
    approvedForms: number;
    rejectedForms: number;
    executedForms: number;
    draftForms?: number; // 可選，因為 API 回應中沒有此欄位
}

// 審核請求介面
export interface ApprovalRequest {
    isApproved: boolean;
    approverId: string;
    comment: string;
}

// 批次處理請求介面
export interface BatchProcessRequest {
    depreciationFormIds: string[];
    action: string;
    approvalComment?: string;
    operatorId?: string;
}

// 獲取所有折舊單
export async function getDepreciationForms(): Promise<ApiResponse<DepreciationForm[]>> {
    try {
        const response = await httpClient(apiEndpoints.getDepreciationForms, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取折舊單列表失敗",
            data: [],
        };
    }
}

// 新增折舊單
export async function createDepreciationForm(data: Partial<DepreciationForm>): Promise<ApiResponse<string>> {
    try {
        const response = await httpClient(apiEndpoints.addDepreciationForm, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log(JSON.stringify(data));
        console.log(response);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增折舊單失敗",
            data: "",
        };
    }
}

// 更新折舊單
export async function updateDepreciationForm(data: Partial<DepreciationForm>): Promise<ApiResponse<string>> {
    try {
        const response = await httpClient(apiEndpoints.editDepreciationForm, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新折舊單失敗",
            data: "",
        };
    }
}

// 刪除折舊單
export async function deleteDepreciationForm(data: Partial<DepreciationForm>): Promise<ApiResponse<string>> {
    try {
        const response = await httpClient(apiEndpoints.deleteDepreciationForm, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除折舊單失敗",
            data: "",
        };
    }
}

// 獲取折舊單明細
export async function getDepreciationFormDetail(depreciationFormId: string): Promise<ApiResponse<DepreciationForm>> {
    try {
        const response = await httpClient(`${apiEndpoints.getDepreciationFormDetail}/${depreciationFormId}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取折舊單明細失敗",
            data: undefined,
        };
    }
}

// 檢查折舊單使用狀態
export async function checkDepreciationUsage(depreciationFormId: string): Promise<ApiResponse<boolean>> {
    try {
        const response = await httpClient(`${apiEndpoints.checkDepreciationFormUsage}/${depreciationFormId}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "檢查折舊單使用狀態失敗",
            data: false,
        };
    }
}

// 獲取所有折舊單 (包含明細)
export async function getAllDepreciationForms(): Promise<ApiResponse<DepreciationForm[]>> {
    try {
        const response = await httpClient(apiEndpoints.getAllDepreciationForms, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log(response);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取折舊單列表失敗",
            data: [],
        };
    }
}

// 審核折舊單
export async function approveDepreciationForm(
    depreciationFormId: string,
    isApproved: boolean,
    approverId: string,
    comment: string
): Promise<ApiResponse<string>> {
    try {
        const response = await httpClient(`${apiEndpoints.approveDepreciationForm}/${depreciationFormId}?isApproved=${isApproved}&approverId=${approverId}&comment=${encodeURIComponent(comment)}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "審核折舊單失敗",
            data: "",
        };
    }
}

// 執行折舊單
export async function executeDepreciationForm(
    depreciationFormId: string,
    operatorId: string
): Promise<ApiResponse<string>> {
    try {
        const response = await httpClient(`${apiEndpoints.executeDepreciationForm}/${depreciationFormId}?operatorId=${operatorId}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "執行折舊單失敗",
            data: "",
        };
    }
}

// 批次處理折舊單
export async function batchProcessDepreciationForms(data: BatchProcessRequest): Promise<ApiResponse<string>> {
    try {
        const response = await httpClient(apiEndpoints.batchProcessDepreciationForm, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "批次處理折舊單失敗",
            data: "",
        };
    }
}

// 獲取折舊單統計資料
export async function getDepreciationFormStatistics(userId?: string): Promise<ApiResponse<DepreciationFormStatistics>> {
    try {
        const url = userId
            ? `${apiEndpoints.getDepreciationFormStatistics}?userId=${userId}`
            : apiEndpoints.getDepreciationFormStatistics;

        const response = await httpClient(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        }) as ApiResponse<DepreciationFormStatisticsResponse>;

        // 如果 API 回應成功，轉換欄位名稱格式
        if (response.success && response.data) {
            const convertedData: DepreciationFormStatistics = {
                totalCount: response.data.totalForms || 0,
                pendingCount: response.data.pendingForms || 0,
                approvedCount: response.data.approvedForms || 0,
                rejectedCount: response.data.rejectedForms || 0,
                executedCount: response.data.executedForms || 0,
                draftCount: response.data.draftForms || 0,
            };

            return {
                success: true,
                message: response.message,
                data: convertedData,
            } as ApiResponse<DepreciationFormStatistics>;
        }

        // 如果失敗，直接返回錯誤響應
        return {
            success: response.success,
            message: response.message,
            data: {
                totalCount: 0,
                pendingCount: 0,
                approvedCount: 0,
                rejectedCount: 0,
                executedCount: 0,
                draftCount: 0,
            },
        } as ApiResponse<DepreciationFormStatistics>;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取折舊單統計資料失敗",
            data: {
                totalCount: 0,
                pendingCount: 0,
                approvedCount: 0,
                rejectedCount: 0,
                executedCount: 0,
                draftCount: 0,
            },
        };
    }
}
