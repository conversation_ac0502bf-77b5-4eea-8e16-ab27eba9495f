'use client';

import React, { useState, useEffect } from 'react';
import {
    Table,
    Button,
    Modal,
    Form,
    Input,
    InputNumber,
    message,
    Popconfirm,
    Space,
    Typography,
    Row,
    Col,
    Card
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, CalendarOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import {
    AnnualInsuranceRate,
    createEmptyAnnualInsuranceRate,
    getAnnualInsuranceRateList,
    addAnnualInsuranceRate,
    editAnnualInsuranceRate,
    deleteAnnualInsuranceRate,
    getAnnualInsuranceRateByYear
} from '@/services/pas/Insurance/AnnualInsuranceRateService';

const { Title, Text } = Typography;

/**
 * 年度保險費率管理組件
 */
export default function AnnualInsuranceRateManagement() {
    // ========== 表單與狀態管理 ==========
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingRecord, setEditingRecord] = useState<AnnualInsuranceRate | null>(null);
    const [dataSource, setDataSource] = useState<AnnualInsuranceRate[]>([]);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);

    // ========== 資料載入相關 ==========

    /**
     * 載入年度保險費率資料
     */
    const loadData = async () => {
        setLoading(true);
        try {
            const response = await getAnnualInsuranceRateList()
            if (response.success && response.data) {
                setDataSource(response.data);
            } else {
                message.error(response.message || '載入年度保險費率資料失敗');
                setDataSource([]);
            }
        } catch (error: any) {
            message.error('載入資料失敗：' + error.message);
            setDataSource([]);
        } finally {
            setLoading(false);
        }
    };

    /**
     * 組件載入時載入資料
     */
    useEffect(() => {
        loadData();
    }, []);

    // ========== 表格配置 ==========

    /**
     * 表格欄位定義
     */
    const columns: ColumnsType<AnnualInsuranceRate> = [
        {
            title: '年度',
            dataIndex: 'year',
            key: 'year',
            width: 100,
            sorter: (a, b) => a.year - b.year,
            defaultSortOrder: 'descend',
            render: (year: number) => (
                <Space>
                    <CalendarOutlined style={{ color: '#1890ff' }} />
                    <span style={{ fontWeight: 'bold' }}>{year}</span>
                </Space>
            ),
        },
        {
            title: '勞保局',
            children: [
                {
                    title: '普通事故保險費率',
                    dataIndex: ['laborInsurance', 'generalAccidentRate'],
                    key: 'generalAccidentRate',
                    width: 150,
                    render: (rate: number) => `${rate}%`,
                },
                {
                    title: '職業災害費率',
                    dataIndex: ['laborInsurance', 'occupationalDisasterRate'],
                    key: 'occupationalDisasterRate',
                    width: 140,
                    render: (rate: number) => `${rate}%`,
                },
                {
                    title: '就業保險費率',
                    dataIndex: ['laborInsurance', 'employmentInsuranceRate'],
                    key: 'employmentInsuranceRate',
                    width: 120,
                    render: (rate: number) => `${rate}%`,
                },
                {
                    title: '員工負擔比率',
                    dataIndex: ['laborInsurance', 'employeeBurdenRatio'],
                    key: 'laborEmployeeBurdenRatio',
                    width: 120,
                    render: (rate: number) => `${rate}%`,
                },
                {
                    title: '單位負擔比率',
                    dataIndex: ['laborInsurance', 'employerBurdenRatio'],
                    key: 'laborEmployerBurdenRatio',
                    width: 120,
                    render: (rate: number) => `${rate}%`,
                },
            ],
        },
        {
            title: '健保局',
            children: [
                {
                    title: '保費費率',
                    dataIndex: ['healthInsurance', 'premiumRate'],
                    key: 'premiumRate',
                    width: 100,
                    render: (rate: number) => `${rate}%`,
                },
                {
                    title: '員工負擔比率',
                    dataIndex: ['healthInsurance', 'employeeBurdenRatio'],
                    key: 'employeeBurdenRatio',
                    width: 120,
                    render: (rate: number) => `${rate}%`,
                },
                {
                    title: '單位負擔比率',
                    dataIndex: ['healthInsurance', 'employerBurdenRatio'],
                    key: 'employerBurdenRatio',
                    width: 120,
                    render: (rate: number) => `${rate}%`,
                },
                {
                    title: '平均眷口數',
                    dataIndex: ['healthInsurance', 'averageDependents'],
                    key: 'averageDependents',
                    width: 110,
                    render: (count: number) => count.toFixed(2),
                },
            ],
        },
        {
            title: '備註',
            dataIndex: 'remark',
            key: 'remark',
            ellipsis: true,
            render: (text: string) => text || '-',
        },
        {
            title: '操作',
            key: 'action',
            fixed: 'right',
            render: (_, record) => (
                <Space size="small">
                    <Button
                        type="link"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    >
                        編輯
                    </Button>
                    <Popconfirm
                        title="確定要刪除此年度費率設定嗎？"
                        onConfirm={() => setDeleteUid(record.uid)}
                        okText="確定"
                        cancelText="取消"
                    >
                        <Button
                            type="link"
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                        >
                            刪除
                        </Button>
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    // ========== 操作處理函數 ==========

    /**
     * 處理新增費率設定
     */
    const handleAdd = () => {
        setEditingRecord(null);
        form.resetFields();

        // 設定預設值
        const defaultValues = createEmptyAnnualInsuranceRate();
        form.setFieldsValue(defaultValues);

        setModalVisible(true);
    };

    /**
     * 處理編輯費率設定
     */
    const handleEdit = (record: AnnualInsuranceRate) => {
        setEditingRecord(record);
        form.setFieldsValue(record);
        setModalVisible(true);
    };

    /**
     * 處理刪除費率設定
     */
    const handleDelete = async (uid: string): Promise<void> => {
        try {
            const response = await deleteAnnualInsuranceRate(uid);

            if (response.success) {
                message.success('刪除成功');
                await loadData(); // 重新載入資料
            } else {
                message.error(response.data?.message || '刪除失敗');
            }
        } catch (error: any) {
            message.error('刪除失敗：' + error.message);
        }
    };

    /**
     * 處理表單提交
     */
    const handleSubmit = async (): Promise<void> => {
        try {
            const values = await form.validateFields();

            let response;

            if (editingRecord) {
                // 編輯模式
                const updatedData = {
                    ...values,
                    uid: editingRecord.uid,
                };
                response = await editAnnualInsuranceRate(updatedData);
            } else {
                // 新增模式
                response = await addAnnualInsuranceRate(values);
            }

            if (response.data?.success) {
                message.success(editingRecord ? '編輯成功' : '新增成功');
                setModalVisible(false);
                await loadData(); // 重新載入資料
            } else {
                message.error(response.data?.message || (editingRecord ? '編輯失敗' : '新增失敗'));
            }
        } catch (error: any) {
            message.error('操作失敗：' + error.message);
        }
    };

    /**
     * 關閉彈窗
     */
    const handleModalCancel = (): void => {
        setModalVisible(false);
        form.resetFields();
    };

    /**
     * 處理刪除倒計時完成
     */
    const handleDeleteComplete = async (): Promise<void> => {
        if (!deleteUid) return;

        try {
            await handleDelete(deleteUid);
            setDeleteUid(null);
        } catch (error) {
            message.error('刪除失敗，請稍後再試');
            setDeleteUid(null);
        }
    };

    /**
     * 取消刪除操作
     */
    const handleDeleteCancel = (): void => {
        setDeleteUid(null);
    };

    // ========== 渲染組件 ==========

    return (
        <div style={{ marginBottom: '32px' }}>
            {/* 標題與新增按鈕 */}
            <Card
                title={
                    <Space>
                        <CalendarOutlined style={{ color: '#1890ff' }} />
                        <Title level={5} style={{ margin: 0 }}>
                            年度保險費率設定
                        </Title>
                    </Space>
                }
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAdd}
                    >
                        新增年度費率
                    </Button>
                }
                style={{
                    borderRadius: '12px',
                    boxShadow: '0 4px 16px rgba(0, 0, 0, 0.08)'
                }}
            >
                <Text type="secondary" style={{ display: 'block', marginBottom: '16px' }}>
                    設定每年度的勞保、健保費率基準，用於薪資計算、報表計算參考
                </Text>

                {/* 費率設定表格 */}
                <Table
                    columns={columns}
                    dataSource={dataSource}
                    rowKey={(record) => record.uid}
                    loading={loading}
                    scroll={{ x: 1200 }}
                    size="small"
                    rowClassName={(record) => {
                        let className = '';
                        if (record.uid === deleteUid) {
                            className += 'row-deleting-pulse';
                        }
                        return className.trim();
                    }}
                    pagination={{
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total} 筆`,
                        pageSize: 10,
                    }}
                />
            </Card>

            {/* 新增/編輯彈窗 */}
            <Modal
                title={editingRecord ? '編輯年度保險費率' : '新增年度保險費率'}
                open={modalVisible}
                onOk={handleSubmit}
                onCancel={handleModalCancel}
                destroyOnClose={false}
                width={800}
            >
                <Form
                    form={form}
                    layout="vertical"
                    preserve={false}
                >
                    {/* 年度設定 */}
                    <Row gutter={16}>
                        <Col span={24}>
                            <Form.Item
                                name="year"
                                label="年度"
                                rules={[
                                    { required: true, message: '請輸入年度' },
                                    { type: 'number', min: 1990, max: 2050, message: '年度必須在1990-2050之間' }
                                ]}
                            >
                                <InputNumber
                                    style={{ width: '100%' }}
                                    placeholder={`請輸入年度 (例：${new Date().getFullYear()})`}
                                    step={1}
                                    precision={0}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    {/* 勞保局費率設定 */}
                    <div style={{
                        marginBottom: '24px',
                        padding: '16px',
                        backgroundColor: '#f6f8fa',
                        borderRadius: '8px',
                        border: '1px solid #e1e4e8'
                    }}>
                        <Title level={5} style={{ margin: '0 0 16px 0', color: '#1890ff' }}>
                            勞保局費率設定
                        </Title>
                        <Row gutter={16}>
                            <Col span={12}>
                                <Form.Item
                                    name={['laborInsurance', 'generalAccidentRate']}
                                    label="普通事故保險費率 (%)"
                                    rules={[
                                        { required: true, message: '請輸入普通事故保險費率' },
                                        { type: 'number', min: 0, max: 100, message: '費率必須在0-100之間' }
                                    ]}
                                >
                                    <InputNumber
                                        style={{ width: '100%' }}
                                        placeholder="例：11.5"
                                        step={0.1}
                                        precision={2}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    name={['laborInsurance', 'occupationalDisasterRate']}
                                    label="適用職業災害費率 (%)"
                                    rules={[
                                        { required: true, message: '請輸入職業災害費率' },
                                        { type: 'number', min: 0, max: 100, message: '費率必須在0-100之間' }
                                    ]}
                                >
                                    <InputNumber
                                        style={{ width: '100%' }}
                                        placeholder="例：0.21"
                                        step={0.01}
                                        precision={3}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row gutter={16}>
                            <Col span={12}>
                                <Form.Item
                                    name={['laborInsurance', 'employmentInsuranceRate']}
                                    label="就業保險費率 (%)"
                                    rules={[
                                        { required: true, message: '請輸入就業保險費率' },
                                        { type: 'number', min: 0, max: 100, message: '費率必須在0-100之間' }
                                    ]}
                                >
                                    <InputNumber
                                        style={{ width: '100%' }}
                                        placeholder="例：1.0"
                                        step={0.1}
                                        precision={2}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                {/* 空白欄位，保持版面平衡 */}
                            </Col>
                        </Row>
                        <Row gutter={16}>
                            <Col span={12}>
                                <Form.Item
                                    name={['laborInsurance', 'employeeBurdenRatio']}
                                    label="員工負擔比率 (%)"
                                    rules={[
                                        { required: true, message: '請輸入員工負擔比率' },
                                        { type: 'number', min: 0, max: 100, message: '比率必須在0-100之間' }
                                    ]}
                                >
                                    <InputNumber
                                        style={{ width: '100%' }}
                                        placeholder="例：20"
                                        step={1}
                                        precision={0}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    name={['laborInsurance', 'employerBurdenRatio']}
                                    label="投保單位負擔比率 (%)"
                                    rules={[
                                        { required: true, message: '請輸入投保單位負擔比率' },
                                        { type: 'number', min: 0, max: 100, message: '比率必須在0-100之間' }
                                    ]}
                                >
                                    <InputNumber
                                        style={{ width: '100%' }}
                                        placeholder="例：70"
                                        step={1}
                                        precision={0}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>

                    </div>

                    {/* 健保局費率設定 */}
                    <div style={{
                        marginBottom: '24px',
                        padding: '16px',
                        backgroundColor: '#f0f9ff',
                        borderRadius: '8px',
                        border: '1px solid #bae6fd'
                    }}>
                        <Title level={5} style={{ margin: '0 0 16px 0', color: '#0ea5e9' }}>
                            健保局費率設定
                        </Title>
                        <Row gutter={16}>
                            <Col span={12}>
                                <Form.Item
                                    name={['healthInsurance', 'premiumRate']}
                                    label="保費費率 (%)"
                                    rules={[
                                        { required: true, message: '請輸入保費費率' },
                                        { type: 'number', min: 0, max: 100, message: '費率必須在0-100之間' }
                                    ]}
                                >
                                    <InputNumber
                                        style={{ width: '100%' }}
                                        placeholder="例：5.17"
                                        step={0.01}
                                        precision={2}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    name={['healthInsurance', 'averageDependents']}
                                    label="平均眷口數"
                                    rules={[
                                        { required: true, message: '請輸入平均眷口數' },
                                        { type: 'number', min: 0, max: 10, message: '眷口數必須在0-10之間' }
                                    ]}
                                >
                                    <InputNumber
                                        style={{ width: '100%' }}
                                        placeholder="例：0.62"
                                        step={0.01}
                                        precision={2}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row gutter={16}>
                            <Col span={12}>
                                <Form.Item
                                    name={['healthInsurance', 'employeeBurdenRatio']}
                                    label="員工負擔比率 (%)"
                                    rules={[
                                        { required: true, message: '請輸入員工負擔比率' },
                                        { type: 'number', min: 0, max: 100, message: '比率必須在0-100之間' }
                                    ]}
                                >
                                    <InputNumber
                                        style={{ width: '100%' }}
                                        placeholder="例：30"
                                        step={1}
                                        precision={0}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    name={['healthInsurance', 'employerBurdenRatio']}
                                    label="投保單位負擔比率 (%)"
                                    rules={[
                                        { required: true, message: '請輸入投保單位負擔比率' },
                                        { type: 'number', min: 0, max: 100, message: '比率必須在0-100之間' }
                                    ]}
                                >
                                    <InputNumber
                                        style={{ width: '100%' }}
                                        placeholder="例：60"
                                        step={1}
                                        precision={0}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 備註 */}
                    <Form.Item
                        name="remark"
                        label="備註"
                    >
                        <Input.TextArea
                            rows={3}
                            placeholder="請輸入備註"
                            maxLength={200}
                            showCount
                        />
                    </Form.Item>
                </Form>
            </Modal>

            {/* 刪除倒計時組件 */}
            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={handleDeleteComplete}
                    onCancel={handleDeleteCancel}
                />
            )}
        </div>
    );
}
