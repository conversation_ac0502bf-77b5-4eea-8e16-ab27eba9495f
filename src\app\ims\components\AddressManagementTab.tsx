"use client";

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Modal, 
  Form, 
  Input, 
  message, 
  Popconfirm, 
  Tag, 
  Tooltip, 
  InputNumber,
  Row,
  Col 
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  HomeOutlined,
  DragOutlined,
  StarOutlined,
  StarFilled 
} from '@ant-design/icons';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { PartnerAddress } from '@/services/ims/partner';
import { logger, SYMBOLS, createContextLogger } from '@/utils/logger';

// 創建上下文日誌器
const addressManagementLogger = createContextLogger({ module: 'AddressManagementTab' });

interface AddressManagementTabProps {
  partnerID?: string;
  addresses: PartnerAddress[];
  onChange: (addresses: PartnerAddress[]) => void;
  readonly?: boolean;
}

// 可排序的表格行元件
const SortableRow = ({ children, ...props }: any) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props['data-row-key'],
  });

  const style = {
    ...props.style,
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <tr {...props} ref={setNodeRef} style={style} {...attributes}>
      {React.Children.map(children, (child) => {
        if (child.key === 'sort') {
          return React.cloneElement(child, {
            children: (
              <div
                {...listeners}
                style={{
                  cursor: 'grab',
                  color: '#999',
                }}
              >
                <DragOutlined />
              </div>
            ),
          });
        }
        return child;
      })}
    </tr>
  );
};

const AddressManagementTab: React.FC<AddressManagementTabProps> = ({
  partnerID,
  addresses = [],
  onChange,
  readonly = false,
}) => {
  const [localAddresses, setLocalAddresses] = useState<PartnerAddress[]>(addresses);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAddress, setEditingAddress] = useState<PartnerAddress | null>(null);
  const [form] = Form.useForm();

  // 拖拽感應器
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    setLocalAddresses(addresses);
  }, [addresses]);

  // 處理拖拽結束
  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    
    if (!over || active.id === over.id) {
      return;
    }

    const oldIndex = localAddresses.findIndex(item => item.partnerAddressID === active.id);
    const newIndex = localAddresses.findIndex(item => item.partnerAddressID === over.id);

    if (oldIndex !== -1 && newIndex !== -1) {
      const newAddresses = arrayMove(localAddresses, oldIndex, newIndex);
      
      // 重新分配 Priority（基於新順序）
      const reorderedAddresses = newAddresses.map((address, index) => ({
        ...address,
        priority: index,
      }));

      setLocalAddresses(reorderedAddresses);
      onChange(reorderedAddresses);
      
      addressManagementLogger.log(SYMBOLS.SUCCESS, '地址順序已更新', { 
        from: oldIndex, 
        to: newIndex 
      });
    }
  };

  // 設為主要地址
  const setPrimaryAddress = (targetAddressId: string) => {
    const updatedAddresses = localAddresses.map(address => ({
      ...address,
      priority: address.partnerAddressID === targetAddressId ? 0 : 
                (address.priority === 0 ? 99 : address.priority)
    }));

    setLocalAddresses(updatedAddresses);
    onChange(updatedAddresses);
    message.success('主要地址已更新');
    
    addressManagementLogger.log(SYMBOLS.SUCCESS, '主要地址已設定', { 
      addressId: targetAddressId 
    });
  };

  // 開啟新增/編輯對話框
  const openModal = (address?: PartnerAddress) => {
    setEditingAddress(address || null);
    setIsModalVisible(true);
    
    if (address) {
      form.setFieldsValue(address);
    } else {
      form.resetFields();
      form.setFieldsValue({
        partnerID: partnerID,
        priority: 99,
        country: 'TW',
      });
    }
  };

  // 儲存地址
  const handleSave = async (values: any) => {
    try {
      const addressData: PartnerAddress = {
        ...values,
        partnerAddressID: editingAddress?.partnerAddressID || `temp-${Date.now()}`,
        partnerID: partnerID || values.partnerID,
        createTime: editingAddress?.createTime || Date.now(),
        createUserId: editingAddress?.createUserId || null,
        updateTime: Date.now(),
        updateUserId: null,
        deleteTime: null,
        deleteUserId: null,
        isDeleted: false,
      };

      let updatedAddresses: PartnerAddress[];
      
      if (editingAddress) {
        // 編輯現有地址
        updatedAddresses = localAddresses.map(addr => 
          addr.partnerAddressID === editingAddress.partnerAddressID ? addressData : addr
        );
      } else {
        // 新增地址
        updatedAddresses = [...localAddresses, addressData];
      }

      setLocalAddresses(updatedAddresses);
      onChange(updatedAddresses);
      setIsModalVisible(false);
      message.success(editingAddress ? '地址已更新' : '地址已新增');
      
      addressManagementLogger.log(SYMBOLS.SUCCESS, editingAddress ? '地址已更新' : '地址已新增', { 
        addressId: addressData.partnerAddressID 
      });
    } catch (error) {
      addressManagementLogger.log(SYMBOLS.ERROR, '儲存地址失敗', error);
      message.error('儲存失敗，請重試');
    }
  };

  // 刪除地址
  const handleDelete = (addressId: string) => {
    const updatedAddresses = localAddresses.filter(addr => addr.partnerAddressID !== addressId);
    setLocalAddresses(updatedAddresses);
    onChange(updatedAddresses);
    message.success('地址已刪除');
    
    addressManagementLogger.log(SYMBOLS.SUCCESS, '地址已刪除', { addressId });
  };

  // 表格欄位定義
  const columns = [
    {
      key: 'sort',
      width: 50,
      render: () => null, // 由 SortableRow 處理
    },
    {
      title: '優先序',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: number, record: PartnerAddress) => (
        <Space>
          {priority === 0 ? (
            <Tag color="gold" icon={<StarFilled />}>主要</Tag>
          ) : (
            <Tag>{priority}</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '地址資訊',
      key: 'addressInfo',
      render: (record: PartnerAddress) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>
            {record.city}{record.district}
          </div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {record.address}
          </div>
          {record.postalCode && (
            <div style={{ color: '#999', fontSize: '11px' }}>
              郵遞區號: {record.postalCode}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (record: PartnerAddress) => (
        <Space>
          {record.priority !== 0 && (
            <Tooltip title="設為主要地址">
              <Button 
                type="text" 
                size="small" 
                icon={<StarOutlined />}
                onClick={() => setPrimaryAddress(record.partnerAddressID)}
                disabled={readonly}
              />
            </Tooltip>
          )}
          <Button 
            type="text" 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => openModal(record)}
            disabled={readonly}
          />
          <Popconfirm
            title="確定要刪除這個地址嗎？"
            onConfirm={() => handleDelete(record.partnerAddressID)}
            disabled={readonly}
          >
            <Button 
              type="text" 
              size="small" 
              danger 
              icon={<DeleteOutlined />}
              disabled={readonly}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title={
        <Space>
          <HomeOutlined />
          <span>地址管理</span>
          <Tag color="blue">{localAddresses.length} 個地址</Tag>
        </Space>
      }
      extra={
        !readonly && (
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => openModal()}
          >
            新增地址
          </Button>
        )
      }
      size="small"
    >
      <DndContext 
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext 
          items={localAddresses.map(addr => addr.partnerAddressID)}
          strategy={verticalListSortingStrategy}
        >
          <Table
            components={{
              body: {
                row: SortableRow,
              },
            }}
            rowKey="partnerAddressID"
            columns={columns}
            dataSource={localAddresses.sort((a, b) => a.priority - b.priority)}
            pagination={false}
            size="small"
            locale={{ emptyText: '尚未新增任何地址' }}
          />
        </SortableContext>
      </DndContext>

      {/* 新增/編輯地址對話框 */}
      <Modal
        title={editingAddress ? '編輯地址' : '新增地址'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="國家/地區"
                name="country"
                rules={[{ required: true, message: '請輸入國家/地區' }]}
              >
                <Input placeholder="例如：TW" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="優先序"
                name="priority"
                rules={[{ required: true, message: '請輸入優先序' }]}
                tooltip="數字越小優先級越高，0 代表主要地址"
              >
                <InputNumber 
                  min={0} 
                  max={999} 
                  style={{ width: '100%' }}
                  placeholder="預設 99"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="縣市"
                name="city"
                rules={[{ required: true, message: '請輸入縣市' }]}
              >
                <Input placeholder="例如：台北市" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="鄉鎮市區"
                name="district"
              >
                <Input placeholder="例如：中正區" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="詳細地址"
                name="address"
                rules={[{ required: true, message: '請輸入詳細地址' }]}
              >
                <Input.TextArea 
                  rows={2}
                  placeholder="請輸入詳細地址"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="郵遞區號"
                name="postalCode"
              >
                <Input placeholder="例如：100" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </Card>
  );
};

export default AddressManagementTab;
