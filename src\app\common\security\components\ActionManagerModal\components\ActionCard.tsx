import React from 'react';
import { Card, Button, Input, Space, Tag } from 'antd';
import { EditOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons';
import { createContextLogger, SYMBOLS } from '@/utils/logger';
import { ActionCardProps } from '../types/actionManager.types';

// 遵循 FastERP 日誌記錄策略
const actionCardLogger = createContextLogger({ module: 'ActionCard' });

const ActionCard: React.FC<ActionCardProps> = ({
  action,
  isEditing,
  editValues,
  onStartEdit,
  onSaveEdit,
  onCancelEdit,
  onUpdateEditValues
}) => {
  const handleStartEdit = () => {
    actionCardLogger.log(SYMBOLS.DEBUG, '開始編輯動作', { actionId: action.granularPermissionActionID });
    onStartEdit();
  };

  const handleSaveEdit = () => {
    actionCardLogger.log(SYMBOLS.DEBUG, '儲存動作編輯', { actionId: action.granularPermissionActionID });
    onSaveEdit();
  };

  const handleCancelEdit = () => {
    actionCardLogger.log(SYMBOLS.DEBUG, '取消動作編輯', { actionId: action.granularPermissionActionID });
    onCancelEdit();
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onUpdateEditValues({ ...editValues, name: e.target.value });
  };

  return (
    <Card
      size="small"
      style={{
        border: isEditing ? '2px solid #52c41a' : '1px solid #f0f0f0',
        background: isEditing ? '#f6ffed' : '#fff',
        transition: 'all 0.3s'
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
        <Tag color="purple" style={{ fontFamily: 'monospace', fontSize: '11px' }}>
          {action.code}
        </Tag>
      </div>
      
      {isEditing ? (
        <div>
          <Input
            size="small"
            value={editValues.name}
            onChange={handleNameChange}
            style={{ width: '100%', marginBottom: '8px' }}
            onPressEnter={handleSaveEdit}
            placeholder="輸入動作名稱"
            autoFocus
          />
          <Space size="small" style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Button
              size="small"
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSaveEdit}
            >
              儲存
            </Button>
            <Button
              size="small"
              icon={<CloseOutlined />}
              onClick={handleCancelEdit}
            >
              取消
            </Button>
          </Space>
        </div>
      ) : (
        <div>
          <div style={{ 
            fontSize: '14px', 
            fontWeight: '500',
            marginBottom: '4px',
            color: '#333'
          }}>
            {action.name}
          </div>
          <div style={{ 
            fontSize: '11px', 
            color: '#666',
            marginBottom: '8px'
          }}>
            排序：{action.sortCode}
          </div>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={handleStartEdit}
            style={{ padding: 0, height: 'auto' }}
          >
            編輯名稱
          </Button>
        </div>
      )}
    </Card>
  );
};

export default ActionCard;
