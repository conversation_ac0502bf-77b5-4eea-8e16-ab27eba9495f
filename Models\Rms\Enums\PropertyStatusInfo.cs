using System.Collections.Generic;
using System.Linq;

namespace FAST_ERP_Backend.Models.Rms.Enums
{
    /// <summary>
    /// 房源狀態（物件化定義）：空置(1)、出租(2)、維修(3)、停用(4)
    /// </summary>
    public sealed class PropertyStatusInfo
    {
        public string Code { get; }
        public string Name { get; }

        private PropertyStatusInfo(string code, string name)
        {
            Code = code;
            Name = name;
        }

        public static readonly PropertyStatusInfo Vacant = new("1", "空置");
        public static readonly PropertyStatusInfo Rented = new("2", "出租");
        public static readonly PropertyStatusInfo Maintenance = new("3", "維修");
        public static readonly PropertyStatusInfo Disabled = new("4", "停用");

        public static IEnumerable<PropertyStatusInfo> All => new[] { Vacant, Rented, Maintenance, Disabled };

        public static PropertyStatusInfo FromCode(string code) => All.First(x => x.Code == code);
    }
}


