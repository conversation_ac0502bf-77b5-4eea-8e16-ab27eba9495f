using System.Collections.Generic;
using System.Linq;

namespace FAST_ERP_Backend.Models.Rms.Enums
{
    /// <summary>
    /// 房型類別（物件化定義）：辦公(1)、住宅(2)、店面(3)、倉庫(4)、其他(5)
    /// </summary>
    public sealed class PropertyTypeInfo
    {
        public string Code { get; }
        public string Name { get; }

        private PropertyTypeInfo(string code, string name)
        {
            Code = code;
            Name = name;
        }

        public static readonly PropertyTypeInfo Office = new("1", "辦公");
        public static readonly PropertyTypeInfo Residential = new("2", "住宅");
        public static readonly PropertyTypeInfo Store = new("3", "店面");
        public static readonly PropertyTypeInfo Warehouse = new("4", "倉庫");
        public static readonly PropertyTypeInfo Other = new("5", "其他");

        public static IEnumerable<PropertyTypeInfo> All => new[] { Office, Residential, Store, Warehouse, Other };

        public static PropertyTypeInfo FromCode(string code) => All.First(x => x.Code == code);
    }
}


