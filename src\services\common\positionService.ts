import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 職稱/職務介面
export interface Position {
    positionId: string;
    name: string;
    sortCode: number;
    createTime: number;
    createUserId: string;
    updateTime?: number;
    updateUserId?: string;
    deleteTime?: number;
    deleteUserId?: string;
    isDeleted: boolean;
}

// 職稱負責人介面
export interface PositionOwner {
    positionOwnerId: string;
    positionId: string;
    positionName: string;
    userId: string;
    userName: string;
    roleType: string; // "主辦" | "協辦"
    orderNo: number;
    departmentName?: string | null; // 部門名稱
    serviceDepartmentName?: string | null;
    eMail?: string | null;
    altPhone?: string | null;
    startDate?: number;
    endDate?: number;
    updateTime?: number;
    updateUserId?: string;
}

// API 回傳的負責人簡化介面
export interface OwnerInfo {
    userId: string;
    userName: string;
    roleType: string;
    orderNo: number;
    positionOwnerId?: string; // 可選的 positionOwnerId，用於刪除操作
    departmentName?: string | null; // 部門名稱
    serviceDepartmentName?: string | null;
    eMail?: string | null;
    altPhone?: string | null;
    startDate?: string | null; // 改為字串格式 "2025/09/17"
    endDate?: string | null;   // 改為字串格式 "2025/09/17" 或 null
    status?: string; // 新增狀態欄位 "接任業務" | "卸任業務"
    updateTime?: number;
    updateUserId?: string;
}

// 業務負責人總表項目 (根據實際 API 回應)
export interface PositionOwnerSummary {
    positionId: string;
    positionName: string;
    owners: OwnerInfo[];  // 所有負責人員（包含主辦和協辦）
}

// 獲取所有職稱列表
export async function getPositions(): Promise<ApiResponse<Position[]>> {
    try {
        const response = await httpClient(apiEndpoints.getPositions, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取職稱列表失敗",
            data: [],
        };
    }
}

// 獲取單一職稱詳細資料
export async function getPositionById(id: string): Promise<ApiResponse<Position>> {
    try {
        const response = await httpClient(`${apiEndpoints.getPositionDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取職稱詳細資料失敗",
            data: {} as Position,
        };
    }
}

// 新增職稱
export async function addPosition(data: Position): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.addPosition, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增職稱失敗",
            data: null,
        };
    }
}

// 編輯職稱
export async function editPosition(data: Position): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.editPosition, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯職稱失敗",
            data: null,
        };
    }
}

// 刪除職稱
export async function deletePosition(data: Position): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.deletePosition, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除職稱失敗",
            data: null,
        };
    }
}

// 獲取業務負責人總表
export async function getPositionOwnersSummary(
    positionId?: string,
    startDate?: string,
    endDate?: string
): Promise<ApiResponse<PositionOwnerSummary[]>> {
    try {
        let url = apiEndpoints.getPositionOwnersSummary;
        const params = new URLSearchParams();
        if (positionId) params.append('positionId', positionId);
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);

        if (params.toString()) {
            url += `?${params.toString()}`;
        }

        const response = await httpClient(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取業務負責人總表失敗",
            data: [],
        };
    }
}

// 獲取職稱負責人列表
export async function getPositionOwners(positionId?: string, roleType?: string): Promise<ApiResponse<PositionOwner[]>> {
    try {
        const params = new URLSearchParams();
        if (positionId) params.append('positionId', positionId);
        if (roleType) params.append('roleType', roleType);

        const url = `${apiEndpoints.getPositionOwners}${params.toString() ? '?' + params.toString() : ''}`;

        const response = await httpClient(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取職稱負責人列表失敗",
            data: [],
        };
    }
}

// 新增職稱負責人
export async function addPositionOwner(data: PositionOwner): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.addPositionOwner, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增職稱負責人失敗",
            data: null,
        };
    }
}

// 編輯職稱負責人
export async function editPositionOwner(data: PositionOwner): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.editPositionOwner, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯職稱負責人失敗",
            data: null,
        };
    }
}

// 刪除職稱負責人（軟刪除）
export async function deletePositionOwner(data: PositionOwner): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.deletePositionOwner, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除職稱負責人失敗",
            data: null,
        };
    }
}

// 卸任職稱負責人（設定結束日期）
export async function resignPositionOwner(data: PositionOwner): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.resignPositionOwner, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "卸任職稱負責人失敗",
            data: null,
        };
    }
}
