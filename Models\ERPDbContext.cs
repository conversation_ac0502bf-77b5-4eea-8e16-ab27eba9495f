﻿using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models.Sms;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Models.Ims;
using FAST_ERP_Backend.Models.Rms;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Services.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection.Emit;
using System.Threading;
using System.Threading.Tasks;
using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Models
{
    public class ERPDbContext : DbContext
    {
        private readonly ILoggerService? _logger;
        private readonly EnhancedEntityChangeTracker _changeTracker;
        private readonly ILogDataProcessor _dataProcessor;

        public ERPDbContext(DbContextOptions<ERPDbContext> options,
                           ILoggerService? logger = null,
                           EnhancedEntityChangeTracker? changeTracker = null,
                           ILogDataProcessor? dataProcessor = null)
            : base(options)
        {
            _logger = logger;
            _changeTracker = changeTracker ?? new EnhancedEntityChangeTracker(
                Microsoft.Extensions.Logging.Abstractions.NullLogger<EnhancedEntityChangeTracker>.Instance);
            _dataProcessor = dataProcessor ?? new LogDataProcessor(
                Microsoft.Extensions.Logging.Abstractions.NullLogger<LogDataProcessor>.Instance);
        }
        #region DbSet
        //Common
        public DbSet<Users> Common_Users { get; set; }
        public DbSet<Department> Common_Departments { get; set; }
        public DbSet<Roles> Common_Roles { get; set; }
        public DbSet<RolesPermissions> Common_RolesPermissions { get; set; }

        public DbSet<AuditLogs> Common_AuditLogs { get; set; }
        public DbSet<SystemMenu> Common_SystemMenu { get; set; }
        public DbSet<SystemGroups> Common_SystemGroups { get; set; }
        public DbSet<EnterpriseGroups> Common_EnterpriseGroups { get; set; }
        public DbSet<Position> Common_Positions { get; set; }
        public DbSet<Division> Common_Divisions { get; set; }
        public DbSet<Unit> Common_Units { get; set; }
        public DbSet<City> Common_Cities { get; set; }
        public DbSet<District> Common_Districts { get; set; }

        public DbSet<EnterpriseImage> Common_EnterpriseImage { get; set; }

        // Granular Permission System
        public DbSet<GranularPermission> Common_GranularPermissions { get; set; }
        public DbSet<GranularPermissionAction> Common_GranularPermissionActions { get; set; }
        public DbSet<RoleGranularPermission> Common_RoleGranularPermissions { get; set; }

        public DbSet<SystemParameters> Common_SystemParameters { get; set; }
        public DbSet<SystemParametersItem> Common_SystemParametersItem { get; set; }
        public DbSet<FileList> Common_FileList { get; set; }
        public DbSet<FileUpload> Common_FileUploads { get; set; }



        public DbSet<ReportSignatureTemplate> Common_ReportSignatureTemplates { get; set; }
        public DbSet<ReportSignatureTemplateDetail> Common_ReportSignatureTemplateDetails { get; set; }

        //PMS
        public DbSet<Asset> Pms_Assets { get; set; }
        public DbSet<InsuranceUnit> Pms_InsuranceUnits { get; set; }
        public DbSet<Manufacturer> Pms_Manufacturers { get; set; }
        public DbSet<StorageLocation> Pms_StorageLocations { get; set; }
        public DbSet<DepreciationForm> Pms_DepreciationForms { get; set; }
        public DbSet<DepreciationFormDetail> Pms_DepreciationFormDetail { get; set; }
        public DbSet<AssetAccount> Pms_AssetAccounts { get; set; }
        public DbSet<AssetSubAccount> Pms_AssetSubAccounts { get; set; }
        public DbSet<AssetSource> Pms_AssetSources { get; set; }
        public DbSet<AssetCategory> Pms_AssetCategory { get; set; }
        public DbSet<AccessoryEquipment> Pms_AccessoryEquipments { get; set; }
        public DbSet<PmsSystemParameter> Pms_SystemParameters { get; set; }

        public DbSet<AmortizationSource> Pms_AmortizationSources { get; set; }

        public DbSet<AssetStatus> Pms_AssetStatus { get; set; }

        public DbSet<PmsUserRole> Pms_UserRoles { get; set; }
        public DbSet<PmsUserRoleMapping> Pms_UserRoleMappings { get; set; }

        public DbSet<EquipmentType> Pms_EquipmentType { get; set; }
        public DbSet<AssetCarryOut> Pms_AssetCarryOut { get; set; }
        public DbSet<VendorMaintenance> Pms_VendorMaintenance { get; set; }
        public DbSet<AssetLocationTransfer> Pms_AssetLocationTransfer { get; set; }
        public DbSet<AssetLocationTransferDetail> Pms_AssetLocationTransferDetail { get; set; }

        public DbSet<AssetScrapForm> Pms_AssetScrapForms { get; set; }
        public DbSet<AssetScrapFormDetail> Pms_AssetScrapFormDetails { get; set; }

        // Inventory
        public DbSet<AssetInventoryForm> Pms_AssetInventoryForms { get; set; }
        public DbSet<AssetInventoryCheckDetail> Pms_AssetInventoryCheckDetails { get; set; }

        //Pas
        public DbSet<Employee> Pas_Employee { get; set; }
        public DbSet<Education> Pas_Education { get; set; }
        public DbSet<Train> Pas_Train { get; set; }
        public DbSet<Examination> Pas_Examination { get; set; }
        public DbSet<Certification> Pas_Certification { get; set; }
        public DbSet<Undergo> Pas_Undergo { get; set; }
        public DbSet<Ensure> Pas_Ensure { get; set; }
        public DbSet<Suspend> Pas_Suspend { get; set; }
        public DbSet<Salary> Pas_Salary { get; set; }
        public DbSet<Hensure> Pas_Hensure { get; set; }
        public DbSet<Dependent> Pas_Dependent { get; set; }
        public DbSet<PerformancePointGroup> Pas_PerformancePointGroup { get; set; }
        public DbSet<PerformancePointType> Pas_PerformancePointType { get; set; }
        public DbSet<PerformancePointRecord> Pas_PerformancePointRecord { get; set; }
        public DbSet<RegularSalaryItem> Pas_RegularSalaryItem { get; set; }
        public DbSet<EmployeeRegularSalary> Pas_EmployeeRegularSalary { get; set; }
        public DbSet<SalaryPoint> Pas_SalaryPoint { get; set; }
        public DbSet<DepartmentSalaryPoint> Pas_DepartmentSalaryPoint { get; set; }
        public DbSet<InsuranceGrade> Pas_InsuranceGrade { get; set; }
        public DbSet<InsuranceHistory> Pas_InsuranceHistory { get; set; }
        public DbSet<Promotion> Pas_Promotion { get; set; }
        public DbSet<ExpenseDepartmentChange> Pas_ExpenseDepartmentChange { get; set; }
        public DbSet<ServiceDepartmentChange> Pas_ServiceDepartmentChange { get; set; }
        public DbSet<TaxRateSetting> Pas_TaxRateSetting { get; set; }
        public DbSet<ProgressiveTaxRate> Pas_ProgressiveTaxRate { get; set; }
        public DbSet<MonthlySalary> Pas_MonthlySalary { get; set; }
        public DbSet<SalaryAdjustment> Pas_SalaryAdjustment { get; set; }
        public DbSet<HensureInsurance> Pas_HensureInsurance { get; set; }
        public DbSet<Bonus> Pas_Bonus { get; set; }
        public DbSet<BackPay> Pas_BackPay { get; set; }

        //Ims
        /// <summary> 商業夥伴 </summary>
        public DbSet<Partner> Ims_Partner { get; set; }
        /// <summary> 自然人 </summary>
        public DbSet<IndividualDetail> Ims_IndividualDetail { get; set; }
        /// <summary> 法人 </summary>
        public DbSet<EnterpriseDetail> Ims_EnterpriseDetail { get; set; }
        /// <summary> 客戶詳細資訊 </summary>
        public DbSet<CustomerDetail> Ims_CustomerDetail { get; set; }
        /// <summary> 供應商詳細資訊 </summary>
        public DbSet<SupplierDetail> Ims_SupplierDetail { get; set; }
        /// <summary> 商業夥伴聯絡人聯結表 </summary>
        public DbSet<PartnerContact> Ims_PartnerContact { get; set; }
        /// <summary> 聯絡人 </summary>
        public DbSet<Contact> Ims_Contact { get; set; }
        /// <summary> 商業夥伴地址 </summary>
        public DbSet<PartnerAddress> Ims_PartnerAddress { get; set; }
        /// <summary> 庫存品 </summary>
        public DbSet<Item> Ims_Item { get; set; }
        /// <summary> 庫存品分類 </summary>
        public DbSet<ItemCategory> Ims_ItemCategory { get; set; }
        /// <summary> 主分類 </summary>
        public DbSet<ItemPrice> Ims_ItemPrice { get; set; }
        /// <summary> 價格類型 </summary>
        public DbSet<PriceType> Ims_PriceType { get; set; }
        /// <summary> 價格歷史記錄 </summary>
        public DbSet<PriceHistory> Ims_PriceHistory { get; set; }
        /// <summary> 倉庫 </summary>
        public DbSet<Warehouse> Ims_Warehouse { get; set; }
        /// <summary> 倉庫別商品價格 </summary>
        public DbSet<WarehouseItemPrice> Ims_WarehouseItemPrice { get; set; }
        /// <summary> 客戶分類 </summary>
        public DbSet<CustomerCategory> Ims_CustomerCategory { get; set; }
        /// <summary> 供應商分類 </summary>
        public DbSet<SupplierCategory> Ims_SupplierCategory { get; set; }

        //Rms
        public DbSet<Property> Rms_Properties { get; set; }
        public DbSet<Tenant> Rms_Tenants { get; set; }
        public DbSet<Contract> Rms_Contracts { get; set; }
        public DbSet<Fee> Rms_Fees { get; set; }
        public DbSet<Payment> Rms_Payments { get; set; }
        public DbSet<PaymentAllocation> Rms_PaymentAllocations { get; set; }

        //Sms
        public DbSet<SmsWebServer> Sms_WebServers { get; set; }
        public DbSet<SmsDbServer> Sms_DbServers { get; set; }
        public DbSet<SmsSite> Sms_Sites { get; set; }

        #endregion

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            new DbSeederData().SeedData(modelBuilder);

            //資料表關聯設定
            modelBuilder.ConfigureSystemMenuRelationships();



            // Granular Permission System 關聯設定
            ConfigureGranularPermissionRelationships(modelBuilder);

            // Entity Framework Core 生成 SQL 查詢時，會自動將過濾條件加入到 WHERE 子句中
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(ModelBaseEntity).IsAssignableFrom(entityType.ClrType))
                {
                    var parameter = Expression.Parameter(entityType.ClrType, "e");
                    var property = Expression.Property(parameter, nameof(ModelBaseEntity.IsDeleted));
                    var filter = Expression.Lambda(
                        Expression.Equal(
                            Expression.Convert(property, typeof(bool?)),
                            Expression.Constant(false, typeof(bool?))),
                        parameter);

                    modelBuilder.Entity(entityType.ClrType).HasQueryFilter(filter);
                }
            }

            // PmsUserRole 配置
            modelBuilder.Entity<PmsUserRole>()
                .HasQueryFilter(e => !e.IsDeleted);

            // PmsUserRoleMapping 配置
            modelBuilder.Entity<PmsUserRoleMapping>()
                .HasQueryFilter(e => !e.IsDeleted);

            // PmsUserRoleMapping 與 Users 關聯
            modelBuilder.Entity<PmsUserRoleMapping>()
                .HasOne(m => m.User)
                .WithMany()
                .HasForeignKey(m => m.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // PmsUserRoleMapping 與 PmsUserRole 關聯
            modelBuilder.Entity<PmsUserRoleMapping>()
                .HasOne(m => m.PmsUserRole)
                .WithMany()
                .HasForeignKey(m => m.PmsUserRoleId)
                .OnDelete(DeleteBehavior.Restrict);

            #region Ims
            modelBuilder.Entity<ItemPrice>(Entity =>
            {
                Entity
                .HasOne(ip => ip.Item)
                .WithMany(ip => ip.Prices)
                .HasForeignKey(i => i.ItemID)
                .OnDelete(DeleteBehavior.Restrict);
                Entity
                .HasOne(ip => ip.PriceType)
                .WithMany()
                .HasForeignKey(ip => ip.PriceTypeID)
                .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<Item>(Entity =>
            {
                Entity
                .HasOne(i => i.ItemCategory)
                .WithMany(ic => ic.Items)
                .HasForeignKey(i => i.ItemCategoryID)
                .OnDelete(DeleteBehavior.Restrict);
            });

            // Ims 模組的 Partner 相關配置
            modelBuilder.Entity<Partner>(entity =>
            {
                // Partner 與 PartnerAddress 的一對多關聯
                entity.HasMany(p => p.PartnerAddress)
                      .WithOne(pa => pa.Partner)
                      .HasForeignKey(pa => pa.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 PartnerAddress 也會被刪除

                // Partner 與 IndividualDetail 的一對一關聯
                entity.HasOne(p => p.IndividualDetail)
                      .WithOne()
                      .HasForeignKey<IndividualDetail>(id => id.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 IndividualDetail 也會被刪除

                // Partner 與 EnterpriseDetail 的一對一關聯
                entity.HasOne(p => p.EnterpriseDetail)
                      .WithOne()
                      .HasForeignKey<EnterpriseDetail>(od => od.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 EnterpriseDetail 也會被刪除

                // Partner 與 CustomerDetail 的一對一關聯
                entity.HasOne(p => p.CustomerDetail)
                      .WithOne()
                      .HasForeignKey<CustomerDetail>(cd => cd.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 CustomerDetail 也會被刪除

                // Partner 與 SupplierDetail 的一對一關聯
                entity.HasOne(p => p.SupplierDetail)
                      .WithOne()
                      .HasForeignKey<SupplierDetail>(sd => sd.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 SupplierDetail 也會被刪除
            });

            // SupplierDetail 與 SupplierCategory 的多對一關聯
            modelBuilder.Entity<SupplierDetail>(entity =>
            {
                entity.HasOne(sd => sd.SupplierCategory)
                      .WithMany()
                      .HasForeignKey(sd => sd.SupplierCategoryID)
                      .OnDelete(DeleteBehavior.Restrict); // 當 SupplierCategory 被刪除時，不允許刪除 (保護資料完整性)
            });

            // CustomerDetail 與 CustomerCategory 的多對一關聯
            modelBuilder.Entity<CustomerDetail>(entity =>
            {
                entity.HasOne(cd => cd.CustomerCategory)
                      .WithMany()
                      .HasForeignKey(cd => cd.CustomerCategoryID)
                      .OnDelete(DeleteBehavior.Restrict); // 當 CustomerCategory 被刪除時，不允許刪除 (保護資料完整性)
            });

            // Partner 與 Contact 的多對多關聯 (透過 PartnerContact 聯結表)
            modelBuilder.Entity<PartnerContact>(entity =>
            {
                entity.HasKey(pc => new { pc.PartnerID, pc.ContactID });

                entity.HasOne(pc => pc.Partner)
                      .WithMany(p => p.PartnerContacts)
                      .HasForeignKey(pc => pc.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(pc => pc.Contact)
                      .WithMany(c => c.PartnerContacts)
                      .HasForeignKey(pc => pc.ContactID)
                      .OnDelete(DeleteBehavior.Cascade);

                // 角色改為字串欄位 pc.Role，不再建立外鍵
            });
            #endregion

            modelBuilder.Entity<Models.Common.ReportSignatureTemplate>()
                .HasMany(t => t.Details)
                .WithOne(d => d.Template)
                .HasForeignKey(d => d.TemplateId)
                .OnDelete(DeleteBehavior.Cascade);

            // RMS 模組的外鍵約束配置
            // Contract 與 Property 的多對一關聯
            modelBuilder.Entity<Contract>(entity =>
            {
                entity.HasOne(c => c.Property)
                      .WithMany(p => p.Contracts)
                      .HasForeignKey(c => c.PropertyId)
                      .OnDelete(DeleteBehavior.Restrict); // 當 Property 被刪除時，不允許刪除 (保護資料完整性)

                entity.HasOne(c => c.Tenant)
                      .WithMany(t => t.Contracts)
                      .HasForeignKey(c => c.TenantId)
                      .OnDelete(DeleteBehavior.Restrict); // 當 Tenant 被刪除時，不允許刪除 (保護資料完整性)
            });

            // PaymentAllocation 關聯配置
            modelBuilder.Entity<PaymentAllocation>(entity =>
            {
                entity.HasOne(pa => pa.Payment)
                      .WithMany(p => p.PaymentAllocations)
                      .HasForeignKey(pa => pa.PaymentId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(pa => pa.Fee)
                      .WithMany(f => f.PaymentAllocations)
                      .HasForeignKey(pa => pa.FeeId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(pa => pa.PaymentId).HasDatabaseName("IX_PaymentAllocation_PaymentId");
                entity.HasIndex(pa => pa.FeeId).HasDatabaseName("IX_PaymentAllocation_FeeId");
            });

            // PaymentAllocation 關聯配置
            modelBuilder.Entity<PaymentAllocation>(entity =>
            {
                entity.HasOne(pa => pa.Payment)
                      .WithMany(p => p.PaymentAllocations)
                      .HasForeignKey(pa => pa.PaymentId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(pa => pa.Fee)
                      .WithMany(f => f.PaymentAllocations)
                      .HasForeignKey(pa => pa.FeeId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(pa => pa.PaymentId).HasDatabaseName("IX_PaymentAllocation_PaymentId");
                entity.HasIndex(pa => pa.FeeId).HasDatabaseName("IX_PaymentAllocation_FeeId");
            });

            // Fee 與 Contract 的多對一關聯
            modelBuilder.Entity<Fee>(entity =>
            {
                entity.HasOne(f => f.Contract)
                      .WithMany(c => c.Fees)
                      .HasForeignKey(f => f.ContractId)
                      .OnDelete(DeleteBehavior.Restrict); // 當 Contract 被刪除時，不允許刪除 (保護資料完整性)
            });

            // Payment 與 Contract 的多對一關聯
            modelBuilder.Entity<Payment>(entity =>
            {
                entity.HasOne(p => p.Contract)
                      .WithMany(c => c.Payments)
                      .HasForeignKey(p => p.ContractId)
                      .OnDelete(DeleteBehavior.Restrict); // 當 Contract 被刪除時，不允許刪除 (保護資料完整性)
            });

            // Sms 模組設定
            modelBuilder.Entity<SmsWebServer>(entity =>
            {
                entity.HasIndex(e => e.ServerName).IsUnique();
            });

            modelBuilder.Entity<SmsDbServer>(entity =>
            {
                entity.HasIndex(e => e.ServerName).IsUnique();
            });

            modelBuilder.Entity<SmsSite>(entity =>
            {
                entity.HasIndex(e => e.SiteName)
                    .IsUnique()
                    .HasFilter("[IsDeleted] = 0");

                entity.HasOne(s => s.WebServer)
                    .WithMany()
                    .HasForeignKey(s => s.WebServerId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(s => s.DbServer)
                    .WithMany()
                    .HasForeignKey(s => s.DbServerId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne<Users>(s => s.Owner)
                    .WithMany()
                    .HasForeignKey(s => s.OwnerUserId)
                    .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne<Users>(s => s.CoOwner1)
                    .WithMany()
                    .HasForeignKey(s => s.CoOwnerUserId1)
                    .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne<Users>(s => s.CoOwner2)
                    .WithMany()
                    .HasForeignKey(s => s.CoOwnerUserId2)
                    .OnDelete(DeleteBehavior.NoAction);
            });
        }

        /// <summary>
        /// 覆寫 SaveChangesAsync 以自動偵測資料變化並記錄日誌
        /// 使用新的增強實體變更追蹤器，避免循環引用問題
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>受影響的行數</returns>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            var transactionId = Guid.NewGuid().ToString();

            // 自動捕捉所有異動的實體
            var allChanges = new List<object>();
            var processedTypes = new HashSet<Type>();
            foreach (var entry in ChangeTracker.Entries())
            {
                if (entry.State == EntityState.Added || entry.State == EntityState.Modified || entry.State == EntityState.Deleted)
                {
                    var entityType = entry.Entity.GetType();
                    // 避免重複捕捉同型別
                    if (!processedTypes.Contains(entityType))
                    {
                        var method = typeof(EnhancedEntityChangeTracker).GetMethod("CaptureEntityChanges")!
                            .MakeGenericMethod(entityType);
                        var changes = (IEnumerable<object>)method.Invoke(_changeTracker, new object[] { ChangeTracker, transactionId });
                        allChanges.AddRange(changes);
                        processedTypes.Add(entityType);
                    }
                }
            }

            try
            {
                var result = await base.SaveChangesAsync(cancellationToken);
                return result;
            }
            catch (Exception ex)
            {
                if (_logger != null) await _logger.LogErrorAsync($"資料庫保存變更失敗，交易ID: {transactionId}", ex, "ERPDbContext");
                throw;
            }
        }

        /// <summary>
        /// 記錄實體變更日誌
        /// 使用新的安全序列化機制，避免循環引用問題
        /// </summary>
        /// <param name="changes">變更記錄列表</param>
        /// <param name="transactionId">交易ID</param>
        private async Task LogEntityChangesAsync(List<object> changes, string transactionId)
        {
            if (_logger == null) return;

            try
            {
                foreach (var change in changes)
                {
                    var changeType = change.GetType();
                    if (changeType.IsGenericType && changeType.GetGenericTypeDefinition() == typeof(EntityChangeRecord<>))
                    {
                        var entityType = changeType.GetGenericArguments()[0];
                        var method = typeof(ILogDataProcessor).GetMethod("ProcessEntityChangeAsync")!.MakeGenericMethod(entityType);
                        var taskObj = method.Invoke(_dataProcessor, new object[] { change });
                        if (taskObj is Task task)
                        {
                            await task.ConfigureAwait(false);
                            var resultProperty = task.GetType().GetProperty("Result");
                            var entry = resultProperty?.GetValue(task);
                            if (entry is SimpleLogEntry logEntry)
                            {
                                await _logger.TryLogAsync(logEntry);
                            }
                        }
                        else
                        {
                            // 可選：記錄警告或處理異常情境
                            Console.WriteLine("[ERPDbContext] ProcessEntityChangeAsync did not return a Task.");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 日誌記錄失敗不應該影響主要業務流程
                Console.WriteLine($"[ERPDbContext] 實體變更日誌記錄失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 配置細粒度權限系統的關聯關係
        /// </summary>
        /// <param name="modelBuilder">模型建構器</param>
        private static void ConfigureGranularPermissionRelationships(ModelBuilder modelBuilder)
        {
            // GranularPermissionAction 與 GranularPermission 的關聯
            modelBuilder.Entity<GranularPermissionAction>()
                .HasOne(gpa => gpa.GranularPermission)
                .WithMany(gp => gp.Actions)
                .HasForeignKey(gpa => gpa.GranularPermissionID)
                .OnDelete(DeleteBehavior.Cascade);

            // RoleGranularPermission 與 Roles 的關聯 - 使用 Restrict 避免循環刪除
            modelBuilder.Entity<RoleGranularPermission>()
                .HasOne(rgp => rgp.Role)
                .WithMany()
                .HasForeignKey(rgp => rgp.RolesID)
                .OnDelete(DeleteBehavior.Restrict);

            // RoleGranularPermission 與 GranularPermission 的關聯 - 使用 Restrict 避免循環刪除
            modelBuilder.Entity<RoleGranularPermission>()
                .HasOne(rgp => rgp.GranularPermission)
                .WithMany(gp => gp.RolePermissions)
                .HasForeignKey(rgp => rgp.GranularPermissionID)
                .OnDelete(DeleteBehavior.Restrict);

            // RoleGranularPermission 與 GranularPermissionAction 的關聯 - 使用 Restrict 避免循環刪除
            modelBuilder.Entity<RoleGranularPermission>()
                .HasOne(rgp => rgp.GranularPermissionAction)
                .WithMany(gpa => gpa.RolePermissions)
                .HasForeignKey(rgp => rgp.GranularPermissionActionID)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}