# Payment 繳費紀錄資料表

建立者: 系統自動產生
建立時間: 2024-07-19
DB相關: DB相關-Rms

## 資料表名稱

Payment

## 資料表說明

用於儲存租金及費用繳費紀錄

## 欄位說明

| 欄位名稱 | 資料型態 | 長度 | 允許空值 | 說明 |
| --- | --- | --- | --- | --- |
| PaymentId | nvarchar | 100 | 否 | 主鍵，繳費紀錄編號 |
| ContractId | nvarchar | 100 | 否 | 合約編號 (FK) |
| FeeId | nvarchar | 100 | 是 | 建議改由 PaymentAllocation 關聯，不直接綁單一費用 |
| PaymentDate | bigint | - | 否 | 繳費日期 |
| Amount | decimal | 18,2 | 否 | 繳費金額 |
| PaymentType | nvarchar | 50 | 否 | 繳費方式（現金/轉帳/支票等）|
| Status | nvarchar | 50 | 否 | 狀態（待確認(0)/已入帳(1)/作廢(9)）|
| Note | nvarchar | MAX | 是 | 備註 |
| CreateTime | bigint | - | 否 | 新增時間 |
| CreateUserId | nvarchar | 100 | 是 | 新增者編號 |
| UpdateTime | bigint | - | 是 | 更新時間 |
| UpdateUserId | nvarchar | 100 | 是 | 更新者編號 |
| DeleteTime | bigint | - | 是 | 刪除時間 |
| DeleteUserId | nvarchar | 100 | 是 | 刪除者編號 | 

## 備註

- 建議新增關聯表 PaymentAllocation：`PaymentId`、`FeeId`、`Amount`；一筆繳費可核銷多筆費用
- 明細主導：`Payment.Amount` 由後端取使用者輸入之 allocations 加總計算（不自動分配、不留餘額）
- 建議索引：`IX_Payment_ContractId_PaymentDate`、`IX_Payment_Status`