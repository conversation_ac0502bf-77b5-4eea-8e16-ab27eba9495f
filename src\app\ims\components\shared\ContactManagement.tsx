/**
 * 統一的 Contact 管理組件
 * 
 * 整合所有 Contact 管理功能，包括：
 * - 列表顯示和搜索
 * - 創建、編輯、刪除操作
 * - 批量操作
 * - 響應式設計
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

"use client";

import React, { useState } from 'react';
import {
  Card,
  Button,
  Space,
  Modal,
  Form,
  message,
  Typography,
  Row,
  Col,
  Divider
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  ReloadOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { Contact } from '@/services/ims/ContactService';
import { useContactManagement } from '@/app/ims/hooks/useContactManagement';
import ContactTable from './ContactTable';
import ContactForm from './ContactForm';
import QuickContactCreate from './QuickContactCreate';
import BatchContactOperations from './BatchContactOperations';
import UnifiedContactFilters from '../contact/UnifiedContactFilters';
import { useScreenSize } from './ResponsiveModalConfig';

const { Title } = Typography;

export interface ContactManagementProps {
  // 標題和描述
  title?: string;
  description?: string;
  
  // 功能配置
  showCreate?: boolean;
  showQuickCreate?: boolean;
  showBatchOperations?: boolean;
  showExport?: boolean;
  showSearch?: boolean;
  
  // 表格配置
  tableProps?: any;
  
  // 表單配置
  formFieldSet?: 'basic' | 'extended' | 'all' | 'quick';
  
  // 事件回調
  onContactSelected?: (contact: Contact) => void;
  onContactCreated?: (contact: Contact) => void;
  onContactUpdated?: (contact: Contact) => void;
  onContactDeleted?: (contactId: string) => void;
}

const ContactManagement: React.FC<ContactManagementProps> = ({
  title = "聯絡人管理",
  description = "管理所有聯絡人資料",
  showCreate = true,
  showQuickCreate = true,
  showBatchOperations = true,
  showExport = true,
  showSearch = true,
  tableProps = {},
  formFieldSet = 'all',
  onContactSelected,
  onContactCreated,
  onContactUpdated,
  onContactDeleted
}) => {
  const { isMobile } = useScreenSize();
  const [form] = Form.useForm();
  
  // 篩選器狀態
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);

  // 選擇狀態
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<Contact[]>([]);

  // 快速創建和批量操作狀態
  const [quickCreateVisible, setQuickCreateVisible] = useState(false);
  const [batchOperationsVisible, setBatchOperationsVisible] = useState(false);

  // Contact 管理 Hook
  const [state, actions] = useContactManagement({
    onContactCreated: (contact) => {
      onContactCreated?.(contact);
    },
    onContactUpdated: (contact) => {
      onContactUpdated?.(contact);
    },
    onContactDeleted: (contactId) => {
      onContactDeleted?.(contactId);
    }
  });

  // 處理篩選結果
  const handleFilterResult = (filtered: Contact[]) => {
    setFilteredContacts(filtered);
  };

  // 處理表單提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (state.formMode === 'create') {
        await actions.createContact(values);
      } else if (state.formMode === 'edit') {
        await actions.updateContactData({ ...state.selectedContact, ...values });
      }
    } catch (error) {
      console.error('表單驗證失敗:', error);
    }
  };

  // 處理選擇變更
  const handleSelectionChange = (selectedRowKeys: string[], selectedRows: Contact[]) => {
    setSelectedRowKeys(selectedRowKeys);
    setSelectedRows(selectedRows);
  };

  // 處理批量操作完成
  const handleBatchOperationComplete = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
    setBatchOperationsVisible(false);
    actions.refreshContacts();
  };

  // 處理匯出
  const handleExport = () => {
    // TODO: 實現匯出功能
    message.info('匯出功能開發中...');
  };

  // 獲取顯示的聯絡人列表
  const displayContacts = filteredContacts.length > 0
    ? filteredContacts
    : state.contacts;

  return (
    <div className="contact-management">
      {/* 標題區域 */}
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              {title}
            </Title>
            {description && (
              <Typography.Text type="secondary">
                {description}
              </Typography.Text>
            )}
          </Col>
          <Col>
            <Space wrap>
              {showCreate && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={actions.openCreateForm}
                  size={isMobile ? 'small' : 'middle'}
                >
                  {isMobile ? '新增' : '新增聯絡人'}
                </Button>
              )}
              {showQuickCreate && (
                <Button
                  icon={<PlusOutlined />}
                  onClick={() => setQuickCreateVisible(true)}
                  size={isMobile ? 'small' : 'middle'}
                >
                  {isMobile ? '快速' : '快速創建'}
                </Button>
              )}
              <Button
                icon={<ReloadOutlined />}
                onClick={actions.refreshContacts}
                loading={state.loading}
                size={isMobile ? 'small' : 'middle'}
              >
                {isMobile ? '' : '刷新'}
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 操作區域 */}
        <Divider />
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={16} md={18}>
            <Space wrap>
              {showBatchOperations && selectedRowKeys.length > 0 && (
                <Button
                  icon={<DeleteOutlined />}
                  onClick={() => setBatchOperationsVisible(true)}
                  size={isMobile ? 'small' : 'middle'}
                >
                  批量操作 ({selectedRowKeys.length})
                </Button>
              )}

              {showExport && (
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleExport}
                  size={isMobile ? 'small' : 'middle'}
                >
                  {isMobile ? '匯出' : '匯出資料'}
                </Button>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 篩選器區域 */}
      {showSearch && (
        <Card style={{ marginTop: 16 }}>
          <UnifiedContactFilters
            contacts={state.contacts}
            onFilterResult={handleFilterResult}
            showStats={true}
            compact={isMobile}
          />
        </Card>
      )}

      {/* 表格區域 */}
      <Card style={{ marginTop: 16 }}>
        <ContactTable
          contacts={displayContacts}
          loading={state.loading || state.searchLoading}
          selectedRowKeys={selectedRowKeys}
          onSelectionChange={handleSelectionChange}
          onView={(contact: Contact) => {
            actions.openViewForm(contact);
            onContactSelected?.(contact);
          }}
          onEdit={(contact: Contact) => {
            actions.openEditForm(contact);
            onContactSelected?.(contact);
          }}
          onDelete={actions.deleteContactData}
          showSelection={showBatchOperations}
          size={isMobile ? 'small' : 'middle'}
          {...tableProps}
        />
      </Card>

      {/* 表單模態框 */}
      <Modal
        title={
          state.formMode === 'create' ? '新增聯絡人' :
          state.formMode === 'edit' ? '編輯聯絡人' : '查看聯絡人'
        }
        open={state.formVisible}
        onOk={state.formMode !== 'view' ? handleFormSubmit : actions.closeForm}
        onCancel={actions.closeForm}
        confirmLoading={state.formLoading}
        okText={
          state.formMode === 'create' ? '創建' :
          state.formMode === 'edit' ? '更新' : '關閉'
        }
        cancelText={state.formMode !== 'view' ? '取消' : undefined}
        width={isMobile ? '95%' : 800}
        style={{ top: isMobile ? 20 : undefined }}
      >
        <ContactForm
          form={form}
          initialData={state.selectedContact}
          mode={state.formMode}
          fieldSet={formFieldSet}
        />
      </Modal>

      {/* 快速創建模態框 */}
      <QuickContactCreate
        visible={quickCreateVisible}
        onClose={() => setQuickCreateVisible(false)}
        onContactCreated={(contact) => {
          onContactCreated?.(contact);
          actions.refreshContacts();
        }}
      />

      {/* 批量操作模態框 */}
      <BatchContactOperations
        visible={batchOperationsVisible}
        onClose={() => setBatchOperationsVisible(false)}
        selectedContacts={selectedRows}
        onOperationComplete={handleBatchOperationComplete}
      />
    </div>
  );
};

export default ContactManagement;
