/**
 * 聯絡人工具函數測試
 * 
 * 測試統一的聯絡人處理工具，確保資料正規化、搜尋、篩選等功能正確
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import {
  createEmptyContact,
  makeContactDefaults,
  normalizeContact,
  normalizePartnerContacts,
  filterContacts,
  getContactStats,
  formatContactDisplayName
} from '../../shared/contactUtils';
import { Contact, PartnerContact } from '@/services/ims/ContactService';

// Mock the searchContacts function
jest.mock('@/services/ims/ContactService', () => ({
  searchContacts: jest.fn()
}));

describe('contactUtils', () => {
  describe('createEmptyContact', () => {
    it('should create contact with default values', () => {
      const contact = createEmptyContact();
      
      expect(contact.contactID).toBe('');
      expect(contact.name).toBe('');
      expect(contact.position).toBe('');
      expect(contact.email).toBe('');
      expect(contact.phone).toBe('');
      expect(contact.department).toBe('');
      expect(contact.company).toBe('');
      expect(contact.contactType).toBe('客戶');
      expect(contact.status).toBe(true);
      expect(contact.isDeleted).toBe(false);
      expect(contact.createTime).toBeNull();
      expect(contact.createUserId).toBeNull();
    });
  });

  describe('makeContactDefaults', () => {
    it('should merge overrides with default values', () => {
      const overrides = {
        name: '張三',
        email: '<EMAIL>',
        contactType: '供應商'
      };

      const contact = makeContactDefaults(overrides);
      
      expect(contact.name).toBe('張三');
      expect(contact.email).toBe('<EMAIL>');
      expect(contact.contactType).toBe('供應商');
      expect(contact.status).toBe(true); // default value preserved
      expect(contact.phone).toBe(''); // default value preserved
    });

    it('should work with empty overrides', () => {
      const contact = makeContactDefaults();
      
      expect(contact.contactType).toBe('客戶');
      expect(contact.status).toBe(true);
    });
  });

  describe('normalizeContact', () => {
    it('should handle null/undefined input', () => {
      const result1 = normalizeContact(null);
      const result2 = normalizeContact(undefined);
      
      expect(result1.contactID).toBe('');
      expect(result1.name).toBe('');
      expect(result2.contactID).toBe('');
      expect(result2.name).toBe('');
    });

    it('should normalize property names (ContactID -> contactID)', () => {
      const backendData = {
        ContactID: 'contact-123',
        Name: '張三',
        Email: '<EMAIL>',
        Status: true,
        ContactType: '客戶'
      };

      const result = normalizeContact(backendData);
      
      expect(result.contactID).toBe('contact-123');
      expect(result.name).toBe('張三');
      expect(result.email).toBe('<EMAIL>');
      expect(result.status).toBe(true);
      expect(result.contactType).toBe('客戶');
    });

    it('should prefer camelCase over PascalCase', () => {
      const mixedData = {
        contactID: 'contact-123',
        ContactID: 'contact-456', // should be ignored
        name: '張三',
        Name: '李四', // should be ignored
        status: false,
        Status: true // should be ignored
      };

      const result = normalizeContact(mixedData);
      
      expect(result.contactID).toBe('contact-123');
      expect(result.name).toBe('張三');
      expect(result.status).toBe(false);
    });

    it('should set default values for missing properties', () => {
      const partialData = {
        contactID: 'contact-123',
        name: '張三'
      };

      const result = normalizeContact(partialData);
      
      expect(result.contactID).toBe('contact-123');
      expect(result.name).toBe('張三');
      expect(result.email).toBe('');
      expect(result.phone).toBe('');
      expect(result.contactType).toBe('客戶');
      expect(result.status).toBe(true);
      expect(result.isDeleted).toBe(false);
    });
  });

  describe('normalizePartnerContacts', () => {
    it('should handle empty/null input', () => {
      expect(normalizePartnerContacts([])).toEqual([]);
      expect(normalizePartnerContacts(null as any)).toEqual([]);
      expect(normalizePartnerContacts(undefined as any)).toEqual([]);
    });

    it('should remove contacts without contactID', () => {
      const contacts = [
        { contactID: 'contact-1', partnerID: 'partner-1', role: '主辦', isPrimary: false, priority: 1 } as PartnerContact,
        { contactID: '', partnerID: 'partner-1', role: '協辦', isPrimary: false, priority: 2 } as PartnerContact,
        { contactID: null, partnerID: 'partner-1', role: '其他', isPrimary: false, priority: 3 } as any
      ];

      const result = normalizePartnerContacts(contacts);
      
      expect(result).toHaveLength(1);
      expect(result[0].contactID).toBe('contact-1');
    });

    it('should deduplicate based on contactID', () => {
      const contacts = [
        { contactID: 'contact-1', partnerID: 'partner-1', role: '主辦', isPrimary: false, priority: 1 } as PartnerContact,
        { contactID: 'contact-1', partnerID: 'partner-1', role: '協辦', isPrimary: true, priority: 2 } as PartnerContact,
        { contactID: 'contact-2', partnerID: 'partner-1', role: '其他', isPrimary: false, priority: 3 } as PartnerContact
      ];

      const result = normalizePartnerContacts(contacts);
      
      expect(result).toHaveLength(2);
      expect(result.find(c => c.contactID === 'contact-1')?.role).toBe('協辦'); // last one wins
    });

    it('should ensure only one primary contact', () => {
      const contacts = [
        { contactID: 'contact-1', partnerID: 'partner-1', role: '主辦', isPrimary: true, priority: 2 } as PartnerContact,
        { contactID: 'contact-2', partnerID: 'partner-1', role: '協辦', isPrimary: true, priority: 1 } as PartnerContact,
        { contactID: 'contact-3', partnerID: 'partner-1', role: '其他', isPrimary: false, priority: 3 } as PartnerContact
      ];

      const result = normalizePartnerContacts(contacts);
      
      const primaries = result.filter(c => c.isPrimary);
      expect(primaries).toHaveLength(1);
      expect(primaries[0].contactID).toBe('contact-2'); // lowest priority wins
    });

    it('should set primary contact if none exists', () => {
      const contacts = [
        { contactID: 'contact-1', partnerID: 'partner-1', role: '主辦', isPrimary: false, priority: 2 } as PartnerContact,
        { contactID: 'contact-2', partnerID: 'partner-1', role: '協辦', isPrimary: false, priority: 1 } as PartnerContact,
        { contactID: 'contact-3', partnerID: 'partner-1', role: '其他', isPrimary: false, priority: 3 } as PartnerContact
      ];

      const result = normalizePartnerContacts(contacts);
      
      const primaries = result.filter(c => c.isPrimary);
      expect(primaries).toHaveLength(1);
      expect(primaries[0].contactID).toBe('contact-2'); // lowest priority becomes primary
    });

    it('should set correct priority values', () => {
      const contacts = [
        { contactID: 'contact-1', partnerID: 'partner-1', role: '主辦', isPrimary: true, priority: 5 } as PartnerContact,
        { contactID: 'contact-2', partnerID: 'partner-1', role: '協辦', isPrimary: false, priority: null } as any,
        { contactID: 'contact-3', partnerID: 'partner-1', role: '其他', isPrimary: false, priority: undefined } as any
      ];

      const result = normalizePartnerContacts(contacts);
      
      expect(result.find(c => c.contactID === 'contact-1')?.priority).toBe(0); // primary = 0
      expect(result.find(c => c.contactID === 'contact-2')?.priority).toBe(99); // null -> 99
      expect(result.find(c => c.contactID === 'contact-3')?.priority).toBe(99); // undefined -> 99
    });

    it('should set partnerID if provided', () => {
      const contacts = [
        { contactID: 'contact-1', partnerID: '', role: '主辦', isPrimary: false, priority: 1 } as PartnerContact
      ];

      const result = normalizePartnerContacts(contacts, 'partner-123');
      
      expect(result[0].partnerID).toBe('partner-123');
    });
  });

  describe('filterContacts', () => {
    const mockContacts: Contact[] = [
      {
        contactID: 'contact-1',
        name: '張三',
        email: '<EMAIL>',
        phone: '0912345678',
        company: 'ABC公司',
        position: '經理',
        department: '業務部',
        contactType: '客戶',
        status: true
      } as Contact,
      {
        contactID: 'contact-2',
        name: '李四',
        email: '<EMAIL>',
        phone: '0987654321',
        company: 'XYZ公司',
        position: '專員',
        department: '技術部',
        contactType: '供應商',
        status: false
      } as Contact,
      {
        contactID: 'contact-3',
        name: '王五',
        email: '<EMAIL>',
        phone: '0955123456',
        company: 'ABC公司',
        position: '助理',
        department: '行政部',
        contactType: '客戶',
        status: true
      } as Contact
    ];

    it('should return all contacts when no filters applied', () => {
      const result = filterContacts(mockContacts);
      expect(result).toHaveLength(3);
    });

    it('should filter by search text (name)', () => {
      const result = filterContacts(mockContacts, '張三');
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('張三');
    });

    it('should filter by search text (email)', () => {
      const result = filterContacts(mockContacts, '<EMAIL>');
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('李四');
    });

    it('should filter by search text (company)', () => {
      const result = filterContacts(mockContacts, 'ABC');
      expect(result).toHaveLength(2);
      expect(result.every(c => c.company?.includes('ABC'))).toBe(true);
    });

    it('should filter by contactType', () => {
      const result = filterContacts(mockContacts, '', { contactType: '客戶' });
      expect(result).toHaveLength(2);
      expect(result.every(c => c.contactType === '客戶')).toBe(true);
    });

    it('should filter by status', () => {
      const result = filterContacts(mockContacts, '', { status: true });
      expect(result).toHaveLength(2);
      expect(result.every(c => c.status === true)).toBe(true);
    });

    it('should filter by company', () => {
      const result = filterContacts(mockContacts, '', { company: 'XYZ' });
      expect(result).toHaveLength(1);
      expect(result[0].company).toBe('XYZ公司');
    });

    it('should filter by department', () => {
      const result = filterContacts(mockContacts, '', { department: '業務' });
      expect(result).toHaveLength(1);
      expect(result[0].department).toBe('業務部');
    });

    it('should combine multiple filters', () => {
      const result = filterContacts(mockContacts, 'ABC', { 
        contactType: '客戶',
        status: true
      });
      expect(result).toHaveLength(2);
      expect(result.every(c => c.contactType === '客戶' && c.status === true)).toBe(true);
    });

    it('should be case insensitive', () => {
      const result = filterContacts(mockContacts, 'abc');
      expect(result).toHaveLength(2);
    });
  });

  describe('getContactStats', () => {
    const mockContacts: Contact[] = [
      { contactID: 'c1', contactType: '客戶', status: true } as Contact,
      { contactID: 'c2', contactType: '客戶', status: false } as Contact,
      { contactID: 'c3', contactType: '供應商', status: true } as Contact,
      { contactID: 'c4', contactType: '合作夥伴', status: true } as Contact,
      { contactID: 'c5', contactType: '', status: true } as Contact
    ];

    it('should return correct statistics', () => {
      const stats = getContactStats(mockContacts);
      
      expect(stats.total).toBe(5);
      expect(stats.active).toBe(4);
      expect(stats.inactive).toBe(1);
      expect(stats.typeStats['客戶']).toBe(2);
      expect(stats.typeStats['供應商']).toBe(1);
      expect(stats.typeStats['合作夥伴']).toBe(1);
      expect(stats.typeStats['未分類']).toBe(1);
    });

    it('should handle empty array', () => {
      const stats = getContactStats([]);
      
      expect(stats.total).toBe(0);
      expect(stats.active).toBe(0);
      expect(stats.inactive).toBe(0);
      expect(Object.keys(stats.typeStats)).toHaveLength(0);
    });
  });

  describe('formatContactDisplayName', () => {
    it('should format name with position and company', () => {
      const contact = {
        name: '張三',
        position: '經理',
        company: 'ABC公司'
      } as Contact;

      const result = formatContactDisplayName(contact);
      expect(result).toBe('張三 (經理) - ABC公司');
    });

    it('should format name with position only', () => {
      const contact = {
        name: '張三',
        position: '經理',
        company: ''
      } as Contact;

      const result = formatContactDisplayName(contact);
      expect(result).toBe('張三 (經理)');
    });

    it('should format name with company only', () => {
      const contact = {
        name: '張三',
        position: '',
        company: 'ABC公司'
      } as Contact;

      const result = formatContactDisplayName(contact);
      expect(result).toBe('張三 - ABC公司');
    });

    it('should format name only', () => {
      const contact = {
        name: '張三',
        position: '',
        company: ''
      } as Contact;

      const result = formatContactDisplayName(contact);
      expect(result).toBe('張三');
    });

    it('should handle missing name', () => {
      const contact = {
        name: '',
        position: '經理',
        company: 'ABC公司'
      } as Contact;

      const result = formatContactDisplayName(contact);
      expect(result).toBe('未命名聯絡人');
    });
  });
});

