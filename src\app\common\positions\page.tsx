"use client";
/* 職務管理
    /app/common/positions/page.tsx
  功能說明
    1. 顯示業務負責人一覽表
    2. 列出所有業務及其主辦、協辦人員
    3. 支援新增、編輯、刪除業務
    4. 支援為業務分配主辦、協辦人員
    5. 支援搜尋和篩選功能
*/
import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  useMemo,
} from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Form,
  Row,
  message,
  Typography,
  Badge,
  Tag,
  Empty,
  Col,
  Grid,
  Tooltip,
  Modal,
  Select,
  InputNumber,
  Dropdown,
  MenuProps,
  Divider,
  Descriptions,
  DatePicker,
  Timeline,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  TeamOutlined,
  SettingOutlined,
  FilterOutlined,
  ClearOutlined,
  EyeOutlined,
  PrinterOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
} from "@ant-design/icons";
import {
  getPositionOwnersSummary,
  getPositionOwners,
  addPosition,
  editPosition,
  deletePosition,
  addPositionOwner,
  editPositionOwner,
  deletePositionOwner,
  Position,
  PositionOwner,
  PositionOwnerSummary,
  OwnerInfo,
  getPositions,
} from "@/services/common/positionService";
import { getUsers } from "@/services/common/userService";
import { useOptions } from "@/contexts/OptionsContext";
import { useAuth } from "@/contexts/AuthContext";
import dayjs, { Dayjs } from "dayjs";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import ReportHeader, {
  getReportPrintStyles,
} from "@/app/components/common/ReportHeader";
import ReportFooter from "@/app/components/common/ReportFooter";
import { siteConfig } from "@/config/site";
import PositionOwnerEditModal from "./components/PositionOwnerEditModal";
import EmployeePositionsModal from "./components/EmployeePositionsModal";

const { Option } = Select;
const { useBreakpoint } = Grid;

// 篩選選項介面
interface FilterOption {
  label: string;
  value: string;
  type?: "input" | "select";
  children?: Array<{ label: string; value: string }>;
}
const { Title, Text } = Typography;
const { confirm } = Modal;

// 主組件
const PositionsPage: React.FC = () => {
  // =========================== 狀態管理 ===========================
  const [data, setData] = useState<PositionOwnerSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState("");

  // 日期範圍篩選狀態
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null]>([
    null,
    null,
  ]);

  // 篩選相關狀態
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});
  const [filteredData, setFilteredData] = useState<PositionOwnerSummary[]>([]);

  // 表單相關
  const [searchForm] = Form.useForm();
  const [positionForm] = Form.useForm();
  const [ownerForm] = Form.useForm();

  // Modal 狀態
  const [isPositionModalVisible, setIsPositionModalVisible] = useState(false);
  const [isOwnerModalVisible, setIsOwnerModalVisible] = useState(false);
  const [isTimelineModalVisible, setIsTimelineModalVisible] = useState(false);
  const [isEmployeePositionsModalVisible, setIsEmployeePositionsModalVisible] = useState(false);
  const [editingPosition, setEditingPosition] = useState<Position | null>(null);
  const [editingOwner, setEditingOwner] = useState<PositionOwner | null>(null);
  const [selectedPositionId, setSelectedPositionId] = useState<string>("");
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [selectedPositionOwnerId, setSelectedPositionOwnerId] = useState<string>("");
  const [ownerEditMode, setOwnerEditMode] = useState<"add" | "edit">("add");
  const [selectedRoleType, setSelectedRoleType] = useState<"主辦" | "協辦" | undefined>(undefined);
  const [selectedPositionForTimeline, setSelectedPositionForTimeline] =
    useState<PositionOwnerSummary | null>(null);
  // 列印/預覽
  const printRef = useRef<HTMLDivElement>(null);
  const [isPrintMode, setIsPrintMode] = useState(false);
  const [selectedSignatureTemplateId, setSelectedSignatureTemplateId] =
    useState<string | undefined>(undefined);

  // 從 Context 取得選單資料
  const { users, refreshOptions, signatureTemplates } = useOptions() as any;
  const { user } = useAuth();

  // 定義篩選選項
  const filterOptions: FilterOption[] = useMemo(
    () => [
      {
        label: "主辦人員",
        value: "primaryOwners",
        type: "select",
        children: (users || []).map((user: any) => ({
          label: user.name,
          value: user.userId,
        })),
      },
      {
        label: "協辦人員",
        value: "secondaryOwners",
        type: "select",
        children: (users || []).map((user: any) => ({
          label: user.name,
          value: user.userId,
        })),
      },
    ],
    [users]
  );

  // 響應式斷點
  const screens = useBreakpoint();
  const isMobile = !screens.md;

  // =========================== 數據載入 ===========================

  // 載入業務負責人一覽表
  const loadData = useCallback(
    async (positionId?: string) => {
      try {
        setLoading(true);

        // 準備日期參數
        const startDate = dateRange[0]
          ? dateRange[0].format("YYYY-MM-DD")
          : undefined;
        const endDate = dateRange[1]
          ? dateRange[1].format("YYYY-MM-DD")
          : undefined;

        // 同時載入總表、詳細資料與所有業務清單
        const [summaryResponse, ownersResponse, positionsResponse] =
          await Promise.all([
            getPositionOwnersSummary(positionId, startDate, endDate),
            getPositionOwners(),
            getPositions(),
          ]);

        if (
          summaryResponse.success &&
          summaryResponse.data &&
          ownersResponse.success &&
          ownersResponse.data &&
          positionsResponse.success &&
          positionsResponse.data
        ) {
          // 將詳細的負責人資料合併到總表資料中
          const enrichedData = summaryResponse.data.map(
            (position: PositionOwnerSummary) => {
              const positionOwners = (ownersResponse.data || []).filter(
                (owner: PositionOwner) =>
                  owner.positionId === position.positionId
              );
              return {
                ...position,
                owners: position.owners.map((owner: OwnerInfo) => {
                  const detailedOwner = positionOwners.find(
                    (po: PositionOwner) =>
                      po.userId === owner.userId &&
                      po.roleType === owner.roleType
                  );
                  const userInfo = users?.find(
                    (u: any) => u.userId === owner.userId
                  );
                  return {
                    ...owner,
                    positionOwnerId:
                      detailedOwner?.positionOwnerId ||
                      `${position.positionId}-${owner.userId}`,
                    // 優先使用 API 總表中的部門欄位，否則回退至使用者清單中的部門名稱
                    departmentName:
                      (owner as any)?.serviceDepartmentName ||
                      userInfo?.serviceDepartmentName ||
                      null,
                    // 其餘 Tooltip 用欄位（Summary 若有則直接吃，否則從詳細列表或 users 回填可得部分）
                    serviceDepartmentName:
                      (owner as any)?.serviceDepartmentName ||
                      userInfo?.serviceDepartmentName ||
                      null,
                    eMail:
                      (owner as any)?.eMail || (userInfo as any)?.eMail || null,
                    altPhone:
                      (owner as any)?.altPhone ||
                      (userInfo as any)?.altPhone ||
                      null,
                    startDate:
                      (owner as any)?.startDate ||
                      (detailedOwner as any)?.startDate ||
                      0,
                    endDate:
                      (owner as any)?.endDate ||
                      (detailedOwner as any)?.endDate ||
                      0,
                  };
                }),
              };
            }
          );

          // 把沒有出現在 summary 的業務（代表目前查詢區間無負責人）也放進清單
          const summaryIds = new Set(enrichedData.map((p) => p.positionId));
          const positionsOnly = (positionsResponse.data || [])
            .filter((p: any) => !summaryIds.has(p.positionId))
            .map(
              (p: any) =>
              ({
                positionId: p.positionId,
                positionName: p.name,
                owners: [],
              } as PositionOwnerSummary)
            );

          // 依 positions 的 sortCode 排序（未在 positions 中的放最後）
          const merged = [...enrichedData, ...positionsOnly].sort(
            (a: any, b: any) => {
              const aPos = (positionsResponse.data || []).find(
                (p: any) => p.positionId === a.positionId
              );
              const bPos = (positionsResponse.data || []).find(
                (p: any) => p.positionId === b.positionId
              );
              const aSort = aPos?.sortCode ?? Number.MAX_SAFE_INTEGER;
              const bSort = bPos?.sortCode ?? Number.MAX_SAFE_INTEGER;
              return aSort - bSort;
            }
          );

          setData(merged);
        } else {
          console.error("API 回應錯誤:", summaryResponse, ownersResponse);
          message.error(
            summaryResponse.message ||
            ownersResponse.message ||
            "載入職務管理資料失敗"
          );
          setData([]);
        }
      } catch (error) {
        console.error("載入職務管理資料錯誤:", error);
        message.error("載入職務管理資料失敗");
        setData([]);
      } finally {
        setLoading(false);
      }
    },
    [dateRange]
  );

  // 初始載入
  useEffect(() => {
    loadData();
  }, [loadData]);

  // =========================== 篩選處理 ===========================

  // 處理篩選值變更
  const handleFilterValueChange = (filterKey: string, value: any) => {
    setFilterValues({
      ...filterValues,
      [filterKey]: value,
    });
  };

  // 處理新增篩選條件
  const handleAddFilter = (filterKey: string) => {
    if (!activeFilters.includes(filterKey)) {
      setActiveFilters([...activeFilters, filterKey]);
    }
  };

  // 處理移除篩選條件
  const handleRemoveFilter = (filterKey: string) => {
    setActiveFilters(activeFilters.filter((key) => key !== filterKey));
    const newFilterValues = { ...filterValues };
    delete newFilterValues[filterKey];
    setFilterValues(newFilterValues);
  };

  // 清除所有篩選
  const handleClearFilters = () => {
    setActiveFilters([]);
    setFilterValues({});
    setSearchText("");
  };

  // 篩選器處理
  const applyFilters = useCallback(() => {
    let filtered = [...data];

    // 先應用搜尋文字篩選
    if (searchText.trim()) {
      filtered = filtered.filter((position) =>
        position.positionName.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // 再應用其他篩選條件
    activeFilters.forEach((filterKey) => {
      const value = filterValues[filterKey];
      const hasValue = Array.isArray(value)
        ? value.length > 0
        : value !== undefined && value !== null && value !== "";

      if (hasValue) {
        switch (filterKey) {
          case "primaryOwners":
            filtered = filtered.filter((position) => {
              const primaryOwners = (position.owners || []).filter(
                (owner) => owner.roleType === "主辦"
              );
              return Array.isArray(value)
                ? value.some((userId) =>
                  primaryOwners.some((owner) => owner.userId === userId)
                )
                : primaryOwners.some((owner) => owner.userId === value);
            });
            break;
          case "secondaryOwners":
            filtered = filtered.filter((position) => {
              const secondaryOwners = (position.owners || []).filter(
                (owner) => owner.roleType === "協辦"
              );
              return Array.isArray(value)
                ? value.some((userId) =>
                  secondaryOwners.some((owner) => owner.userId === userId)
                )
                : secondaryOwners.some((owner) => owner.userId === value);
            });
            break;
          default:
            break;
        }
      }
    });

    setFilteredData(filtered);
  }, [activeFilters, filterValues, searchText, data]);

  // 更新篩選器
  useEffect(() => {
    if (data.length > 0) {
      applyFilters();
    }
  }, [activeFilters, filterValues, searchText, data.length, applyFilters]);

  // =========================== 事件處理 ===========================

  // 搜尋處理 - 改為即時搜尋
  const handleSearch = (value: string) => {
    setSearchText(value || "");
  };

  // 重置搜尋
  const handleResetSearch = () => {
    searchForm.resetFields();
    setSearchText("");
    setDateRange([null, null]);
  };

  // 列印處理
  const handlePrint = () => {
    if (printRef.current) {
      const printContent = printRef.current.innerHTML;
      const printWindow = window.open("", "_blank");

      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>${siteConfig?.copyright || "FAST"}職務負責人清單</title>
              <style>
                body { background: white; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #000; padding: 4px 6px; font-size: 12px; }
                th { background: #f5f5f5; font-weight: bold; text-align: center; }
                .text-center { text-align: center; }
                .text-right { text-align: right; }
                ${getReportPrintStyles("職務負責人清單")}
              </style>
            </head>
            <body>
              ${printContent}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  // Timeline 處理
  const handleShowTimeline = (position: PositionOwnerSummary) => {
    setSelectedPositionForTimeline(position);
    setIsTimelineModalVisible(true);
  };

  // 計算任職期間，輸出格式：(X年Y天) 或 (Y天)
  const formatTenure = (start?: string | null, end?: string | null): string => {
    if (!start) return "";
    const startDay = dayjs(start, "YYYY-MM-DD");
    const endDay = end ? dayjs(end, "YYYY-MM-DD") : dayjs();
    if (!startDay.isValid() || !endDay.isValid() || endDay.isBefore(startDay))
      return "";
    const years = endDay.diff(startDay, "year");
    const afterYears = startDay.add(years, "year");
    const days = endDay.diff(afterYears, "day");
    const parts: string[] = [];
    if (years > 0) parts.push(`${years}年`);
    if (days > 0) parts.push(`${days}天`);
    const text = parts.join("");
    return text ? ` (${text})` : "";
  };

  // 產生 Timeline 資料
  const generateTimelineData = useMemo(() => {
    if (!selectedPositionForTimeline) return [];

    const timelineItems: any[] = [];
    const owners = selectedPositionForTimeline.owners || [];

    // 將所有負責人的開始和結束事件收集起來
    const events: Array<{
      date: string;
      type: "start" | "end";
      owner: OwnerInfo;
    }> = [];

    owners.forEach((owner) => {
      if (owner.startDate) {
        events.push({
          date: owner.startDate,
          type: "start",
          owner,
        });
      }
      if (owner.endDate) {
        events.push({
          date: owner.endDate,
          type: "end",
          owner,
        });
      }
    });

    // 按日期排序（最新的在前面）
    events.sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    // 轉換為 Timeline 項目
    events.forEach((event) => {
      const isStart = event.type === "start";
      const color = isStart ? "green" : "red";
      const icon = isStart ? <PlayCircleOutlined /> : <PauseCircleOutlined />;

      timelineItems.push({
        color,
        dot: icon,
        children: (
          <div>
            <div style={{ fontWeight: "bold", marginBottom: 4 }}>
              {isStart
                ? event.date
                : `${event.owner.startDate || ""}~${event.owner.endDate || event.date
                }`}{" "}
              <span style={{ color }}>{isStart ? "接任業務" : "卸任業務"}</span>
              {formatTenure(
                event.owner.startDate,
                isStart ? null : event.owner.endDate || event.date
              )}
            </div>
            <div>
              <Badge
                status={
                  event.owner.roleType === "主辦" ? "processing" : "default"
                }
                text={event.owner.roleType}
              />
              <span style={{ marginLeft: 8 }}>
                {event.owner.departmentName ||
                  event.owner.serviceDepartmentName}
                {event.owner.departmentName || event.owner.serviceDepartmentName
                  ? " - "
                  : ""}
                {event.owner.userName}
              </span>
              {event.owner.altPhone && (
                <span style={{ color: "#666", marginLeft: 8 }}>
                  (分機: {event.owner.altPhone})
                </span>
              )}
            </div>
          </div>
        ),
      });
    });

    return timelineItems;
  }, [selectedPositionForTimeline]);

  // 更新篩選選單
  const filterMenu: MenuProps = {
    items: [
      ...filterOptions.map((option) => ({
        key: option.value,
        label: option.label,
        disabled: activeFilters.includes(option.value),
        onClick: () => {
          if (!activeFilters.includes(option.value)) {
            handleAddFilter(option.value);
          }
        },
      })),
      ...(activeFilters.length > 0
        ? [
          { type: "divider" as const },
          {
            key: "clear",
            label: "清除所有篩選",
            onClick: handleClearFilters,
            danger: true,
          },
        ]
        : []),
    ],
  };

  // 新增業務
  const handleAddPosition = () => {
    setEditingPosition(null);
    positionForm.resetFields();

    // 計算下一個排序編號（基於現有資料數量）
    const nextSortCode = data.length + 1;

    // 預設填入排序編號
    positionForm.setFieldsValue({
      sortCode: nextSortCode,
    });

    setIsPositionModalVisible(true);
  };

  // 編輯業務
  const handleEditPosition = (record: PositionOwnerSummary) => {
    // 根據業務在列表中的位置計算排序編號
    const positionIndex = data.findIndex(
      (p) => p.positionId === record.positionId
    );
    const sortCode = positionIndex >= 0 ? positionIndex + 1 : data.length + 1;

    const position: Position = {
      positionId: record.positionId,
      name: record.positionName,
      sortCode: sortCode,
      createTime: Date.now(),
      createUserId: user?.userId || "",
      isDeleted: false,
    };
    setEditingPosition(position);
    positionForm.setFieldsValue({
      name: position.name,
      sortCode: position.sortCode,
    });
    setIsPositionModalVisible(true);
  };

  // 刪除業務
  const handleDeletePosition = (record: PositionOwnerSummary) => {
    confirm({
      title: "確認刪除",
      content: (
        <span>
          確定要刪除業務
          <span style={{ fontWeight: "bold", color: "#ff4d4f" }}>
            {record.positionName}
          </span>
          嗎？
        </span>
      ),
      okText: "確定",
      cancelText: "取消",
      onOk: async () => {
        try {
          const position: Position = {
            positionId: record.positionId,
            name: record.positionName,
            sortCode: 0, // API 回應中沒有 sortCode，使用預設值
            createTime: Date.now(),
            createUserId: user?.userId || "",
            isDeleted: true,
          };

          const response = await deletePosition(position);
          if (response.success) {
            message.success("刪除業務成功");
            loadData();
          } else {
            message.error(response.message || "刪除業務失敗");
          }
        } catch (error) {
          console.error("刪除業務錯誤:", error);
          message.error("刪除業務失敗");
        }
      },
    });
  };

  // 新增/編輯負責人
  const handleAddOwner = (positionId: string, roleType: "主辦" | "協辦") => {
    setSelectedPositionId(positionId);
    setSelectedRoleType(roleType);
    setOwnerEditMode("add");
    setIsOwnerModalVisible(true);
  };

  // 編輯負責人 - 通過 positionOwnerId
  const handleEditOwnerById = (positionOwnerId: string) => {
    setSelectedPositionOwnerId(positionOwnerId);
    setOwnerEditMode("edit");
    setIsOwnerModalVisible(true);
  };

  // 顯示員工職務列表
  const handleShowEmployeePositions = (userId: string) => {
    setSelectedUserId(userId);
    setIsEmployeePositionsModalVisible(true);
  };

  // 刪除負責人
  const handleDeleteOwner = (owner: PositionOwner) => {
    confirm({
      title: "確認刪除",
      content: (
        <span>
          確定要刪除{owner.roleType}
          <span style={{ fontWeight: "bold", color: "#ff4d4f" }}>
            {owner.departmentName
              ? `${owner.departmentName} - ${owner.userName}`
              : owner.userName}
          </span>
          的職務資料嗎？
        </span>
      ),
      okText: "確定",
      cancelText: "取消",
      onOk: async () => {
        try {
          const ownerWithUpdateInfo: PositionOwner = {
            ...owner,
            updateTime: Date.now(),
            updateUserId: user?.userId || "",
          };
          const response = await deletePositionOwner(ownerWithUpdateInfo);
          if (response.success) {
            message.success("刪除負責人成功");
            loadData();
          } else {
            message.error(response.message || "刪除負責人失敗");
          }
        } catch (error) {
          console.error("刪除負責人錯誤:", error);
          message.error("刪除負責人失敗");
        }
      },
    });
  };

  // 業務表單提交
  const handlePositionSubmit = async () => {
    try {
      const values = await positionForm.validateFields();

      const positionData: Position = {
        ...values,
        positionId: editingPosition?.positionId || "",
        createTime: editingPosition?.createTime || Date.now(),
        createUserId: editingPosition?.createUserId || user?.userId || "",
        updateTime: Date.now(),
        updateUserId: user?.userId || "",
        isDeleted: false,
      };

      // 顯示確認對話框
      confirm({
        title: editingPosition ? "確認編輯" : "確認新增",
        content: (
          <span>
            確定要{editingPosition ? "編輯" : "新增"}業務
            <span style={{ fontWeight: "bold", color: "#ff4d4f" }}>
              {values.name}
            </span>
            嗎？
          </span>
        ),
        okText: "確定",
        cancelText: "取消",
        onOk: async () => {
          try {
            const response = editingPosition
              ? await editPosition(positionData)
              : await addPosition(positionData);

            if (response.success) {
              message.success(
                editingPosition ? "編輯業務成功" : "新增業務成功"
              );
              setIsPositionModalVisible(false);
              loadData();
              refreshOptions();
            } else {
              message.error(response.message || "操作失敗");
            }
          } catch (error) {
            console.error("業務操作錯誤:", error);
            message.error("操作失敗");
          }
        },
      });
    } catch (error) {
      console.error("表單驗證錯誤:", error);
      message.error("請檢查輸入資料");
    }
  };

  // 處理新組件的成功回調
  const handleOwnerEditSuccess = () => {
    loadData();
  };

  // =========================== 渲染組件 ===========================

  // 表格欄位定義
  const columns = [
    {
      title: "序號",
      key: "index",
      width: 80,
      align: "center" as const,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: "業務名稱",
      dataIndex: "positionName",
      key: "positionName",
      width: 200,
      render: (text: string, record: PositionOwnerSummary) => (
        <Text strong>{text}</Text>
      ),
    },
    {
      title: "主辦",
      key: "primaryOwners",
      width: 300,
      render: (_: any, record: PositionOwnerSummary) => {
        const primaryOwners = (record.owners || []).filter(
          (owner) => owner.roleType === "主辦"
        );
        return (
          <Space wrap>
            {primaryOwners.map((owner) => (
              <Tag
                key={`${record.positionId}-${owner.userId}-${owner.roleType}`}
                color={owner.status === "卸任業務" ? "red" : "blue"}
                closable
                style={{ cursor: "pointer" }}
                onClick={() => {
                  if (owner.positionOwnerId) {
                    handleEditOwnerById(owner.positionOwnerId);
                  }
                }}
                onClose={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleDeleteOwner({
                    positionOwnerId:
                      owner.positionOwnerId ||
                      `${record.positionId}-${owner.userId}`,
                    positionId: record.positionId,
                    positionName: record.positionName,
                    userId: owner.userId,
                    userName: owner.userName,
                    roleType: owner.roleType,
                    orderNo: owner.orderNo,
                  });
                }}
              >
                <Tooltip
                  title={
                    <div style={{ padding: 8 }}>
                      <Descriptions size="small" bordered column={1}>
                        <Descriptions.Item label="負責人">
                          {owner.userName || "-"}
                        </Descriptions.Item>
                        <Descriptions.Item label="服務部門">
                          {owner.serviceDepartmentName ||
                            owner.departmentName ||
                            "-"}
                        </Descriptions.Item>
                        <Descriptions.Item label="電子信箱">
                          {owner.eMail || "-"}
                        </Descriptions.Item>
                        <Descriptions.Item label="分機">
                          {owner.altPhone || "-"}
                        </Descriptions.Item>
                        <Descriptions.Item label="開始日期">
                          {owner.startDate || "-"}
                        </Descriptions.Item>
                        <Descriptions.Item label="結束日期">
                          {owner.endDate || "-"}
                        </Descriptions.Item>
                        <Descriptions.Item label="任職狀態">
                          <Badge
                            status={
                              owner.status === "接任業務"
                                ? "processing"
                                : "default"
                            }
                            text={owner.status || "-"}
                          />
                        </Descriptions.Item>
                      </Descriptions>
                    </div>
                  }
                  color="#fff"
                  placement="top"
                  overlayInnerStyle={{ padding: 0 }}
                >
                  <span>
                    <UserOutlined />{" "}
                    <span
                      style={{ cursor: "pointer", textDecoration: "underline" }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleShowEmployeePositions(owner.userId);
                      }}
                    >
                      {(owner.departmentName
                        ? `${owner.departmentName} - ${owner.userName}`
                        : owner.userName) +
                        (owner.altPhone ? ` (分機:${owner.altPhone})` : "")}
                    </span>
                  </span>
                </Tooltip>
              </Tag>
            ))}
            <Button
              type="dashed"
              size="small"
              icon={<PlusOutlined />}
              onClick={() => handleAddOwner(record.positionId, "主辦")}
            >
              新增主辦
            </Button>
          </Space>
        );
      },
    },
    {
      title: "協辦",
      key: "secondaryOwners",
      width: 300,
      render: (_: any, record: PositionOwnerSummary) => {
        const secondaryOwners = (record.owners || [])
          .filter((owner) => owner.roleType === "協辦")
          .sort((a, b) => a.orderNo - b.orderNo);
        return (
          <Space wrap>
            {secondaryOwners.map((owner, index) => (
              <Tag
                key={`${record.positionId}-${owner.userId}-${owner.roleType}`}
                color={owner.status === "卸任業務" ? "red" : "green"}
                closable
                style={{ cursor: "pointer" }}
                onClick={() => {
                  if (owner.positionOwnerId) {
                    handleEditOwnerById(owner.positionOwnerId);
                  }
                }}
                onClose={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleDeleteOwner({
                    positionOwnerId:
                      owner.positionOwnerId ||
                      `${record.positionId}-${owner.userId}`,
                    positionId: record.positionId,
                    positionName: record.positionName,
                    userId: owner.userId,
                    userName: owner.userName,
                    roleType: owner.roleType,
                    orderNo: owner.orderNo,
                  });
                }}
              >
                <Tooltip
                  title={
                    <div style={{ padding: 8 }}>
                      <Descriptions size="small" bordered column={1}>
                        <Descriptions.Item label="負責人">
                          {owner.userName || "-"}
                        </Descriptions.Item>
                        <Descriptions.Item label="服務部門">
                          {owner.serviceDepartmentName ||
                            owner.departmentName ||
                            "-"}
                        </Descriptions.Item>
                        <Descriptions.Item label="電子信箱">
                          {owner.eMail || "-"}
                        </Descriptions.Item>
                        <Descriptions.Item label="分機">
                          {owner.altPhone || "-"}
                        </Descriptions.Item>
                        <Descriptions.Item label="開始日期">
                          {owner.startDate || "-"}
                        </Descriptions.Item>
                        <Descriptions.Item label="結束日期">
                          {owner.endDate || "-"}
                        </Descriptions.Item>
                        <Descriptions.Item label="任職狀態">
                          <Badge
                            status={
                              owner.status === "接任業務"
                                ? "processing"
                                : "default"
                            }
                            text={owner.status || "-"}
                          />
                        </Descriptions.Item>
                      </Descriptions>
                    </div>
                  }
                  color="#fff"
                  placement="top"
                  overlayInnerStyle={{ padding: 0 }}
                >
                  <span>
                    <TeamOutlined /> 協辦{index + 1}
                    {":"}
                    <span
                      style={{ cursor: "pointer", textDecoration: "underline" }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleShowEmployeePositions(owner.userId);
                      }}
                    >
                      {(owner.departmentName
                        ? `${owner.departmentName} - ${owner.userName}`
                        : owner.userName) +
                        (owner.altPhone ? ` (分機:${owner.altPhone})` : "")}{" "}
                    </span>
                  </span>
                </Tooltip>
              </Tag>
            ))}
            <Button
              type="dashed"
              size="small"
              icon={<PlusOutlined />}
              onClick={() => handleAddOwner(record.positionId, "協辦")}
            >
              新增協辦
            </Button>
          </Space>
        );
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 180,
      align: "center" as const,
      render: (_: any, record: PositionOwnerSummary) => (
        <Space>
          <Tooltip title="查看負責人異動記錄">
            <Button
              type="link"
              icon={<ClockCircleOutlined />}
              onClick={() => handleShowTimeline(record)}
            />
          </Tooltip>
          <Tooltip title="編輯業務">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEditPosition(record)}
            />
          </Tooltip>
          <Tooltip title="刪除業務">
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeletePosition(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 報表欄位（預覽/列印）所需的最大主辦/協辦數量
  const maxPrimaryOwners = useMemo(() => {
    return filteredData.reduce((max, row) => {
      const count = (row.owners || []).filter(
        (o) => o.roleType === "主辦" && o.status !== "卸任業務"
      ).length;
      return count > max ? count : max;
    }, 0);
  }, [filteredData]);

  const maxSecondaryOwners = useMemo(() => {
    return filteredData.reduce((max, row) => {
      const count = (row.owners || []).filter(
        (o) => o.roleType === "協辦" && o.status !== "卸任業務"
      ).length;
      return count > max ? count : max;
    }, 0);
  }, [filteredData]);

  return (
    <div style={{ padding: "20px" }}>
      <Card title="職務管理">
        {/* 搜尋區域 */}
        <Card title="查詢條件" style={{ marginBottom: "24px" }}>
          <Form form={searchForm} layout="inline">
            <Form.Item name="searchText">
              <Input
                placeholder="請輸入業務名稱"
                prefix={<SearchOutlined />}
                style={{ width: 200 }}
                allowClear
                onChange={(e) => handleSearch(e.target.value)}
              />
            </Form.Item>
            <Form.Item label="任職時間範圍">
              <Space direction="vertical">
                <DatePicker.RangePicker
                  value={dateRange}
                  onChange={(dates) => setDateRange(dates || [null, null])}
                  format="YYYY-MM-DD"
                  placeholder={["開始日期", "結束日期"]}
                  style={{ width: 300 }}
                  allowClear
                />
              </Space>
            </Form.Item>
            <Form.Item>
              <Space>
                <Button icon={<ReloadOutlined />} onClick={handleResetSearch}>
                  重置
                </Button>
                <Dropdown menu={filterMenu} trigger={["click"]}>
                  <Button icon={<FilterOutlined />}>
                    篩選條件{" "}
                    {activeFilters.length > 0 && `(${activeFilters.length})`}
                  </Button>
                </Dropdown>
                {activeFilters.length > 0 && (
                  <Button icon={<ClearOutlined />} onClick={handleClearFilters}>
                    清除篩選
                  </Button>
                )}
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddPosition}
                >
                  新增業務
                </Button>
              </Space>
            </Form.Item>
          </Form>

          {/* 顯示活動的篩選條件 */}
          {activeFilters.length > 0 && (
            <div style={{ marginTop: 16 }}>
              <Space wrap>
                {activeFilters.map((filterKey) => {
                  const option = filterOptions.find(
                    (opt) => opt.value === filterKey
                  );
                  const value = filterValues[filterKey];
                  if (!option) return null;

                  return (
                    <div key={filterKey} style={{ marginBottom: 8 }}>
                      <span style={{ marginRight: 8, fontWeight: "bold" }}>
                        {option.label}:
                      </span>
                      {option.type === "input" ? (
                        <Input
                          size="middle"
                          placeholder={`請輸入${option.label}`}
                          value={value || ""}
                          onChange={(e) =>
                            handleFilterValueChange(filterKey, e.target.value)
                          }
                          style={{ width: 150 }}
                        />
                      ) : option.children ? (
                        <Select
                          size="middle"
                          placeholder={`請選擇${option.label}`}
                          mode="multiple"
                          value={
                            Array.isArray(value) ? value : value ? [value] : []
                          }
                          onChange={(val) =>
                            handleFilterValueChange(filterKey, val)
                          }
                          style={{ width: 150 }}
                          allowClear
                        >
                          {option.children.map((child) => (
                            <Select.Option
                              key={child.value}
                              value={child.value}
                            >
                              {child.label}
                            </Select.Option>
                          ))}
                        </Select>
                      ) : null}
                      <Button
                        type="text"
                        size="small"
                        onClick={() => handleRemoveFilter(filterKey)}
                        style={{ marginLeft: 4 }}
                      >
                        ×
                      </Button>
                    </div>
                  );
                })}
              </Space>
            </div>
          )}
        </Card>

        {/* 報表操作區 */}
        <Card style={{ marginBottom: 16 }}>
          <Space>
            <Select
              placeholder="選擇簽核章模板"
              allowClear
              style={{ width: 240 }}
              value={selectedSignatureTemplateId}
              onChange={setSelectedSignatureTemplateId}
            >
              {(signatureTemplates || []).map((template: any) => (
                <Option key={template.id} value={template.id}>
                  {template.name}
                </Option>
              ))}
            </Select>
            {isPrintMode && (
              <Button
                type="primary"
                icon={<PrinterOutlined />}
                onClick={handlePrint}
              >
                列印
              </Button>
            )}
            <Button
              icon={<EyeOutlined />}
              onClick={() => setIsPrintMode(!isPrintMode)}
            >
              {isPrintMode ? "返回列表" : "預覽報表"}
            </Button>
          </Space>
        </Card>

        {/* 資料表格 */}
        <Card
          title={`業務負責人一覽表 共 ${filteredData.length} 筆資料`}
          extra={
            <Space>
              <Badge count={filteredData.length} showZero>
                <Button size="small">總計</Button>
              </Badge>
            </Space>
          }
        >
          {!isPrintMode && (
            <Table
              columns={columns}
              dataSource={filteredData}
              rowKey="positionId"
              loading={loading}
              scroll={{ x: 1200 }}
              locale={{
                emptyText: (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={
                      <span style={{ color: "#999" }}>
                        查無符合條件的資料
                        <br />
                        請調整查詢條件或新增業務資料
                      </span>
                    }
                  />
                ),
              }}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
                pageSizeOptions: ["10", "20", "50", "100"],
              }}
            />
          )}

          {isPrintMode && (
            <div ref={printRef}>
              <div
                style={{
                  background: "white",
                  padding: "5px",
                }}
              >
                <style>
                  {`
                    table.report-table { border-collapse: collapse; width: 100%; }
                    .report-table th, .report-table td { border: 1px solid #000; padding: 4px 6px; font-size: 12px; }
                    .report-table th { background: #f5f5f5; font-weight: bold; text-align: center; }
                    .text-center { text-align: center; }
                    .text-right { text-align: right; }
                  `}
                </style>
                <ReportHeader
                  reportTitle="業務負責人一覽表"
                  currentPage={1}
                  totalPages={1}
                  isPrintMode={true}
                />

                <table
                  className="report-table"
                  style={{ width: "100%", borderCollapse: "collapse" }}
                >
                  <thead>
                    <tr>
                      <th style={{ whiteSpace: "nowrap" }}>序號</th>
                      <th style={{ whiteSpace: "nowrap" }}>業務名稱</th>
                      <th style={{ whiteSpace: "nowrap" }}>主辦</th>
                      {Array.from({
                        length: Math.max(1, maxSecondaryOwners),
                      }).map((_, i) => (
                        <th key={`s-${i}`} style={{ whiteSpace: "nowrap" }}>
                          協辦{i + 1}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {filteredData.map((row, idx) => (
                      <tr key={row.positionId}>
                        <td className="text-center">{idx + 1}</td>
                        <td>{row.positionName}</td>
                        <td>
                          {(row.owners || [])
                            .filter(
                              (o) =>
                                o.roleType === "主辦" && o.status !== "卸任業務"
                            )
                            .map((o, i) => (
                              <div key={`p-line-${row.positionId}-${i}`}>
                                {(o.departmentName
                                  ? `${o.departmentName} - ${o.userName}`
                                  : o.userName) +
                                  (o.altPhone ? ` (分機:${o.altPhone})` : "")}
                              </div>
                            ))}
                        </td>
                        {(() => {
                          const seconds = (row.owners || [])
                            .filter(
                              (o) =>
                                o.roleType === "協辦" && o.status !== "卸任業務"
                            )
                            .sort((a, b) => a.orderNo - b.orderNo);
                          const cells = [] as React.ReactNode[];
                          for (
                            let i = 0;
                            i < Math.max(1, maxSecondaryOwners);
                            i++
                          ) {
                            const o = seconds[i];
                            cells.push(
                              <td key={`s-${row.positionId}-${i}`}>
                                {o
                                  ? (o.departmentName
                                    ? `${o.departmentName} - ${o.userName}`
                                    : o.userName) +
                                  (o.altPhone ? ` (分機:${o.altPhone})` : "")
                                  : ""}
                              </td>
                            );
                          }
                          return cells;
                        })()}
                      </tr>
                    ))}
                  </tbody>
                </table>

                {selectedSignatureTemplateId && (
                  <div style={{ marginTop: 24 }}>
                    <ReportFooter
                      selectedTemplate={(signatureTemplates || []).find(
                        (t: any) => t.id === selectedSignatureTemplateId
                      )}
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        </Card>
      </Card>

      {/* 業務編輯 Modal */}
      <Modal
        title={editingPosition ? "編輯業務" : "新增業務"}
        open={isPositionModalVisible}
        onOk={handlePositionSubmit}
        onCancel={() => setIsPositionModalVisible(false)}
        okText="確定"
        cancelText="取消"
      >
        <Form form={positionForm} layout="vertical">
          <Form.Item
            name="name"
            label="業務名稱"
            rules={[{ required: true, message: "請輸入業務名稱" }]}
          >
            <Input placeholder="請輸入業務名稱" />
          </Form.Item>
          <Form.Item
            name="sortCode"
            label="排序編號"
            rules={[{ required: true, message: "請輸入排序編號" }]}
          >
            <InputNumber
              placeholder="自動編號"
              style={{ width: "100%" }}
              min={0}
              readOnly={!editingPosition}
              disabled={!editingPosition}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 職務負責人編輯 Modal */}
      <PositionOwnerEditModal
        visible={isOwnerModalVisible}
        onClose={() => setIsOwnerModalVisible(false)}
        onSuccess={handleOwnerEditSuccess}
        mode={ownerEditMode}
        positionOwnerId={ownerEditMode === "edit" ? selectedPositionOwnerId : undefined}
        defaultPositionId={ownerEditMode === "add" ? selectedPositionId : undefined}
        defaultRoleType={ownerEditMode === "add" ? selectedRoleType : undefined}
      />

      {/* 員工職務列表 Modal */}
      <EmployeePositionsModal
        visible={isEmployeePositionsModalVisible}
        onClose={() => setIsEmployeePositionsModalVisible(false)}
        userId={selectedUserId}
        onDataChange={handleOwnerEditSuccess}
      />

      {/* Timeline Modal */}
      <Modal
        title={
          <Space>
            <ClockCircleOutlined />
            {selectedPositionForTimeline?.positionName} - 業務負責人異動記錄
          </Space>
        }
        open={isTimelineModalVisible}
        onCancel={() => setIsTimelineModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsTimelineModalVisible(false)}>
            關閉
          </Button>,
        ]}
        width={800}
      >
        <div style={{ maxHeight: "60vh", overflowY: "auto" }}>
          {generateTimelineData.length > 0 ? (
            <>
              <div style={{ marginBottom: 16, color: "#666" }}></div>
              <div style={{ padding: 3, color: "#666" }}>
                <Timeline mode="left" items={generateTimelineData} />
              </div>
            </>
          ) : (
            <Empty
              description="暫無時間軸資料"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </div>
      </Modal>
    </div>
  );
};

export default PositionsPage;
