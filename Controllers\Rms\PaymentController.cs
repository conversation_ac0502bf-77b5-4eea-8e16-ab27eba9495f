using FAST_ERP_Backend.Interfaces.Rms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Rms;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace FAST_ERP_Backend.Controllers.Rms
{
    /// <summary>
    /// 繳費管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class PaymentController : ControllerBase
    {
        private readonly IPaymentService _paymentService;

        /// <summary>
        /// 初始化繳費控制器
        /// </summary>
        /// <param name="paymentService">繳費服務</param>
        public PaymentController(IPaymentService paymentService)
        {
            _paymentService = paymentService;
        }

        private ClaimsPrincipal LoginUser => HttpContext.User;

        [HttpGet]
        [ProducesResponseType(typeof(ApiResponse<List<PaymentDTO>>), 200)]
        public async Task<IActionResult> GetPayments([FromQuery, Required] string contractId)
        {
            var result = await _paymentService.GetPaymentsAsync(contractId);
            return result.Success ? Ok(result) : StatusCode(result.HttpCode, result);
        }

        [HttpPost]
        [ProducesResponseType(typeof(ApiResponse<string>), 200)]
        public async Task<IActionResult> CreatePayment([FromBody] PaymentCreateRequest request)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier)!.Value;
            var result = await _paymentService.CreatePaymentAsync(request, tokenUid);
            return result.Success ? Ok(result) : StatusCode(result.HttpCode, result);
        }
    }
}


