/**
 * 統一的 Contact 管理 Hook
 * 
 * 集中管理所有 Contact 相關的狀態和操作邏輯
 * 提供統一的 CRUD 操作接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useState, useCallback, useEffect } from 'react';
import { message } from 'antd';
import { 
  Contact, 
  getContactList, 
  addContact, 
  updateContact, 
  deleteContact,
  searchContacts 
} from '@/services/ims/ContactService';
import { createEmptyContact } from '@/app/ims/components/shared/contactUtils';
import { validateContactData } from '@/app/ims/components/contact/shared/contactValidation';
import { logger, createContextLogger } from '@/utils/logger';

// 創建上下文日誌器
const contactHookLogger = createContextLogger({ module: 'useContactManagement' });

export interface ContactManagementState {
  contacts: Contact[];
  loading: boolean;
  selectedContact: Contact | null;
  searchResults: Contact[];
  searchLoading: boolean;
  formVisible: boolean;
  formMode: 'create' | 'edit' | 'view';
  formLoading: boolean;
}

export interface ContactManagementActions {
  // 列表操作
  loadContacts: () => Promise<void>;
  refreshContacts: () => Promise<void>;
  
  // 搜索操作
  searchContactsAsync: (query: string) => Promise<void>;
  clearSearchResults: () => void;
  
  // 表單操作
  openCreateForm: () => void;
  openEditForm: (contact: Contact) => void;
  openViewForm: (contact: Contact) => void;
  closeForm: () => void;
  
  // CRUD 操作
  createContact: (contactData: Partial<Contact>) => Promise<boolean>;
  updateContactData: (contactData: Contact) => Promise<boolean>;
  deleteContactData: (contactId: string) => Promise<boolean>;
  
  // 選擇操作
  selectContact: (contact: Contact | null) => void;
}

export interface UseContactManagementOptions {
  autoLoad?: boolean;
  onContactCreated?: (contact: Contact) => void;
  onContactUpdated?: (contact: Contact) => void;
  onContactDeleted?: (contactId: string) => void;
}

export const useContactManagement = (
  options: UseContactManagementOptions = {}
): [ContactManagementState, ContactManagementActions] => {
  const { autoLoad = true, onContactCreated, onContactUpdated, onContactDeleted } = options;

  // 狀態管理
  const [state, setState] = useState<ContactManagementState>({
    contacts: [],
    loading: false,
    selectedContact: null,
    searchResults: [],
    searchLoading: false,
    formVisible: false,
    formMode: 'create',
    formLoading: false,
  });

  // 更新狀態的輔助函數
  const updateState = useCallback((updates: Partial<ContactManagementState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // 載入聯絡人列表
  const loadContacts = useCallback(async () => {
    try {
      updateState({ loading: true });
      contactHookLogger.info('開始載入聯絡人列表');
      
      const response = await getContactList();
      if (response.success) {
        updateState({ 
          contacts: response.data || [],
          loading: false 
        });
        contactHookLogger.info(`成功載入 ${response.data?.length || 0} 個聯絡人`);
      } else {
        throw new Error(response.message || '載入聯絡人列表失敗');
      }
    } catch (error) {
      contactHookLogger.error('載入聯絡人列表失敗', error);
      message.error('載入聯絡人列表失敗');
      updateState({ loading: false });
    }
  }, [updateState]);

  // 刷新聯絡人列表
  const refreshContacts = useCallback(async () => {
    await loadContacts();
  }, [loadContacts]);

  // 搜索聯絡人
  const searchContactsAsync = useCallback(async (query: string) => {
    if (!query.trim()) {
      updateState({ searchResults: [], searchLoading: false });
      return;
    }

    try {
      updateState({ searchLoading: true });
      contactHookLogger.info(`搜索聯絡人: ${query}`);
      
      const response = await searchContacts(query);
      if (response.success) {
        updateState({ 
          searchResults: response.data || [],
          searchLoading: false 
        });
        contactHookLogger.info(`搜索到 ${response.data?.length || 0} 個聯絡人`);
      } else {
        throw new Error(response.message || '搜索聯絡人失敗');
      }
    } catch (error) {
      contactHookLogger.error('搜索聯絡人失敗', error);
      message.error('搜索聯絡人失敗');
      updateState({ searchLoading: false });
    }
  }, [updateState]);

  // 清除搜索結果
  const clearSearchResults = useCallback(() => {
    updateState({ searchResults: [], searchLoading: false });
  }, [updateState]);

  // 表單操作
  const openCreateForm = useCallback(() => {
    updateState({
      formVisible: true,
      formMode: 'create',
      selectedContact: createEmptyContact() as Contact
    });
  }, [updateState]);

  const openEditForm = useCallback((contact: Contact) => {
    updateState({
      formVisible: true,
      formMode: 'edit',
      selectedContact: contact
    });
  }, [updateState]);

  const openViewForm = useCallback((contact: Contact) => {
    updateState({
      formVisible: true,
      formMode: 'view',
      selectedContact: contact
    });
  }, [updateState]);

  const closeForm = useCallback(() => {
    updateState({
      formVisible: false,
      selectedContact: null,
      formLoading: false
    });
  }, [updateState]);

  // CRUD 操作
  const createContact = useCallback(async (contactData: Partial<Contact>): Promise<boolean> => {
    try {
      updateState({ formLoading: true });
      
      // 驗證資料
      const validation = validateContactData(contactData);
      if (!validation.isValid) {
        message.error(validation.errors.join(', '));
        updateState({ formLoading: false });
        return false;
      }

      contactHookLogger.info('創建聯絡人', contactData);
      const response = await addContact(contactData as Contact);
      
      if (response.success) {
        message.success('聯絡人創建成功');
        await refreshContacts();
        closeForm();
        if (response.data) {
          onContactCreated?.(response.data);
        }
        contactHookLogger.info('聯絡人創建成功', response.data);
        return true;
      } else {
        throw new Error(response.message || '創建聯絡人失敗');
      }
    } catch (error) {
      contactHookLogger.error('創建聯絡人失敗', error);
      message.error('創建聯絡人失敗');
      updateState({ formLoading: false });
      return false;
    }
  }, [updateState, refreshContacts, closeForm, onContactCreated]);

  const updateContactData = useCallback(async (contactData: Contact): Promise<boolean> => {
    try {
      updateState({ formLoading: true });
      
      // 驗證資料
      const validation = validateContactData(contactData);
      if (!validation.isValid) {
        message.error(validation.errors.join(', '));
        updateState({ formLoading: false });
        return false;
      }

      contactHookLogger.info('更新聯絡人', contactData);
      const response = await updateContact(contactData);
      
      if (response.success) {
        message.success('聯絡人更新成功');
        await refreshContacts();
        closeForm();
        onContactUpdated?.(contactData);
        contactHookLogger.info('聯絡人更新成功', contactData);
        return true;
      } else {
        throw new Error(response.message || '更新聯絡人失敗');
      }
    } catch (error) {
      contactHookLogger.error('更新聯絡人失敗', error);
      message.error('更新聯絡人失敗');
      updateState({ formLoading: false });
      return false;
    }
  }, [updateState, refreshContacts, closeForm, onContactUpdated]);

  const deleteContactData = useCallback(async (contactId: string): Promise<boolean> => {
    try {
      contactHookLogger.info(`刪除聯絡人: ${contactId}`);
      const response = await deleteContact(contactId);
      
      if (response.success) {
        message.success('聯絡人刪除成功');
        await refreshContacts();
        onContactDeleted?.(contactId);
        contactHookLogger.info(`聯絡人刪除成功: ${contactId}`);
        return true;
      } else {
        throw new Error(response.message || '刪除聯絡人失敗');
      }
    } catch (error) {
      contactHookLogger.error('刪除聯絡人失敗', error);
      message.error('刪除聯絡人失敗');
      return false;
    }
  }, [refreshContacts, onContactDeleted]);

  // 選擇操作
  const selectContact = useCallback((contact: Contact | null) => {
    updateState({ selectedContact: contact });
  }, [updateState]);

  // 自動載入
  useEffect(() => {
    if (autoLoad) {
      loadContacts();
    }
  }, [autoLoad, loadContacts]);

  // 組裝 actions
  const actions: ContactManagementActions = {
    loadContacts,
    refreshContacts,
    searchContactsAsync,
    clearSearchResults,
    openCreateForm,
    openEditForm,
    openViewForm,
    closeForm,
    createContact,
    updateContactData,
    deleteContactData,
    selectContact,
  };

  return [state, actions];
};

export default useContactManagement;
