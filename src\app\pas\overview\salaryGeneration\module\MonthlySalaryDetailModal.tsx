'use client';

import React, { useState, useEffect } from 'react';
import { Modal, Row, Col, message, Spin, Button, Form, Space, Popconfirm } from 'antd';
import { EditOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { ApiResponse } from '@/config/api';
import {
    MonthlySalary,
    SalaryAdjustment,
    HensureInsurance,
    getMonthlySalaryDetail,
    editMonthlySalary,
    addMonthlySalary,
} from '@/services/pas/MonthlySalary/MonthlySalaryService';
import { getSalaryPointAmountByUserId } from '@/services/pas/SalaryPointService';
import { getDepartments } from '@/services/common/departmentService';
import { getAllowanceTypeOptions } from '@/services/pas/OptionParameterService';
import { getEmployeeDetail } from '@/services/pas/EmployeeService';
import {
    getInsuranceGradeList,
    getInsuranceGradeDetail,
    InsuranceGrade,
    INSURANCE_TYPES
} from '@/services/pas/Insurance/InsuranceGradeService';
import { getAllEffectiveInsuranceGrades } from '@/services/pas/Insurance/InsuranceHistoryService';

// 匯入組件
import EmployeeCurrentDataCard from '@/app/pas/employee_main/employee_modules/employee/EmployeeCurrentDataCard';
import BasicInfoCard from '../components/MonthlySalaryDetail/BasicInfoCard';
import SalaryDetailCard from '../components/MonthlySalaryDetail/SalaryDetailCard';
import InsuranceCard from '../components/MonthlySalaryDetail/InsuranceCard';
import SalaryAdjustmentCard from '../components/MonthlySalaryDetail/SalaryAdjustmentCard';
import HensureInsuranceCard from '../components/MonthlySalaryDetail/HensureInsuranceCard';

type ModalMode = 'view' | 'edit' | 'add';

interface MonthlySalaryDetailModalProps {
    open: boolean;
    onCancel: () => void;
    monthlySalaryUid: string;
    mode?: ModalMode; // 模式：view=檢視, edit=編輯, add=新增  
    onSuccess?: () => void;
    // 新增：外部傳入的員工編號（僅在新增模式使用）
    externalUserId?: string;
}

const MonthlySalaryDetailModal: React.FC<MonthlySalaryDetailModalProps> = ({
    open,
    onCancel,
    monthlySalaryUid,
    mode = 'view',
    onSuccess,
    externalUserId,
}) => {
    const [loading, setLoading] = useState(false);
    const [monthlySalary, setMonthlySalary] = useState<MonthlySalary | null>(null);
    const [adjustments, setAdjustments] = useState<SalaryAdjustment[]>([]);
    const [hensureInsurances, setHensureInsurances] = useState<HensureInsurance[]>([]);
    const [currentMode, setCurrentMode] = useState<ModalMode>(mode);
    const [departments, setDepartments] = useState<any[]>([]);
    const [allowanceTypes, setAllowanceTypes] = useState<any[]>([]);

    const [selectedEmployee, setSelectedEmployee] = useState<any>(null);
    const [adjustmentTotal, setAdjustmentTotal] = useState(0);
    const [hensureTotal, setHensureTotal] = useState(0);
    const [insuranceGrades, setInsuranceGrades] = useState<{
        labor: InsuranceGrade[];
        health: InsuranceGrade[];
        accident: InsuranceGrade[];
    }>({
        labor: [],
        health: [],
        accident: []
    });

    // 表單實例
    const [form] = Form.useForm();

    // 載入選項資料
    const loadOptions = async () => {
        try {
            const [deptResponse, allowanceResponse] = await Promise.all([
                getDepartments(),
                getAllowanceTypeOptions(),
            ]);

            if (deptResponse.success) {
                setDepartments(deptResponse.data || []);
            }

            if (allowanceResponse.success) {
                setAllowanceTypes(allowanceResponse.data || []);
            }

            // 載入保險級距選項
            await loadInsuranceGrades();
        } catch (error) {
            message.error('載入選項資料失敗');
        }
    };

    // 載入保險級距選項
    const loadInsuranceGrades = async () => {
        try {
            const [laborResponse, healthResponse, accidentResponse] = await Promise.all([
                getInsuranceGradeList(INSURANCE_TYPES.LABOR),
                getInsuranceGradeList(INSURANCE_TYPES.HEALTH),
                getInsuranceGradeList(INSURANCE_TYPES.ACCIDENT),
            ]);

            setInsuranceGrades({
                labor: laborResponse.success ? (laborResponse.data || []) : [],
                health: healthResponse.success ? (healthResponse.data || []) : [],
                accident: accidentResponse.success ? (accidentResponse.data || []) : [],
            });
        } catch (error) {
            message.error('載入保險級距選項失敗');
        }
    };



    // 取得月薪詳細資料
    const fetchMonthlySalaryDetail = async (uid: string) => {
        setLoading(true);
        try {
            const response = await getMonthlySalaryDetail(uid);
            if (response.success && response.data) {
                setMonthlySalary(response.data);

                // 設定子項目資料 - 如果沒有資料則設為空陣列
                const salaryAdjustments = response.data.salaryAdjustments || [];
                const hensureInsurances = response.data.hensureInsurances || [];

                setAdjustments(salaryAdjustments);
                setHensureInsurances(hensureInsurances);

                // 計算總額
                const adjTotal = salaryAdjustments.reduce((sum, adj) => sum + adj.amount, 0);
                const hensureTotal = hensureInsurances.reduce((sum, hens) => sum + hens.hensureHealthInsurancePremium, 0);
                setAdjustmentTotal(adjTotal);
                setHensureTotal(hensureTotal);

                // 設定表單初始值
                form.setFieldsValue({
                    payMonth: response.data.payMonth ? dayjs(response.data.payMonth) : null,
                    serviceDepartmentUid: response.data.serviceDepartmentUid,
                    expenseDepartmentUid: response.data.expenseDepartmentUid,
                    salaryType: response.data.salaryType,
                    baseSalary: response.data.baseSalary,
                    baseSalaryAmount: response.data.baseSalaryAmount,
                    basicSalaryAmount: response.data.basicSalaryAmount,
                    allowanceType: response.data.allowanceType,
                    allowance: response.data.allowance,
                    netIncome: response.data.netIncome,
                    taxableAmount: response.data.taxableAmount,
                    incomeTax: response.data.incomeTax,
                    employeeContribution: response.data.employeeContribution,
                    employerContribution: response.data.employerContribution,
                    laborInsuranceLevel: response.data.laborInsuranceLevel,
                    healthInsuranceLevel: response.data.healthInsuranceLevel,
                    occupationalHazardLevel: response.data.occupationalHazardLevel,
                    laborInsurancePremium: response.data.laborInsurancePremium,
                    healthInsurancePremium: response.data.healthInsurancePremium,
                    occupationalHazardPremium: response.data.occupationalHazardPremium,
                    calculationStartDate: response.data.calculationStartDate ? dayjs(response.data.calculationStartDate) : null,
                    calculationEndDate: response.data.calculationEndDate ? dayjs(response.data.calculationEndDate) : null,
                });

                // 編輯模式：載入員工詳細資料以供參考
                if (response.data.userId) {
                    await loadEmployeeForReference(response.data.userId);
                }

            } else {
                // 如果沒有資料，設定所有值為null或空陣列
                setMonthlySalary(null);
                setAdjustments([]);
                setHensureInsurances([]);
                setAdjustmentTotal(0);
                setHensureTotal(0);

                // 清空表單
                form.resetFields();

                // 顯示錯誤訊息
                const errorMsg = response.message || '取得月薪詳細資料失敗';
                message.error(errorMsg);
                console.error('月薪詳細資料載入失敗:', errorMsg);
            }
        } catch (error) {
            // 發生錯誤時，設定所有值為null或空陣列
            setMonthlySalary(null);
            setAdjustments([]);
            setHensureInsurances([]);
            setAdjustmentTotal(0);
            setHensureTotal(0);

            // 清空表單
            form.resetFields();

            // 顯示錯誤訊息
            const errorMsg = '取得月薪詳細資料失敗';
            message.error(errorMsg);
            console.error('月薪詳細資料載入失敗:', error);
        } finally {
            setLoading(false);
        }
    };



    // 統一儲存所有資料
    const handleSaveAllData = async () => {
        try {
            const values = await form.validateFields();

            // 準備薪資加減項資料
            const salaryAdjustmentsData = adjustments.map(adj => ({
                uid: adj.uid.startsWith('temp_') ? '' : adj.uid,
                monthlySalaryUid: '',
                itemName: adj.itemName,
                adjustmentType: adj.adjustmentType,
                amount: adj.amount,
                isTaxable: adj.isTaxable,
                remark: adj.remark,
            }));

            // 準備眷屬依附保費資料
            const hensureInsurancesData = hensureInsurances.map(hens => ({
                uid: hens.uid.startsWith('temp_') ? '' : hens.uid,
                monthlySalaryUid: '',
                hensureUid: hens.hensureUid,
                hensureHealthInsurancePremium: hens.hensureHealthInsurancePremium,
                remark: hens.remark,
            }));

            // 統一準備資料結構
            const baseData = {
                payMonth: values.payMonth ? values.payMonth.format('YYYY-MM') : dayjs().format('YYYY-MM'),
                userId: externalUserId,
                serviceDepartmentUid: values.serviceDepartmentUid,
                expenseDepartmentUid: values.expenseDepartmentUid,
                salaryType: values.salaryType,
                baseSalary: values.baseSalary,
                baseSalaryAmount: values.baseSalaryAmount,
                basicSalaryAmount: values.basicSalaryAmount,
                allowanceType: values.allowanceType,
                allowance: values.allowance,
                netIncome: values.netIncome,
                taxableAmount: values.taxableAmount,
                incomeTax: values.incomeTax,
                employeeContribution: values.employeeContribution,
                employerContribution: values.employerContribution,
                laborInsuranceLevel: values.laborInsuranceLevel,
                healthInsuranceLevel: values.healthInsuranceLevel,
                occupationalHazardLevel: values.occupationalHazardLevel,
                laborInsurancePremium: values.laborInsurancePremium,
                healthInsurancePremium: values.healthInsurancePremium,
                occupationalHazardPremium: values.occupationalHazardPremium,
                calculationStartDate: values.calculationStartDate ? values.calculationStartDate.format('YYYY-MM-DD') : undefined,
                calculationEndDate: values.calculationEndDate ? values.calculationEndDate.format('YYYY-MM-DD') : undefined,
                // 包含子項目
                salaryAdjustments: salaryAdjustmentsData,
                hensureInsurances: hensureInsurancesData,
            };

            let response: ApiResponse | undefined; // 統一回復資料

            if (currentMode === 'add') {
                // 新增模式：呼叫 Add API
                const addRequest = {
                    ...baseData,
                    uid: '', // 新增時 uid 為空
                };
                response = await addMonthlySalary(addRequest);

            } else if (currentMode === 'edit') {
                // 編輯模式：呼叫 Edit API
                const editRequest = {
                    ...baseData,
                    uid: monthlySalaryUid, // 編輯時需要 uid
                };
                response = await editMonthlySalary(editRequest);

            }

            if (response && response.success) {
                message.success(currentMode === 'add' ? '新增成功' : '儲存成功');

                if (currentMode === 'add') {
                    onCancel();
                } else {
                    setCurrentMode('view');
                    // 重新載入資料
                    await fetchMonthlySalaryDetail(monthlySalaryUid);
                }

                onSuccess?.();
            } else {
                message.error(response?.message || (currentMode === 'add' ? '新增失敗' : '儲存失敗'));
            }
        } catch (error) {
            message.error(currentMode === 'add' ? '新增失敗' : '儲存失敗');
        }
    };

    // 當 Modal 開啟時載入資料
    useEffect(() => {
        if (open) {
            // 設定當前模式
            setCurrentMode(mode);

            // 每次開啟都重新載入基礎選項
            loadOptions();

            if (mode === 'add') {
                // 新增模式：重置所有狀態並載入指定員工資料
                setMonthlySalary(null);
                setAdjustments([]);
                setHensureInsurances([]);
                setSelectedEmployee(null);
                setAdjustmentTotal(0);
                setHensureTotal(0);
                form.resetFields();
                // 設定新增模式的預設值
                form.setFieldsValue({
                    payMonth: dayjs(), // 預設為當前月份
                    basicSalaryAmount: 0,
                    taxableAmount: 0,
                    incomeTax: 0,
                    netIncome: 0,
                    employeeContribution: 0,
                    employerContribution: 0,
                    laborInsuranceLevel: 0,
                    healthInsuranceLevel: 0,
                    occupationalHazardLevel: 0,
                    laborInsurancePremium: 0,
                    healthInsurancePremium: 0,
                    occupationalHazardPremium: 0,
                    allowance: 0,
                    baseSalary: 0,
                    baseSalaryAmount: 0,
                });

                // 如果有外部傳入的員工編號，則驗證並載入該員工資料
                if (externalUserId) {
                    validateAndLoadEmployee(externalUserId);
                } else {
                    message.error('新增模式需要指定員工編號');
                    onCancel(); // 關閉 Modal
                }
            } else if (monthlySalaryUid && (mode === 'view' || mode === 'edit')) {
                // 檢視/編輯模式：根據 UID 載入完整資料（包含子項目）
                fetchMonthlySalaryDetail(monthlySalaryUid);
            }
        }
    }, [open, monthlySalaryUid, mode, externalUserId]);

    // 新增：驗證並載入員工資料
    const validateAndLoadEmployee = async (userId: string) => {
        setLoading(true);
        try {
            const response = await getEmployeeDetail(userId);
            if (response.success && response.data) {
                // 員工存在，設定員工資料
                setSelectedEmployee(response.data);
                message.success(`已找到員工：${response.data.usersDTO?.name || userId}`);

                // 設定員工資料作為表單預設值
                const currentDate = dayjs().format('YYYY-MM-DD');
                await setEmployeeDataAsDefaults(response.data, currentDate);
            } else {
                // 員工不存在
                message.error(`找不到員工編號：${userId}，請確認員工編號是否正確`);
                onCancel(); // 關閉 Modal
            }
        } catch (error) {
            console.error('驗證員工編號錯誤:', error);
            message.error('驗證員工編號失敗，請稍後再試');
            onCancel(); // 關閉 Modal
        } finally {
            setLoading(false);
        }
    };

    // 新增：載入員工資料以供參考（編輯模式使用）
    const loadEmployeeForReference = async (userId: string) => {
        try {
            const response = await getEmployeeDetail(userId);
            if (response.success && response.data) {
                // 設定員工資料以供參考
                setSelectedEmployee(response.data);
            } else {
                console.warn(`無法載入員工資料以供參考：${userId}`);
            }
        } catch (error) {
            console.error('載入員工資料以供參考失敗:', error);
        }
    };

    // 新增：將員工資料設定為表單預設值
    const setEmployeeDataAsDefaults = async (employee: any, baseDate?: string) => {
        if (!employee) return;

        const defaultValues: any = {
            // 基本資料預設值
            payMonth: dayjs(), // 預設為當前月份
            basicSalaryAmount: 0,
            taxableAmount: 0,
            incomeTax: 0,
            employeeContribution: 0,
            employerContribution: 0,
            laborInsuranceLevel: 0,
            healthInsuranceLevel: 0,
            occupationalHazardLevel: 0,
            laborInsurancePremium: 0,
            healthInsurancePremium: 0,
            occupationalHazardPremium: 0,
            allowance: 0,
            baseSalary: 0,
            baseSalaryAmount: 0,
            netIncome: 0,
        };

        // 設定服務部門（優先使用當前服務部門，如果沒有則使用員工的部門）
        if (employee.currentServiceDepartment?.serviceDepartmentId) {
            defaultValues.serviceDepartmentUid = employee.currentServiceDepartment.serviceDepartmentId;
        } else if (employee.usersDTO?.serviceDepartmentUid) {
            defaultValues.serviceDepartmentUid = employee.usersDTO.serviceDepartmentUid;
        }

        // 設定開支部門（優先使用當前開支部門，如果沒有則使用員工的部門）
        if (employee.currentExpenseDepartment?.expenseDepartmentId) {
            defaultValues.expenseDepartmentUid = employee.currentExpenseDepartment.expenseDepartmentId;
        } else if (employee.usersDTO?.expenseDepartmentUid) {
            defaultValues.expenseDepartmentUid = employee.usersDTO.expenseDepartmentUid;
        }

        // 設定薪資相關預設值（優先使用當前升遷資料，如果沒有則使用當前薪資資料）
        if (employee.currentPromotion) {
            // 使用升遷資料中的薪資資訊
            defaultValues.salaryType = employee.currentPromotion.salaryType || '1';
            defaultValues.baseSalary = parseFloat(employee.currentPromotion.salaryAmount) || 0;
            defaultValues.allowanceType = employee.currentPromotion.allowanceType || '0';
            defaultValues.allowance = 0; // 升遷資料中沒有加給金額，預設為0

            // 如果是薪點類型，需要設定薪點金額
            if (employee.currentPromotion.salaryType === '1') {
                // 先設定預設值
                defaultValues.baseSalaryAmount = 0;
                defaultValues.basicSalaryAmount = parseFloat(employee.currentPromotion.salaryAmount);

                // 非同步獲取薪點金額
                if (employee.userId && baseDate) {
                    try {

                        const response = await getSalaryPointAmountByUserId(employee.userId, baseDate);
                        if (response.success && response.data) {
                            defaultValues.baseSalaryAmount = response.data.salaryPointAmount;
                            // 重新計算基礎薪資 = 薪點點數 * 薪點金額
                            defaultValues.basicSalaryAmount = parseFloat(employee.currentPromotion.salaryAmount) * response.data.salaryPointAmount;
                        }
                    } catch (error) {
                        console.error('獲取薪點金額失敗:', error);
                        // 如果獲取失敗，保持預設值
                    }
                }
            } else {
                // 如果不是薪點類型，薪點金額設為0
                defaultValues.baseSalaryAmount = 0;
                defaultValues.basicSalaryAmount = parseFloat(employee.currentPromotion.salaryAmount) || 0; // 薪俸 (月薪金額) 
            }

            // 設定所得淨額（基礎薪資 + 加給）
            defaultValues.netIncome = defaultValues.basicSalaryAmount + defaultValues.allowance;

            // 設定應稅金額（預設等於所得淨額，實際應根據稅法規定計算）
            defaultValues.taxableAmount = defaultValues.netIncome;

            // 設定員工自提額和雇主提撥額（預設為0，需要根據實際規定計算）
            defaultValues.employeeContribution = 0;
            defaultValues.employerContribution = 0;

            // 設定所得稅額（預設為0，需要根據稅法規定計算）
            defaultValues.incomeTax = 0;
        } else if (employee.currentSalary) {
            // 使用薪資資料
            defaultValues.salaryType = employee.currentSalary.salaryType || '1';
            defaultValues.baseSalary = employee.currentSalary.baseSalary || 0;
            defaultValues.baseSalaryAmount = employee.currentSalary.baseSalaryAmount || 0;
            defaultValues.basicSalaryAmount = employee.currentSalary.basicSalaryAmount || 0;
            defaultValues.allowanceType = employee.currentSalary.allowanceType || '0';
            defaultValues.allowance = employee.currentSalary.allowance || 0;

            // 如果是薪點類型，需要設定薪點金額
            if (employee.currentSalary.salaryType === '1') {
                // 薪點金額通常需要根據薪點和薪點單價來計算
                // 這裡先設為0，讓使用者手動輸入或透過其他邏輯計算
                defaultValues.baseSalaryAmount = employee.currentSalary.baseSalaryAmount || 0;
            } else {
                // 如果不是薪點類型，薪點金額設為0
                defaultValues.baseSalaryAmount = 0;
            }

            // 設定所得淨額（基礎薪資 + 加給）
            defaultValues.netIncome = defaultValues.basicSalaryAmount + defaultValues.allowance;

            // 設定應稅金額（預設等於所得淨額，實際應根據稅法規定計算）
            defaultValues.taxableAmount = defaultValues.netIncome;

            // 設定員工自提額和雇主提撥額（預設為0，需要根據實際規定計算）
            defaultValues.employeeContribution = 0;
            defaultValues.employerContribution = 0;

            // 設定所得稅額（預設為0，需要根據稅法規定計算）
            defaultValues.incomeTax = 0;
        }

        // 設定保險相關預設值
        try {
            // 取得員工在目標日期的有效保險級距
            const targetDate = dayjs().format('YYYY-MM-DD'); // 使用當前日期作為目標日期
            const insuranceResponse = await getAllEffectiveInsuranceGrades(employee.userId, targetDate);

            if (insuranceResponse.success && insuranceResponse.data) {
                const effectiveGrades = insuranceResponse.data;

                // 設定勞保級距
                if (effectiveGrades[INSURANCE_TYPES.LABOR]) {
                    const laborGrade = effectiveGrades[INSURANCE_TYPES.LABOR];
                    if (laborGrade && laborGrade.insuranceGradeUid) {
                        // 根據 insuranceGradeUid 取得級距詳細資訊
                        const gradeDetailResponse = await getInsuranceGradeDetail(laborGrade.insuranceGradeUid);
                        if (gradeDetailResponse.success && gradeDetailResponse.data) {
                            defaultValues.laborInsuranceLevel = gradeDetailResponse.data.monthlySalary;
                        } else {
                            defaultValues.laborInsuranceLevel = null;
                        }
                    } else {
                        defaultValues.laborInsuranceLevel = null;
                    }
                } else {
                    defaultValues.laborInsuranceLevel = null;
                }

                // 設定健保級距
                if (effectiveGrades[INSURANCE_TYPES.HEALTH]) {
                    const healthGrade = effectiveGrades[INSURANCE_TYPES.HEALTH];
                    if (healthGrade && healthGrade.insuranceGradeUid) {
                        const gradeDetailResponse = await getInsuranceGradeDetail(healthGrade.insuranceGradeUid);
                        if (gradeDetailResponse.success && gradeDetailResponse.data) {
                            defaultValues.healthInsuranceLevel = gradeDetailResponse.data.monthlySalary;
                        } else {
                            defaultValues.healthInsuranceLevel = null;
                        }
                    } else {
                        defaultValues.healthInsuranceLevel = null;
                    }
                } else {
                    defaultValues.healthInsuranceLevel = null;
                }

                // 設定職災級距
                if (effectiveGrades[INSURANCE_TYPES.ACCIDENT]) {
                    const accidentGrade = effectiveGrades[INSURANCE_TYPES.ACCIDENT];
                    if (accidentGrade && accidentGrade.insuranceGradeUid) {
                        const gradeDetailResponse = await getInsuranceGradeDetail(accidentGrade.insuranceGradeUid);
                        if (gradeDetailResponse.success && gradeDetailResponse.data) {
                            defaultValues.occupationalHazardLevel = gradeDetailResponse.data.monthlySalary;
                        } else {
                            defaultValues.occupationalHazardLevel = null;
                        }
                    } else {
                        defaultValues.occupationalHazardLevel = null;
                    }
                } else {
                    defaultValues.occupationalHazardLevel = null;
                }
            } else {
                // 如果沒有取得到有效級距，設定為空值
                defaultValues.laborInsuranceLevel = null;
                defaultValues.healthInsuranceLevel = null;
                defaultValues.occupationalHazardLevel = null;
            }
        } catch (error) {
            console.error('取得保險級距預設值失敗:', error);
            // 如果取得失敗，設定為空值
            defaultValues.laborInsuranceLevel = null;
            defaultValues.healthInsuranceLevel = null;
            defaultValues.occupationalHazardLevel = null;
        }

        // 保險費用預設為0，需要手動輸入
        defaultValues.laborInsurancePremium = 0;
        defaultValues.healthInsurancePremium = 0;
        defaultValues.occupationalHazardPremium = 0;

        // 設定計算日期區間（預設為當月1號到月底）
        const currentDate = dayjs();
        defaultValues.calculationStartDate = currentDate.startOf('month');
        defaultValues.calculationEndDate = currentDate.endOf('month');

        // 設定表單預設值
        form.setFieldsValue(defaultValues);
    };


    // 當 Modal 關閉時清空資料
    useEffect(() => {
        if (!open) {
            // 徹底重置所有狀態
            setMonthlySalary(null);
            setAdjustments([]);
            setHensureInsurances([]);
            setCurrentMode('view');
            setSelectedEmployee(null);
            setDepartments([]);
            setAllowanceTypes([]);

            setAdjustmentTotal(0);
            setHensureTotal(0);

            // 重置表單
            form.resetFields();
        }
    }, [open]);

    // 處理進入編輯模式
    const handleEdit = () => {
        if (monthlySalaryUid) {
            setCurrentMode('edit');
        }
    };

    // 處理取消編輯
    const handleCancelEdit = () => {
        if (currentMode === 'add') {
            onCancel();
        } else {
            setCurrentMode('view');
            // 重新載入所有資料，確保取消後恢復到原始狀態
            if (monthlySalaryUid) {
                fetchMonthlySalaryDetail(monthlySalaryUid).catch(error => {
                    console.error('重新載入資料失敗:', error);
                    message.error('重新載入資料失敗');
                });
            }
        }
    };

    // 取得當前模式下的 userId (帶入員工資料卡片用)
    const getCurrentUserId = () => {
        if (currentMode === 'add') {
            // 新增模式：使用選中的員工 userId
            return selectedEmployee?.userId || '';
        } else {
            // 編輯/檢視模式：優先使用 monthlySalary 的 userId，如果沒有則使用 selectedEmployee 的
            return monthlySalary?.userId || '';
        }
    };

    const getModalTitle = () => {
        switch (currentMode) {
            case 'add':
                return '新增月薪資料';
            case 'edit':
                return '編輯月薪資料';
            case 'view':
            default:
                return '月薪明細';
        }
    };

    return (
        <Modal
            title={getModalTitle()}
            open={open}
            onCancel={onCancel}
            footer={null}
            width={1400}
            maskClosable={false}
            keyboard={false}
        >
            {/* 員工當前薪資資料區塊 */}
            <div style={{ marginBottom: 24 }}>
                <EmployeeCurrentDataCard
                    userId={getCurrentUserId()}
                    title="員工當前薪資資料"
                    size="default"
                    style={{ backgroundColor: '#f8f9fa' }}
                />
            </div>

            <Spin
                spinning={loading}
                tip="載入月薪詳細資料中，請稍候..."
                size="large"
                style={{
                    minHeight: '400px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}
            >
                {/* 新增模式或有月薪資料時顯示內容 */}
                {loading ? (
                    // Loading時顯示空白內容，讓Spin組件處理loading效果
                    <div style={{ minHeight: '400px' }} />
                ) : (currentMode === 'add' || monthlySalary) ? (
                    <>
                        {/* 編輯/新增模式：統一的表單結構 */}
                        {(currentMode === 'add' || currentMode === 'edit') && (
                            <>
                                {/* 按鈕區域 */}
                                <div style={{ marginBottom: 24, textAlign: 'right' }}>
                                    <Space>
                                        <Popconfirm
                                            title="確認儲存"
                                            description={`確定要${currentMode === 'add' ? '新增' : '儲存'}月薪資料嗎？`}
                                            onConfirm={handleSaveAllData}
                                            okText="確定"
                                            cancelText="取消"
                                        >
                                            <Button
                                                type="primary"
                                                icon={<SaveOutlined />}
                                            >
                                                {currentMode === 'add' ? '儲存' : '儲存變更'}
                                            </Button>
                                        </Popconfirm>
                                        <Button
                                            icon={<CloseOutlined />}
                                            onClick={handleCancelEdit}
                                        >
                                            取消
                                        </Button>
                                    </Space>
                                </div>

                                <Form form={form} layout="vertical">
                                    {/* 基本資料、薪資明細和保險編輯區域 - 三個並排 */}
                                    <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
                                        <Col xs={24} md={8}>
                                            <BasicInfoCard
                                                mode={currentMode}
                                                monthlySalary={monthlySalary}
                                                selectedEmployee={selectedEmployee} // 新增模式使用
                                                departments={departments}
                                                form={form}
                                            />
                                        </Col>
                                        <Col xs={24} md={8}>
                                            <SalaryDetailCard
                                                mode={currentMode}
                                                monthlySalary={monthlySalary}
                                                allowanceTypes={allowanceTypes}
                                                form={form}
                                                adjustmentTotal={adjustmentTotal}
                                                hensureTotal={hensureTotal}
                                            />
                                        </Col>
                                        <Col xs={24} md={8}>
                                            <InsuranceCard
                                                mode={currentMode}
                                                monthlySalary={monthlySalary}
                                                insuranceGrades={insuranceGrades}
                                                form={form}
                                            />
                                        </Col>
                                    </Row>
                                </Form>

                                {/* 薪資加減項和眷屬依附保費 */}
                                <SalaryAdjustmentCard
                                    mode={currentMode}
                                    adjustments={adjustments}
                                    onAdjustmentsChange={setAdjustments}
                                    onTotalChange={setAdjustmentTotal}
                                />

                                <HensureInsuranceCard
                                    mode={currentMode}
                                    userId={getCurrentUserId()}
                                    hensureInsurances={hensureInsurances}
                                    onHensureInsurancesChange={setHensureInsurances}
                                    onTotalChange={setHensureTotal}
                                />
                            </>
                        )}

                        {/* 檢視模式：顯示詳細資料 */}
                        {currentMode === 'view' && (
                            <>
                                {/* 編輯按鈕區域 */}
                                <div style={{ marginBottom: 24, textAlign: 'right' }}>
                                    <Button
                                        type="primary"
                                        icon={<EditOutlined />}
                                        onClick={handleEdit}
                                    >
                                        編輯基本資料
                                    </Button>
                                </div>

                                {/* 基本資料、薪資明細和保險區域 - 三個並排 */}
                                <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
                                    <Col xs={24} md={8}>
                                        <BasicInfoCard
                                            mode={currentMode}
                                            monthlySalary={monthlySalary}
                                            selectedEmployee={selectedEmployee}
                                            departments={departments}
                                            form={form}
                                        />
                                    </Col>
                                    <Col xs={24} md={8}>
                                        <SalaryDetailCard
                                            mode={currentMode}
                                            monthlySalary={monthlySalary}
                                            allowanceTypes={allowanceTypes}
                                            form={form}
                                            adjustmentTotal={adjustmentTotal}
                                            hensureTotal={hensureTotal}
                                        />
                                    </Col>
                                    <Col xs={24} md={8}>
                                        <InsuranceCard
                                            mode={currentMode}
                                            monthlySalary={monthlySalary}
                                            insuranceGrades={insuranceGrades}
                                            form={form}
                                        />
                                    </Col>
                                </Row>

                                {/* 薪資加減項和眷屬依附保費 */}
                                <SalaryAdjustmentCard
                                    mode={currentMode}
                                    adjustments={adjustments}
                                    onAdjustmentsChange={setAdjustments}
                                    onTotalChange={setAdjustmentTotal}
                                />

                                <HensureInsuranceCard
                                    mode={currentMode}
                                    userId={getCurrentUserId()}
                                    hensureInsurances={hensureInsurances}
                                    onHensureInsurancesChange={setHensureInsurances}
                                    onTotalChange={setHensureTotal}
                                />
                            </>
                        )}
                    </>
                ) : (
                    // 當沒有資料且不是新增模式時顯示錯誤訊息
                    <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        minHeight: '300px',
                        padding: '40px'
                    }}>
                        <div style={{
                            fontSize: '18px',
                            color: '#ff4d4f',
                            marginBottom: '16px',
                            fontWeight: 'bold'
                        }}>
                            無法載入月薪詳細資料
                        </div>
                        <div style={{
                            fontSize: '14px',
                            color: '#666',
                            textAlign: 'center',
                            maxWidth: '400px'
                        }}>
                            請檢查網路連線或聯絡系統管理員
                        </div>
                        <Button
                            type="primary"
                            onClick={onCancel}
                            style={{ marginTop: '24px' }}
                        >
                            關閉
                        </Button>
                    </div>
                )}
            </Spin>
        </Modal>
    );
};

export default MonthlySalaryDetailModal;