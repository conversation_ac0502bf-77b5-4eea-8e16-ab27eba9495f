# Contact 組件測試指南

## 🧪 測試建議

### 1. 快速新增模式測試

驗證 ContactFormModal 的 'quick' 模式是否包含所有必要欄位：

```typescript
// 測試快速新增模式欄位
describe('ContactFormModal - Quick Mode', () => {
  it('should display all required quick mode fields', () => {
    render(
      <ContactFormModal
        visible={true}
        mode="quick"
        onClose={() => {}}
        onSubmit={() => {}}
      />
    );
    
    // 驗證必要欄位存在
    expect(screen.getByLabelText('姓名')).toBeInTheDocument();
    expect(screen.getByLabelText('電子郵件')).toBeInTheDocument();
    expect(screen.getByLabelText('電話')).toBeInTheDocument();
    expect(screen.getByLabelText('職位')).toBeInTheDocument();
    expect(screen.getByLabelText('聯絡人類型')).toBeInTheDocument();
    expect(screen.getByLabelText('狀態')).toBeInTheDocument();
  });
});
```

### 2. PartnerContact 驗證測試

驗證 PartnerContact 關聯欄位的驗證規則：

```typescript
// 測試 PartnerContact 驗證規則
describe('PartnerContact Validation', () => {
  it('should not require priority field', async () => {
    render(
      <ContactFormModal
        visible={true}
        mode="quick"
        partnerID="test-partner-id"
        showPartnerContactFields={true}
        onClose={() => {}}
        onSubmit={() => {}}
      />
    );
    
    // 填寫聯絡人基本資訊
    fireEvent.change(screen.getByLabelText('姓名'), { target: { value: '測試聯絡人' } });
    fireEvent.change(screen.getByLabelText('電子郵件'), { target: { value: '<EMAIL>' } });
    
    // 不填寫優先順序，應該可以提交
    fireEvent.click(screen.getByText('確定'));
    
    // 驗證沒有優先順序必填錯誤
    expect(screen.queryByText('請輸入優先順序')).not.toBeInTheDocument();
  });

  it('should not require role field', async () => {
    // 類似測試角色欄位不是必填
  });
});
```

### 3. 聯絡人選擇器測試

測試 ContactSelector 的搜尋和選擇功能：

```typescript
// 測試聯絡人選擇器
describe('ContactSelector', () => {
  it('should search and display contacts', async () => {
    const mockContacts = [
      { contactID: '1', name: '張三', email: '<EMAIL>' },
      { contactID: '2', name: '李四', email: '<EMAIL>' }
    ];
    
    // Mock API 回應
    jest.spyOn(ContactService, 'searchContacts').mockResolvedValue({
      success: true,
      data: mockContacts
    });
    
    render(
      <ContactSelector
        onChange={() => {}}
        placeholder="搜尋聯絡人"
      />
    );
    
    // 輸入搜尋文字
    fireEvent.change(screen.getByPlaceholderText('搜尋聯絡人'), { 
      target: { value: '張' } 
    });
    
    // 等待搜尋結果
    await waitFor(() => {
      expect(screen.getByText('張三')).toBeInTheDocument();
    });
  });
});
```

### 4. 響應式設計測試

測試組件在不同螢幕尺寸下的表現：

```typescript
// 測試響應式設計
describe('Responsive Design', () => {
  it('should adapt to mobile screen size', () => {
    // 模擬移動裝置螢幕
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });
    
    render(<ContactFormModal visible={true} mode="create" />);
    
    // 驗證移動裝置適配
    const modal = screen.getByRole('dialog');
    expect(modal).toHaveStyle({ width: '95%' });
  });
});
```

## 🔧 手動測試檢查清單

### ContactFormModal 測試

- [ ] **快速新增模式**
  - [ ] 顯示姓名、電子郵件、電話、職位、聯絡人類型、狀態欄位
  - [ ] 所有欄位都可以正常輸入
  - [ ] 表單驗證正確運作

- [ ] **完整新增模式**
  - [ ] 顯示所有聯絡人欄位
  - [ ] 欄位分組清晰
  - [ ] 響應式佈局正常

- [ ] **編輯模式**
  - [ ] 正確載入現有聯絡人資料
  - [ ] 可以修改所有欄位
  - [ ] 儲存功能正常

### PartnerContact 關聯測試

- [ ] **驗證規則**
  - [ ] 優先順序欄位不是必填
  - [ ] 角色欄位不是必填
  - [ ] 預設值正確設定（優先順序 = 99）

- [ ] **關聯功能**
  - [ ] 可以成功建立 PartnerContact 關聯
  - [ ] 關聯資料正確儲存
  - [ ] 關聯列表正確顯示

### ContactSelector 測試

- [ ] **搜尋功能**
  - [ ] 自動完成搜尋正常運作
  - [ ] 搜尋結果正確顯示
  - [ ] 可以選擇搜尋結果

- [ ] **模態框選擇**
  - [ ] 瀏覽按鈕正常運作
  - [ ] 模態框正確開啟
  - [ ] 可以從列表中選擇聯絡人

- [ ] **新增功能**
  - [ ] 新增按鈕正常運作
  - [ ] 新增流程順暢

### 響應式設計測試

- [ ] **移動裝置 (≤768px)**
  - [ ] 模態框寬度適配
  - [ ] 按鈕大小適當
  - [ ] 表單佈局正確

- [ ] **平板裝置 (769-1024px)**
  - [ ] 欄位排列合理
  - [ ] 間距適當

- [ ] **桌面裝置 (>1024px)**
  - [ ] 完整功能顯示
  - [ ] 最佳使用體驗

## 🚀 自動化測試執行

```bash
# 執行所有 Contact 相關測試
npm test -- --testPathPattern=contact

# 執行特定組件測試
npm test -- ContactFormModal.test.tsx
npm test -- ContactSelector.test.tsx
npm test -- PartnerContactManager.test.tsx

# 執行測試覆蓋率檢查
npm test -- --coverage --testPathPattern=contact
```

## 📊 測試覆蓋率目標

- **單元測試覆蓋率**: ≥ 80%
- **整合測試覆蓋率**: ≥ 70%
- **E2E 測試覆蓋率**: ≥ 60%

## 🐛 常見問題排查

### 1. 表單驗證問題
- 檢查驗證規則是否與後端一致
- 確認必填欄位設定正確

### 2. 搜尋功能問題
- 檢查 API 回應格式
- 確認搜尋參數正確傳遞

### 3. 響應式問題
- 檢查斷點設定
- 確認 CSS 樣式正確應用

### 4. 效能問題
- 檢查是否有不必要的重新渲染
- 確認 API 呼叫次數合理
