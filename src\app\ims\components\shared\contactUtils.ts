/**
 * 聯絡人工具函數
 * 
 * 統一的聯絡人處理工具，對應後端 Tools/ 中的工具類別
 * 包含資料正規化、搜尋、篩選等功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Contact } from '@/services/ims/ContactService';
import { PartnerContact } from '@/services/ims/partner';
import { searchContacts } from '@/services/ims/ContactService';
import { CONTACT_DEFAULTS } from '../contact/shared/contactConstants';

/**
 * 創建空的聯絡人物件
 * 對應後端 ContactService.createEmptyContact()
 */
export const createEmptyContact = (): Partial<Contact> => ({
  contactID: '',
  name: '',
  position: '',
  email: '',
  phone: '',
  department: '',
  company: '',
  contactType: CONTACT_DEFAULTS.contactType,
  isActive: CONTACT_DEFAULTS.isActive,
  createTime: null,
  createUserId: null,
  updateTime: null,
  updateUserId: null,
  deleteTime: null,
  deleteUserId: null,
  isDeleted: false
});

/**
 * 創建聯絡人預設值（支援覆蓋）
 * 
 * @param overrides 要覆蓋的欄位值
 * @returns 合併後的聯絡人物件
 */
export const makeContactDefaults = (overrides: Partial<Contact> = {}): Partial<Contact> => ({
  ...createEmptyContact(),
  ...overrides
});

/**
 * 正規化聯絡人資料
 * 對應後端 ContactService.normalizeContact()
 * 
 * @param contact 原始聯絡人資料
 * @returns 正規化後的聯絡人資料
 */
export const normalizeContact = (contact: any): Contact | null => {
  if (!contact) return null;

  // 確保必要欄位存在
  if (!contact.contactID && !contact.name) {
    return null;
  }

  return {
    contactID: contact.contactID || '',
    name: contact.name || '',
    position: contact.position || '',
    email: contact.email || '',
    phone: contact.phone || '',
    department: contact.department || '',
    company: contact.company || '',
    contactType: contact.contactType || CONTACT_DEFAULTS.contactType,
    isActive: contact.isActive !== undefined ? contact.isActive : CONTACT_DEFAULTS.isActive,
    createTime: contact.createTime || null,
    createUserId: contact.createUserId || null,
    updateTime: contact.updateTime || null,
    updateUserId: contact.updateUserId || null,
    deleteTime: contact.deleteTime || null,
    deleteUserId: contact.deleteUserId || null,
    isDeleted: contact.isDeleted || false
  };
};

/**
 * 檢查聯絡人是否有效
 * 
 * @param contact 聯絡人資料
 * @returns 是否有效
 */
export const isValidContact = (contact: Partial<Contact>): boolean => {
  return !!(contact.name && contact.name.trim().length > 0);
};

/**
 * 格式化聯絡人顯示名稱
 * 
 * @param contact 聯絡人資料
 * @returns 格式化的顯示名稱
 */
export const formatContactDisplayName = (contact: Contact): string => {
  const parts = [];
  
  if (contact.name) {
    parts.push(contact.name);
  }
  
  if (contact.position) {
    parts.push(`(${contact.position})`);
  }
  
  if (contact.company) {
    parts.push(`- ${contact.company}`);
  }
  
  return parts.join(' ') || '未命名聯絡人';
};

/**
 * 聯絡人搜尋篩選邏輯
 * 統一的前端搜尋邏輯，支援多欄位搜尋和篩選
 * 
 * @param contacts 聯絡人陣列
 * @param searchText 搜尋關鍵字
 * @param filters 篩選條件
 * @returns 篩選後的聯絡人陣列
 */
export const filterContacts = (
  contacts: Contact[],
  searchText: string = '',
  filters: {
    name?: string;
    email?: string;
    contactType?: string;
    status?: boolean;
    company?: string;
    department?: string;
  } = {}
): Contact[] => {
  return contacts.filter(contact => {
    // 搜尋條件：多欄位模糊搜尋
    const matchesSearch = !searchText || 
      contact.name?.toLowerCase().includes(searchText.toLowerCase()) ||
      contact.email?.toLowerCase().includes(searchText.toLowerCase()) ||
      contact.phone?.toLowerCase().includes(searchText.toLowerCase()) ||
      contact.company?.toLowerCase().includes(searchText.toLowerCase()) ||
      contact.position?.toLowerCase().includes(searchText.toLowerCase()) ||
      contact.department?.toLowerCase().includes(searchText.toLowerCase());

    // 姓名篩選
    const matchesName = !filters.name || 
      contact.name?.toLowerCase().includes(filters.name.toLowerCase());

    // 電子郵件篩選
    const matchesEmail = !filters.email || 
      contact.email?.toLowerCase().includes(filters.email.toLowerCase());

    // 類型篩選
    const matchesType = !filters.contactType || contact.contactType === filters.contactType;

    // 狀態篩選
    const matchesStatus = filters.status === undefined || contact.isActive === filters.status;

    // 公司篩選
    const matchesCompany = !filters.company || 
      contact.company?.toLowerCase().includes(filters.company.toLowerCase());

    // 部門篩選
    const matchesDepartment = !filters.department || 
      contact.department?.toLowerCase().includes(filters.department.toLowerCase());

    return matchesSearch && matchesName && matchesEmail && matchesType && 
           matchesStatus && matchesCompany && matchesDepartment;
  });
};

/**
 * 應用聯絡人篩選器（配合 FilterSearchContainer 使用）
 * 
 * @param contacts 聯絡人陣列
 * @param searchText 搜尋文字
 * @param filterValues 篩選器值
 * @returns 篩選後的聯絡人陣列
 */
export const applyContactFilters = (
  contacts: Contact[],
  searchText: string,
  filterValues: Record<string, any>
): Contact[] => {
  return filterContacts(contacts, searchText, {
    name: filterValues.name,
    email: filterValues.email,
    contactType: filterValues.contactType,
    status: filterValues.isActive === 'true' ? true :
            filterValues.isActive === 'false' ? false : undefined,
    company: filterValues.company,
    department: filterValues.department
  });
};

/**
 * 獲取聯絡人統計資訊
 * 
 * @param contacts 聯絡人陣列
 * @returns 統計資訊
 */
export const getContactStats = (contacts: Contact[]) => {
  const total = contacts.length;
  const active = contacts.filter(c => c.isActive).length;
  const inactive = total - active;
  
  const typeStats = contacts.reduce((acc, contact) => {
    const type = contact.contactType || '未分類';
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    total,
    active,
    inactive,
    typeStats
  };
};

/**
 * 比較兩個聯絡人是否相同
 * 
 * @param contact1 聯絡人1
 * @param contact2 聯絡人2
 * @returns 是否相同
 */
export const areContactsEqual = (contact1: Contact, contact2: Contact): boolean => {
  return contact1.contactID === contact2.contactID;
};

/**
 * 從聯絡人陣列中移除指定聯絡人
 * 
 * @param contacts 聯絡人陣列
 * @param contactToRemove 要移除的聯絡人
 * @returns 移除後的陣列
 */
export const removeContactFromArray = (contacts: Contact[], contactToRemove: Contact): Contact[] => {
  return contacts.filter(contact => !areContactsEqual(contact, contactToRemove));
};

/**
 * 更新聯絡人陣列中的指定聯絡人
 * 
 * @param contacts 聯絡人陣列
 * @param updatedContact 更新後的聯絡人
 * @returns 更新後的陣列
 */
export const updateContactInArray = (contacts: Contact[], updatedContact: Contact): Contact[] => {
  return contacts.map(contact => 
    areContactsEqual(contact, updatedContact) ? updatedContact : contact
  );
};
