# SmsWebServers API 規格書

## 概述

SmsWebServers 提供 Web 伺服器的管理功能，包括查詢、新增、更新、刪除和批次操作。Web 伺服器用於部署和運行 Web 應用程式。

## API 端點

### 1. 查詢 Web 伺服器

**GET** `/api/sms/SmsWebServers`

#### 查詢參數

| 參數名稱 | 類型 | 必填 | 說明 |
|---------|------|------|------|
| keyword | string | 否 | 關鍵字搜尋 |
| environment | string | 否 | 環境 |

#### 回應

- **200 OK**: 成功查詢 Web 伺服器列表

### 2. 新增 Web 伺服器

**POST** `/api/sms/SmsWebServers`

#### 請求內容

```json
{
  "createTime": 0,
  "createUserId": "string",
  "updateTime": 0,
  "updateUserId": "string",
  "deleteTime": 0,
  "deleteUserId": "string",
  "isDeleted": false,
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "serverName": "string",
  "location": "string",
  "ipAddress": "string",
  "operatingSystem": "string",
  "environment": "string",
  "remarks": "string",
  "vmPort": 0,
  "dockerPort": 0,
  "enableDate": 0,
  "disableDate": 0
}
```

#### 回應

- **200 OK**: 成功新增 Web 伺服器

### 3. 取得 Web 伺服器明細

**GET** `/api/sms/SmsWebServers/{id}`

#### 路徑參數

| 參數名稱 | 類型 | 必填 | 說明 |
|---------|------|------|------|
| id | string(uuid) | 是 | Web 伺服器 ID |

#### 回應

- **200 OK**: 成功取得 Web 伺服器明細

### 4. 更新 Web 伺服器

**PUT** `/api/sms/SmsWebServers/{id}`

#### 路徑參數

| 參數名稱 | 類型 | 必填 | 說明 |
|---------|------|------|------|
| id | string(uuid) | 是 | Web 伺服器 ID |

#### 請求內容

同新增 Web 伺服器的請求內容格式

#### 回應

- **200 OK**: 成功更新 Web 伺服器

### 5. 刪除 Web 伺服器

**DELETE** `/api/sms/SmsWebServers/{id}`

#### 路徑參數

| 參數名稱 | 類型 | 必填 | 說明 |
|---------|------|------|------|
| id | string(uuid) | 是 | Web 伺服器 ID |

#### 回應

- **200 OK**: 成功刪除 Web 伺服器

### 6. 批次新增 Web 伺服器

**POST** `/api/sms/SmsWebServers/batch`

#### 請求內容

```json
[
  {
    "createTime": 0,
    "createUserId": "string",
    "updateTime": 0,
    "updateUserId": "string",
    "deleteTime": 0,
    "deleteUserId": "string",
    "isDeleted": false,
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "serverName": "string",
    "location": "string",
    "ipAddress": "string",
    "operatingSystem": "string",
    "environment": "string",
    "remarks": "string",
    "vmPort": 0,
    "dockerPort": 0,
    "enableDate": 0,
    "disableDate": 0
  }
]
```

#### 回應

- **200 OK**: 成功批次新增 Web 伺服器

## 資料模型

### SmsWebServer

| 欄位名稱 | 類型 | 說明 |
|---------|------|------|
| id | string(uuid) | 唯一識別碼 |
| serverName | string | 伺服器名稱 |
| location | string | 位置 |
| ipAddress | string | IP 位址 |
| operatingSystem | string | 作業系統 |
| environment | string | 環境 |
| remarks | string | 備註 |
| vmPort | number | VM 連接埠 |
| dockerPort | number | Docker 連接埠 |
| enableDate | number | 啟用日期 |
| disableDate | number | 停用日期 |
| createTime | number | 建立時間 |
| createUserId | string | 建立使用者 ID |
| updateTime | number | 更新時間 |
| updateUserId | string | 更新使用者 ID |
| deleteTime | number | 刪除時間 |
| deleteUserId | string | 刪除使用者 ID |
| isDeleted | boolean | 是否已刪除 |

## 欄位說明

### 必填欄位

- `serverName`: 伺服器名稱，用於識別伺服器
- `ipAddress`: IP 位址，伺服器的網路位址
- `environment`: 環境，如 production、staging、development 等

### 選填欄位

- `location`: 伺服器實體位置
- `operatingSystem`: 作業系統，如 Windows Server、Ubuntu、CentOS 等
- `remarks`: 備註說明
- `vmPort`: 虛擬機器連接埠
- `dockerPort`: Docker 容器連接埠
- `enableDate`: 啟用日期（時間戳記）
- `disableDate`: 停用日期（時間戳記）

## 錯誤處理

- **400 Bad Request**: 請求參數錯誤
- **404 Not Found**: Web 伺服器不存在
- **409 Conflict**: 伺服器名稱重複或 IP 位址已被使用
- **500 Internal Server Error**: 伺服器內部錯誤

## 使用範例

### 查詢 Web 伺服器

```bash
curl -X GET "https://api.example.com/api/sms/SmsWebServers?keyword=nginx&environment=production"
```

### 新增 Web 伺服器

```bash
curl -X POST "https://api.example.com/api/sms/SmsWebServers" \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "WebServer-Prod-01",
    "location": "Taipei",
    "ipAddress": "*************",
    "operatingSystem": "Ubuntu 20.04",
    "environment": "production",
    "vmPort": 80,
    "dockerPort": 8080,
    "remarks": "主要生產環境 Web 伺服器"
  }'
```

### 更新 Web 伺服器

```bash
curl -X PUT "https://api.example.com/api/sms/SmsWebServers/123e4567-e89b-12d3-a456-426614174000" \
  -H "Content-Type: application/json" \
  -d '{
    "remarks": "更新後的備註說明",
    "dockerPort": 9090
  }'
```

### 刪除 Web 伺服器

```bash
curl -X DELETE "https://api.example.com/api/sms/SmsWebServers/123e4567-e89b-12d3-a456-426614174000"
```

### 批次新增 Web 伺服器

```bash
curl -X POST "https://api.example.com/api/sms/SmsWebServers/batch" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "serverName": "WebServer-Staging-01",
      "location": "Taipei",
      "ipAddress": "*************",
      "operatingSystem": "Ubuntu 20.04",
      "environment": "staging",
      "vmPort": 80,
      "dockerPort": 8080
    },
    {
      "serverName": "WebServer-Dev-01",
      "location": "Taipei",
      "ipAddress": "*************",
      "operatingSystem": "Ubuntu 20.04",
      "environment": "development",
      "vmPort": 80,
      "dockerPort": 8080
    }
  ]'
```

## 最佳實踐

### 命名規範

- 伺服器名稱應具有描述性，建議格式：`{類型}-{環境}-{序號}`
- 例如：`WebServer-Prod-01`、`WebServer-Staging-02`

### 環境分類

- `production`: 生產環境
- `staging`: 測試環境
- `development`: 開發環境
- `qa`: 品質保證環境

### 安全性考量

- 定期更新作業系統和軟體
- 使用防火牆限制連接埠存取
- 記錄伺服器存取日誌
- 定期備份重要資料
