# FastERP UI/UX 設計指南

## 📋 概述

本文件記錄了 FastERP 系統中 UI/UX 改善的設計原則和實現標準，特別針對共用組件的視覺設計和用戶體驗優化。

## 🎯 設計原則

### 1. 視覺層次優化

#### **縮排和間距**
- ✅ **統一縮排間距**：使用 24px 作為主要層級間距
- ✅ **層級顏色區分**：不同層級使用不同的邊框顏色
  - 第一層：`#e6f7ff` (淺藍色)
  - 第二層：`#f0f9ff` (更淺的藍色)
- ✅ **圓角效果**：統一使用 8px 圓角，提升現代感
- ✅ **陰影系統**：添加微妙陰影效果，增強視覺深度

#### **字體和排版**
- ✅ **響應式字體**：移動端和桌面端使用不同字體大小
- ✅ **字體權重**：標題使用適當的字體權重突出層次
- ✅ **行高優化**：確保文字可讀性和視覺舒適度

### 2. 互動體驗提升

#### **動畫效果**
- ✅ **流暢動畫**：使用 `cubic-bezier(0.4, 0, 0.2, 1)` 緩動函數
- ✅ **懸停效果**：微妙的上移效果 (`translateY(-1px)`)
- ✅ **選中狀態**：清晰的選中視覺反饋
- ✅ **載入動畫**：提供適當的載入狀態反饋

#### **按鈕設計**
- ✅ **語義化顏色**：
  - 新增按鈕：綠色 (`#52c41a`)
  - 編輯按鈕：藍色 (`#1890ff`)
  - 刪除按鈕：紅色 (`#ff4d4f`)
- ✅ **懸停狀態**：每個按鈕都有對應的懸停背景色
- ✅ **邊框效果**：透明邊框在懸停時顯示
- ✅ **移動端優化**：確保觸控區域足夠大

### 3. 響應式設計

#### **斷點設計**
- **移動端** (≤768px)：
  - 全屏 Modal 顯示
  - 較大的觸控區域
  - 始終顯示操作按鈕
  - 優化的內邊距和間距

- **平板** (769-1024px)：
  - 適中的 Modal 寬度
  - 平衡的佈局比例
  - 適當的按鈕大小

- **桌面** (>1024px)：
  - 大尺寸 Modal 顯示
  - 豐富的懸停效果
  - 精細的動畫交互

#### **容器設計**
- ✅ **圓角設計**：桌面端使用 12px 圓角
- ✅ **高度控制**：響應式高度設置
- ✅ **內邊距優化**：不同設備的內邊距調整
- ✅ **滾動優化**：為浮動按鈕預留空間

## 🎨 設計系統一致性

### 遵循 FastERP 設計原則

- ✅ **極簡風格**：與系統整體風格保持一致
- ✅ **語義化顏色**：使用標準的 Ant Design 色彩系統
- ✅ **統一間距**：遵循 8px 網格系統
- ✅ **一致動畫**：統一的緩動函數和持續時間

### 無障礙設計

- ✅ **鍵盤導航**：完整的 focus 狀態
- ✅ **高對比度**：支援高對比度模式
- ✅ **減少動畫**：支援 `prefers-reduced-motion`
- ✅ **語義化標籤**：正確的 ARIA 屬性

## 🔧 技術實現標準

### CSS-in-JS + CSS Modules 混合方案

- **動態樣式**：使用 CSS-in-JS 處理響應式和狀態相關樣式
- **靜態樣式**：使用 CSS Modules 處理基礎樣式
- **最佳實踐**：平衡性能和可維護性

### 智能狀態管理

- **ResizeObserver**：監聽容器大小變化
- **防抖處理**：滾動事件防抖處理
- **記憶化**：響應式配置記憶化

### 動畫系統

- **統一緩動**：使用統一的緩動函數
- **分層動畫**：不同層級的動畫效果
- **性能優化**：使用 transform 動畫提升性能

## 📱 組件設計標準

### Modal 組件

#### **標題區域**
- ✅ **視覺層次**：改善標題字體大小和權重
- ✅ **計數徽章**：顯示相關統計資訊
- ✅ **背景色**：標題區域使用淺灰背景 (`#fafafa`)
- ✅ **響應式字體**：移動端和桌面端不同字體大小

#### **容器佈局**
- ✅ **圓角設計**：桌面端使用 12px 圓角
- ✅ **高度控制**：響應式高度設置
- ✅ **內邊距優化**：不同設備的內邊距調整
- ✅ **滾動優化**：為浮動按鈕預留空間

### 浮動按鈕組件

#### **邊界控制**
- ✅ **邊界控制**：確保按鈕始終在 Modal 內部
- ✅ **智能顯示**：有滾動條時顯示滾動按鈕，否則顯示新增按鈕
- ✅ **避免衝突**：z-index 設為 999，避免與關閉按鈕衝突
- ✅ **響應式定位**：移動端和桌面端不同的定位策略

#### **視覺設計**
- ✅ **毛玻璃效果**：`backdrop-filter: blur(8px)`
- ✅ **動態陰影**：懸停時增強陰影效果
- ✅ **顏色語義**：滾動按鈕藍色，新增按鈕綠色

## ✅ 解決的常見問題

1. ✅ 浮動按鈕超出 Modal 邊界
2. ✅ 與關閉按鈕的衝突問題  
3. ✅ 響應式設計不佳
4. ✅ 視覺層次不清晰
5. ✅ 操作按鈕互動體驗差
6. ✅ 缺乏無障礙支援
7. ✅ 動畫效果不流暢
8. ✅ 移動端體驗不佳

## 📋 檢查清單

### 新組件開發
- [ ] 遵循 8px 網格系統
- [ ] 使用統一的顏色系統
- [ ] 實現響應式設計
- [ ] 添加適當的動畫效果
- [ ] 確保無障礙支援
- [ ] 測試不同設備的顯示效果

### 現有組件改善
- [ ] 檢查視覺層次是否清晰
- [ ] 確認互動體驗是否流暢
- [ ] 驗證響應式設計是否完整
- [ ] 測試無障礙功能是否正常
- [ ] 檢查動畫效果是否適當

## 📞 技術支援

如果在 UI/UX 實現過程中遇到問題，請：
1. 參考本設計指南
2. 檢查 Ant Design 官方文檔
3. 確認響應式斷點設置
4. 驗證無障礙屬性配置
5. 測試不同設備的顯示效果

---

**注意**：本指南會隨著系統發展持續更新，請定期查看最新版本。
