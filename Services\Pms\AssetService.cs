﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using AutoMapper;
using OfficeOpenXml;
using FAST_ERP_Backend.Helper;

namespace FAST_ERP_Backend.Services.Pms
{
    public partial class AssetService : IAssetService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly IMapper _mapper;
        private readonly IDepreciationFormDetailService _depreciationService;
        private readonly IUnitService _unitService;
        private readonly IAssetStatusService _assetStatusService;
        private readonly IEquipmentTypeService _equipmentTypeService;
        private readonly ISignalRMessageService _signalRMessageService;
        private static readonly Dictionary<string, bool> _userImportCancelFlags = new();

        public AssetService(Baseform baseform, ERPDbContext context, IMapper mapper, IDepreciationFormDetailService depreciationService,
            IUnitService unitService, IAssetStatusService assetStatusService, IEquipmentTypeService equipmentTypeService,
            ISignalRMessageService signalRMessageService)
        {
            _baseform = baseform;
            _context = context;
            _mapper = mapper;
            _depreciationService = depreciationService;
            _unitService = unitService;
            _assetStatusService = assetStatusService;
            _equipmentTypeService = equipmentTypeService;
            _signalRMessageService = signalRMessageService;
        }

        /// <summary>
        /// 取消目前使用者的批次匯入作業
        /// </summary>
        public Task<bool> CancelBatchImportAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
            {
                return Task.FromResult(false);
            }
            lock (_userImportCancelFlags)
            {
                _userImportCancelFlags[userId] = true;
            }
            return Task.FromResult(true);
        }

        /// <summary>
        /// 取得新的財產編號
        /// </summary>
        /// <param name="subject">科目</param>
        /// <param name="subSubject">子目</param>
        /// <param name="category">類別</param>
        /// <returns>新的財產編號</returns>
        public async Task<(bool success, string assetNo, string message)> GetNewAssetNoAsync(string subject, string subSubject, string category)
        {
            try
            {
                // 驗證輸入參數
                if (string.IsNullOrEmpty(subject) || string.IsNullOrEmpty(subSubject) || string.IsNullOrEmpty(category))
                {
                    return (false, string.Empty, "科目、子目和類別都不能為空");
                }

                // 組合前綴
                string prefix = $"{subject}{subSubject}{category}";

                // 查詢現有的財產編號
                var existingAssetNos = await _context.Set<Asset>()
                    .Where(a => a.AssetNo.StartsWith(prefix))
                    .Select(a => a.AssetNo)
                    .ToListAsync();

                // 找出最大的流水號
                int maxSerial = 0;
                foreach (var assetNo in existingAssetNos)
                {
                    if (assetNo.Length > prefix.Length)
                    {
                        string serialPart = assetNo.Substring(prefix.Length);
                        if (int.TryParse(serialPart, out int currentSerial))
                        {
                            maxSerial = Math.Max(maxSerial, currentSerial);
                        }
                    }
                }

                // 產生新的流水號
                int newSerial = maxSerial + 1;
                string serialNumber = newSerial.ToString("D5"); // 補足5位數
                string newAssetNo = $"{prefix}{serialNumber}";

                return (true, newAssetNo, "產生財產編號成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"產生財產編號時發生錯誤: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"內部異常: {ex.InnerException.Message}");
                    Console.WriteLine($"堆疊跟踪: {ex.StackTrace}");
                }
                return (false, string.Empty, $"產生財產編號失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得財產列表
        /// </summary>
        /// <returns>財產列表</returns>
        public async Task<List<AssetWithAccessoriesDTO>> GetAssetAsync()
        {
            try
            {
                // 查詢所有財產
                var assets = await _context.Set<Asset>()
                    .Where(a => a.DeleteTime == 0 || a.DeleteTime == null)
                    .OrderBy(a => a.AssetNo)
                    .AsNoTracking()
                    .ToListAsync();

                if (assets.Count == 0)
                {
                    return new List<AssetWithAccessoriesDTO>();
                }

                // 取得所有財產ID
                var assetIds = assets.Select(a => a.AssetId).ToList();

                // 取得所有部門ID、組別ID和單位ID
                var departmentIds = assets.Where(a => a.DepartmentId != null).Select(a => a.DepartmentId).Distinct().ToList();
                var divisionIds = assets.Where(a => a.DivisionId != null).Select(a => a.DivisionId).Distinct().ToList();
                var unitIds = assets.Where(a => a.UnitId != null).Select(a => a.UnitId).Distinct().ToList();

                // 批量查詢部門、組別和單位資料
                var departments = await _context.Set<Department>()
                    .Where(d => departmentIds.Contains(d.DepartmentId) && (d.DeleteTime == 0 || d.DeleteTime == null))
                        .AsNoTracking()
                    .ToDictionaryAsync(d => d.DepartmentId, d => d);

                var divisions = await _context.Set<Division>()
                    .Where(d => divisionIds.Contains(d.DivisionId) && (d.DeleteTime == 0 || d.DeleteTime == null))
                        .AsNoTracking()
                    .ToDictionaryAsync(d => d.DivisionId, d => d);

                var units = await _context.Set<Unit>()
                    .Where(u => unitIds.Contains(u.UnitId) && (u.DeleteTime == 0 || u.DeleteTime == null))
                        .AsNoTracking()
                    .ToDictionaryAsync(u => u.UnitId, u => u);

                // 批量查詢所有附屬設備
                var accessoryEquipments = await _context.Set<AccessoryEquipment>()
                    .Where(ae => assetIds.Contains(ae.AssetId) && (ae.DeleteTime == 0 || ae.DeleteTime == null))
                    .AsNoTracking()
                    .ToListAsync();

                // 將附屬設備按財產ID分組
                var accessoryEquipmentsByAssetId = accessoryEquipments
                    .GroupBy(ae => ae.AssetId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // 批量查詢所有保險單位關聯
                var insuranceUnitMappings = await _context.Set<AssetInsuranceUnitMapping>()
                    .Where(aiu => assetIds.Contains(aiu.AssetId))
                    .AsNoTracking()
                    .ToListAsync();

                // 取得所有保險單位ID
                var insuranceUnitIds = insuranceUnitMappings.Select(m => m.InsuranceUnitId).Distinct().ToList();

                // 批量查詢所有保險單位
                var insuranceUnits = await _context.Set<InsuranceUnit>()
                    .Where(iu => insuranceUnitIds.Contains(iu.InsuranceUnitId) && (iu.DeleteTime == 0 || iu.DeleteTime == null))
                    .AsNoTracking()
                    .ToDictionaryAsync(iu => iu.InsuranceUnitId, iu => iu);

                // 將保險單位關聯按財產ID分組
                var insuranceUnitMappingsByAssetId = insuranceUnitMappings
                    .GroupBy(m => m.AssetId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // 批量查詢所有攤提來源關聯
                var amortizationSourceMappings = await _context.Set<AssetAmortizationSourceMapping>()
                    .Where(aas => assetIds.Contains(aas.AssetId))
                    .AsNoTracking()
                    .ToListAsync();

                // 取得所有攤提來源ID
                var amortizationSourceIds = amortizationSourceMappings.Select(m => m.AmortizationSourceId).Distinct().ToList();

                // 批量查詢所有攤提來源
                var amortizationSources = await _context.Set<AmortizationSource>()
                    .Where(ams => amortizationSourceIds.Contains(ams.AmortizationSourceId) && (ams.DeleteTime == 0 || ams.DeleteTime == null))
                    .AsNoTracking()
                    .ToDictionaryAsync(ams => ams.AmortizationSourceId, ams => ams);

                // 將攤提來源關聯按財產ID分組
                var amortizationSourceMappingsByAssetId = amortizationSourceMappings
                    .GroupBy(m => m.AssetId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // 批量查詢所有財產來源關聯
                var assetSourceMappings = await _context.Set<AssetAssetSourceMapping>()
                    .Where(aas => assetIds.Contains(aas.AssetId))
                    .AsNoTracking()
                    .ToListAsync();

                // 取得所有財產來源ID
                var assetSourceIds = assetSourceMappings.Select(m => m.AssetSourceId).Distinct().ToList();

                // 批量查詢所有財產來源
                var assetSources = await _context.Set<AssetSource>()
                    .Where(ass => assetSourceIds.Contains(ass.AssetSourceId) && (ass.DeleteTime == 0 || ass.DeleteTime == null))
                    .AsNoTracking()
                    .ToDictionaryAsync(ass => ass.AssetSourceId, ass => ass);

                // 將財產來源關聯按財產ID分組
                var assetSourceMappingsByAssetId = assetSourceMappings
                    .GroupBy(m => m.AssetId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // 組裝結果
                List<AssetWithAccessoriesDTO> result = new List<AssetWithAccessoriesDTO>();

                foreach (var asset in assets)
                {
                    // 建立 DTO 實例
                    var assetDto = new AssetWithAccessoriesDTO
                    {
                        Asset = _mapper.Map<AssetDTO>(asset),
                        AccessoryEquipments = new List<AccessoryEquipmentDTO>(),
                        InsuranceUnits = new List<InsuranceUnitDTO>(),
                        AmortizationSources = new List<AmortizationSourceDTO>(),
                        AssetSources = new List<AssetSourceDTO>()
                    };

                    // 設定部門名稱
                    if (asset.DepartmentId != null && departments.TryGetValue(asset.DepartmentId, out var department))
                    {
                        assetDto.Asset.DepartmentName = department.Name;
                    }

                    // 設定組別名稱
                    if (asset.DivisionId != null && divisions.TryGetValue(asset.DivisionId, out var division))
                    {
                        assetDto.Asset.DivisionName = division.Name;
                    }

                    // 設定單位名稱
                    if (asset.UnitId != null && units.TryGetValue(asset.UnitId, out var unit))
                    {
                        assetDto.Asset.UnitName = unit.Name;
                    }

                    // 設定附屬設備 - 轉換為DTO
                    if (accessoryEquipmentsByAssetId.TryGetValue(asset.AssetId, out var equipments))
                    {
                        assetDto.AccessoryEquipments = _mapper.Map<List<AccessoryEquipmentDTO>>(equipments);
                    }

                    // 設定保險單位
                    if (insuranceUnitMappingsByAssetId.TryGetValue(asset.AssetId, out var insuranceMappings))
                    {
                        foreach (var mapping in insuranceMappings)
                        {
                            if (insuranceUnits.TryGetValue(mapping.InsuranceUnitId, out var insuranceUnit))
                            {
                                assetDto.InsuranceUnits.Add(_mapper.Map<InsuranceUnitDTO>(insuranceUnit));
                            }
                        }
                    }

                    // 設定攤提來源
                    if (amortizationSourceMappingsByAssetId.TryGetValue(asset.AssetId, out var amortizationMappings))
                    {
                        foreach (var mapping in amortizationMappings)
                        {
                            if (amortizationSources.TryGetValue(mapping.AmortizationSourceId, out var amortizationSource))
                            {
                                assetDto.AmortizationSources.Add(_mapper.Map<AmortizationSourceDTO>(amortizationSource));
                            }
                        }
                    }

                    // 設定財產來源
                    if (assetSourceMappingsByAssetId.TryGetValue(asset.AssetId, out var sourceRelations))
                    {
                        foreach (var mapping in sourceRelations)
                        {
                            if (assetSources.TryGetValue(mapping.AssetSourceId, out var assetSource))
                            {
                                assetDto.AssetSources.Add(_mapper.Map<AssetSourceDTO>(assetSource));
                            }
                        }
                    }

                    result.Add(assetDto);
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查詢財產列表時發生錯誤: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"內部異常: {ex.InnerException.Message}");
                    Console.WriteLine($"堆疊跟踪: {ex.StackTrace}");
                }
                // 如果發生異常，返回空列表
                return new List<AssetWithAccessoriesDTO>();
            }
        }

        /// <summary>
        /// 編輯財產
        /// </summary>
        /// <param name="_data">財產資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool, string)> EditAssetAsync(AssetWithAccessoriesDTO _data)
        {
            try
            {
                // 檢查財產是否存在
                var existingAsset = await _context.Set<Asset>()
                    .FirstOrDefaultAsync(a => a.AssetId == _data.Asset.AssetId && (a.DeleteTime == 0 || a.DeleteTime == null));

                if (existingAsset == null)
                {
                    return (false, "找不到指定的財產");
                }

                // 更新財產資料
                _mapper.Map(_data.Asset, existingAsset);
                existingAsset.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                existingAsset.UpdateUserId = _data.Asset.UpdateUserId;

                // 財產狀態如果有更新的時候，更新狀態異動日期
                if (existingAsset.AssetStatusId != _data.Asset.AssetStatusId)
                {
                    existingAsset.StatusChangeDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                }

                // 更新附屬設備
                var existingAccessories = await _context.Set<AccessoryEquipment>()
                    .Where(ae => ae.AssetId == _data.Asset.AssetId && (ae.DeleteTime == 0 || ae.DeleteTime == null))
                        .ToListAsync();

                // 刪除不在新列表中的附屬設備
                foreach (var existingAccessory in existingAccessories)
                {
                    if (!_data.AccessoryEquipments.Any(ae => ae.AccessoryEquipmentId == existingAccessory.AccessoryEquipmentId))
                    {
                        existingAccessory.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        existingAccessory.DeleteUserId = _data.Asset.UpdateUserId;
                    }
                }

                // 新增或更新附屬設備
                foreach (var accessoryDto in _data.AccessoryEquipments)
                {
                    var existingAccessory = existingAccessories
                        .FirstOrDefault(ae => ae.AccessoryEquipmentId == accessoryDto.AccessoryEquipmentId);

                    if (existingAccessory != null)
                    {
                        // 更新現有附屬設備
                        _mapper.Map(accessoryDto, existingAccessory);
                        existingAccessory.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        existingAccessory.UpdateUserId = _data.Asset.UpdateUserId;
                    }
                    else
                    {
                        // 新增附屬設備
                        var newAccessory = _mapper.Map<AccessoryEquipment>(accessoryDto);
                        newAccessory.AssetId = _data.Asset.AssetId;
                        newAccessory.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        newAccessory.CreateUserId = _data.Asset.UpdateUserId;
                        await _context.Set<AccessoryEquipment>().AddAsync(newAccessory);
                    }
                }

                // 更新保險單位關聯
                var existingMappings = await _context.Set<AssetInsuranceUnitMapping>()
                    .Where(m => m.AssetId == _data.Asset.AssetId)
                        .ToListAsync();

                // 刪除不在新列表中的關聯
                foreach (var mapping in existingMappings)
                {
                    if (!_data.InsuranceUnits.Any(iu => iu.InsuranceUnitId == mapping.InsuranceUnitId))
                    {
                        _context.Set<AssetInsuranceUnitMapping>().Remove(mapping);
                    }
                }

                // 新增或更新保險單位
                foreach (var insuranceUnitDto in _data.InsuranceUnits)
                {
                    // 檢查保險單位是否已存在
                    var existingInsuranceUnit = await _context.Set<InsuranceUnit>()
                        .FirstOrDefaultAsync(iu => iu.InsuranceUnitId == insuranceUnitDto.InsuranceUnitId);

                    if (existingInsuranceUnit == null)
                    {
                        // 新增保險單位
                        var insuranceUnit = _mapper.Map<InsuranceUnit>(insuranceUnitDto);
                        await _context.Set<InsuranceUnit>().AddAsync(insuranceUnit);
                        await _context.SaveChangesAsync();
                    }

                    // 檢查關聯是否已存在
                    if (!existingMappings.Any(m => m.InsuranceUnitId == insuranceUnitDto.InsuranceUnitId))
                    {
                        // 新增關聯
                        var mapping = new AssetInsuranceUnitMapping
                        {
                            AssetId = _data.Asset.AssetId,
                            InsuranceUnitId = insuranceUnitDto.InsuranceUnitId,
                            CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                            CreateUserId = _data.Asset.UpdateUserId,
                            IsDeleted = false
                        };
                        await _context.Set<AssetInsuranceUnitMapping>().AddAsync(mapping);
                    }
                }

                // 更新攤提來源關聯
                var existingAmortizationMappings = await _context.Set<AssetAmortizationSourceMapping>()
                    .Where(m => m.AssetId == _data.Asset.AssetId)
                        .ToListAsync();

                // 刪除不在新列表中的關聯
                foreach (var mapping in existingAmortizationMappings)
                {
                    if (!_data.AmortizationSources.Any(ams => ams.AmortizationSourceId == mapping.AmortizationSourceId))
                    {
                        _context.Set<AssetAmortizationSourceMapping>().Remove(mapping);
                    }
                }

                // 新增新的關聯
                foreach (var amortizationSourceDto in _data.AmortizationSources)
                {
                    if (!existingAmortizationMappings.Any(m => m.AmortizationSourceId == amortizationSourceDto.AmortizationSourceId))
                    {
                        var mapping = new AssetAmortizationSourceMapping
                        {
                            AssetId = _data.Asset.AssetId,
                            AmortizationSourceId = amortizationSourceDto.AmortizationSourceId
                        };
                        await _context.Set<AssetAmortizationSourceMapping>().AddAsync(mapping);
                    }
                }

                // 更新財產來源關聯
                var existingAssetSourceMappings = await _context.Set<AssetAssetSourceMapping>()
                    .Where(m => m.AssetId == _data.Asset.AssetId)
                        .ToListAsync();

                // 刪除不在新列表中的關聯
                foreach (var mapping in existingAssetSourceMappings)
                {
                    if (!_data.AssetSources.Any(ass => ass.AssetSourceId == mapping.AssetSourceId))
                    {
                        _context.Set<AssetAssetSourceMapping>().Remove(mapping);
                    }
                }

                // 新增新的關聯
                foreach (var assetSourceDto in _data.AssetSources)
                {
                    if (!existingAssetSourceMappings.Any(m => m.AssetSourceId == assetSourceDto.AssetSourceId))
                    {
                        var mapping = new AssetAssetSourceMapping
                        {
                            AssetId = _data.Asset.AssetId,
                            AssetSourceId = assetSourceDto.AssetSourceId
                        };
                        await _context.Set<AssetAssetSourceMapping>().AddAsync(mapping);
                    }
                }

                await _context.SaveChangesAsync();
                return (true, "更新財產成功");
            }
            catch (Exception ex)
            {
                return (false, $"更新財產失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除財產
        /// </summary>
        /// <param name="_data">財產資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool, string)> DeleteAssetAsync(AssetWithAccessoriesDTO _data)
        {
            try
            {
                // 檢查必要欄位
                if (_data.Asset == null)
                {
                    return (false, "財產資料不可為空");
                }

                if (string.IsNullOrEmpty(_data.Asset.AssetId.ToString()) || _data.Asset.AssetId == Guid.Empty)
                {
                    return (false, "財產ID不可為空");
                }

                // 開始交易
                using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    // 查詢要刪除的財產
                    var asset = await _context.Set<Asset>()
                        .FirstOrDefaultAsync(a => a.AssetId == _data.Asset.AssetId);

                    if (asset == null)
                    {
                        return (false, "找不到要刪除的財產");
                    }

                    // 檢查是否已被刪除
                    if (asset.DeleteTime != null && asset.DeleteTime > 0)
                    {
                        return (false, "此財產已被刪除");
                    }

                    long currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                    // 標記財產為已刪除
                    asset.DeleteTime = currentTime;
                    asset.DeleteUserId = _data.Asset.DeleteUserId;
                    asset.IsDeleted = true;

                    _context.Update(asset);
                    await _context.SaveChangesAsync();
                    Console.WriteLine($"已標記財產為已刪除: {asset.AssetName}, ID: {asset.AssetId}");

                    // 標記相關附屬設備為已刪除
                    var accessoryEquipments = await _context.Set<AccessoryEquipment>()
                        .Where(ae => ae.AssetId == _data.Asset.AssetId && (ae.DeleteTime == 0 || ae.DeleteTime == null))
                        .ToListAsync();

                    foreach (var equipment in accessoryEquipments)
                    {
                        equipment.DeleteTime = currentTime;
                        equipment.DeleteUserId = _data.Asset.DeleteUserId;
                        equipment.UpdateTime = currentTime;
                        equipment.UpdateUserId = _data.Asset.DeleteUserId;
                        _context.Update(equipment);
                    }

                    if (accessoryEquipments.Any())
                    {
                        await _context.SaveChangesAsync();
                        Console.WriteLine($"已標記 {accessoryEquipments.Count} 個附屬設備為已刪除");
                    }

                    // 刪除保險單位關聯
                    var insuranceUnitRelations = await _context.Set<AssetInsuranceUnitMapping>()
                        .Where(aiu => aiu.AssetId == _data.Asset.AssetId)
                        .ToListAsync();

                    if (insuranceUnitRelations.Any())
                    {
                        _context.Set<AssetInsuranceUnitMapping>().RemoveRange(insuranceUnitRelations);
                        await _context.SaveChangesAsync();
                        Console.WriteLine($"已刪除 {insuranceUnitRelations.Count} 個保險單位關聯");
                    }

                    // 刪除攤提來源關聯
                    var amortizationSourceRelations = await _context.Set<AssetAmortizationSourceMapping>()
                        .Where(aas => aas.AssetId == _data.Asset.AssetId)
                        .ToListAsync();

                    if (amortizationSourceRelations.Any())
                    {
                        _context.Set<AssetAmortizationSourceMapping>().RemoveRange(amortizationSourceRelations);
                        await _context.SaveChangesAsync();
                        Console.WriteLine($"已刪除 {amortizationSourceRelations.Count} 個攤提來源關聯");
                    }

                    // 刪除財產來源關聯
                    var assetSourceRelations = await _context.Set<AssetAssetSourceMapping>()
                        .Where(aas => aas.AssetId == _data.Asset.AssetId)
                        .ToListAsync();

                    if (assetSourceRelations.Any())
                    {
                        _context.Set<AssetAssetSourceMapping>().RemoveRange(assetSourceRelations);
                        await _context.SaveChangesAsync();
                        Console.WriteLine($"已刪除 {assetSourceRelations.Count} 個財產來源關聯");
                    }

                    // 提交交易
                    await transaction.CommitAsync();
                    Console.WriteLine("交易成功提交");
                    return (true, _data.Asset.AssetNo);
                }
                catch (Exception ex)
                {
                    // 回滾交易
                    await transaction.RollbackAsync();
                    Console.WriteLine($"交易回滾: {ex.Message}");

                    // 記錄內部異常詳情
                    if (ex.InnerException != null)
                    {
                        Console.WriteLine($"內部異常: {ex.InnerException.Message}");
                        Console.WriteLine($"堆疊跟踪: {ex.StackTrace}");
                    }

                    return (false, $"刪除財產失敗: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"一般異常: {ex.Message}");

                // 記錄內部異常詳情
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"內部異常: {ex.InnerException.Message}");
                    Console.WriteLine($"堆疊跟踪: {ex.StackTrace}");
                }

                return (false, $"刪除財產失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得財產詳細資料
        /// </summary>
        /// <param name="_assetId">財產ID</param>
        /// <returns>財產詳細資料</returns>
        public async Task<AssetWithAccessoriesDTO> GetAssetDetailAsync(string _assetId)
        {
            try
            {
                if (string.IsNullOrEmpty(_assetId))
                {
                    Console.WriteLine("財產ID不可為空");
                    return null;
                }

                // 轉換ID字串為Guid
                if (!Guid.TryParse(_assetId, out Guid assetId))
                {
                    Console.WriteLine("財產ID格式錯誤");
                    return null;
                }

                // 查詢財產基本資料
                var asset = await _context.Set<Asset>()
                    .FirstOrDefaultAsync(a => a.AssetId == assetId && (a.DeleteTime == null || a.DeleteTime == 0));

                if (asset == null)
                {
                    Console.WriteLine($"找不到ID為 {_assetId} 的財產");
                    return null;
                }

                Console.WriteLine($"找到財產: {asset.AssetName}, ID: {asset.AssetId}");

                // 查詢部門名稱
                var department = await _context.Set<Department>()
                    .Where(d => d.DepartmentId == asset.DepartmentId && (d.DeleteTime == 0 || d.DeleteTime == null))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                // 查詢組別名稱
                var division = await _context.Set<Division>()
                    .Where(d => d.DivisionId == asset.DivisionId && (d.DeleteTime == 0 || d.DeleteTime == null))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                // 查詢單位名稱
                var unit = await _context.Set<Unit>()
                    .Where(u => u.UnitId == asset.UnitId && (u.DeleteTime == 0 || u.DeleteTime == null))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                // 設定部門和組別名稱
                if (department != null)
                {
                    asset.DepartmentName = department.Name;
                }
                if (division != null)
                {
                    asset.DivisionName = division.Name;
                }

                if (unit != null)
                {
                    asset.UnitName = unit.Name;
                }

                // 建立 DTO 實例
                var assetDto = new AssetWithAccessoriesDTO
                {
                    Asset = _mapper.Map<AssetDTO>(asset),
                    AccessoryEquipments = new List<AccessoryEquipmentDTO>(),
                    InsuranceUnits = new List<InsuranceUnitDTO>(),
                    AmortizationSources = new List<AmortizationSourceDTO>(),
                    AssetSources = new List<AssetSourceDTO>()
                };

                // 查詢附屬設備並轉換為DTO
                var accessoryEquipments = await _context.Set<AccessoryEquipment>()
                    .Where(ae => ae.AssetId == assetId && (ae.DeleteTime == null || ae.DeleteTime == 0))
                    .Include(ae => ae.Asset) // 包含 Asset 以便在 FromEntity 中獲取 AssetNo 和 AssetName
                    .AsNoTracking()
                    .ToListAsync();

                assetDto.AccessoryEquipments = _mapper.Map<List<AccessoryEquipmentDTO>>(accessoryEquipments);

                Console.WriteLine($"附屬設備數量: {assetDto.AccessoryEquipments.Count}");

                // 查詢保險單位關聯
                var insuranceUnitRelations = await _context.Set<AssetInsuranceUnitMapping>()
                    .Where(aiu => aiu.AssetId == assetId)
                    .AsNoTracking()
                    .ToListAsync();

                foreach (var relation in insuranceUnitRelations)
                {
                    var insuranceUnit = await _context.Set<InsuranceUnit>()
                        .Where(iu => iu.InsuranceUnitId == relation.InsuranceUnitId && (iu.DeleteTime == null || iu.DeleteTime == 0))
                        .AsNoTracking()
                        .FirstOrDefaultAsync();

                    if (insuranceUnit != null)
                    {
                        assetDto.InsuranceUnits.Add(_mapper.Map<InsuranceUnitDTO>(insuranceUnit));
                    }
                }

                Console.WriteLine($"保險單位數量: {assetDto.InsuranceUnits.Count}");

                // 查詢攤提來源關聯
                var amortizationSourceRelations = await _context.Set<AssetAmortizationSourceMapping>()
                    .Where(aas => aas.AssetId == assetId)
                    .AsNoTracking()
                    .ToListAsync();

                foreach (var relation in amortizationSourceRelations)
                {
                    var amortizationSource = await _context.Set<AmortizationSource>()
                        .Where(ams => ams.AmortizationSourceId == relation.AmortizationSourceId && (ams.DeleteTime == null || ams.DeleteTime == 0))
                        .AsNoTracking()
                        .FirstOrDefaultAsync();

                    if (amortizationSource != null)
                    {
                        assetDto.AmortizationSources.Add(_mapper.Map<AmortizationSourceDTO>(amortizationSource));
                    }
                }

                Console.WriteLine($"攤提來源數量: {assetDto.AmortizationSources.Count}");

                // 查詢財產來源關聯
                var assetSourceRelations = await _context.Set<AssetAssetSourceMapping>()
                    .Where(aas => aas.AssetId == assetId)
                    .AsNoTracking()
                    .ToListAsync();

                foreach (var relation in assetSourceRelations)
                {
                    var assetSource = await _context.Set<AssetSource>()
                        .Where(ass => ass.AssetSourceId == relation.AssetSourceId && (ass.DeleteTime == null || ass.DeleteTime == 0))
                        .AsNoTracking()
                        .FirstOrDefaultAsync();

                    if (assetSource != null)
                    {
                        assetDto.AssetSources.Add(_mapper.Map<AssetSourceDTO>(assetSource));
                    }
                }

                Console.WriteLine($"財產來源數量: {assetDto.AssetSources.Count}");

                Console.WriteLine($"財產詳細資料查詢完成: {asset.AssetName}");
                return assetDto;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查詢財產詳細資料時發生錯誤: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"內部異常: {ex.InnerException.Message}");
                    Console.WriteLine($"堆疊跟踪: {ex.StackTrace}");
                }
                return null;
            }
        }

        /// <summary>
        /// 新增財產
        /// </summary>
        /// <param name="_data">財產資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool, string)> AddAssetAsync(AssetWithAccessoriesDTO _data)
        {
            // 開始交易
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                // 查詢財產科目
                AssetAccount assetAccount = null;
                if (_data.Asset.AssetAccountId != Guid.Empty)
                {
                    assetAccount = await _context.Set<AssetAccount>()
                        .FirstOrDefaultAsync(a => a.AssetAccountId == _data.Asset.AssetAccountId && (a.DeleteTime == 0 || a.DeleteTime == null));
                }

                if (assetAccount == null)
                {
                    return (false, "找不到指定的財產科目");
                }

                // 查詢財產子目
                AssetSubAccount assetSubAccount = null;
                if (_data.Asset.AssetSubAccountId != Guid.Empty)
                {
                    assetSubAccount = await _context.Set<AssetSubAccount>()
                        .FirstOrDefaultAsync(a => a.AssetSubAccountId == _data.Asset.AssetSubAccountId && (a.DeleteTime == 0 || a.DeleteTime == null));
                }

                if (assetSubAccount == null)
                {
                    return (false, "找不到指定的財產子目");
                }

                // 取得新的財產編號
                var (success, assetNo, message) = await GetNewAssetNoAsync(
                        assetAccount.AssetAccountNo,
                        assetSubAccount.AssetSubAccountNo,
                    _data.Asset.AssetCategoryId);

                if (!success)
                {
                    return (false, message);
                }

                // 設定財產編號
                _data.Asset.AssetNo = assetNo;

                // 檢查財產編號是否已存在
                var existingAsset = await _context.Set<Asset>()
                    .FirstOrDefaultAsync(a => a.AssetNo == _data.Asset.AssetNo && (a.DeleteTime == 0 || a.DeleteTime == null));

                if (existingAsset != null)
                {
                    return (false, "財產編號已存在");
                }

                // 將 DTO 轉換為實體
                var newAsset = _mapper.Map<Asset>(_data.Asset);
                newAsset.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                newAsset.CreateUserId = _data.Asset.CreateUserId;

                // 新增財產
                await _context.Set<Asset>().AddAsync(newAsset);

                // 新增附屬設備
                if (_data.AccessoryEquipments != null && _data.AccessoryEquipments.Any())
                {
                    foreach (var accessoryDto in _data.AccessoryEquipments)
                    {
                        var accessory = _mapper.Map<AccessoryEquipment>(accessoryDto);
                        accessory.AssetId = newAsset.AssetId;
                        accessory.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        accessory.CreateUserId = _data.Asset.CreateUserId;
                        await _context.Set<AccessoryEquipment>().AddAsync(accessory);
                    }
                }

                // 新增保險單位關聯
                if (_data.InsuranceUnits != null && _data.InsuranceUnits.Any())
                {
                    foreach (var insuranceUnitDto in _data.InsuranceUnits)
                    {
                        var insuranceUnit = _mapper.Map<InsuranceUnit>(insuranceUnitDto);
                        insuranceUnit.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        insuranceUnit.CreateUserId = _data.Asset.CreateUserId;

                        // 檢查保險單位是否已存在
                        var existingInsuranceUnit = await _context.Set<InsuranceUnit>()
                            .FirstOrDefaultAsync(iu => iu.Name == insuranceUnit.Name && (iu.DeleteTime == 0 || iu.DeleteTime == null));

                        if (existingInsuranceUnit == null)
                        {
                            await _context.Set<InsuranceUnit>().AddAsync(insuranceUnit);
                            await _context.SaveChangesAsync(); // 需要先儲存以獲得 InsuranceUnitId

                            // 新增關聯
                            var mapping = new AssetInsuranceUnitMapping
                            {
                                AssetId = newAsset.AssetId,
                                InsuranceUnitId = insuranceUnit.InsuranceUnitId
                            };
                            await _context.Set<AssetInsuranceUnitMapping>().AddAsync(mapping);
                        }
                        else
                        {
                            // 使用現有的保險單位建立關聯
                            var mapping = new AssetInsuranceUnitMapping
                            {
                                AssetId = newAsset.AssetId,
                                InsuranceUnitId = existingInsuranceUnit.InsuranceUnitId
                            };
                            await _context.Set<AssetInsuranceUnitMapping>().AddAsync(mapping);
                        }
                    }
                }

                // 新增攤提來源關聯
                if (_data.AmortizationSources != null && _data.AmortizationSources.Any())
                {
                    foreach (var amortizationSourceDto in _data.AmortizationSources)
                    {
                        // 檢查攤提來源是否已存在
                        var existingAmortizationSource = await _context.Set<AmortizationSource>()
                            .FirstOrDefaultAsync(ams => ams.AmortizationSourceId == amortizationSourceDto.AmortizationSourceId &&
                                (ams.DeleteTime == 0 || ams.DeleteTime == null));

                        if (existingAmortizationSource == null)
                        {
                            var amortizationSource = _mapper.Map<AmortizationSource>(amortizationSourceDto);
                            amortizationSource.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                            amortizationSource.CreateUserId = _data.Asset.CreateUserId;

                            await _context.Set<AmortizationSource>().AddAsync(amortizationSource);
                            await _context.SaveChangesAsync(); // 需要先儲存以獲得 AmortizationSourceId
                        }

                        // 新增關聯
                        var mapping = new AssetAmortizationSourceMapping
                        {
                            AssetId = newAsset.AssetId,
                            AmortizationSourceId = amortizationSourceDto.AmortizationSourceId
                        };
                        await _context.Set<AssetAmortizationSourceMapping>().AddAsync(mapping);
                    }
                }

                // 新增財產來源關聯
                if (_data.AssetSources != null && _data.AssetSources.Any())
                {
                    foreach (var assetSourceDto in _data.AssetSources)
                    {
                        var assetSource = _mapper.Map<AssetSource>(assetSourceDto);
                        assetSource.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        assetSource.CreateUserId = _data.Asset.CreateUserId;

                        // 檢查財產來源是否已存在
                        var existingAssetSource = await _context.Set<AssetSource>()
                            .FirstOrDefaultAsync(ams => ams.AssetSourceName == assetSource.AssetSourceName && (ams.DeleteTime == 0 || ams.DeleteTime == null));

                        if (existingAssetSource == null)
                        {
                            await _context.Set<AssetSource>().AddAsync(assetSource);
                            await _context.SaveChangesAsync(); // 需要先儲存以獲得 AssetSourceId

                            // 新增關聯
                            var mapping = new AssetAssetSourceMapping
                            {
                                AssetId = newAsset.AssetId,
                                AssetSourceId = assetSource.AssetSourceId
                            };
                            await _context.Set<AssetAssetSourceMapping>().AddAsync(mapping);
                        }
                        else
                        {
                            // 使用現有的財產來源建立關聯
                            var mapping = new AssetAssetSourceMapping
                            {
                                AssetId = newAsset.AssetId,
                                AssetSourceId = existingAssetSource.AssetSourceId
                            };
                            await _context.Set<AssetAssetSourceMapping>().AddAsync(mapping);
                        }
                    }
                }

                await _context.SaveChangesAsync();

                // 產生折舊紀錄（如果不是土地或未完工程）
                if (assetAccount != null && assetAccount.AssetAccountNo != "1" && assetAccount.AssetAccountNo != "9")
                {
                    var (depreciationSuccess, depreciationMessage) = await _depreciationService.GenerateDepreciationScheduleAsync(
                        newAsset.AssetId.ToString(), _data.Asset.CreateUserId);

                    Console.WriteLine(depreciationMessage);
                }

                // 提交交易
                await transaction.CommitAsync();
                return (true, assetNo);
            }
            catch (Exception ex)
            {
                // 發生錯誤時回滾交易
                await transaction.RollbackAsync();
                return (false, $"新增財產失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 驗證Excel檔案格式和內容
        /// </summary>
        /// <param name="fileStream">Excel檔案流</param>
        /// <returns>驗證結果</returns>
        public async Task<BatchValidationResultDTO> ValidateExcelFileAsync(Stream fileStream)
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                using (var package = new ExcelPackage(fileStream))
                {
                    var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                    if (worksheet == null)
                    {
                        return new BatchValidationResultDTO
                        {
                            IsValid = false,
                            Errors = new List<BatchValidationErrorDTO>
                            {
                                new BatchValidationErrorDTO
                                {
                                    RowIndex = 0,
                                    ColumnName = "檔案",
                                    ErrorMessage = "Excel檔案中沒有工作表",
                                    CellValue = ""
                                }
                            }
                        };
                    }

                    var columnMappings = ExcelHelper.GetAssetColumnMappings();
                    var validationResult = ExcelHelper.ValidateExcelFormat(worksheet, columnMappings);

                    // 額外驗證：檢查財產編號重複和業務邏輯
                    if (validationResult.IsValid)
                    {
                        await ValidateBusinessLogicAsync(worksheet, columnMappings, validationResult);
                    }

                    // 如果驗證成功，讀取並回傳完整的Excel資料
                    if (validationResult.IsValid)
                    {
                        ReadExcelDataToResult(worksheet, columnMappings, validationResult);
                    }

                    return validationResult;
                }
            }
            catch (Exception ex)
            {
                return new BatchValidationResultDTO
                {
                    IsValid = false,
                    Errors = new List<BatchValidationErrorDTO>
                    {
                        new BatchValidationErrorDTO
                        {
                            RowIndex = 0,
                            ColumnName = "檔案",
                            ErrorMessage = $"檔案驗證發生錯誤：{ex.Message}",
                            CellValue = ""
                        }
                    }
                };
            }
        }

        /// <summary>
        /// 批次匯入財產資料
        /// </summary>
        /// <param name="fileStream">Excel檔案流</param>
        /// <param name="userId">操作使用者ID</param>
        /// <returns>匯入結果</returns>
        public async Task<BatchImportResultDTO> BatchImportAssetsAsync(Stream fileStream, string userId)
        {
            var result = new BatchImportResultDTO();

            try
            {
                // 發送開始匯入通知
                await SendImportProgressNotification(userId, "start", new
                {
                    message = "開始批次匯入財產資料",
                    totalRows = 0,
                    currentRow = 0,
                    successRows = 0,
                    failedRows = 0
                });

                // 先驗證檔案格式
                var validationResult = await ValidateExcelFileAsync(fileStream);
                if (!validationResult.IsValid)
                {
                    result.Success = false;
                    result.Message = "檔案驗證失敗，請修正錯誤後重新上傳";
                    result.Errors = validationResult.Errors.Select(e => new BatchImportErrorDTO
                    {
                        RowIndex = e.RowIndex,
                        ErrorMessage = e.ErrorMessage
                    }).ToList();

                    // 發送驗證失敗通知
                    await SendImportProgressNotification(userId, "validation_failed", new
                    {
                        message = "檔案驗證失敗",
                        errors = result.Errors
                    });

                    return result;
                }

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                fileStream.Position = 0; // 重置檔案流位置

                using (var package = new ExcelPackage(fileStream))
                {
                    var worksheet = package.Workbook.Worksheets.First();
                    var columnMappings = ExcelHelper.GetAssetColumnMappings();

                    result.TotalRows = validationResult.TotalRows;

                    // 發送驗證成功通知
                    await SendImportProgressNotification(userId, "validation_success", new
                    {
                        message = "檔案驗證成功，開始匯入資料",
                        totalRows = result.TotalRows,
                        currentRow = 0,
                        successRows = 0,
                        failedRows = 0
                    });

                    // 清除該使用者既有取消旗標
                    lock (_userImportCancelFlags)
                    {
                        if (_userImportCancelFlags.ContainsKey(userId))
                        {
                            _userImportCancelFlags.Remove(userId);
                        }
                    }

                    // 開始逐行處理資料
                    for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
                    {
                        // 檢查是否要求取消
                        bool canceled = false;
                        lock (_userImportCancelFlags)
                        {
                            canceled = _userImportCancelFlags.ContainsKey(userId) && _userImportCancelFlags[userId];
                        }
                        if (canceled)
                        {
                            result.Success = false;
                            result.Message = $"批次匯入已被使用者取消於第 {row - 1} 行";
                            await SendImportProgressNotification(userId, "canceled", new
                            {
                                message = result.Message,
                                totalRows = result.TotalRows,
                                currentRow = row - 1,
                                successRows = result.SuccessRows,
                                failedRows = result.FailedRows
                            });
                            break;
                        }
                        try
                        {
                            var assetDto = ExcelHelper.ConvertExcelRowToAssetDTO(worksheet, row, columnMappings);

                            // 處理字串到GUID的轉換
                            await ConvertStringToGuidAsync(assetDto, worksheet, row);

                            // 設定建立資訊
                            assetDto.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                            assetDto.CreateUserId = userId;

                            // 建立包含附屬設備的DTO
                            var assetWithAccessoriesDto = new AssetWithAccessoriesDTO
                            {
                                Asset = assetDto,
                                AccessoryEquipments = new List<AccessoryEquipmentDTO>(),
                                InsuranceUnits = new List<InsuranceUnitDTO>(),
                                AmortizationSources = new List<AmortizationSourceDTO>(),
                                AssetSources = new List<AssetSourceDTO>()
                            };

                            // 新增財產
                            var (success, message) = await AddAssetAsync(assetWithAccessoriesDto);

                            if (success)
                            {
                                result.SuccessRows++;
                                result.SuccessAssetNos.Add(message);

                                // 成功匯入財產資訊
                                result.SuccessAssets.Add(new AssetImportSuccessDTO
                                {
                                    AssetNo = message,
                                    AssetName = assetDto.AssetName
                                });
                            }
                            else
                            {
                                result.FailedRows++;
                                result.Errors.Add(new BatchImportErrorDTO
                                {
                                    RowIndex = row,
                                    AssetNo = assetDto.AssetNo,
                                    AssetName = assetDto.AssetName,
                                    ErrorMessage = message
                                });
                            }

                            // 每處理10行或是最後一行時發送進度通知
                            if (row % 10 == 0 || row == worksheet.Dimension.End.Row)
                            {
                                await SendImportProgressNotification(userId, "progress", new
                                {
                                    message = $"正在處理第 {row} 行，共 {result.TotalRows} 行",
                                    totalRows = result.TotalRows,
                                    currentRow = row,
                                    successRows = result.SuccessRows,
                                    failedRows = result.FailedRows,
                                    progress = Math.Round((double)row / result.TotalRows * 100, 2)
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            result.FailedRows++;
                            result.Errors.Add(new BatchImportErrorDTO
                            {
                                RowIndex = row,
                                AssetNo = "",
                                AssetName = "",
                                ErrorMessage = $"處理第{row}行時發生錯誤：{ex.Message}"
                            });
                        }
                    }

                    // 若未被取消才送完成訊息
                    bool isCanceled;
                    lock (_userImportCancelFlags)
                    {
                        isCanceled = _userImportCancelFlags.ContainsKey(userId) && _userImportCancelFlags[userId];
                        if (isCanceled)
                        {
                            // 取消後清理旗標
                            _userImportCancelFlags.Remove(userId);
                        }
                    }

                    if (!isCanceled)
                    {
                        result.Success = result.FailedRows == 0;
                        result.Message = result.Success
                            ? $"批次匯入完成，成功匯入{result.SuccessRows}筆資料"
                            : $"批次匯入完成，成功{result.SuccessRows}筆，失敗{result.FailedRows}筆";

                        // 發送完成通知
                        await SendImportProgressNotification(userId, "completed", new
                        {
                            message = result.Message,
                            totalRows = result.TotalRows,
                            successRows = result.SuccessRows,
                            failedRows = result.FailedRows,
                            errors = result.Errors,
                            successAssets = result.SuccessAssets
                        });
                    }

                    return result;
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"批次匯入發生錯誤：{ex.Message}";
                result.Errors.Add(new BatchImportErrorDTO
                {
                    RowIndex = 0,
                    ErrorMessage = ex.Message
                });

                // 發送錯誤通知
                await SendImportProgressNotification(userId, "error", new
                {
                    message = result.Message,
                    error = ex.Message
                });

                return result;
            }
        }

        /// <summary>
        /// 發送匯入進度通知
        /// </summary>
        /// <param name="userId">使用者ID</param>
        /// <param name="action">動作類型</param>
        /// <param name="data">通知資料</param>
        private async Task SendImportProgressNotification(string userId, string action, object data)
        {
            try
            {
                var notification = new NotificationDto
                {
                    Type = "AssetImport",
                    Action = action,
                    Data = data
                };

                await _signalRMessageService.SendToUserIdGroupAsync(notification, userId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"發送匯入進度通知失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 將字串值轉換為對應的GUID (新格式)
        /// </summary>
        /// <param name="assetDto">財產DTO</param>
        /// <param name="worksheet">工作表</param>
        /// <param name="row">行號</param>
        private async Task ConvertStringToGuidAsync(AssetDTO assetDto, ExcelWorksheet worksheet, int row)
        {
            // 處理單位名稱轉換為ID
            var unitName = worksheet.Cells[row, 7].Value?.ToString()?.Trim();
            if (!string.IsNullOrEmpty(unitName))
            {
                var units = await _unitService.GetUnitAsync();
                var unit = units.FirstOrDefault(u => u.Name == unitName);
                if (unit != null)
                {
                    assetDto.UnitId = unit.UnitId;
                }
            }

            // 處理部門名稱轉換為ID
            var departmentName = worksheet.Cells[row, 13].Value?.ToString()?.Trim();
            if (!string.IsNullOrEmpty(departmentName))
            {
                var departments = await _context.Set<Department>()
                    .Where(d => d.Name == departmentName && (d.DeleteTime == 0 || d.DeleteTime == null))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();
                if (departments != null)
                {
                    assetDto.DepartmentId = departments.DepartmentId;
                }
            }

            // 處理股別名稱轉換為ID
            var divisionName = worksheet.Cells[row, 14].Value?.ToString()?.Trim();
            if (!string.IsNullOrEmpty(divisionName))
            {
                var divisions = await _context.Set<Division>()
                    .Where(d => d.Name == divisionName && (d.DeleteTime == 0 || d.DeleteTime == null))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();
                if (divisions != null)
                {
                    assetDto.DivisionId = divisions.DivisionId;
                }
            }

            // 處理保管人姓名轉換為ID
            var custodianName = worksheet.Cells[row, 15].Value?.ToString()?.Trim();
            if (!string.IsNullOrEmpty(custodianName))
            {
                var custodians = await _context.Set<Users>()
                    .Where(c => c.Name == custodianName && (c.IsDeleted == false))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();
                if (custodians != null)
                {
                    assetDto.CustodianId = custodians.UserId;
                }
                else
                {
                    // 如果保管人不存在，則新增一個
                    var newCustodian = new Users
                    {
                        UserId = Guid.NewGuid().ToString(),
                        Name = custodianName,
                        IsDeleted = false,
                        CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        CreateUserId = assetDto.CreateUserId,
                    };
                    await _context.Set<Users>().AddAsync(newCustodian);
                    await _context.SaveChangesAsync();
                    assetDto.CustodianId = newCustodian.UserId;
                }
            }

            // 處理使用人姓名轉換為ID
            var userName = worksheet.Cells[row, 16].Value?.ToString()?.Trim();
            if (!string.IsNullOrEmpty(userName))
            {
                var users = await _context.Set<Users>()
                    .Where(u => u.Name == userName && (u.IsDeleted == false))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();
                if (users != null)
                {
                    assetDto.UserId = users.UserId;
                }
                else
                {
                    // 如果使用人不存在，則新增一個
                    var newUser = new Users
                    {
                        UserId = Guid.NewGuid().ToString(),
                        Name = userName,
                        IsDeleted = false,
                        CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        CreateUserId = assetDto.CreateUserId,
                    };
                    await _context.Set<Users>().AddAsync(newUser);
                    await _context.SaveChangesAsync();
                    assetDto.UserId = newUser.UserId;
                }
            }

            // 處理財產狀態名稱轉換為GUID
            var assetStatusName = worksheet.Cells[row, 17].Value?.ToString()?.Trim();
            if (!string.IsNullOrEmpty(assetStatusName))
            {
                var assetStatuses = await _assetStatusService.GetAssetStatusAsync();
                var assetStatus = assetStatuses.FirstOrDefault(s => s.Name == assetStatusName);
                if (assetStatus != null)
                {
                    assetDto.AssetStatusId = assetStatus.AssetStatusId;
                }
            }

            // 處理存放地點名稱轉換為GUID
            var storageLocationName = worksheet.Cells[row, 21].Value?.ToString()?.Trim();
            if (!string.IsNullOrEmpty(storageLocationName))
            {
                var storageLocations = await _context.Set<StorageLocation>()
                    .Where(s => s.Name == storageLocationName && (s.DeleteTime == 0 || s.DeleteTime == null))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();
                if (storageLocations != null)
                {
                    assetDto.StorageLocationId = storageLocations.StorageLocationId.ToString();
                }
                else
                {
                    // 如果存放地點不存在，則新增一個
                    var newStorageLocation = new StorageLocation
                    {
                        StorageLocationId = Guid.NewGuid(),
                        Name = storageLocationName,
                        SortCode = 0,
                        CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        CreateUserId = assetDto.CreateUserId,
                        IsDeleted = false
                    };
                    await _context.Set<StorageLocation>().AddAsync(newStorageLocation);
                    await _context.SaveChangesAsync();
                    assetDto.StorageLocationId = newStorageLocation.StorageLocationId.ToString();
                }
            }

            // 處理廠牌型號名稱轉換為GUID
            var manufacturerName = worksheet.Cells[row, 24].Value?.ToString()?.Trim();
            if (!string.IsNullOrEmpty(manufacturerName))
            {
                var manufacturers = await _context.Set<Manufacturer>()
                    .Where(m => m.Name == manufacturerName && (m.DeleteTime == 0 || m.DeleteTime == null))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();
                if (manufacturers != null)
                {
                    assetDto.ManufacturerId = manufacturers.ManufacturerId;
                }
                else
                {
                    // 如果廠牌型號不存在，則新增一個
                    var newManufacturer = new Manufacturer
                    {
                        ManufacturerId = Guid.NewGuid(),
                        Name = manufacturerName,
                        Model = "",
                        ManufacturerName = manufacturerName,
                        Supplier = "",
                        ContactPerson = "",
                        ContactPhone = "",
                        ContactEmail = "",
                        Description = "",
                        SortCode = 0,
                        CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        CreateUserId = assetDto.CreateUserId,
                        IsDeleted = false
                    };
                    await _context.Set<Manufacturer>().AddAsync(newManufacturer);
                    await _context.SaveChangesAsync();
                    assetDto.ManufacturerId = newManufacturer.ManufacturerId;
                }
            }

            // 處理財產科目名稱轉換為GUID
            var assetAccountName = worksheet.Cells[row, 39].Value?.ToString()?.Trim();
            if (!string.IsNullOrEmpty(assetAccountName))
            {
                var assetAccounts = await _context.Set<AssetAccount>()
                    .Where(a => a.AssetAccountName == assetAccountName && (a.DeleteTime == 0 || a.DeleteTime == null))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();
                if (assetAccounts != null)
                {
                    assetDto.AssetAccountId = assetAccounts.AssetAccountId;
                }
                else
                {
                    // 如果財產科目不存在，則新增一個
                    var newAssetAccount = new AssetAccount
                    {
                        AssetAccountId = Guid.NewGuid(),
                        AssetAccountNo = assetAccountName.Substring(0, Math.Min(assetAccountName.Length, 10)),
                        AssetAccountName = assetAccountName,
                        SortCode = 0,
                        CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        CreateUserId = assetDto.CreateUserId,
                        IsDeleted = false
                    };
                    await _context.Set<AssetAccount>().AddAsync(newAssetAccount);
                    await _context.SaveChangesAsync();
                    assetDto.AssetAccountId = newAssetAccount.AssetAccountId;
                }
            }

            // 處理財產子目名稱轉換為GUID
            var assetSubAccountName = worksheet.Cells[row, 40].Value?.ToString()?.Trim();
            if (!string.IsNullOrEmpty(assetSubAccountName))
            {
                var assetSubAccounts = await _context.Set<AssetSubAccount>()
                    .Where(a => a.AssetSubAccountName == assetSubAccountName && (a.DeleteTime == 0 || a.DeleteTime == null))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();
                if (assetSubAccounts != null)
                {
                    assetDto.AssetSubAccountId = assetSubAccounts.AssetSubAccountId;
                }
                else
                {
                    // 如果財產子目不存在，則新增一個
                    var newAssetSubAccount = new AssetSubAccount
                    {
                        AssetSubAccountId = Guid.NewGuid(),
                        AssetSubAccountNo = assetSubAccountName.Substring(0, Math.Min(assetSubAccountName.Length, 10)),
                        AssetSubAccountName = assetSubAccountName,
                        AssetAccountId = assetDto.AssetAccountId, // 使用已設定的財產科目ID
                        SortCode = 0,
                        CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        CreateUserId = assetDto.CreateUserId,
                        IsDeleted = false
                    };
                    await _context.Set<AssetSubAccount>().AddAsync(newAssetSubAccount);
                    await _context.SaveChangesAsync();
                    assetDto.AssetSubAccountId = newAssetSubAccount.AssetSubAccountId;
                }
            }

            // 處理設備類型名稱轉換為GUID
            var equipmentTypeName = worksheet.Cells[row, 49].Value?.ToString()?.Trim();
            if (!string.IsNullOrEmpty(equipmentTypeName))
            {
                var equipmentTypes = await _equipmentTypeService.GetEquipmentTypeAsync();
                var equipmentType = equipmentTypes.FirstOrDefault(e => e.Name == equipmentTypeName);
                if (equipmentType != null)
                {
                    assetDto.EquipmentTypeId = equipmentType.EquipmentTypeId;
                }
                else
                {
                    // 如果設備類型不存在，則新增一個
                    var newEquipmentType = new EquipmentType
                    {
                        EquipmentTypeId = Guid.NewGuid(),
                        EquipmentTypeNo = equipmentTypeName.Substring(0, Math.Min(equipmentTypeName.Length, 10)),
                        Name = equipmentTypeName,
                        SortCode = 0,
                        CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        CreateUserId = assetDto.CreateUserId,
                        IsDeleted = false
                    };
                    await _context.Set<EquipmentType>().AddAsync(newEquipmentType);
                    await _context.SaveChangesAsync();
                    assetDto.EquipmentTypeId = newEquipmentType.EquipmentTypeId;
                }
            }
        }

        /// <summary>
        /// 下載批次轉檔範本
        /// </summary>
        /// <returns>Excel範本檔案</returns>
        public async Task<(byte[] fileBytes, string fileName)> DownloadBatchTemplateAsync()
        {
            try
            {
                var fileBytes = ExcelHelper.GenerateBatchTemplate();
                var fileName = $"PmsBatch_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                return (fileBytes, fileName);
            }
            catch (Exception ex)
            {
                throw new Exception($"產生範本檔案時發生錯誤：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 讀取Excel資料到驗證結果中
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <param name="columnMappings">欄位對應</param>
        /// <param name="validationResult">驗證結果</param>
        private void ReadExcelDataToResult(ExcelWorksheet worksheet, List<ExcelColumnMappingDTO> columnMappings, BatchValidationResultDTO validationResult)
        {
            try
            {
                // 設定欄位標題（依照Excel檔案順序）
                validationResult.ColumnHeaders = columnMappings.OrderBy(c => c.ColumnIndex).Select(c => c.ColumnName).ToList();

                // 讀取所有資料行
                for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
                {
                    var rowData = new ExcelRowDataDTO
                    {
                        RowIndex = row
                    };

                    // 依照欄位順序讀取每個儲存格的值
                    foreach (var mapping in columnMappings.OrderBy(c => c.ColumnIndex))
                    {
                        var cellValue = worksheet.Cells[row, mapping.ColumnIndex].Value?.ToString()?.Trim() ?? "";
                        rowData.ColumnValues[mapping.ColumnName] = cellValue;
                    }

                    validationResult.ExcelData.Add(rowData);
                }

                Console.WriteLine($"成功讀取Excel資料，共{validationResult.ExcelData.Count}行資料");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"讀取Excel資料時發生錯誤: {ex.Message}");
                // 如果讀取資料失敗，清空已讀取的資料
                validationResult.ColumnHeaders.Clear();
                validationResult.ExcelData.Clear();
            }
        }

        /// <summary>
        /// 驗證業務邏輯
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <param name="columnMappings">欄位對應</param>
        /// <param name="validationResult">驗證結果</param>
        private async Task ValidateBusinessLogicAsync(ExcelWorksheet worksheet, List<ExcelColumnMappingDTO> columnMappings, BatchValidationResultDTO validationResult)
        {
            var assetNos = new HashSet<string>();
            var errors = new List<BatchValidationErrorDTO>();

            // 查詢所有需要驗證的資料
            var allUnits = await _context.Set<Unit>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(u => u.Name, u => u);

            var allDepartments = await _context.Set<Department>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(d => d.Name, d => d);

            var allDivisions = await _context.Set<Division>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(d => d.Name, d => d);

            var allPmsUserMappings = await _context.Set<PmsUserRoleMapping>()
                .Where(u => u.IsDeleted == false)
                .Select(m => m.UserId)
                .Distinct()
                .ToHashSetAsync();

            var allAssetStatuses = await _context.Set<AssetStatus>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(s => s.Name, s => s);

            var allAssetAccounts = await _context.Set<AssetAccount>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(a => a.AssetAccountId, a => a);

            var allAssetSubAccounts = await _context.Set<AssetSubAccount>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(a => a.AssetSubAccountId, a => a);

            var allEquipmentTypes = await _context.Set<EquipmentType>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(e => e.Name, e => e);

            for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
            {
                var assetNoCell = worksheet.Cells[row, 2].Value?.ToString()?.Trim(); // 財產編號在第2欄

                // 檢查Excel內部財產編號重複
                if (!string.IsNullOrEmpty(assetNoCell))
                {
                    if (assetNos.Contains(assetNoCell))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "財產編號",
                            ErrorMessage = $"財產編號在檔案中重複: {assetNoCell}    ",
                            CellValue = assetNoCell
                        });
                    }
                    else
                    {
                        assetNos.Add(assetNoCell);

                        // 檢查資料庫中是否已存在
                        var existingAsset = await _context.Set<Asset>()
                            .FirstOrDefaultAsync(a => a.AssetNo == assetNoCell && (a.DeleteTime == 0 || a.DeleteTime == null));

                        if (existingAsset != null)
                        {
                            errors.Add(new BatchValidationErrorDTO
                            {
                                RowIndex = row,
                                ColumnName = "財產編號",
                                ErrorMessage = $"財產編號已存在於系統中: {assetNoCell}",
                                CellValue = assetNoCell
                            });
                        }
                    }
                }

                // 驗證單位名稱是否存在 (第7欄) - 新格式使用單位名稱
                var unitNameCell = worksheet.Cells[row, 7].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(unitNameCell))
                {
                    if (!allUnits.ContainsKey(unitNameCell))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "單位名稱",
                            ErrorMessage = $"單位名稱不存在於系統中: {unitNameCell}",
                            CellValue = unitNameCell
                        });
                    }
                }

                // 驗證部門是否存在 (第13欄) - 新格式使用部門名稱
                var departmentNameCell = worksheet.Cells[row, 13].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(departmentNameCell))
                {
                    if (!allDepartments.ContainsKey(departmentNameCell))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "部門",
                            ErrorMessage = $"部門名稱不存在於系統中: {departmentNameCell}",
                            CellValue = departmentNameCell
                        });
                    }
                }

                // 驗證股別是否存在 (第14欄) - 新格式使用股別名稱
                var divisionNameCell = worksheet.Cells[row, 14].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(divisionNameCell))
                {
                    if (!allDivisions.ContainsKey(divisionNameCell))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "股別",
                            ErrorMessage = $"股別名稱不存在於系統中: {divisionNameCell}",
                            CellValue = divisionNameCell
                        });
                    }
                }

                // 驗證保管人是否存在 (第15欄) - 新格式使用保管人姓名
                var custodianNameCell = worksheet.Cells[row, 15].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(custodianNameCell))
                {
                    // 這裡需要根據保管人姓名查找對應的使用者ID
                    // 暫時跳過驗證，因為需要額外的使用者查詢邏輯
                }

                // 驗證使用人是否存在 (第16欄) - 新格式使用使用人姓名
                var userNameCell = worksheet.Cells[row, 16].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(userNameCell))
                {
                    // 這裡需要根據使用人姓名查找對應的使用者ID
                    // 暫時跳過驗證，因為需要額外的使用者查詢邏輯
                }

                // 驗證財產狀態是否存在 (第17欄) - 新格式使用狀態名稱
                var assetStatusNameCell = worksheet.Cells[row, 17].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(assetStatusNameCell))
                {
                    if (!allAssetStatuses.ContainsKey(assetStatusNameCell))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "財產狀態",
                            ErrorMessage = $"財產狀態名稱不存在於系統中: {assetStatusNameCell}",
                            CellValue = assetStatusNameCell
                        });
                    }
                }

                // 驗證財產科目是否存在 (第39欄) - 仍使用GUID
                var assetAccountIdCell = worksheet.Cells[row, 39].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(assetAccountIdCell) && Guid.TryParse(assetAccountIdCell, out var assetAccountId))
                {
                    if (!allAssetAccounts.ContainsKey(assetAccountId))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "財產科目",
                            ErrorMessage = $"財產科目ID不存在於系統中: {assetAccountIdCell}",
                            CellValue = assetAccountIdCell
                        });
                    }
                }

                // 驗證財產子目是否存在 (第40欄) - 仍使用GUID
                var assetSubAccountIdCell = worksheet.Cells[row, 40].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(assetSubAccountIdCell) && Guid.TryParse(assetSubAccountIdCell, out var assetSubAccountId))
                {
                    if (!allAssetSubAccounts.ContainsKey(assetSubAccountId))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "財產子目",
                            ErrorMessage = $"財產子目ID不存在於系統中: {assetSubAccountIdCell}",
                            CellValue = assetSubAccountIdCell
                        });
                    }
                }

                // 驗證設備類型是否存在 (第49欄) - 新格式使用設備類型名稱
                var equipmentTypeNameCell = worksheet.Cells[row, 49].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(equipmentTypeNameCell))
                {
                    if (!allEquipmentTypes.ContainsKey(equipmentTypeNameCell))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "設備類型",
                            ErrorMessage = $"設備類型名稱不存在於系統中: {equipmentTypeNameCell}",
                            CellValue = equipmentTypeNameCell
                        });
                    }
                }
            }

            if (errors.Any())
            {
                validationResult.IsValid = false;
                validationResult.Errors.AddRange(errors);
                validationResult.ErrorRows = validationResult.Errors.Select(e => e.RowIndex).Distinct().Count();
                validationResult.ValidRows = validationResult.TotalRows - validationResult.ErrorRows;
            }
        }

        #region 財產統計功能

        /// <summary>
        /// 取得財產統計資料
        /// </summary>
        /// <param name="request">統計查詢參數</param>
        /// <returns>完整統計結果</returns>
        public async Task<AssetStatisticsResultDTO> GetAssetStatisticsAsync(AssetStatisticsRequestDTO? request = null)
        {
            try
            {
                var result = new AssetStatisticsResultDTO();

                // 根據請求參數決定要執行哪些統計
                var statisticTypes = request?.StatisticTypes ?? new List<string>
                {
                    "overview", "department", "status", "account", "year", "month",
                    "manufacturer", "equipmentType", "age", "depreciation", "valueRange"
                };

                // 並行執行所有統計查詢以提高效能
                var tasks = new List<Task>();

                if (statisticTypes.Contains("overview"))
                    tasks.Add(Task.Run(async () => result.Overview = await GetAssetStatisticsOverviewAsync()));

                if (statisticTypes.Contains("department"))
                    tasks.Add(Task.Run(async () => result.ByDepartment = await GetAssetStatisticsByDepartmentAsync(request?.DepartmentIds)));

                if (statisticTypes.Contains("status"))
                    tasks.Add(Task.Run(async () => result.ByStatus = await GetAssetStatisticsByStatusAsync(request?.StatusIds)));

                if (statisticTypes.Contains("account"))
                    tasks.Add(Task.Run(async () => result.ByAccount = await GetAssetStatisticsByAccountAsync(request?.AccountIds)));

                if (statisticTypes.Contains("year"))
                    tasks.Add(Task.Run(async () => result.ByYear = await GetAssetStatisticsByYearAsync()));

                if (statisticTypes.Contains("month"))
                    tasks.Add(Task.Run(async () => result.ByMonth = await GetAssetStatisticsByMonthAsync()));

                if (statisticTypes.Contains("manufacturer"))
                    tasks.Add(Task.Run(async () => result.ByManufacturer = await GetAssetStatisticsByManufacturerAsync()));

                if (statisticTypes.Contains("equipmentType"))
                    tasks.Add(Task.Run(async () => result.ByEquipmentType = await GetAssetStatisticsByEquipmentTypeAsync()));

                if (statisticTypes.Contains("age"))
                    tasks.Add(Task.Run(async () => result.ByAge = await GetAssetStatisticsByAgeAsync()));

                if (statisticTypes.Contains("depreciation"))
                    tasks.Add(Task.Run(async () => result.DepreciationStatistics = await GetAssetDepreciationStatisticsAsync()));

                if (statisticTypes.Contains("valueRange"))
                    tasks.Add(Task.Run(async () => result.ByValueRange = await GetAssetStatisticsByValueRangeAsync()));

                await Task.WhenAll(tasks);

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得財產統計資料時發生錯誤: {ex.Message}");
                return new AssetStatisticsResultDTO();
            }
        }

        /// <summary>
        /// 取得財產統計總覽
        /// </summary>
        /// <returns>統計總覽資料</returns>
        public async Task<AssetStatisticsOverviewDTO> GetAssetStatisticsOverviewAsync()
        {
            try
            {
                var currentYear = DateTime.Now.Year;
                var currentMonth = DateTime.Now.Month;
                var currentYearStart = new DateTime(currentYear, 1, 1);
                var currentMonthStart = new DateTime(currentYear, currentMonth, 1);
                var currentYearStartTs = ((DateTimeOffset)currentYearStart).ToUnixTimeSeconds();
                var currentMonthStartTs = ((DateTimeOffset)currentMonthStart).ToUnixTimeSeconds();

                var assets = await _context.Set<Asset>()
                    .Where(a => !a.IsDeleted && (a.DeleteTime == null || a.DeleteTime == 0))
                    .AsNoTracking()
                    .ToListAsync();

                var overview = new AssetStatisticsOverviewDTO
                {
                    TotalAssets = assets.Count,
                    TotalValue = assets.Sum(a => a.PurchaseAmount),
                    TotalDepreciation = assets.Sum(a => a.AccumulatedDepreciationAmount),
                    ThisYearNewAssets = assets.Count(a => a.CreateTime >= currentYearStartTs),
                    ThisMonthNewAssets = assets.Count(a => a.CreateTime >= currentMonthStartTs)
                };

                overview.TotalNetValue = overview.TotalValue - overview.TotalDepreciation;

                // 統計不同狀態的資產
                var assetStatuses = await _context.Set<AssetStatus>()
                    .Where(s => !s.IsDeleted)
                    .AsNoTracking()
                    .ToListAsync();

                var statusDict = assetStatuses.ToDictionary(s => s.AssetStatusId, s => s.Name);

                foreach (var asset in assets)
                {
                    if (statusDict.TryGetValue(asset.AssetStatusId, out var statusName))
                    {
                        switch (statusName.ToLower())
                        {
                            case "已報廢":
                                overview.PendingScrapAssets++;
                                break;
                            case "維修中":
                                overview.UnderMaintenanceAssets++;
                                break;
                            case "租借中":
                                overview.IdleAssets++;
                                break;
                            case "報廢後堪用":
                                overview.ReusableAfterScrapAssets++;
                                break;
                        }
                    }
                }

                return overview;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得財產統計總覽時發生錯誤: {ex.Message}");
                return new AssetStatisticsOverviewDTO();
            }
        }

        #endregion
    }
}
