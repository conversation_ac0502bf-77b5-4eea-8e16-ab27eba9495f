/**
 * 聯絡人驗證工具測試
 * 
 * 測試統一的聯絡人驗證規則，確保與後端 ValidationHelper.cs 一致
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import {
  getNameValidationRules,
  getEmailValidationRules,
  getPhoneValidationRules,
  getPositionValidationRules,
  getDepartmentValidationRules,
  getCompanyValidationRules,
  getContactFieldValidationRules,
  validateContactData,
  sanitizeContactData
} from '../contactValidation';

describe('contactValidation', () => {
  describe('getNameValidationRules', () => {
    it('should return required and max length rules', () => {
      const rules = getNameValidationRules();
      
      expect(rules).toHaveLength(2);
      expect(rules[0]).toEqual({
        required: true,
        message: '請輸入姓名'
      });
      expect(rules[1]).toEqual({
        max: 100,
        message: '姓名不能超過100個字元'
      });
    });
  });

  describe('getEmailValidationRules', () => {
    it('should return email type and max length rules', () => {
      const rules = getEmailValidationRules();
      
      expect(rules).toHaveLength(2);
      expect(rules[0]).toEqual({
        type: 'email',
        message: '請輸入有效的電子郵件地址'
      });
      expect(rules[1]).toEqual({
        max: 255,
        message: '電子郵件不能超過255個字元'
      });
    });
  });

  describe('getPhoneValidationRules', () => {
    it('should return max length rule', () => {
      const rules = getPhoneValidationRules();
      
      expect(rules).toHaveLength(1);
      expect(rules[0]).toEqual({
        max: 50,
        message: '電話不能超過50個字元'
      });
    });
  });

  describe('getContactFieldValidationRules', () => {
    it('should return correct rules for name field', () => {
      const rules = getContactFieldValidationRules('name');
      expect(rules).toHaveLength(2);
      expect(rules[0].required).toBe(true);
    });

    it('should return correct rules for email field', () => {
      const rules = getContactFieldValidationRules('email');
      expect(rules).toHaveLength(2);
      expect(rules[0].type).toBe('email');
    });

    it('should return empty array for unknown field', () => {
      const rules = getContactFieldValidationRules('unknown');
      expect(rules).toHaveLength(0);
    });
  });

  describe('validateContactData', () => {
    it('should pass validation for valid contact data', () => {
      const validData = {
        name: '張三',
        email: '<EMAIL>',
        phone: '0912345678',
        position: '經理',
        department: '業務部',
        company: 'ABC公司'
      };

      const result = validateContactData(validData);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should fail validation when name is missing', () => {
      const invalidData = {
        email: '<EMAIL>'
      };

      const result = validateContactData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('請輸入姓名');
    });

    it('should fail validation when name is empty string', () => {
      const invalidData = {
        name: '   ',
        email: '<EMAIL>'
      };

      const result = validateContactData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('請輸入姓名');
    });

    it('should fail validation when name exceeds max length', () => {
      const invalidData = {
        name: 'a'.repeat(101), // 101 characters
        email: '<EMAIL>'
      };

      const result = validateContactData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('姓名不能超過100個字元');
    });

    it('should fail validation for invalid email format', () => {
      const invalidData = {
        name: '張三',
        email: 'invalid-email'
      };

      const result = validateContactData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('請輸入有效的電子郵件地址');
    });

    it('should fail validation when email exceeds max length', () => {
      const invalidData = {
        name: '張三',
        email: 'a'.repeat(250) + '@example.com' // > 255 characters
      };

      const result = validateContactData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('電子郵件不能超過255個字元');
    });

    it('should fail validation when phone exceeds max length', () => {
      const invalidData = {
        name: '張三',
        phone: '0'.repeat(51) // 51 characters
      };

      const result = validateContactData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('電話不能超過50個字元');
    });

    it('should pass validation with empty optional fields', () => {
      const validData = {
        name: '張三',
        email: '',
        phone: '',
        position: '',
        department: '',
        company: ''
      };

      const result = validateContactData(validData);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should collect multiple validation errors', () => {
      const invalidData = {
        name: '', // missing name
        email: 'invalid-email', // invalid format
        phone: '0'.repeat(51) // too long
      };

      const result = validateContactData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(3);
      expect(result.errors).toContain('請輸入姓名');
      expect(result.errors).toContain('請輸入有效的電子郵件地址');
      expect(result.errors).toContain('電話不能超過50個字元');
    });
  });

  describe('sanitizeContactData', () => {
    it('should trim whitespace from string fields', () => {
      const dirtyData = {
        name: '  張三  ',
        email: '  <EMAIL>  ',
        phone: '  0912345678  ',
        position: '  經理  ',
        department: '  業務部  ',
        company: '  ABC公司  '
      };

      const result = sanitizeContactData(dirtyData);
      
      expect(result.name).toBe('張三');
      expect(result.email).toBe('<EMAIL>'); // should be lowercase
      expect(result.phone).toBe('0912345678');
      expect(result.position).toBe('經理');
      expect(result.department).toBe('業務部');
      expect(result.company).toBe('ABC公司');
    });

    it('should convert email to lowercase', () => {
      const data = {
        name: '張三',
        email: '<EMAIL>'
      };

      const result = sanitizeContactData(data);
      
      expect(result.email).toBe('<EMAIL>');
    });

    it('should handle empty/null values', () => {
      const data = {
        name: null,
        email: undefined,
        phone: '',
        position: null
      };

      const result = sanitizeContactData(data);
      
      expect(result.name).toBe('');
      expect(result.email).toBe('');
      expect(result.phone).toBe('');
      expect(result.position).toBe('');
    });

    it('should set default values for contactType and status', () => {
      const data = {
        name: '張三'
      };

      const result = sanitizeContactData(data);
      
      expect(result.contactType).toBe('客戶');
      expect(result.status).toBe(true);
    });

    it('should preserve boolean status value', () => {
      const data = {
        name: '張三',
        status: false
      };

      const result = sanitizeContactData(data);
      
      expect(result.status).toBe(false);
    });

    it('should preserve existing contactType', () => {
      const data = {
        name: '張三',
        contactType: '供應商'
      };

      const result = sanitizeContactData(data);
      
      expect(result.contactType).toBe('供應商');
    });
  });
});
