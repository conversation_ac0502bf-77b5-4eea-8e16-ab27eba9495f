/** @type {import('next').NextConfig} */
const nextConfig = {
    reactStrictMode: false,
    transpilePackages: [
        "antd",
        "@ant-design",
        "rc-util",
        "rc-pagination",
        "rc-picker",
    ],
    output: 'export',
    trailingSlash: true,
    images: {
        unoptimized: true
    },
    //basePath: '/fasterp',
    //assetPrefix: '/fasterp',

    // 加入這些設定來改善客戶端路由
    experimental: {
        optimizePackageImports: ['antd'],
    },

    // 確保靜態匯出時的路由行為一致
    skipTrailingSlashRedirect: true,

     // 啟用 standalone 輸出模式，讓容器更小更高效
     output: 'standalone',
    
     // 移除 Next.js 自身的 API 重寫，改由 A-Nginx 單一路徑處理 (/api → backend)
     // 若未來需要本機直連後端，可在開發環境動態加入 rewrites
     // async rewrites() {
     //     return []
     // },
     
     // 環境變數配置
     env: {
         // 建議前端以相對路徑呼叫 API (/api)，由反向代理統一處理
         // 如需直連，請改用 NEXT_PUBLIC_API_URL 並於程式內讀取
     },
};

module.exports = nextConfig;