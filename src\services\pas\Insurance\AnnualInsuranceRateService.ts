import { apiEndpoints } from "@/config/api";
import { httpClient } from "../../http";
import { ApiResponse } from "@/config/api";

// 年度保險費率介面 - 匹配後端 DTO 欄位名稱
export interface AnnualInsuranceRate {
    uid: string;
    year: number; // 年度
    // 勞保局相關費率
    laborInsurance: {
        generalAccidentRate: number; // 普通事故保險費率 (%)
        occupationalDisasterRate: number; // 適用職業災害費率 (%)
        employmentInsuranceRate: number; // 就業保險費率 (%)
        employeeBurdenRatio: number; // 員工負擔比率 (%)
        employerBurdenRatio: number; // 投保單位負擔比率 (%)
    };
    // 健保局相關費率
    healthInsurance: {
        premiumRate: number; // 保費費率 (%)
        employeeBurdenRatio: number; // 員工負擔比率 (%)
        employerBurdenRatio: number; // 投保單位負擔比率 (%)
        averageDependents: number; // 平均眷口數
    };
    remark?: string | null; // 備註
}

// 創建空的年度保險費率資料
export const createEmptyAnnualInsuranceRate = (): AnnualInsuranceRate => ({
    uid: '',
    year: new Date().getFullYear(),
    laborInsurance: {
        generalAccidentRate: 0,
        occupationalDisasterRate: 0,
        employmentInsuranceRate: 0,
        employeeBurdenRatio: 0,
        employerBurdenRatio: 0
    },
    healthInsurance: {
        premiumRate: 0,
        employeeBurdenRatio: 0,
        employerBurdenRatio: 0,
        averageDependents: 0
    },
    remark: null,
});

// 取得年度保險費率列表
export async function getAnnualInsuranceRateList(): Promise<ApiResponse<AnnualInsuranceRate[]>> {
    try {
        const response = await httpClient(apiEndpoints.getAnnualInsuranceRateList, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得年度保險費率列表失敗",
        };
    }
}

// 取得年度保險費率明細
export async function getAnnualInsuranceRateDetail(uid: string): Promise<ApiResponse<AnnualInsuranceRate>> {
    try {
        const response = await httpClient(`${apiEndpoints.getAnnualInsuranceRateDetail}/${uid}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得年度保險費率明細失敗",
        };
    }
}

// 新增年度保險費率
export async function addAnnualInsuranceRate(data: Partial<AnnualInsuranceRate>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addAnnualInsuranceRate, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增年度保險費率失敗",
        };
    }
}

// 編輯年度保險費率
export async function editAnnualInsuranceRate(data: Partial<AnnualInsuranceRate>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editAnnualInsuranceRate, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯年度保險費率失敗",
        };
    }
}

// 刪除年度保險費率
export async function deleteAnnualInsuranceRate(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteAnnualInsuranceRate, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: {
                "Content-Type": "application/json",
            },
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除年度保險費率失敗",
        };
    }
}

// 根據年度取得保險費率
export async function getAnnualInsuranceRateByYear(year: number): Promise<ApiResponse<AnnualInsuranceRate>> {
    try {
        const response = await httpClient(`${apiEndpoints.getAnnualInsuranceRateByYear}/${year}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "查詢年度保險費率失敗",
        };
    }
}
