using FAST_ERP_Backend.Interfaces.Rms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Rms;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace FAST_ERP_Backend.Controllers.Rms
{
    /// <summary>
    /// 費用管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class FeeController : ControllerBase
    {
        private readonly IFeeService _feeService;

        /// <summary>
        /// 初始化費用控制器
        /// </summary>
        /// <param name="feeService">費用服務</param>
        public FeeController(IFeeService feeService)
        {
            _feeService = feeService;
        }

        private ClaimsPrincipal LoginUser => HttpContext.User;

        /// <summary>
        /// 取得費用列表
        /// </summary>
        /// <param name="contractId">合約編號</param>
        /// <param name="status">費用狀態</param>
        /// <param name="feeType">費用類型</param>
        /// <param name="billingPeriod">計費期間</param>
        /// <returns>費用列表</returns>
        [HttpGet]
        [ProducesResponseType(typeof(ApiResponse<List<FeeDTO>>), 200)]
        public async Task<IActionResult> GetFees(
            [FromQuery, Required] string contractId,
            [FromQuery] string? status,
            [FromQuery] string? feeType,
            [FromQuery] string? billingPeriod)
        {
            var result = await _feeService.GetFeeListAsync(contractId, status, feeType, billingPeriod);
            return result.Success ? Ok(result) : StatusCode(result.HttpCode, result);
        }

        /// <summary>
        /// 建立費用
        /// </summary>
        /// <param name="request">費用建立請求</param>
        /// <returns>建立結果</returns>
        [HttpPost]
        [ProducesResponseType(typeof(ApiResponse<FeeDTO>), 200)]
        public async Task<IActionResult> CreateFee([FromBody] FeeCreateRequestDTO request)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier)!.Value;
            var result = await _feeService.CreateFeeAsync(request, tokenUid);
            return result.Success ? Ok(result) : StatusCode(result.HttpCode, result);
        }

        /// <summary>
        /// 更新費用
        /// </summary>
        /// <param name="request">費用更新請求</param>
        /// <returns>更新結果</returns>
        [HttpPut]
        [ProducesResponseType(typeof(ApiResponse<string>), 200)]
        public async Task<IActionResult> UpdateFee([FromBody] FeeUpdateRequestDTO request)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier)!.Value;
            var result = await _feeService.UpdateFeeAsync(request, tokenUid);
            return result.Success ? Ok(result) : StatusCode(result.HttpCode, result);
        }

        /// <summary>
        /// 刪除費用
        /// </summary>
        /// <param name="request">費用刪除請求</param>
        /// <returns>刪除結果</returns>
        [HttpDelete]
        [ProducesResponseType(typeof(ApiResponse<string>), 200)]
        public async Task<IActionResult> DeleteFee([FromBody] FeeDeleteRequestDTO request)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier)!.Value;
            var result = await _feeService.DeleteFeeAsync(request, tokenUid);
            return result.Success ? Ok(result) : StatusCode(result.HttpCode, result);
        }
    }
}


