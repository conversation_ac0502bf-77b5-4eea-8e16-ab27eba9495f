import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 折舊紀錄介面
export interface Depreciation {
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
    depreciationId: string;
    assetId: string;
    assetNo: string;
    assetName: string;
    depreciationYear: number;
    depreciationMonth: number;
    originalAmount: number;
    accumulatedDepreciation: number;
    // 新增：前期累計折舊
    priorAccumulatedDepreciation?: number;
    currentDepreciation: number;
    depreciationRate: number;
    depreciationMethod: string;
    serviceLifeRemaining: number;
    isAdjustment: boolean;
    adjustmentReason: string;
    notes: string;
    beginningBookValue: number;
    endingBookValue: number;
    depreciationDate: number;
    createUserName?: string;
    updateUserName?: string;
    deleteUserName?: string;
}

// 折舊計算請求介面
export interface DepreciationCalculateRequest {
    assetId: string;
    year: number;
    month: number;
    userId: string;
}

// 獲取所有折舊紀錄
export async function getDepreciations(): Promise<ApiResponse<Depreciation[]>> {
    try {
        const response = await httpClient(apiEndpoints.getDepreciations, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log("getDepreciations");
        console.log(response.data);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取折舊紀錄列表失敗",
            data: [],
        };
    }
}

// 依年月取得折舊紀錄
export async function getDepreciationsByYearMonth(
    year: number,
    month: number,
    hasCreateDepreciationFormDate?: boolean | null
): Promise<ApiResponse<Depreciation[]>> {
    try {
        let url = `${apiEndpoints.getDepreciationsByYearMonth}?year=${year}&month=${month}`;

        // 如果有指定 hasCreateDepreciationFormDate 參數，則加入查詢字串
        if (hasCreateDepreciationFormDate !== undefined && hasCreateDepreciationFormDate !== null) {
            url += `&hasCreateDepreciationFormDate=${hasCreateDepreciationFormDate}`;
        }

        const response = await httpClient(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log("getDepreciationsByYearMonth", year, month, hasCreateDepreciationFormDate);
        console.log(response.data);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "依年月取得折舊紀錄失敗",
            data: [],
        };
    }
}

// 獲取特定折舊紀錄
export async function getDepreciationById(id: string): Promise<ApiResponse<Depreciation>> {
    try {
        const response = await httpClient(`${apiEndpoints.getDepreciationDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取折舊紀錄詳情失敗",
            data: undefined,
        };
    }
}

// 新增折舊紀錄
export async function createDepreciation(data: Partial<Depreciation>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addDepreciation, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增折舊紀錄失敗",
        };
    }
}

// 更新折舊紀錄
export async function updateDepreciation(data: Partial<Depreciation>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editDepreciation, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新折舊紀錄失敗",
        };
    }
}

// 刪除折舊紀錄
export async function deleteDepreciation(data: Partial<Depreciation>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteDepreciation, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除折舊紀錄失敗",
        };
    }
}

// 取得折舊方法列表
export async function getDepreciationMethods(): Promise<ApiResponse<string[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getDepreciations}/Methods`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取折舊方法列表失敗",
            data: [],
        };
    }
}

// 取得餘額遞減法折舊率
export async function getDecliningBalanceRate(assetAccountId: string): Promise<ApiResponse<number>> {
    try {
        const response = await httpClient(`${apiEndpoints.getDecliningBalanceRate}/${assetAccountId}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取餘額遞減法折舊率失敗",
            data: 0,
        };
    }
}

// 計算折舊
export async function calculateDepreciation(request: DepreciationCalculateRequest): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.getDepreciations}/Calculate`, {
            method: "POST",
            body: JSON.stringify(request),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "計算折舊失敗",
        };
    }
}

