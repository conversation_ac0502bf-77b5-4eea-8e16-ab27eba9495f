/**
 * 聯絡人表單欄位組件
 * 
 * 可重用的純表單欄位組件，支援不同模式和響應式設計
 * 充分利用統一的常數、驗證規則和工具函數
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

"use client";

import React, { useEffect } from 'react';
import { Form, Input, Select, Switch, Row, Col } from 'antd';
import { UserOutlined, MailOutlined, PhoneOutlined } from '@ant-design/icons';

// 統一的常數和工具
import { 
  CONTACT_TYPES, 
  CONTACT_FORM_FIELDS, 
  CONTACT_DEFAULTS 
} from './shared/contactConstants';
import {
  getContactFieldValidationRules
} from './shared/contactValidation';
import { Contact } from '@/services/ims/ContactService';

// 響應式工具
import { useScreenSize } from '../shared/ResponsiveModalConfig';

const { Option } = Select;

export interface ContactFormFieldsProps {
  /** 表單模式：快速、完整、唯讀 */
  mode?: 'quick' | 'full' | 'readonly';
  /** 初始值 */
  initialValues?: Partial<Contact>;
  /** 值變更回調 */
  onChange?: (values: Partial<Contact>) => void;
  /** 表單實例（外部控制） */
  form?: any;
  /** 是否禁用所有欄位 */
  disabled?: boolean;
  /** 自定義樣式類名 */
  className?: string;
}

/**
 * 聯絡人表單欄位組件
 * 
 * 支援三種模式：
 * - quick: 快速新增模式，只顯示核心欄位
 * - full: 完整模式，顯示所有欄位
 * - readonly: 唯讀模式，所有欄位都禁用
 */
const ContactFormFields: React.FC<ContactFormFieldsProps> = ({
  mode = 'full',
  initialValues,
  onChange,
  form,
  disabled = false,
  className = ''
}) => {
  const { isMobile } = useScreenSize();
  const isReadonly = mode === 'readonly';
  const isDisabled = disabled || isReadonly;

  // 根據模式決定要顯示的欄位
  const fieldsToShow = mode === 'quick' 
    ? [...CONTACT_FORM_FIELDS.quick] 
    : [...CONTACT_FORM_FIELDS.all];

  // 初始化表單值
  useEffect(() => {
    if (form && initialValues) {
      form.setFieldsValue({
        ...CONTACT_DEFAULTS,
        ...initialValues
      });
    }
  }, [form, initialValues]);

  // 處理表單值變更
  const handleValuesChange = (changedValues: any, allValues: any) => {
    onChange?.(allValues);
  };

  // 欄位渲染配置
  const fieldConfigs = {
    name: {
      label: "姓名",
      required: true,
      render: () => (
        <Input
          prefix={<UserOutlined />}
          placeholder="請輸入姓名"
          size={isMobile ? "large" : "middle"}
          disabled={isDisabled}
        />
      )
    },
    position: {
      label: "職位",
      render: () => (
        <Input
          placeholder="請輸入職位"
          size={isMobile ? "large" : "middle"}
          disabled={isDisabled}
        />
      )
    },
    email: {
      label: "電子郵件",
      render: () => (
        <Input
          prefix={<MailOutlined />}
          placeholder="請輸入電子郵件"
          size={isMobile ? "large" : "middle"}
          disabled={isDisabled}
        />
      )
    },
    phone: {
      label: "電話",
      render: () => (
        <Input
          prefix={<PhoneOutlined />}
          placeholder="請輸入電話"
          size={isMobile ? "large" : "middle"}
          disabled={isDisabled}
        />
      )
    },
    department: {
      label: "部門",
      render: () => (
        <Input
          placeholder="請輸入部門"
          size={isMobile ? "large" : "middle"}
          disabled={isDisabled}
        />
      )
    },
    company: {
      label: "公司",
      render: () => (
        <Input
          placeholder="請輸入公司"
          size={isMobile ? "large" : "middle"}
          disabled={isDisabled}
        />
      )
    },
    contactType: {
      label: "聯絡人類型",
      render: () => (
        <Select
          placeholder="請選擇聯絡人類型"
          size={isMobile ? "large" : "middle"}
          allowClear
          disabled={isDisabled}
        >
          {CONTACT_TYPES.map((type) => (
            <Option key={type.value} value={type.value}>
              {type.label}
            </Option>
          ))}
        </Select>
      )
    },
    isActive: {
      label: "狀態",
      render: () => (
        <Switch
          checkedChildren="啟用"
          unCheckedChildren="停用"
          size={isMobile ? "default" : "default"}
          disabled={isDisabled}
        />
      )
    }
  };

  // 渲染欄位
  const renderField = (fieldName: string) => {
    const config = fieldConfigs[fieldName as keyof typeof fieldConfigs];
    if (!config) return null;

    const validationRules = getContactFieldValidationRules(fieldName);
    const isStatusField = fieldName === 'isActive';

    return (
      <Form.Item
        key={fieldName}
        label={config.label}
        name={fieldName}
        rules={validationRules}
        valuePropName={isStatusField ? "checked" : "value"}
      >
        {config.render()}
      </Form.Item>
    );
  };

  // 欄位佈局配置
  const getFieldLayout = () => {
    if (isMobile) {
      // 移動端：所有欄位單列
      return fieldsToShow.map(field => ({ field, span: 24 }));
    }

    // 桌面端：雙列佈局
    return fieldsToShow.map((field, index) => ({
      field,
      span: 12
    }));
  };

  const fieldLayout = getFieldLayout();

  return (
    <div className={`contact-form-fields ${className}`}>
      <Row gutter={isMobile ? [8, 16] : [16, 16]}>
        {fieldLayout.map(({ field, span }, index) => (
          <Col key={field} xs={24} sm={span} md={span}>
            {renderField(field)}
          </Col>
        ))}
      </Row>

      {/* 模式指示器（開發時用，生產環境可移除） */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ 
          marginTop: 16, 
          padding: 8, 
          backgroundColor: '#f0f0f0', 
          borderRadius: 4,
          fontSize: 12,
          color: '#666'
        }}>
          模式: {mode} | 欄位數: {fieldsToShow.length} | 響應式: {isMobile ? '移動端' : '桌面端'}
        </div>
      )}
    </div>
  );
};

export default ContactFormFields;

