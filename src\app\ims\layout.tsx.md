"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { usePermission } from '@/contexts/PermissionContext';

export default function ImsLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { ready, can } = usePermission();

  useEffect(() => {
    if (!ready) return;
    if (!can('Ims/**', 'Read')) router.replace('/403');
  }, [ready, can, router]);

  if (!ready) return null;
  if (!can('Ims/**', 'Read')) return null;
  return <>{children}</>;
}

