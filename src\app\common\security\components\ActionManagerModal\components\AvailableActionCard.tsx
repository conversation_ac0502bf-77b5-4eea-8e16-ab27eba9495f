import React from 'react';
import { createContextLogger, SYMBOLS } from '@/utils/logger';
import { AvailableActionCardProps } from '../types/actionManager.types';

// 遵循 FastERP 日誌記錄策略
const availableActionCardLogger = createContextLogger({ module: 'AvailableActionCard' });

const AvailableActionCard: React.FC<AvailableActionCardProps> = ({
  action,
  isSelected,
  onToggle
}) => {
  const handleToggle = () => {
    availableActionCardLogger.log(SYMBOLS.DEBUG, '切換動作選取狀態', { 
      actionCode: action.code, 
      isSelected: !isSelected 
    });
    onToggle();
  };

  return (
    <div
      style={{
        border: isSelected ? '2px solid #1890ff' : '1px solid #d9d9d9',
        background: isSelected ? '#f0f8ff' : '#fff',
        transition: 'border-color 0.2s ease, background-color 0.2s ease',
        textAlign: 'center',
        padding: '16px 12px',
        borderRadius: '6px',
        cursor: 'pointer',
        position: 'relative',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        minHeight: '80px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center'
      }}
      onClick={handleToggle}
    >
      {/* 選取狀態指示器 */}
      {isSelected && (
        <div style={{
          position: 'absolute',
          top: '6px',
          right: '6px',
          width: '12px',
          height: '12px',
          background: '#1890ff',
          borderRadius: '50%',
          border: '2px solid #fff'
        }} />
      )}

      {/* 動作名稱 */}
      <div style={{ 
        fontSize: '14px', 
        fontWeight: '600',
        marginBottom: '4px',
        color: isSelected ? '#1890ff' : '#262626'
      }}>
        {action.name}
      </div>
      
      {/* 動作代碼 */}
      <div style={{ 
        fontSize: '11px', 
        color: isSelected ? '#1890ff' : '#8c8c8c',
        fontFamily: 'monospace',
        marginBottom: '4px'
      }}>
        {action.code}
      </div>
      
      {/* 選取狀態提示 */}
      {isSelected && (
        <div style={{ 
          fontSize: '10px', 
          color: '#1890ff',
          fontWeight: '500'
        }}>
          已選取
        </div>
      )}
    </div>
  );
};

export default AvailableActionCard;
