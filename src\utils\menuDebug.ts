/**
 * 選單權限除錯工具
 * 用於診斷選單顯示問題
 */

export interface MenuDebugInfo {
  systemGroupId: string;
  systemCode: string;
  name: string;
  menus: MenuNode[];
}

export interface MenuNode {
  systemMenuId: string;
  systemGroupId: string;
  label: string;
  key: string;
  icon: string | null;
  parentId: string | null;
  isMenu: boolean;
  children: MenuNode[];
}

/**
 * 分析選單權限問題
 */
export const analyzeMenuPermissionIssue = (
  allMenus: MenuDebugInfo[],
  myMenus: MenuDebugInfo[],
  rolePermissions: string[]
) => {
  const analysis = {
    totalSystemGroups: allMenus.length,
    mySystemGroups: myMenus.length,
    totalMenus: 0,
    myMenus: 0,
    missingMenus: [] as string[],
    permissionAnalysis: {
      totalPermissions: rolePermissions.length,
      permissionsInAllMenus: 0,
      permissionsInMyMenus: 0,
      missingPermissions: [] as string[]
    },
    systemGroupAnalysis: [] as any[]
  };

  // 計算總選單數
  const countMenus = (menus: MenuNode[]): number => {
    let count = menus.length;
    menus.forEach(menu => {
      count += countMenus(menu.children);
    });
    return count;
  };

  analysis.totalMenus = allMenus.reduce((sum, group) => sum + countMenus(group.menus), 0);
  analysis.myMenus = myMenus.reduce((sum, group) => sum + countMenus(group.menus), 0);

  // 分析每個系統群組
  allMenus.forEach(allGroup => {
    const myGroup = myMenus.find(g => g.systemGroupId === allGroup.systemGroupId);
    const groupAnalysis = {
      systemCode: allGroup.systemCode,
      name: allGroup.name,
      allMenusCount: countMenus(allGroup.menus),
      myMenusCount: myGroup ? countMenus(myGroup.menus) : 0,
      missingMenus: [] as string[]
    };

    // 找出缺失的選單
    const findMissingMenus = (allMenus: MenuNode[], myMenus: MenuNode[], path: string = '') => {
      allMenus.forEach(menu => {
        const currentPath = path ? `${path} > ${menu.label}` : menu.label;
        const myMenu = myMenus.find(m => m.systemMenuId === menu.systemMenuId);
        
        if (!myMenu) {
          groupAnalysis.missingMenus.push(currentPath);
        } else if (menu.children.length > 0) {
          findMissingMenus(menu.children, myMenu.children, currentPath);
        }
      });
    };

    if (myGroup) {
      findMissingMenus(allGroup.menus, myGroup.menus);
    } else {
      // 整個群組都缺失
      const findAllMenus = (menus: MenuNode[], path: string = '') => {
        menus.forEach(menu => {
          const currentPath = path ? `${path} > ${menu.label}` : menu.label;
          groupAnalysis.missingMenus.push(currentPath);
          if (menu.children.length > 0) {
            findAllMenus(menu.children, currentPath);
          }
        });
      };
      findAllMenus(allGroup.menus);
    }

    analysis.systemGroupAnalysis.push(groupAnalysis);
  });

  // 分析權限
  const allMenuIds = new Set<string>();
  const myMenuIds = new Set<string>();

  const collectMenuIds = (menus: MenuNode[], targetSet: Set<string>) => {
    menus.forEach(menu => {
      targetSet.add(menu.systemMenuId);
      collectMenuIds(menu.children, targetSet);
    });
  };

  allMenus.forEach(group => collectMenuIds(group.menus, allMenuIds));
  myMenus.forEach(group => collectMenuIds(group.menus, myMenuIds));

  analysis.permissionAnalysis.permissionsInAllMenus = rolePermissions.filter(id => allMenuIds.has(id)).length;
  analysis.permissionAnalysis.permissionsInMyMenus = rolePermissions.filter(id => myMenuIds.has(id)).length;
  analysis.permissionAnalysis.missingPermissions = rolePermissions.filter(id => !myMenuIds.has(id));

  return analysis;
};

/**
 * 生成除錯報告
 */
export const generateDebugReport = (analysis: any) => {
  const report = [
    '=== 選單權限除錯報告 ===',
    '',
    `總系統群組數: ${analysis.totalSystemGroups}`,
    `我的系統群組數: ${analysis.mySystemGroups}`,
    `總選單數: ${analysis.totalMenus}`,
    `我的選單數: ${analysis.myMenus}`,
    `選單顯示率: ${((analysis.myMenus / analysis.totalMenus) * 100).toFixed(2)}%`,
    '',
    '=== 權限分析 ===',
    `總權限數: ${analysis.permissionAnalysis.totalPermissions}`,
    `權限在全部選單中: ${analysis.permissionAnalysis.permissionsInAllMenus}`,
    `權限在我的選單中: ${analysis.permissionAnalysis.permissionsInMyMenus}`,
    `缺失權限: ${analysis.permissionAnalysis.missingPermissions.length}`,
    '',
    '=== 系統群組分析 ==='
  ];

  analysis.systemGroupAnalysis.forEach(group => {
    report.push(`\n${group.name} (${group.systemCode}):`);
    report.push(`  全部選單: ${group.allMenusCount}`);
    report.push(`  我的選單: ${group.myMenusCount}`);
    report.push(`  缺失選單: ${group.missingMenus.length}`);
    
    if (group.missingMenus.length > 0) {
      report.push('  缺失的選單:');
      group.missingMenus.slice(0, 10).forEach(menu => {
        report.push(`    - ${menu}`);
      });
      if (group.missingMenus.length > 10) {
        report.push(`    ... 還有 ${group.missingMenus.length - 10} 個選單`);
      }
    }
  });

  return report.join('\n');
};

/**
 * 檢查選單權限邏輯
 */
export const checkMenuPermissionLogic = (rolePermissions: string[], allMenus: MenuDebugInfo[]) => {
  const issues = [];
  
  // 收集所有選單ID
  const allMenuIds = new Set<string>();
  const menuParentMap = new Map<string, string>();
  
  const collectMenuInfo = (menus: MenuNode[]) => {
    menus.forEach(menu => {
      allMenuIds.add(menu.systemMenuId);
      if (menu.parentId) {
        menuParentMap.set(menu.systemMenuId, menu.parentId);
      }
      collectMenuInfo(menu.children);
    });
  };
  
  allMenus.forEach(group => collectMenuInfo(group.menus));
  
  // 檢查權限是否在選單中存在
  rolePermissions.forEach(permissionId => {
    if (!allMenuIds.has(permissionId)) {
      issues.push(`權限 ${permissionId} 不存在於選單中`);
    }
  });
  
  // 檢查是否需要父節點權限
  rolePermissions.forEach(permissionId => {
    const parentId = menuParentMap.get(permissionId);
    if (parentId && !rolePermissions.includes(parentId)) {
      issues.push(`選單 ${permissionId} 的父節點 ${parentId} 沒有權限`);
    }
  });
  
  return issues;
};
