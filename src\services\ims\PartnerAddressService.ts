// =========================================================================================
// Service for Partner Address Management - 商業夥伴地址管理服務
// =========================================================================================

import { PartnerAddress } from '@/services/ims/partner';
import { apiEndpoints, ApiResponse } from "@/config/api";
import { httpClient } from "../http";
import { logger, SYMBOLS, createContextLogger } from '@/utils/logger';

// 創建上下文日誌器
const partnerAddressServiceLogger = createContextLogger({ module: 'PartnerAddressService' });

// =========================================================================================
// 工具函數
// =========================================================================================

/**
 * 建立空的地址物件
 */
export const createEmptyPartnerAddress = (partnerID: string): Partial<PartnerAddress> => ({
    partnerAddressID: '',
    partnerID,
    addressType: '',
    country: '',
    state: '',
    city: '',
    district: '',
    address: '',
    postalCode: '',
    priority: 99, // 預設優先序為 99
    createTime: null,
    createUserId: null,
    updateTime: null,
    updateUserId: null,
    deleteTime: null,
    deleteUserId: null,
    isDeleted: false
});

// =========================================================================================
// API 服務函數
// =========================================================================================

/**
 * 取得夥伴地址列表
 * @param partnerID 夥伴ID
 * @returns Promise<ApiResponse<PartnerAddress[]>>
 */
export async function getPartnerAddresses(partnerID: string): Promise<ApiResponse<PartnerAddress[]>> {
    try {
        if (!partnerID) {
            return {
                success: false,
                message: "夥伴ID不能為空",
                data: []
            };
        }

        partnerAddressServiceLogger.log(SYMBOLS.LOADING, '載入夥伴地址列表', { partnerID });
        const response = await httpClient(`${apiEndpoints.getPartnerAddressList}?PartnerID=${partnerID}`, {
            method: "GET",
        });

        if (response.success) {
            // 確保按 Priority 排序
            const sortedData = response.data?.sort((a: PartnerAddress, b: PartnerAddress) => a.priority - b.priority) || [];
            partnerAddressServiceLogger.log(SYMBOLS.SUCCESS, '夥伴地址列表載入成功', { count: sortedData.length });
            return { ...response, data: sortedData };
        } else {
            partnerAddressServiceLogger.log(SYMBOLS.WARNING, '夥伴地址列表載入失敗', response.message);
        }

        return response;
    } catch (error: any) {
        partnerAddressServiceLogger.log(SYMBOLS.ERROR, '載入夥伴地址列表失敗', error);
        return {
            success: false,
            message: error.message || "載入夥伴地址列表失敗",
            data: []
        };
    }
}

/**
 * 新增夥伴地址
 * @param addressData 地址資料
 * @returns Promise<ApiResponse>
 */
export async function addPartnerAddress(addressData: Partial<PartnerAddress>): Promise<ApiResponse> {
    try {
        // 基本驗證
        if (!addressData.partnerID) {
            return {
                success: false,
                message: "夥伴ID不能為空",
            };
        }

        if (!addressData.address?.trim()) {
            return {
                success: false,
                message: "地址不能為空",
            };
        }

        partnerAddressServiceLogger.log(SYMBOLS.LOADING, '新增夥伴地址', { partnerID: addressData.partnerID });
        const response = await httpClient(apiEndpoints.addPartnerAddress, {
            method: "POST",
            body: JSON.stringify(addressData),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            partnerAddressServiceLogger.log(SYMBOLS.SUCCESS, '夥伴地址新增成功');
        } else {
            partnerAddressServiceLogger.log(SYMBOLS.WARNING, '夥伴地址新增失敗', response.message);
        }

        return response;
    } catch (error: any) {
        partnerAddressServiceLogger.log(SYMBOLS.ERROR, '新增夥伴地址時發生錯誤', error);
        return {
            success: false,
            message: error.message || "新增夥伴地址失敗",
        };
    }
}

/**
 * 更新夥伴地址
 * @param addressData 地址資料
 * @returns Promise<ApiResponse>
 */
export async function updatePartnerAddress(addressData: Partial<PartnerAddress>): Promise<ApiResponse> {
    try {
        // 基本驗證
        if (!addressData.partnerAddressID) {
            return {
                success: false,
                message: "地址ID不能為空",
            };
        }

        if (!addressData.partnerID) {
            return {
                success: false,
                message: "夥伴ID不能為空",
            };
        }

        if (!addressData.address?.trim()) {
            return {
                success: false,
                message: "地址不能為空",
            };
        }

        partnerAddressServiceLogger.log(SYMBOLS.LOADING, '更新夥伴地址', { 
            partnerAddressID: addressData.partnerAddressID,
            partnerID: addressData.partnerID 
        });
        
        const response = await httpClient(apiEndpoints.editPartnerAddress, {
            method: "PUT",
            body: JSON.stringify(addressData),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            partnerAddressServiceLogger.log(SYMBOLS.SUCCESS, '夥伴地址更新成功');
        } else {
            partnerAddressServiceLogger.log(SYMBOLS.WARNING, '夥伴地址更新失敗', response.message);
        }

        return response;
    } catch (error: any) {
        partnerAddressServiceLogger.log(SYMBOLS.ERROR, '更新夥伴地址時發生錯誤', error);
        return {
            success: false,
            message: error.message || "更新夥伴地址失敗",
        };
    }
}

/**
 * 刪除夥伴地址
 * @param partnerAddressID 地址ID
 * @returns Promise<ApiResponse>
 */
export async function deletePartnerAddress(partnerAddressID: string): Promise<ApiResponse> {
    try {
        if (!partnerAddressID) {
            return {
                success: false,
                message: "地址ID不能為空",
            };
        }

        partnerAddressServiceLogger.log(SYMBOLS.LOADING, '刪除夥伴地址', { partnerAddressID });
        const response = await httpClient(`${apiEndpoints.deletePartnerAddress}?PartnerAddressID=${partnerAddressID}`, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            partnerAddressServiceLogger.log(SYMBOLS.SUCCESS, '夥伴地址刪除成功');
        } else {
            partnerAddressServiceLogger.log(SYMBOLS.WARNING, '夥伴地址刪除失敗', response.message);
        }

        return response;
    } catch (error: any) {
        partnerAddressServiceLogger.log(SYMBOLS.ERROR, '刪除夥伴地址時發生錯誤', error);
        return {
            success: false,
            message: error.message || "刪除夥伴地址失敗",
        };
    }
}

/**
 * 還原已刪除的夥伴地址
 * @param partnerAddressID 地址ID
 * @returns Promise<ApiResponse>
 */
export async function restorePartnerAddress(partnerAddressID: string): Promise<ApiResponse> {
    try {
        if (!partnerAddressID) {
            return {
                success: false,
                message: "地址ID不能為空",
            };
        }

        partnerAddressServiceLogger.log(SYMBOLS.LOADING, '還原夥伴地址', { partnerAddressID });
        const response = await httpClient(`api/partneraddress/restore?PartnerAddressID=${partnerAddressID}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            partnerAddressServiceLogger.log(SYMBOLS.SUCCESS, '夥伴地址還原成功');
        } else {
            partnerAddressServiceLogger.log(SYMBOLS.WARNING, '夥伴地址還原失敗', response.message);
        }

        return response;
    } catch (error: any) {
        partnerAddressServiceLogger.log(SYMBOLS.ERROR, '還原夥伴地址時發生錯誤', error);
        return {
            success: false,
            message: error.message || "還原夥伴地址失敗",
        };
    }
}
