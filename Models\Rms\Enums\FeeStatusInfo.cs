using System.Collections.Generic;
using System.Linq;

namespace FAST_ERP_Backend.Models.Rms.Enums
{
    /// <summary>
    /// 費用狀態（物件化定義）：未繳(0)、部分已繳(1)、已繳(2)、作廢(9)
    /// </summary>
    public sealed class FeeStatusInfo
    {
        public string Code { get; }
        public string Name { get; }

        private FeeStatusInfo(string code, string name)
        {
            Code = code;
            Name = name;
        }

        public static readonly FeeStatusInfo Unpaid     = new("0", "未繳");
        public static readonly FeeStatusInfo Partially  = new("1", "部分已繳");
        public static readonly FeeStatusInfo Paid       = new("2", "已繳");
        public static readonly FeeStatusInfo Voided     = new("9", "作廢");

        public static IEnumerable<FeeStatusInfo> All => new[] { Unpaid, Partially, Paid, Voided };

        public static FeeStatusInfo FromCode(string code) => All.First(x => x.Code == code);
    }
}


