import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { JSEncrypt } from "jsencrypt";

/**
 * RSA加密服務
 * 用於前端密碼加密，與後端RSA解密配合使用
 */
export class RsaService {
    private rsaEncrypt: any = null;

    constructor() {
        // 靜態初始化加密器
        this.initRsaEncryptSync();
    }

    // 靜態初始化RSA加密器
    private initRsaEncryptSync() {
        if (this.rsaEncrypt) return;
        this.rsaEncrypt = new JSEncrypt();
    }

    /**
     * 從後端獲取RSA公鑰
     */
    private async fetchPublicKey(): Promise<string> {
        try {
            const response = await httpClient(apiEndpoints.getRsaPublicKey, {
                method: "GET",
            });

            if (response.success && response.data) {
                return response.data as string;
            } else {
                throw new Error("獲取RSA公鑰失敗");
            }
        } catch (error) {
            console.error("獲取RSA公鑰時發生錯誤:", error);
            throw new Error("獲取RSA公鑰失敗");
        }
    }

    /**
     * 使用RSA公鑰以 PKCS#1 v1.5 方式加密字串
     * @param plaintext 要加密的明文
     * @returns 加密後的Base64字串
     */
    async encrypt(plaintext: string): Promise<string> {
        try {
            if (!this.rsaEncrypt) {
                this.initRsaEncryptSync();
            }
            const publicKey = await this.fetchPublicKey();
            this.rsaEncrypt.setPublicKey(publicKey);
            const encrypted = this.rsaEncrypt.encrypt(plaintext);
            if (!encrypted) {
                throw new Error("RSA加密失敗");
            }
            return encrypted; // Base64
        } catch (error) {
            console.error("RSA加密時發生錯誤:", error);
            throw new Error("密碼加密失敗");
        }
    }


}

// 建立單例實例
export const rsaService = new RsaService();
