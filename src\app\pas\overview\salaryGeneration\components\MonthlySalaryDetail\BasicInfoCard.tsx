'use client';

import React from 'react';
import { Card, Typography, Tag, DatePicker, Form, Select, Col } from 'antd';
import dayjs from 'dayjs';
import type { MonthlySalary } from '@/services/pas/MonthlySalary/MonthlySalaryService';
import { Employee } from '@/services/pas/EmployeeService';

type ModalMode = 'view' | 'edit' | 'add';

interface BasicInfoCardProps {
    mode: ModalMode;
    monthlySalary: MonthlySalary | null;
    selectedEmployee?: Employee;
    departments: any[];
    form: any;
}

const BasicInfoCard: React.FC<BasicInfoCardProps> = ({
    mode,
    monthlySalary,
    selectedEmployee,
    departments,
    form,
}) => {
    if (mode === 'view') {
        return (
            <Card title="基本資料" style={{ height: '100%' }}>
                <p>
                    發薪月份: <Typography.Text style={{ color: '#52c41a', marginLeft: 8 }}>{monthlySalary?.payMonth}</Typography.Text>
                </p>
                <p>
                    計算日期區間:
                    <Typography.Text style={{ color: '#52c41a', marginLeft: 8 }}>
                        {monthlySalary?.calculationStartDate} ~ {monthlySalary?.calculationEndDate}
                    </Typography.Text>
                </p>
                <p>
                    服務部門: <Tag color="blue">{monthlySalary?.serviceDepartmentName}</Tag>
                </p>
                <p>
                    開支部門: <Tag color="geekblue">{monthlySalary?.expenseDepartmentName}</Tag>
                </p>
            </Card>
        );
    }

    return (
        <Card title="基本資料編輯" style={{ height: '100%' }}>
            <Col span={24}>
                <Form.Item
                    label="發薪月份"
                    name="payMonth"
                    rules={[{ required: true, message: '請選擇發薪月份' }]}
                >
                    <DatePicker
                        picker="month"
                        format="YYYY-MM"
                        placeholder="選擇發薪月份"
                        style={{ width: '100%' }}
                    />
                </Form.Item>
            </Col>

            <Col span={24}>
                <Form.Item
                    label="服務部門"
                    name="serviceDepartmentUid"
                    rules={[{ required: true, message: '請選擇服務部門' }]}
                >
                    <Select
                        placeholder="選擇服務部門"
                        options={departments.map(dept => ({
                            value: dept.departmentId,
                            label: dept.name,
                        }))}
                    />
                </Form.Item>
            </Col>

            <Col span={24}>
                <Form.Item
                    label="開支部門"
                    name="expenseDepartmentUid"
                    rules={[{ required: true, message: '請選擇開支部門' }]}
                >
                    <Select
                        placeholder="選擇開支部門"
                        options={departments.map(dept => ({
                            value: dept.departmentId,
                            label: dept.name,
                        }))}
                    />
                </Form.Item>
            </Col>

            <Col span={24}>
                <Form.Item
                    label="計算日期區間"
                    style={{ marginBottom: '8px' }}
                >
                    <Form.Item
                        name="calculationStartDate"
                        style={{ display: 'inline-block', width: 'calc(50% - 8px)' }}
                        rules={[{ required: true, message: '請選擇計算日期區間' }]}
                    >
                        <DatePicker
                            placeholder="開始日期"
                            format="YYYY-MM-DD"
                            size="small"
                            style={{ width: '100%' }}
                        />
                    </Form.Item>
                    <span style={{ display: 'inline-block', width: '16px', textAlign: 'center' }}>~</span>
                    <Form.Item
                        name="calculationEndDate"
                        style={{ display: 'inline-block', width: 'calc(50% - 8px)' }}
                        rules={[{ required: true, message: '請選擇計算日期區間' }]}
                    >
                        <DatePicker
                            placeholder="結束日期"
                            format="YYYY-MM-DD"
                            size="small"
                            style={{ width: '100%' }}
                        />
                    </Form.Item>
                </Form.Item>
            </Col>
        </Card>
    );
};

export default BasicInfoCard;