"use client";

import React, { useEffect, useState, useCallback, useMemo, useRef } from "react";
import { useRouter } from 'next/navigation';
import { useGranularPermissions } from '@/contexts/GranularPermissionContext';
import { PagePermissionGuard, ButtonPermissionGuard } from '@/app/components/common/PermissionGuard';
import { Card, Spin, Form, InputNumber, Button, Space, Modal, message, Switch, Select, Tag, Tooltip, Popconfirm, Typography, Statistic, Row, Col, Progress, TreeSelect, List, Descriptions } from "antd";
import {
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  StopOutlined,
  SettingOutlined,
  FolderOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  TagsOutlined,
  ShopOutlined,
  SyncOutlined,
  FileAddOutlined,
  ExperimentOutlined,
} from "@ant-design/icons";

// Services and Types
import { getItemList, getItem, deleteItem, editItem, addItem, generateTestItems, getItemTaxTypes, type Item, type ItemPrice, type PriceType, type ItemTaxTypeOption } from '@/services/ims/ItemService';
import { getItemCategoryList, buildCategoryTree, getCategoryHierarchyName, type ItemCategory } from '@/services/ims/ItemCategoryService';
import { getPriceTypeList } from '@/services/ims/PriceTypeService';
import { getItemPriceList, addItemPrice, editItemPrice } from '@/services/ims/ItemPriceService';
import { getUnits } from '@/services/common/unitService';

// Components
import PriceTypeManagement from '@/app/ims/components/PriceTypeManagement';
import ItemCategoryAdapter from '@/app/ims/components/shared/ItemCategoryAdapter';
import ItemFormModal from '@/app/ims/components/ItemFormModal';
import TestDataCountInput from '@/app/ims/components/TestDataCountInput';
import FilterSearchContainer from '@/app/ims/components/shared/FilterSearchContainer';
import ResponsiveStyles from '@/app/ims/components/shared/ResponsiveStyles';
import ResponsiveTable, { SmartColumnType } from '@/app/ims/components/shared/ResponsiveTable';
import ItemPriceDisplay from '@/app/ims/components/shared/ItemPriceDisplay';
import { FilterOption } from '@/app/ims/types/filter';
import UnitManagement from '@/app/components/common/UnitManagement';

// Data Validation Utils
import { processApiResponse, safeString, safeBoolean } from '@/utils/dataValidation';

// Development Logging
import { logger, SYMBOLS, createContextLogger } from '@/utils/logger';

// 創建上下文日誌器
const itemPageLogger = createContextLogger({ module: 'ItemPage' });

const { Title } = Typography;

// 整合資料介面
interface IntegratedData {
  items: Item[];
  categories: ItemCategory[];
  priceTypes: PriceType[];
  itemPrices: ItemPrice[];
}

// 統計資料介面
interface StatsData {
  totalItems: number;
  activeItems: number;
  totalCategories: number;
  totalPriceTypes: number;
  totalUnits: number; // 新增單位統計
}

// 強化的API回應驗證函數
const validateItemApiResponse = (response: any): { success: boolean; data: Item[]; message?: string } => {
  itemPageLogger.log(SYMBOLS.INFO, '驗證庫存品API回應', { hasResponse: !!response });

  // 檢查基本回應結構
  if (!response || typeof response !== 'object') {
    itemPageLogger.log(SYMBOLS.ERROR, 'API回應格式錯誤', { expected: 'object', actual: typeof response });
    return { success: false, data: [], message: 'API回應格式錯誤' };
  }

  // 檢查success欄位
  if (typeof response.success !== 'boolean') {
    itemPageLogger.log(SYMBOLS.ERROR, 'API回應缺少success欄位', { hasSuccess: 'success' in response });
    return { success: false, data: [], message: 'API回應格式錯誤：缺少success欄位' };
  }

  // 如果API回應失敗
  if (!response.success) {
    itemPageLogger.log(SYMBOLS.WARNING, 'API回應失敗', response.message);
    return {
      success: false,
      data: [],
      message: response.message || 'API操作失敗'
    };
  }

  // 驗證data欄位
  if (!response.data) {
    itemPageLogger.log(SYMBOLS.WARNING, 'API回應成功但無資料');
    return { success: true, data: [], message: '無資料' };
  }

  // 確保data是陣列
  if (!Array.isArray(response.data)) {
    itemPageLogger.log(SYMBOLS.ERROR, 'API回應的data不是陣列', {
      expected: 'array',
      actual: typeof response.data
    });
    return { success: false, data: [], message: '資料格式錯誤：期望陣列格式' };
  }

  // 驗證陣列中的每個項目並進行資料類型轉換
  const validItems = response.data.filter((item: any) => {
    if (!item || typeof item !== 'object') {
      itemPageLogger.log(SYMBOLS.WARNING, '無效的庫存品項目', { hasItem: !!item });
      return false;
    }

    // 檢查必要欄位 - 使用camelCase格式
    const requiredFields = ['itemID', 'name', 'customNO', 'unit'];
    const hasRequired = requiredFields.every(field => {
      const hasField = item.hasOwnProperty(field) && item[field] !== null && item[field] !== undefined;
      if (!hasField) {
        itemPageLogger.log(SYMBOLS.WARNING, '庫存品缺少必要欄位', { field, itemID: item.itemID });
      }
      return hasField;
    });

    if (hasRequired) {
      // 確保 itemCategoryID 是字串格式
      if (item.itemCategoryID !== null && item.itemCategoryID !== undefined) {
        item.itemCategoryID = String(item.itemCategoryID);
      }

      // 確保其他 ID 欄位也是字串格式
      item.itemID = String(item.itemID);


      itemPageLogger.debug('庫存品資料轉換完成', {
        itemID: item.itemID,
        name: item.name,
        itemCategoryID: item.itemCategoryID
      });
    }

    return hasRequired;
  });

  itemPageLogger.log(SYMBOLS.INFO, '庫存品資料驗證完成', {
    valid: validItems.length,
    total: response.data.length
  });

  return {
    success: true,
    data: validItems,
    message: `成功載入 ${validItems.length} 個庫存品`
  };
};

const ItemPage = () => {
  const [data, setData] = useState<IntegratedData>({
    items: [],
    categories: [],
    priceTypes: [],
    itemPrices: []
  });
  const [stats, setStats] = useState<StatsData>({
    totalItems: 0,
    activeItems: 0,
    totalCategories: 0,
    totalPriceTypes: 0,
    totalUnits: 0
  });
  const [loading, setLoading] = useState(true);

  // FilterSearchContainer 控制 ref - 用於重新載入時清除篩選
  const filterSearchRef = useRef<{ clearAll: () => void } | null>(null);

  // 表單和互動視窗狀態
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [form] = Form.useForm();

  // 篩選後的庫存品列表狀態
  const [filteredItems, setFilteredItems] = useState<Item[]>([]);

  // 分頁相關狀態
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // 測試資料產生狀態
  const [isGeneratingTestData, setIsGeneratingTestData] = useState(false);
  const [testDataCount, setTestDataCount] = useState<number | null>(20000);
  const [isTestDataModalVisible, setIsTestDataModalVisible] = useState(false);

  // 庫存品稅別狀態
  const [itemTaxTypes, setItemTaxTypes] = useState<ItemTaxTypeOption[]>([]);

  // CRUD 互動視窗狀態
  const [isCategoryModalVisible, setIsCategoryModalVisible] = useState(false);
  const [isPriceTypeModalVisible, setIsPriceTypeModalVisible] = useState(false);
  const [isUnitModalVisible, setIsUnitModalVisible] = useState(false);

  // 庫存品價格管理狀態
  const [itemPrices, setItemPrices] = useState<Record<string, number>>({});

  // 移動端檢測狀態
  const [isMobile, setIsMobile] = useState(false);

  // 檢查分類是否匹配（支援父分類選擇）
  const isCategoryMatch = useCallback((itemCategoryId: string | null, selectedCategoryId: string, categories: ItemCategory[]): boolean => {
    if (!itemCategoryId || !selectedCategoryId) return false;

    // 直接匹配
    if (itemCategoryId === selectedCategoryId) return true;

    // 檢查是否為子分類
    const findInTree = (cats: ItemCategory[], targetId: string, parentId: string): boolean => {
      for (const cat of cats) {
        if (cat.itemCategoryID === parentId) {
          // 找到父分類，檢查其所有子分類
          const checkChildren = (children: ItemCategory[]): boolean => {
            for (const child of children) {
              if (child.itemCategoryID === targetId) return true;
              if (child.children && child.children.length > 0) {
                if (checkChildren(child.children)) return true;
              }
            }
            return false;
          };

          if (cat.children && cat.children.length > 0) {
            return checkChildren(cat.children);
          }
        }

        if (cat.children && cat.children.length > 0) {
          if (findInTree(cat.children, targetId, parentId)) return true;
        }
      }
      return false;
    };

    const tree = buildCategoryTree(categories);
    return findInTree(tree, itemCategoryId, selectedCategoryId);
  }, []);

  // 載入所有資料的主要函數並重置篩選條件
  const loadAllData = useCallback(async () => {
    setLoading(true);
    itemPageLogger.log(SYMBOLS.LOADING, '開始載入IMS庫存品模組資料並重置篩選...');

    // 清除所有篩選條件
    if (filterSearchRef.current) {
      filterSearchRef.current.clearAll();
    }

    try {
      // 並行載入所有必要資料
      const [itemsRes, categoriesRes, priceTypesRes, itemPricesRes, itemTaxTypesRes, unitsRes] = await Promise.all([
        getItemList(),
        getItemCategoryList(),
        getPriceTypeList(),
        getItemPriceList(),
        getItemTaxTypes(),
        getUnits() // 新增單位資料載入
      ]);

      itemPageLogger.log(SYMBOLS.INFO, '庫存品 API 回應', itemsRes);
      itemPageLogger.log(SYMBOLS.INFO, '分類 API 回應', categoriesRes);
      itemPageLogger.log(SYMBOLS.INFO, '價格類型 API 回應', priceTypesRes);
      itemPageLogger.log(SYMBOLS.INFO, '庫存品價格 API 回應', itemPricesRes);
      itemPageLogger.log(SYMBOLS.INFO, '稅別類型 API 回應', itemTaxTypesRes);
      itemPageLogger.log(SYMBOLS.INFO, '單位 API 回應', unitsRes);

      // 使用強化的驗證函數處理庫存品資料
      const itemsResult = validateItemApiResponse(itemsRes);

      // 調試：檢查庫存品的分類ID (僅開發環境)
      if (itemsResult.success && itemsResult.data.length > 0) {
        const itemsWithCategories = itemsResult.data.filter(item => item.itemCategoryID);
        itemPageLogger.log(SYMBOLS.DEBUG, `有分類的庫存品數量: ${itemsWithCategories.length} / 總庫存品數: ${itemsResult.data.length}`);
        if (itemsWithCategories.length > 0) {
          itemPageLogger.log(SYMBOLS.INFO, '庫存品分類ID範例', itemsWithCategories.slice(0, 3).map(item => ({
            name: item.name,
            itemCategoryID: item.itemCategoryID,
            categoryType: typeof item.itemCategoryID
          })));
        }
      }

      // 使用通用驗證函數處理其他資料
      const categoriesResult = processApiResponse<ItemCategory>(categoriesRes, '分類');

      // 調試：檢查分類資料 (僅開發環境)
      if (categoriesResult.success && categoriesResult.data.length > 0) {
        itemPageLogger.log(SYMBOLS.DEBUG, `可用分類數量: ${categoriesResult.data.length}`);
        itemPageLogger.log(SYMBOLS.DEBUG, '分類資料範例:', categoriesResult.data.slice(0, 3).map(cat => ({
          id: cat.itemCategoryID,
          name: cat.name,
          idType: typeof cat.itemCategoryID
        })));
      }
      const priceTypesResult = processApiResponse<PriceType>(priceTypesRes, '價格類型');
      const itemPricesResult = processApiResponse<ItemPrice>(itemPricesRes, '庫存品價格');
      const itemTaxTypesResult = processApiResponse<ItemTaxTypeOption>(itemTaxTypesRes, '庫存品稅別');
      const unitsResult = processApiResponse(unitsRes, '單位');

      // 更新狀態
      const newData: IntegratedData = {
        items: itemsResult.data,
        categories: categoriesResult.data,
        priceTypes: priceTypesResult.data,
        itemPrices: itemPricesResult.data
      };

      setData(newData);
      setItemTaxTypes(itemTaxTypesResult.data);

      // 計算統計資料
      const newStats: StatsData = {
        totalItems: newData.items.length,
        activeItems: newData.items.filter(item => !safeBoolean(item.isStop)).length,
        totalCategories: newData.categories.length,
        totalPriceTypes: newData.priceTypes.length,
        totalUnits: unitsResult.data.length
      };

      setStats(newStats);

      // 檢查載入結果
      const errors = [
        !itemsResult.success && `庫存品: ${itemsResult.message}`,
        !categoriesResult.success && `分類: ${categoriesResult.message}`,
        !priceTypesResult.success && `價格類型: ${priceTypesResult.message}`,
        !itemPricesResult.success && `庫存品價格: ${itemPricesResult.message}`,
        !itemTaxTypesResult.success && `庫存品稅別: ${itemTaxTypesResult.message}`,
        !unitsResult.success && `單位: ${unitsResult.message}`
      ].filter(Boolean);

      if (errors.length > 0) {
        itemPageLogger.log(SYMBOLS.WARNING, '部分資料載入失敗:', errors);
        message.warning(`部分資料載入失敗: ${errors.join(', ')}`);
      } else {
        itemPageLogger.log(SYMBOLS.SUCCESS, '所有資料載入成功，篩選條件已重置');
        message.success('資料重新載入完成，篩選條件已清除');
      }

    } catch (error) {
      itemPageLogger.log(SYMBOLS.ERROR, '載入資料時發生錯誤:', error);
      message.error('載入資料失敗，請檢查網路連接和後端服務');
    } finally {
      setLoading(false);
    }
  }, []);

  // 重新載入特定類型的資料
  const reloadData = useCallback(async (type: keyof IntegratedData | 'units') => {
    try {
      itemPageLogger.log(SYMBOLS.LOADING, `重新載入${type}...`);

      const apiMap = {
        items: getItemList,
        categories: getItemCategoryList,
        priceTypes: getPriceTypeList,
        itemPrices: getItemPriceList,
        units: getUnits
      };

      const apiFunction = apiMap[type];
      if (!apiFunction) {
        itemPageLogger.log(SYMBOLS.WARNING, `未知的資料類型: ${type}`);
        return;
      }

      const response = await apiFunction();

      let result;
      if (type === 'items') {
        result = validateItemApiResponse(response);
      } else {
        result = processApiResponse(response, type);
      }

      if (result.success) {
        if (type !== 'units') {
          setData(prev => ({
            ...prev,
            [type]: result.data
          }));
        }

        // 更新統計資料
        if (type === 'items') {
          setStats(prev => ({
            ...prev,
            totalItems: result.data.length,
            activeItems: (result.data as Item[]).filter(item => !safeBoolean(item.isStop)).length
          }));
        } else if (type === 'categories') {
          setStats(prev => ({
            ...prev,
            totalCategories: result.data.length
          }));
        } else if (type === 'priceTypes') {
          setStats(prev => ({
            ...prev,
            totalPriceTypes: result.data.length
          }));
        } else if (type === 'units') {
          setStats(prev => ({
            ...prev,
            totalUnits: result.data.length
          }));
        }

        itemPageLogger.log(SYMBOLS.SUCCESS, `${type} 重新載入成功: ${result.data.length} 項`);
      } else {
        itemPageLogger.log(SYMBOLS.WARNING, `${type} 重新載入失敗:`, result.message);
        message.error(`重新載入${type}失敗: ${result.message}`);
      }
    } catch (error) {
      itemPageLogger.log(SYMBOLS.ERROR, `重新載入${type}時發生錯誤:`, error);
      message.error(`重新載入${type}失敗`);
    }
  }, []);

  // 初始載入
  useEffect(() => {
    loadAllData();
  }, [loadAllData]);

  // 移動端檢測
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  /**
   * 檢查篩選值是否有效（非空）
   * @param value 要檢查的值
   * @returns 是否為有效的篩選值
   */
  const isValidFilterValue = useCallback((value: any): boolean => {
    if (value === undefined || value === null || value === '') {
      return false;
    }

    // 對於陣列類型（如多選分類），檢查是否為非空陣列
    if (Array.isArray(value)) {
      return value.length > 0;
    }

    // 對於字串類型，檢查是否為非空字串
    if (typeof value === 'string') {
      return value.trim().length > 0;
    }

    return true;
  }, []);

  // 初始化篩選後的庫存品列表
  useEffect(() => {
    setFilteredItems(data.items);
  }, [data.items]);

  // 分類名稱快取 - 使用 useMemo 避免重複計算
  const categoryNameCache = useMemo(() => {
    const cache = new Map<string, string>();

    // 預先計算所有分類的層級名稱
    data.categories.forEach(category => {
      if (category.itemCategoryID) {
        const hierarchyName = getCategoryHierarchyName(data.categories, category.itemCategoryID);
        cache.set(category.itemCategoryID, hierarchyName);
      }
    });

    itemPageLogger.log(SYMBOLS.LOADING, `分類名稱快取已建立，包含 ${cache.size} 個分類`);
    itemPageLogger.log(SYMBOLS.INFO, '快取內容:', Array.from(cache.entries()));

    return cache;
  }, [data.categories]);

  // 獲取分類名稱的輔助函數 - 使用快取提升效能
  const getCategoryName = useCallback((categoryId: string | null): string => {
    if (!categoryId) return '未分類';

    // 確保 categoryId 是字串格式
    const categoryIdStr = String(categoryId);

    // 從快取中獲取結果
    const cachedName = categoryNameCache.get(categoryIdStr);
    if (cachedName) {
      return cachedName;
    }

    // 快取中沒有找到，返回預設值
    itemPageLogger.log(SYMBOLS.WARNING, `getCategoryName - category not found in cache for ID: ${categoryIdStr}`);
    return '未知分類';
  }, [categoryNameCache]);

  // 移動端卡片渲染函數
  const mobileCardRender = ({ record: item, actions }: { record: Item; actions?: React.ReactNode }) => (
    <div>
      <div style={{ marginBottom: 12 }}>
        <Typography.Text strong style={{ fontSize: '16px', display: 'block' }}>
          {safeString(item.name)}
        </Typography.Text>
        <div style={{ marginTop: 4 }}>
          <Tag color={item.isStop ? 'red' : 'green'}>
            {item.isStop ? '停用' : '啟用'}
          </Tag>
        </div>
      </div>

      <Descriptions size="small" column={1} colon={false}>
        <Descriptions.Item label="庫存品編號">
          <Typography.Text code>{safeString(item.customNO)}</Typography.Text>
        </Descriptions.Item>
        <Descriptions.Item label="國際條碼">
          {safeString(item.internationalBarCode) || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="單位">
          <Tag color="blue">{safeString(item.unit)}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="分類">
          <Tag color="purple">{getCategoryName(item.itemCategoryID)}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="價格">
          <ItemPriceDisplay
            prices={item.prices}
            priceTypes={data.priceTypes}
            mode="minimal"
            showTooltip={true}
          />
        </Descriptions.Item>
        {item.description && (
          <Descriptions.Item label="描述">
            <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
              {safeString(item.description)}
            </Typography.Text>
          </Descriptions.Item>
        )}
      </Descriptions>
    </div>
  );

  // 處理刪除
  const handleDelete = async (itemId: string) => {
    if (!itemId) {
      message.error('庫存品刪除發生異常，請重新整理後重試!');
      return;
    }

    Modal.confirm({
      title: '確認刪除',
      content: '確定要刪除此庫存品嗎？此操作無法復原。',
      okText: '確定刪除',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          itemPageLogger.log(SYMBOLS.LOADING, '刪除庫存品:', itemId);
          const response = await deleteItem(itemId);

          if (response.success) {
            message.success('庫存品刪除成功');
            reloadData('items');
          } else {
            message.error(response.message || '刪除失敗');
          }
        } catch (error) {
          itemPageLogger.log(SYMBOLS.ERROR, '刪除庫存品時發生錯誤:', error);
          message.error('刪除失敗，請重試');
        }
      }
    });
  };

  // 顯示編輯互動視窗
  const showEditModal = async (item: Item) => {
    if (!item || !item.itemID) {
      message.error('庫存品資料無效');
      return;
    }

    setLoading(true);
    try {
      itemPageLogger.log(SYMBOLS.LOADING, '載入庫存品詳細資料:', item.itemID);

      // 獲取最新的庫存品資料
      const itemResponse = await getItem(item.itemID);
      if (itemResponse.success && itemResponse.data) {
        const latestItem = itemResponse.data;
        setSelectedItem(latestItem);

        // 設定表單值
        form.setFieldsValue({
          name: safeString(latestItem.name, ''),
          customNO: safeString(latestItem.customNO, ''),
          internationalBarCode: safeString(latestItem.internationalBarCode, ''),
          unit: safeString(latestItem.unit, ''),
          itemCategoryID: latestItem.itemCategoryID || null,
          description: safeString(latestItem.description, ''),
          isStop: safeBoolean(latestItem.isStop, false),
          taxType: latestItem.taxType || 1 // ItemTaxType.Taxable
        });

        // 載入庫存品價格
        if (Array.isArray(latestItem.prices) && latestItem.prices.length > 0) {
          const priceMap: Record<string, number> = {};
          latestItem.prices.forEach(price => {
            // 確保價格資料有效
            if (price &&
              typeof price === 'object' &&
              price.priceTypeID &&
              typeof price.price === 'number' &&
              price.price >= 0) {
              priceMap[price.priceTypeID] = price.price;
            }
          });
          setItemPrices(priceMap);
          itemPageLogger.log(SYMBOLS.SUCCESS, '載入庫存品價格:', priceMap);
        } else {
          setItemPrices({});
          itemPageLogger.log(SYMBOLS.INFO, '庫存品無價格資料');
        }

        setIsModalVisible(true);
        itemPageLogger.log(SYMBOLS.SUCCESS, '庫存品資料載入成功');
      } else {
        message.error('無法載入庫存品詳細資料: ' + (itemResponse.message || '未知錯誤'));
      }
    } catch (error) {
      itemPageLogger.log(SYMBOLS.ERROR, '載入庫存品詳細資料時發生錯誤:', error);
      message.error('載入庫存品詳細資料失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 顯示新增互動視窗
  const showAddModal = () => {
    setSelectedItem(null);
    form.resetFields();
    form.setFieldsValue({
      isStop: false,  // 預設為啟用狀態 (false = 啟用, true = 停用)
      taxType: 1  // 預設為應稅 (ItemTaxType.Taxable)
    });
    setItemPrices({}); // 重置價格
    setIsModalVisible(true);
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      itemPageLogger.log(SYMBOLS.LOADING, '提交庫存品資料:', values);

      // 資料驗證
      if (!values.name || values.name.trim() === '') {
        message.error('庫存品名稱不能為空');
        return;
      }
      if (!values.customNO || values.customNO.trim() === '') {
        message.error('庫存品編號不能為空');
        return;
      }
      if (!values.unit || values.unit.trim() === '') {
        message.error('單位不能為空');
        return;
      }

      const itemData = {
        ...values,
        itemID: selectedItem?.itemID,
        isStop: safeBoolean(values.isStop, false)
      };

      const response = selectedItem
        ? await editItem(itemData)
        : await addItem(itemData);

      if (response.success) {
        // 如果庫存品操作成功，處理價格資料
        if (Object.keys(itemPrices).length > 0) {
          try {
            const itemId = selectedItem?.itemID || response.data?.itemID;
            if (itemId) {
              // 為每個有價格的價格類型創建或更新價格記錄
              const pricePromises = Object.entries(itemPrices).map(async ([priceTypeID, price]) => {
                // 確保價格是有效數值且大於0
                const numericPrice = Number(price);
                if (!isNaN(numericPrice) && numericPrice > 0) {
                  const priceData = {
                    itemID: itemId,
                    priceTypeID,
                    price: numericPrice
                  };

                  // 檢查是否已存在價格記錄
                  const existingPrice = selectedItem?.prices?.find(p => p.priceTypeID === priceTypeID);

                  return existingPrice
                    ? await editItemPrice({ ...existingPrice, price: numericPrice })
                    : await addItemPrice(priceData);
                }
                return Promise.resolve({ success: true });
              });

              await Promise.all(pricePromises);
              itemPageLogger.log(SYMBOLS.SUCCESS, '庫存品價格操作成功');
            }
          } catch (priceError) {
            itemPageLogger.log(SYMBOLS.WARNING, '價格操作部分失敗:', priceError);
            message.warning('庫存品保存成功，但部分價格設定失敗');
          }
        }

        message.success(selectedItem ? '庫存品更新成功' : '庫存品新增成功');
        setIsModalVisible(false);
        form.resetFields();
        setSelectedItem(null);
        setItemPrices({});
        reloadData('items');
        reloadData('itemPrices');
        itemPageLogger.log(SYMBOLS.SUCCESS, '庫存品操作成功');
      } else {
        message.error(response.message || '操作失敗');
      }
    } catch (error) {
      itemPageLogger.log(SYMBOLS.ERROR, '提交庫存品時發生錯誤:', error);
      message.error('操作失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 建構分類樹狀結構用於選擇器
  const categoryTreeData = useMemo(() => {
    const tree = buildCategoryTree(data.categories);

    const convertToTreeSelectData = (categories: ItemCategory[]): any[] => {
      return categories.map(category => ({
        title: category.name,
        value: category.itemCategoryID,
        key: category.itemCategoryID,
        children: category.children && category.children.length > 0
          ? convertToTreeSelectData(category.children)
          : undefined,
        // 允許選擇任何節點（父節點或子節點）
        disabled: false,
        selectable: true
      }));
    };

    return convertToTreeSelectData(tree);
  }, [data.categories]);

  // 建構按階層排序的分類列表用於顯示
  const sortedCategoriesForDisplay = useMemo(() => {
    const tree = buildCategoryTree(data.categories);
    const flattenedCategories: ItemCategory[] = [];

    // 遞迴展開樹狀結構，保持階層順序
    const flattenTree = (categories: ItemCategory[], level: number = 0) => {
      categories.forEach(category => {
        // 添加層級資訊用於顯示縮排
        const categoryWithLevel = { ...category, level };
        flattenedCategories.push(categoryWithLevel);

        // 如果有子分類，遞迴處理
        if (category.children && category.children.length > 0) {
          flattenTree(category.children, level + 1);
        }
      });
    };

    flattenTree(tree);
    return flattenedCategories;
  }, [data.categories]);



  // 庫存品篩選邏輯
  const applyItemFilters = useCallback((
    items: Item[],
    searchText: string,
    activeFilters: string[],
    filterValues: Record<string, any>
  ): Item[] => {
    if (!Array.isArray(items)) {
      itemPageLogger.log(SYMBOLS.WARNING, 'items 不是陣列:', items);
      return [];
    }

    return items.filter(item => {
      if (!item) return false;

      // 搜尋文字篩選
      const matchesSearch = !searchText ||
        safeString(item.name).toLowerCase().includes(searchText.toLowerCase()) ||
        safeString(item.customNO).toLowerCase().includes(searchText.toLowerCase()) ||
        safeString(item.internationalBarCode).toLowerCase().includes(searchText.toLowerCase());

      // 動態篩選條件
      const matchesFilters = activeFilters.every(filterKey => {
        const value = filterValues[filterKey];
        if (!value) return true;

        try {
          switch (filterKey) {
            case "category":
              // 支援多選分類篩選
              if (Array.isArray(value)) {
                return value.some(categoryId => {
                  if (!categoryId) return false;
                  return isCategoryMatch(item.itemCategoryID, categoryId, data.categories);
                });
              } else {
                return isCategoryMatch(item.itemCategoryID, value, data.categories);
              }

            case "status":
              return Array.isArray(value)
                ? value.some(v => (v === 'active' && !safeBoolean(item.isStop)) || (v === 'inactive' && safeBoolean(item.isStop)))
                : (value === 'active' && !safeBoolean(item.isStop)) || (value === 'inactive' && safeBoolean(item.isStop));

            case "customNO":
              return safeString(item.customNO).toLowerCase().includes(value.toLowerCase());

            case "name":
              return safeString(item.name).toLowerCase().includes(value.toLowerCase());

            case "internationalBarCode":
              return safeString(item.internationalBarCode).toLowerCase().includes(value.toLowerCase());

            case "unit":
              return safeString(item.unit).toLowerCase().includes(value.toLowerCase());

            default:
              itemPageLogger.log(SYMBOLS.WARNING, `未知的篩選類型: ${filterKey}`);
              return true;
          }
        } catch (error) {
          itemPageLogger.log(SYMBOLS.ERROR, `篩選邏輯錯誤 (${filterKey}):`, error);
          return true;
        }
      });

      return matchesSearch && matchesFilters;
    });
  }, [data.categories, isCategoryMatch]);

  /**
   * 處理測試資料產生（使用自訂 Modal 支持動態更新）
   */
  const handleGenerateTestData = () => {
    setIsTestDataModalVisible(true);
  };

  /**
   * 執行測試資料產生
   */
  const executeTestDataGeneration = async () => {
    setIsTestDataModalVisible(false);
    setIsGeneratingTestData(true);

    try {
      // 使用當前的數量值，確保使用最新的狀態
      const count = testDataCount || 20000;

      // 驗證數量範圍
      if (count < 1 || count > 50000) {
        message.error('測試資料數量必須在 1 到 50,000 之間');
        return;
      }

      itemPageLogger.log(SYMBOLS.INFO, `開始產生測試資料: ${count} 筆`);

      // 呼叫簡化的測試資料產生函數
      const result = await generateTestItems(count);

      if (result.success) {
        message.success(`${result.message} (實際產生 ${count.toLocaleString()} 筆)`);
        // 重新載入庫存品資料
        await loadAllData();
      } else {
        message.error(result.message);
      }

    } catch (error: any) {
      itemPageLogger.log(SYMBOLS.ERROR, '產生測試資料時發生錯誤:', error);
      message.error('啟動測試資料產生失敗，請稍後再試');
    } finally {
      setIsGeneratingTestData(false);
    }
  };

  // 定義篩選選項供 AdvancedFilterComponent 使用
  const itemFilterOptions: FilterOption[] = useMemo(() => [
    {
      label: "庫存品分類",
      value: "category",
      type: "treeSelect",
      treeData: categoryTreeData,
      width: 250
    },
    {
      label: "庫存品狀態",
      value: "status",
      children: [
        { label: "啟用", value: "active" },
        { label: "停用", value: "inactive" },
      ],
    },
    {
      label: "庫存品編號",
      value: "customNO",
      type: "input",
      placeholder: "輸入庫存品編號"
    },
    {
      label: "庫存品名稱",
      value: "name",
      type: "input",
      placeholder: "輸入庫存品名稱"
    },
    {
      label: "國際條碼",
      value: "internationalBarCode",
      type: "input",
      placeholder: "輸入國際條碼"
    },
    {
      label: "單位",
      value: "unit",
      type: "input",
      placeholder: "輸入單位"
    },
  ], [categoryTreeData]);

  // 表格列定義
  const columns: SmartColumnType<Item>[] = [
    {
      title: '庫存品編號',
      dataIndex: 'customNO',
      key: 'customNO',
      width: 120,
      smartFilter: true,
      filterType: 'text',
      filterPlaceholder: '搜尋庫存品編號',
      sorter: (a: Item, b: Item) => safeString(a.customNO).localeCompare(safeString(b.customNO)),
      render: (text: string, record: Item) => {
        return safeString(text || record.customNO);
      }
    },
    {
      title: '庫存品名稱',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
      smartFilter: true,
      filterType: 'text',
      filterPlaceholder: '搜尋庫存品名稱',
      sorter: (a: Item, b: Item) => safeString(a.name).localeCompare(safeString(b.name)),
      render: (text: string, record: Item) => {
        return safeString(text || record.name);
      }
    },
    {
      title: '國際條碼',
      dataIndex: 'internationalBarCode',
      key: 'internationalBarCode',
      width: 150,
      smartFilter: true,
      filterType: 'text',
      filterPlaceholder: '搜尋國際條碼',
      sorter: (a: Item, b: Item) => safeString(a.internationalBarCode).localeCompare(safeString(b.internationalBarCode)),
      render: (text: string) => safeString(text)
    },
    {
      title: '單位',
      dataIndex: 'unit',
      key: 'unit',
      width: 80,
      responsive: ['md' as const],
      smartFilter: true,
      filterType: 'select-search',
      filterPlaceholder: '搜尋單位',
      sorter: (a: Item, b: Item) => safeString(a.unit).localeCompare(safeString(b.unit)),
      render: (text: string) => (
        <Tag
          color="blue"
          style={{
            fontSize: '11px',
            padding: '2px 6px',
            borderRadius: '4px',
            fontWeight: 500,
            maxWidth: '60px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}
        >
          {safeString(text)}
        </Tag>
      )
    },
    {
      title: '分類',
      dataIndex: 'itemCategoryID',
      key: 'itemCategoryID',
      width: 120,
      responsive: ['lg' as const],
      ellipsis: true,
      smartFilter: true,
      filterType: 'select-search',
      filterPlaceholder: '搜尋分類',
      filterDisplayValue: (id: string | null) => getCategoryName(id),
      sorter: (a: Item, b: Item) => {
        const categoryNameA = getCategoryName(a.itemCategoryID);
        const categoryNameB = getCategoryName(b.itemCategoryID);
        return categoryNameA.localeCompare(categoryNameB);
      },
      render: (categoryId: string | null) => {
        const categoryName = getCategoryName(categoryId);
        return (
          <Tag
            color="purple"
            icon={<FolderOutlined />}
            style={{
              fontSize: '11px',
              padding: '2px 6px',
              borderRadius: '4px',
              fontWeight: 500,
              maxWidth: '100px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
            title={categoryName}
          >
            {categoryName}
          </Tag>
        );
      }
    },
    {
      title: '價格資訊',
      key: 'priceInfo',
      width: 180,
      responsive: ['lg' as const],
      ellipsis: true,
      render: (_: any, record: Item) => (
        <div style={{ maxWidth: '160px', overflow: 'hidden' }}>
          <ItemPriceDisplay
            prices={record.prices}
            priceTypes={data.priceTypes}
            mode="compact"
            maxDisplay={2}
            showTooltip={true}
            style={{ fontSize: '10px' }}
          />
        </div>
      )
    },
    {
      title: '庫存品稅別',
      dataIndex: 'taxType',
      key: 'taxType',
      width: 100,
      render: (taxType: number) => {
        const getTaxTypeName = (type: number) => {
          switch (type) {
            case 1: return '應稅';
            case 2: return '免稅';
            case 3: return '零稅率';
            default: return '應稅';
          }
        };
        const getTaxTypeColor = (type: number) => {
          switch (type) {
            case 1: return 'blue';
            case 2: return 'green';
            case 3: return 'orange';
            default: return 'blue';
          }
        };
        const typeName = getTaxTypeName(taxType || 1);
        const typeColor = getTaxTypeColor(taxType || 1);
        return (
          <Tag color={typeColor}>
            {typeName}
          </Tag>
        );
      }
    },
    {
      title: '狀態',
      dataIndex: 'isStop',
      key: 'isStop',
      width: 80,
      render: (isStop: boolean) => (
        <Tag
          color={safeBoolean(isStop) ? 'red' : 'green'}
          icon={safeBoolean(isStop) ? <StopOutlined /> : <CheckCircleOutlined />}
        >
          {safeBoolean(isStop) ? '停用' : '啟用'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
      render: (_: any, record: Item) => (
        <Space size="small">
          <ButtonPermissionGuard resource="Ims/Item" action="Update" fallback={
            <Tooltip title="編輯">
              <Button
                type="primary"
                size="small"
                icon={<EditOutlined />}
                disabled={true}
              />
            </Tooltip>
          }>
            <Tooltip title="編輯">
              <Button
                type="primary"
                size="small"
                icon={<EditOutlined />}
                onClick={() => showEditModal(record)}
              />
            </Tooltip>
          </ButtonPermissionGuard>
          <ButtonPermissionGuard resource="Ims/Item" action="Delete" fallback={
            <Tooltip title="刪除">
              <Button
                danger
                size="small"
                icon={<DeleteOutlined />}
                disabled={true}
              />
            </Tooltip>
          }>
            <Popconfirm
              title="確定要刪除這個庫存品嗎？"
              description="此操作無法復原"
              onConfirm={() => handleDelete(record.itemID)}
              okText="確定"
              cancelText="取消"
              okType="danger"
            >
              <Tooltip title="刪除">
                <Button
                  danger
                  size="small"
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          </ButtonPermissionGuard>
        </Space>
      ),
    },
  ];

  return (
    <PagePermissionGuard resource="Ims/Item" action="Read">
      <div className="p-6">
        <ResponsiveStyles />
      <Spin spinning={loading}>
        <div style={{ marginBottom: '24px' }}>
          <Title level={2} style={{ marginBottom: '8px' }}>
            庫存品管理
          </Title>
          <p style={{ color: '#666', margin: 0, fontSize: '14px' }}>
            統一管理庫存品資訊、分類結構與價格設定
          </p>
        </div>

        {/* 響應式統計卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={12} lg={8}>
            <Card
              size={isMobile ? 'small' : 'default'}
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <ShopOutlined style={{ color: '#52c41a' }} />
                  <span>庫存品狀態統計</span>
                </div>
              }
              className="unified-card"
              style={{ height: '100%' }}
              styles={{
                header: {
                  backgroundColor: '#fafafa',
                  borderBottom: '1px solid #f0f0f0',
                  padding: isMobile ? '12px 16px' : '16px 20px'
                },
                body: {
                  padding: isMobile ? '12px 16px' : '16px 20px'
                }
              }}
            >
              <div style={{ marginBottom: '16px' }}>
                <div style={{ fontSize: isMobile ? '20px' : '24px', fontWeight: 'bold', marginBottom: '12px' }}>
                  {stats.activeItems} / {stats.totalItems}
                </div>
                <Progress
                  percent={stats.totalItems > 0 ? Math.round((stats.activeItems / stats.totalItems) * 100) : 0}
                  strokeColor="#52c41a"
                  showInfo={false}
                  size="small"
                />
                <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                  啟用率: {stats.totalItems > 0 ? Math.round((stats.activeItems / stats.totalItems) * 100) : 0}%
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={24} lg={16}>
            <Card
              size={isMobile ? 'small' : 'default'}
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <AppstoreOutlined style={{ color: '#1890ff' }} />
                  <span>分類與價格管理</span>
                </div>
              }
              className="unified-card"
              style={{ height: '100%' }}
              styles={{
                header: {
                  backgroundColor: '#fafafa',
                  borderBottom: '1px solid #f0f0f0',
                  padding: isMobile ? '12px 16px' : '16px 20px'
                },
                body: {
                  padding: isMobile ? '12px 16px' : '16px 20px'
                }
              }}
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={8}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                    <Statistic
                      title="庫存品分類"
                      value={stats.totalCategories}
                      prefix={<FolderOutlined />}
                      valueStyle={{ fontSize: isMobile ? '18px' : '20px' }}
                    />
                  </div>
                  <ButtonPermissionGuard resource="Ims/Item" action="Update" fallback={
                    <Button
                      type="primary"
                      size={isMobile ? 'small' : 'small'}
                      icon={<SettingOutlined />}
                      className="unified-button"
                      style={{ width: '100%' }}
                      disabled={true}
                    >
                      管理分類
                    </Button>
                  }>
                    <Button
                      type="primary"
                      size={isMobile ? 'small' : 'small'}
                      icon={<SettingOutlined />}
                      onClick={() => setIsCategoryModalVisible(true)}
                      className="unified-button"
                      style={{ width: '100%' }}
                    >
                      管理分類
                    </Button>
                  </ButtonPermissionGuard>
                </Col>
                <Col xs={24} sm={8}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                    <Statistic
                      title="價格類型"
                      value={stats.totalPriceTypes}
                      prefix={<TagsOutlined />}
                      valueStyle={{ fontSize: isMobile ? '18px' : '20px' }}
                    />
                  </div>
                  <ButtonPermissionGuard resource="Ims/Item" action="Update" fallback={
                    <Button
                      type="primary"
                      size={isMobile ? 'small' : 'small'}
                      icon={<SettingOutlined />}
                      className="unified-button"
                      style={{ width: '100%' }}
                      disabled={true}
                    >
                      管理價格類型
                    </Button>
                  }>
                    <Button
                      type="primary"
                      size={isMobile ? 'small' : 'small'}
                      icon={<SettingOutlined />}
                      onClick={() => setIsPriceTypeModalVisible(true)}
                      className="unified-button"
                      style={{ width: '100%' }}
                    >
                      管理價格類型
                    </Button>
                  </ButtonPermissionGuard>
                </Col>
                <Col xs={24} sm={8}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                    <Statistic
                      title="單位"
                      value={stats.totalUnits}
                      prefix={<TagsOutlined />}
                      valueStyle={{ fontSize: isMobile ? '18px' : '20px' }}
                    />
                  </div>
                  <ButtonPermissionGuard resource="Ims/Item" action="Update" fallback={
                    <Button
                      type="primary"
                      size={isMobile ? 'small' : 'small'}
                      icon={<SettingOutlined />}
                      className="unified-button"
                      style={{ width: '100%' }}
                      disabled={true}
                    >
                      管理單位
                    </Button>
                  }>
                    <Button
                      type="primary"
                      size={isMobile ? 'small' : 'small'}
                      icon={<SettingOutlined />}
                      onClick={() => setIsUnitModalVisible(true)}
                      className="unified-button"
                      style={{ width: '100%' }}
                    >
                      管理單位
                    </Button>
                  </ButtonPermissionGuard>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* 動態搜尋和篩選 */}
        <FilterSearchContainer
          ref={filterSearchRef}
          title="篩選與搜尋"
          filterOptions={itemFilterOptions}
          searchPlaceholder={isMobile ? "搜尋庫存品" : "搜尋庫存品名稱、編號或條碼"}
          showStats={true}
          stats={{
            total: data.items.length,
            filtered: filteredItems.length
          }}
          showClearMessage={true}
          clearMessage="已清除所有庫存品篩選條件"
          onFilterResult={(state) => {
            // 應用篩選邏輯
            const filtered = applyItemFilters(
              data.items,
              state.searchText,
              state.activeFilters,
              state.filterValues
            );
            setFilteredItems(filtered);
            setCurrentPage(1); // 重置分頁

            // 篩選結果已更新
          }}
          compact={isMobile}
          className="mb-6"
        />

        {/* 庫存品表格/列表 */}
        <Card
          title={
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-start',
              width: '100%',
              flexWrap: isMobile ? 'wrap' : 'nowrap',
              gap: '12px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <UnorderedListOutlined />
                <span>庫存品列表</span>
                <Tag color="blue">{filteredItems.length} 項</Tag>
              </div>

              {/* 主要操作按鈕 - 左對齊到標題列 */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                flexWrap: 'wrap',
                marginTop: isMobile ? '8px' : '0',
                marginLeft: isMobile ? '0' : '16px'
              }}>
                <ButtonPermissionGuard resource="Ims/Item" action="Read" fallback={
                  <Button
                    icon={<SyncOutlined />}
                    size="small"
                    title="重新載入資料並清除所有篩選條件"
                    className="unified-button"
                    disabled={true}
                  >
                    {!isMobile && '重新載入'}
                  </Button>
                }>
                  <Button
                    icon={<SyncOutlined />}
                    onClick={loadAllData}
                    loading={loading}
                    size="small"
                    title="重新載入資料並清除所有篩選條件"
                    className="unified-button"
                  >
                    {!isMobile && '重新載入'}
                  </Button>
                </ButtonPermissionGuard>
                <ButtonPermissionGuard resource="Ims/Item" action="Create" fallback={
                  <Button
                    type="primary"
                    icon={<FileAddOutlined />}
                    size="small"
                    className="unified-button"
                    disabled={true}
                  >
                    {isMobile ? '新增' : '新增庫存品'}
                  </Button>
                }>
                  <Button
                    type="primary"
                    icon={<FileAddOutlined />}
                    onClick={showAddModal}
                    size="small"
                    className="unified-button"
                  >
                    {isMobile ? '新增' : '新增庫存品'}
                  </Button>
                </ButtonPermissionGuard>
                {process.env.NODE_ENV === 'development' && (
                  <Button
                    danger
                    icon={<ExperimentOutlined />}
                    onClick={handleGenerateTestData}
                    loading={isGeneratingTestData}
                    disabled={loading}
                    size="small"
                    title="產生測試資料"
                    className="unified-button"
                  >
                    {isMobile ? '測試' : '產生測試資料'}
                  </Button>
                )}
              </div>
            </div>
          }
          className="enhanced-table unified-card"
          styles={{
            body: { padding: isMobile ? '8px' : '24px' },
            header: {
              padding: isMobile ? '12px 16px' : '16px 24px',
              borderBottom: '1px solid #f0f0f0',
              backgroundColor: '#fafafa'
            }
          }}
        >
          <ResponsiveTable
            columns={columns}
            dataSource={filteredItems}
            rowKey="itemID"
            loading={loading}
            mobileCardRender={mobileCardRender}
          />
        </Card>
      </Spin>

      {/* 庫存品編輯互動視窗 */}
      <ItemFormModal
        visible={isModalVisible}
        onClose={() => {
          setIsModalVisible(false);
          form.resetFields();
          setSelectedItem(null);
          setItemPrices({});
        }}
        selectedItem={selectedItem}
        onSubmit={handleSubmit}
        loading={loading}
        categories={data.categories}
        categoryTreeData={categoryTreeData}
        sortedCategoriesForDisplay={sortedCategoriesForDisplay}
        priceTypes={data.priceTypes}
        itemTaxTypes={itemTaxTypes}
        itemPrices={itemPrices}
        onItemPricesChange={setItemPrices}
        onCategoryDataChange={() => reloadData('categories')}
        onPriceTypeModalOpen={() => setIsPriceTypeModalVisible(true)}
      />

      {/* 分類管理組件 */}
      <ItemCategoryAdapter
        visible={isCategoryModalVisible}
        onClose={() => setIsCategoryModalVisible(false)}
        categories={data.categories}
        categoryTreeData={categoryTreeData}
        sortedCategoriesForDisplay={sortedCategoriesForDisplay}
        onDataChange={() => reloadData('categories')}
      />

      {/* 價格類型管理組件 */}
      <PriceTypeManagement
        visible={isPriceTypeModalVisible}
        onClose={() => setIsPriceTypeModalVisible(false)}
        priceTypes={data.priceTypes}
        onDataChange={() => reloadData('priceTypes')}
      />

      {/* 單位管理組件 */}
      <UnitManagement
        visible={isUnitModalVisible}
        onClose={() => setIsUnitModalVisible(false)}
        onDataChange={() => reloadData('units')}
      />

      {/* 測試資料生成 Modal */}
      <Modal
        title="⚠️ 產生測試資料"
        open={isTestDataModalVisible}
        onOk={executeTestDataGeneration}
        onCancel={() => setIsTestDataModalVisible(false)}
        okText="確定產生"
        cancelText="取消"
        okType="danger"
        width={500}
        confirmLoading={isGeneratingTestData}
      >
        <div>
          <p>此操作將產生 <strong>{(testDataCount || 20000).toLocaleString()} 筆</strong> 測試庫存品資料。</p>
          <div style={{ margin: '16px 0' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              自訂產生筆數：
            </label>
            <TestDataCountInput
              value={testDataCount}
              onChange={(value) => {
                itemPageLogger.log(SYMBOLS.LOADING, '測試資料數量變更:', value);
                setTestDataCount(value);
              }}
              min={1}
              max={50000}
              defaultValue={20000}
              placeholder="請輸入要產生的庫存品數量 (1-50,000)"
              showHint={true}
              onBlur={(value) => {
                itemPageLogger.log(SYMBOLS.INFO, '測試資料數量失去焦點:', value);
              }}
              onFocus={(value) => {
                itemPageLogger.log(SYMBOLS.INFO, '測試資料數量獲得焦點:', value);
              }}
            />
          </div>
          <p style={{ color: '#ff4d4f', marginBottom: 0 }}>
            ⚠️ 注意：這將在資料庫中新增大量測試資料，建議僅在開發環境中使用。
          </p>
        </div>
      </Modal>
    </div>
    </PagePermissionGuard>
  );
}

export default ItemPage;