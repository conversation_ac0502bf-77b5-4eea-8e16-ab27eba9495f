﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 折舊單-折舊紀錄
    /// </summary>
    public class DepreciationFormDetail : ModelBaseEntity
    {
        [Key]
        [Comment("折舊紀錄編號")]
        [Column(TypeName = "nvarchar(100)")]
        public Guid DepreciationId { get; set; } // 折舊紀錄編號

        [Comment("財產編號")]
        [ForeignKey("AssetId")]
        public Guid AssetId { get; set; } // 財產編號

        [Comment("折舊年度")]
        [Column(TypeName = "int")]
        public int DepreciationYear { get; set; } // 折舊年度

        [Comment("折舊月份")]
        [Column(TypeName = "int")]
        public int DepreciationMonth { get; set; } // 折舊月份

        [Comment("原始金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal OriginalAmount { get; set; } = 0; // 原始金額

        [Comment("期初帳面價值")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal BeginningBookValue { get; set; } = 0; // 期初帳面價值

        [Comment("期末帳面價值")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal EndingBookValue { get; set; } = 0; // 期末帳面價值

        [Comment("累計折舊金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal AccumulatedDepreciation { get; set; } = 0; // 累計折舊金額

        [Comment("前期累計折舊金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PriorAccumulatedDepreciation { get; set; } = 0; // 前期累計折舊金額

        [Comment("本期折舊金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentDepreciation { get; set; } = 0; // 本期折舊金額

        [Comment("折舊率")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal DepreciationRate { get; set; } = 0; // 折舊率

        [Comment("折舊方法")]
        [Column(TypeName = "nvarchar(50)")]
        public string DepreciationMethod { get; set; } // 折舊方法

        [Comment("剩餘耐用年限")]
        [Column(TypeName = "int")]
        public int ServiceLifeRemaining { get; set; } = 0; // 剩餘耐用年限

        [Comment("是否為調整紀錄")]
        [Column(TypeName = "bit")]
        public bool IsAdjustment { get; set; } = false; // 是否為調整紀錄

        [Comment("調整原因")]
        [Column(TypeName = "nvarchar(500)")]
        public string AdjustmentReason { get; set; } // 調整原因

        [Comment("備註")]
        [Column(TypeName = "nvarchar(500)")]
        public string Notes { get; set; } // 備註

        [Comment("新增折舊單日期")]
        [Column(TypeName = "bigint")]
        public long CreateDepreciationFormDate { get; set; } // 新增折舊單日期

        [Comment("折舊日期")]
        [Column(TypeName = "bigint")]
        public long DepreciationDate { get; set; } // 折舊日期

        public DepreciationFormDetail()
        {
            DepreciationId = Guid.NewGuid();
            AssetId = Guid.Empty;
            DepreciationYear = 0;
            DepreciationMonth = 0;
            OriginalAmount = 0;
            AccumulatedDepreciation = 0;
            PriorAccumulatedDepreciation = 0;
            CurrentDepreciation = 0;
            DepreciationRate = 0;
            DepreciationMethod = "";
            ServiceLifeRemaining = 0;
            IsAdjustment = false;
            AdjustmentReason = "";
            Notes = "";
            BeginningBookValue = 0;
            EndingBookValue = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
            DepreciationDate = 0;
        }
    }

    public class DepreciationFormDetailDTO : ModelBaseEntityDTO
    {
        public string DepreciationId { get; set; } // 折舊紀錄編號
        public Guid AssetId { get; set; } // 財產編號
        public string AssetNo { get; set; } // 財產編號
        public string AssetName { get; set; } // 財產名稱
        public int DepreciationYear { get; set; } // 折舊年度
        public int DepreciationMonth { get; set; } // 折舊月份
        public decimal OriginalAmount { get; set; } = 0; // 原始金額
        public decimal PriorAccumulatedDepreciation { get; set; } = 0; // 前期累計折舊金額
        public decimal AccumulatedDepreciation { get; set; } = 0; // 累計折舊金額
        public decimal CurrentDepreciation { get; set; } = 0; // 本期折舊金額
        public decimal DepreciationRate { get; set; } = 0; // 折舊率
        public string DepreciationMethod { get; set; } // 折舊方法
        public int ServiceLifeRemaining { get; set; } = 0; // 剩餘耐用年限
        public bool IsAdjustment { get; set; } = false; // 是否為調整紀錄
        public string AdjustmentReason { get; set; } // 調整原因
        public string Notes { get; set; } // 備註
        public decimal BeginningBookValue { get; set; } = 0; // 期初帳面價值
        public decimal EndingBookValue { get; set; } = 0; // 期末帳面價值
        public long DepreciationDate { get; set; } // 折舊日期
        public long CreateDepreciationFormDate { get; set; } // 新增折舊單日期
        public string? CreateUserName { get; set; } // 建立者姓名

        public DepreciationFormDetailDTO()
        {
            DepreciationId = "";
            AssetId = Guid.Empty;
            AssetNo = "";
            AssetName = "";
            DepreciationYear = 0;
            DepreciationMonth = 0;
            OriginalAmount = 0;
            AccumulatedDepreciation = 0;
            PriorAccumulatedDepreciation = 0;
            CurrentDepreciation = 0;
            DepreciationRate = 0;
            DepreciationMethod = "";
            ServiceLifeRemaining = 0;
            IsAdjustment = false;
            AdjustmentReason = "";
            Notes = "";
            BeginningBookValue = 0;
            EndingBookValue = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
            DepreciationDate = 0;
            CreateDepreciationFormDate = 0;
            CreateUserName = null;
        }
    }
}

