using FAST_ERP_Backend.Interfaces.Rms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Rms;
using Microsoft.EntityFrameworkCore;
using System.Data;

namespace FAST_ERP_Backend.Services.Rms
{
    /// <summary>
    /// 繳費管理服務
    /// </summary>
    public class PaymentService : IPaymentService
    {
        private readonly ERPDbContext _context;
        private readonly ILogger<PaymentService> _logger;

        /// <summary>
        /// 初始化繳費服務
        /// </summary>
        /// <param name="context">資料庫上下文</param>
        /// <param name="logger">日誌服務</param>
        public PaymentService(ERPDbContext context, ILogger<PaymentService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 建立繳費記錄
        /// </summary>
        /// <param name="request">繳費建立請求</param>
        /// <param name="tokenUid">使用者識別碼</param>
        /// <returns>繳費建立回應</returns>
        public async Task<ApiResponse<string>> CreatePaymentAsync(PaymentCreateRequest request, string tokenUid)
        {
            try
            {
                if (request.Allocations == null || request.Allocations.Count == 0)
                    return ApiResponse<string>.ErrorResult("請至少分配一筆費用", 400);

                var feeIds = request.Allocations.Select(a => a.FeeId).Distinct().ToList();
                var fees = await _context.Rms_Fees.Where(f => feeIds.Contains(f.FeeId)).ToListAsync();
                if (fees.Count != feeIds.Count) return ApiResponse<string>.ErrorResult("部分費用不存在", 404);

                // 所有費用必須屬於同一合約
                var contractId = fees.Select(f => f.ContractId).Distinct().Single();
                if (contractId != request.ContractId) return ApiResponse<string>.ErrorResult("費用與合約不一致", 400);

                // 驗證各筆分配不可超過未繳金額
                foreach (var a in request.Allocations)
                {
                    var fee = fees.First(f => f.FeeId == a.FeeId);
                    var remaining = fee.Amount - fee.PaidAmount;
                    if (a.Amount <= 0 || a.Amount > remaining)
                        return ApiResponse<string>.ErrorResult($"分配金額不合法：FeeId={a.FeeId}", 400);
                }

                var paymentAmount = request.Allocations.Sum(a => a.Amount);

                await using var tx = await _context.Database.BeginTransactionAsync(IsolationLevel.ReadCommitted);

                var payment = new Payment
                {
                    PaymentId = Guid.NewGuid().ToString(),
                    ContractId = request.ContractId,
                    PaymentDate = request.PaymentDate,
                    Amount = paymentAmount, // 後端以分配加總
                    PaymentType = request.PaymentType,
                    Status = "1", // 已入帳
                    Note = request.Note,
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    CreateUserId = tokenUid
                };
                _context.Rms_Payments.Add(payment);

                foreach (var a in request.Allocations)
                {
                    var fee = fees.First(f => f.FeeId == a.FeeId);
                    var alloc = new PaymentAllocation
                    {
                        PaymentAllocationId = Guid.NewGuid().ToString(),
                        PaymentId = payment.PaymentId,
                        FeeId = fee.FeeId,
                        Amount = a.Amount,
                        CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        CreateUserId = tokenUid
                    };
                    _context.Rms_PaymentAllocations.Add(alloc);

                    fee.PaidAmount += a.Amount;
                    fee.Status = fee.PaidAmount >= fee.Amount ? "2" : "1"; // 已繳 / 部分已繳
                    fee.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                    fee.UpdateUserId = tokenUid;
                }

                await _context.SaveChangesAsync();
                await tx.CommitAsync();

                return ApiResponse<string>.SuccessResult("建立繳費成功", payment.PaymentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "建立繳費時發生錯誤");
                return ApiResponse<string>.ErrorResult("建立繳費時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 取得繳費記錄列表
        /// </summary>
        /// <param name="contractId">合約編號</param>
        /// <returns>繳費記錄列表</returns>
        public async Task<ApiResponse<List<PaymentDTO>>> GetPaymentsAsync(string contractId)
        {
            try
            {
                var list = await _context.Rms_Payments.AsNoTracking()
                    .Where(p => p.ContractId == contractId)
                    .OrderByDescending(p => p.PaymentDate)
                    .Select(p => new PaymentDTO
                    {
                        PaymentId = p.PaymentId,
                        ContractId = p.ContractId,
                        PaymentDate = p.PaymentDate,
                        Amount = p.Amount,
                        PaymentType = p.PaymentType,
                        Status = p.Status,
                        Note = p.Note,
                        CreateTime = p.CreateTime ?? 0,
                        CreateUserId = p.CreateUserId,
                        UpdateTime = p.UpdateTime ?? 0,
                        UpdateUserId = p.UpdateUserId
                    }).ToListAsync();

                return ApiResponse<List<PaymentDTO>>.SuccessResult(list, "取得繳費列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得繳費列表時發生錯誤");
                return ApiResponse<List<PaymentDTO>>.ErrorResult("取得繳費列表時發生錯誤", 500);
            }
        }
    }
}


