# SMS API 使用範例與測試案例

## 概述

本文檔提供 SMS 模組各 API 的使用範例和測試案例，幫助開發者快速理解和使用 API。

## 測試環境設定

### 基礎 URL

```
https://api.example.com/api/sms
```

### 認證標頭

```bash
Authorization: Bearer {your_access_token}
Content-Type: application/json
```

## SmsDbServers API 測試案例

### 1. 查詢資料庫伺服器

#### 基本查詢

```bash
curl -X GET "https://api.example.com/api/sms/SmsDbServers" \
  -H "Authorization: Bearer {token}"
```

#### 關鍵字搜尋

```bash
curl -X GET "https://api.example.com/api/sms/SmsDbServers?keyword=mysql" \
  -H "Authorization: Bearer {token}"
```

#### 環境篩選

```bash
curl -X GET "https://api.example.com/api/sms/SmsDbServers?environment=production" \
  -H "Authorization: Bearer {token}"
```

#### 組合查詢

```bash
curl -X GET "https://api.example.com/api/sms/SmsDbServers?keyword=mysql&environment=production&dbEngine=MySQL" \
  -H "Authorization: Bearer {token}"
```

### 2. 新增資料庫伺服器

#### 基本新增

```bash
curl -X POST "https://api.example.com/api/sms/SmsDbServers" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "MySQL-Prod-01",
    "location": "Taipei",
    "ipAddress": "*************",
    "dbEngine": "MySQL",
    "version": "8.0",
    "environment": "production",
    "vmPort": 3306,
    "dockerPort": 3306,
    "remarks": "主要生產環境 MySQL 伺服器"
  }'
```

#### 完整欄位新增

```bash
curl -X POST "https://api.example.com/api/sms/SmsDbServers" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "PostgreSQL-Staging-01",
    "location": "Taipei",
    "ipAddress": "*************",
    "dbEngine": "PostgreSQL",
    "version": "14.0",
    "environment": "staging",
    "vmPort": 5432,
    "dockerPort": 5432,
    "enableDate": 1640995200000,
    "remarks": "測試環境 PostgreSQL 伺服器"
  }'
```

### 3. 更新資料庫伺服器

```bash
curl -X PUT "https://api.example.com/api/sms/SmsDbServers/{id}" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "version": "8.0.32",
    "remarks": "更新到最新版本 8.0.32"
  }'
```

### 4. 批次新增資料庫伺服器

```bash
curl -X POST "https://api.example.com/api/sms/SmsDbServers/batch" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "serverName": "MySQL-Dev-01",
      "location": "Taipei",
      "ipAddress": "*************",
      "dbEngine": "MySQL",
      "version": "8.0",
      "environment": "development",
      "vmPort": 3306,
      "dockerPort": 3306
    },
    {
      "serverName": "MongoDB-Dev-01",
      "location": "Taipei",
      "ipAddress": "*************",
      "dbEngine": "MongoDB",
      "version": "6.0",
      "environment": "development",
      "vmPort": 27017,
      "dockerPort": 27017
    }
  ]'
```

## SmsSites API 測試案例

### 1. 查詢站台

#### 基本查詢

```bash
curl -X GET "https://api.example.com/api/sms/SmsSites" \
  -H "Authorization: Bearer {token}"
```

#### 關鍵字搜尋

```bash
curl -X GET "https://api.example.com/api/sms/SmsSites?keyword=production" \
  -H "Authorization: Bearer {token}"
```

#### 伺服器篩選

```bash
curl -X GET "https://api.example.com/api/sms/SmsSites?webServerId={webServerId}&dbServerId={dbServerId}" \
  -H "Authorization: Bearer {token}"
```

### 2. 新增站台

```bash
curl -X POST "https://api.example.com/api/sms/SmsSites" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "siteName": "Production-Site-01",
    "webServerId": "123e4567-e89b-12d3-a456-426614174000",
    "dbServerId": "987fcdeb-51a2-43d1-b789-123456789abc",
    "purposeDescription": "主要生產環境站台，用於客戶服務系統",
    "ownerUserId": "user001",
    "coOwnerUserId1": "user002",
    "enableDate": 1640995200000
  }'
```

### 3. 批次新增站台

```bash
curl -X POST "https://api.example.com/api/sms/SmsSites/batch" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "siteName": "Staging-Site-01",
      "webServerId": "456e7890-e89b-12d3-a456-426614174000",
      "dbServerId": "654fcdeb-51a2-43d1-b789-123456789abc",
      "purposeDescription": "測試環境站台",
      "ownerUserId": "user003"
    },
    {
      "siteName": "Development-Site-01",
      "webServerId": "789e0123-e89b-12d3-a456-426614174000",
      "dbServerId": "321fcdeb-51a2-43d1-b789-123456789abc",
      "purposeDescription": "開發環境站台",
      "ownerUserId": "user004"
    }
  ]'
```

## SmsWebServers API 測試案例

### 1. 查詢 Web 伺服器

#### 基本查詢

```bash
curl -X GET "https://api.example.com/api/sms/SmsWebServers" \
  -H "Authorization: Bearer {token}"
```

#### 環境篩選

```bash
curl -X GET "https://api.example.com/api/sms/SmsWebServers?environment=production" \
  -H "Authorization: Bearer {token}"
```

### 2. 新增 Web 伺服器

```bash
curl -X POST "https://api.example.com/api/sms/SmsWebServers" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "WebServer-Prod-01",
    "location": "Taipei",
    "ipAddress": "*************",
    "operatingSystem": "Ubuntu 20.04",
    "environment": "production",
    "vmPort": 80,
    "dockerPort": 8080,
    "remarks": "主要生產環境 Web 伺服器，運行 Nginx"
  }'
```

### 3. 批次新增 Web 伺服器

```bash
curl -X POST "https://api.example.com/api/sms/SmsWebServers/batch" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "serverName": "WebServer-Staging-01",
      "location": "Taipei",
      "ipAddress": "*************",
      "operatingSystem": "Ubuntu 20.04",
      "environment": "staging",
      "vmPort": 80,
      "dockerPort": 8080
    },
    {
      "serverName": "WebServer-Dev-01",
      "location": "Taipei",
      "ipAddress": "*************",
      "operatingSystem": "Ubuntu 20.04",
      "environment": "development",
      "vmPort": 80,
      "dockerPort": 8080
    }
  ]'
```

## 錯誤處理測試案例

### 1. 無效的 UUID 格式

```bash
curl -X GET "https://api.example.com/api/sms/SmsDbServers/invalid-uuid" \
  -H "Authorization: Bearer {token}"
```

**預期回應**: 400 Bad Request

### 2. 重複的伺服器名稱

```bash
curl -X POST "https://api.example.com/api/sms/SmsDbServers" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "MySQL-Prod-01",
    "ipAddress": "*************",
    "dbEngine": "MySQL",
    "environment": "production"
  }'
```

**預期回應**: 409 Conflict

### 3. 缺少必填欄位

```bash
curl -X POST "https://api.example.com/api/sms/SmsDbServers" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "Test-Server"
  }'
```

**預期回應**: 400 Bad Request

## 效能測試案例

### 1. 大量資料查詢

```bash
# 測試查詢 1000 筆資料的效能
curl -X GET "https://api.example.com/api/sms/SmsDbServers" \
  -H "Authorization: Bearer {token}" \
  -w "@curl-format.txt"
```

### 2. 批次新增效能

```bash
# 測試批次新增 100 筆資料的效能
curl -X POST "https://api.example.com/api/sms/SmsDbServers/batch" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d @large-batch-data.json \
  -w "@curl-format.txt"
```

## 安全性測試案例

### 1. 未授權存取

```bash
curl -X GET "https://api.example.com/api/sms/SmsDbServers"
```

**預期回應**: 401 Unauthorized

### 2. 權限不足

```bash
# 使用權限不足的 token
curl -X DELETE "https://api.example.com/api/sms/SmsDbServers/{id}" \
  -H "Authorization: Bearer {limited_token}"
```

**預期回應**: 403 Forbidden

## 測試資料準備

### 建立測試資料腳本

```bash
#!/bin/bash

# 建立測試用資料庫伺服器
echo "建立測試資料庫伺服器..."
curl -X POST "https://api.example.com/api/sms/SmsDbServers" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "Test-DB-01",
    "location": "Test",
    "ipAddress": "192.168.1.999",
    "dbEngine": "TestDB",
    "environment": "test"
  }'

# 建立測試用 Web 伺服器
echo "建立測試 Web 伺服器..."
curl -X POST "https://api.example.com/api/sms/SmsWebServers" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "Test-Web-01",
    "location": "Test",
    "ipAddress": "192.168.1.998",
    "operatingSystem": "TestOS",
    "environment": "test"
  }'

echo "測試資料建立完成"
```

## 監控與日誌

### 1. API 回應時間監控

```bash
curl -X GET "https://api.example.com/api/sms/SmsDbServers" \
  -H "Authorization: Bearer {token}" \
  -w "時間統計:\n   DNS 解析: %{time_namelookup}s\n   連接建立: %{time_connect}s\n   請求處理: %{time_starttransfer}s\n   總時間: %{time_total}s\n"
```

### 2. 錯誤率監控

```bash
# 監控 404 錯誤
curl -X GET "https://api.example.com/api/sms/SmsDbServers/non-existent-id" \
  -H "Authorization: Bearer {token}" \
  -w "HTTP 狀態碼: %{http_code}\n"
```

## 總結

本文檔提供了 SMS 模組各 API 的完整測試案例，包括：

- 基本 CRUD 操作測試
- 錯誤處理測試
- 效能測試
- 安全性測試
- 監控與日誌

建議開發者根據實際需求調整測試案例，並建立自動化測試腳本以提高測試效率。
