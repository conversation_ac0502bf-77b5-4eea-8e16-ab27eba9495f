/**
 * 聯絡人篩選器組件
 * 
 * 充分利用現有的 FilterSearchContainer 和 AdvancedFilterComponent
 * 提供統一的聯絡人篩選和搜尋體驗
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

"use client";

import React from 'react';

// 共享組件
import FilterSearchContainer, { 
  FilterSearchContainerProps 
} from '../shared/FilterSearchContainer';

// 聯絡人相關
import { Contact } from '@/services/ims/ContactService';
import { CONTACT_FILTER_OPTIONS } from './shared/contactConstants';
import { applyContactFilters, getContactStats } from './shared/contactUtils';

export interface ContactFiltersProps extends Omit<FilterSearchContainerProps, 'filterOptions' | 'onFilterResult'> {
  /** 聯絡人資料 */
  contacts: Contact[];
  /** 篩選結果回調 */
  onFilterResult: (filteredContacts: Contact[], filterState: {
    searchText: string;
    activeFilters: string[];
    filterValues: Record<string, any>;
    hasActiveFilters: boolean;
    filterCount: number;
  }) => void;
  /** 是否顯示統計資訊 */
  showStats?: boolean;
}

/**
 * 聯絡人篩選器組件
 * 
 * 特性：
 * - 使用統一的 CONTACT_FILTER_OPTIONS 配置
 * - 整合 filterContacts 工具函數
 * - 自動計算統計資訊
 * - 支援響應式設計
 */
const ContactFilters: React.FC<ContactFiltersProps> = ({
  contacts = [],
  onFilterResult,
  showStats = true,
  title = "聯絡人篩選與搜尋",
  searchPlaceholder = "搜尋聯絡人姓名、郵件、電話、公司...",
  ...containerProps
}) => {

  // 處理篩選結果
  const handleFilterResult = (filterState: {
    searchText: string;
    activeFilters: string[];
    filterValues: Record<string, any>;
    hasActiveFilters: boolean;
    filterCount: number;
  }) => {
    // 使用統一的 applyContactFilters 工具函數
    const filteredContacts = applyContactFilters(
      contacts,
      filterState.searchText,
      filterState.filterValues
    );

    // 回調篩選結果
    onFilterResult(filteredContacts, filterState);
  };

  // 計算統計資訊
  const stats = React.useMemo(() => {
    if (!showStats) return undefined;
    
    const contactStats = getContactStats(contacts);
    return {
      total: contactStats.total,
      filtered: contacts.length // 這會被 handleFilterResult 更新
    };
  }, [contacts, showStats]);

  return (
    <FilterSearchContainer
      title={title}
      searchPlaceholder={searchPlaceholder}
      filterOptions={[...CONTACT_FILTER_OPTIONS] as any}
      onFilterResult={handleFilterResult}
      showStats={showStats}
      stats={stats}
      {...containerProps}
    />
  );
};

export default ContactFilters;
