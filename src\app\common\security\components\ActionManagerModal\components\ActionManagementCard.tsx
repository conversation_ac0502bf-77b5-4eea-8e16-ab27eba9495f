import React from 'react';
import { Card, Button, Space, Typography, Tag, Divider } from 'antd';
import { SettingOutlined, PlusOutlined, CheckOutlined } from '@ant-design/icons';
import { createContextLogger, SYMBOLS } from '@/utils/logger';
import { useScreenSize } from '@/hooks/useResponsive';
import ActionCard from './ActionCard';
import AvailableActionCard from './AvailableActionCard';
import { ActionManagementCardProps } from '../types/actionManager.types';

// 遵循 FastERP 日誌記錄策略
const actionManagementLogger = createContextLogger({ module: 'ActionManagementCard' });

const { Text, Title } = Typography;

const ActionManagementCard: React.FC<ActionManagementCardProps> = ({
  state,
  availableActions,
  permission,
  onStartEdit,
  onCancelEdit,
  onSaveEdit,
  onUpdateEditValues,
  onToggleBatchSelection,
  onClearAllSelections,
  onBatchAdd
}) => {
  const { isMobile } = useScreenSize();

  const handleStartEdit = (actionId: string, action: any) => {
    actionManagementLogger.log(SYMBOLS.DEBUG, '開始編輯動作', { actionId });
    onStartEdit(actionId, action);
  };

  const handleCancelEdit = (actionId: string) => {
    actionManagementLogger.log(SYMBOLS.DEBUG, '取消編輯動作', { actionId });
    onCancelEdit(actionId);
  };

  const handleSaveEdit = (actionId: string) => {
    actionManagementLogger.log(SYMBOLS.DEBUG, '儲存編輯動作', { actionId });
    onSaveEdit(actionId);
  };

  const handleUpdateEditValues = (actionId: string, values: { code: string; name: string; sortCode: number }) => {
    actionManagementLogger.log(SYMBOLS.DEBUG, '更新編輯值', { actionId, values });
    onUpdateEditValues(actionId, values);
  };

  const handleToggleBatchSelection = (actionCode: string) => {
    actionManagementLogger.log(SYMBOLS.DEBUG, '切換批次選取', { actionCode });
    onToggleBatchSelection(actionCode);
  };

  const handleClearAllSelections = () => {
    actionManagementLogger.log(SYMBOLS.DEBUG, '清除所有選取');
    onClearAllSelections();
  };

  const handleBatchAdd = () => {
    actionManagementLogger.log(SYMBOLS.DEBUG, '執行批次新增', { 
      selectedCount: state.ui.selectedBatchCodes.size 
    });
    onBatchAdd();
  };

  return (
    <div>
      {/* 權限資訊 */}
      <Card size="small" style={{ marginBottom: 16, background: '#f0f8ff' }}>
        <Space direction="vertical" size="small">
          <div>
            <Text strong>權限路徑：</Text>
            <Tag color="blue" style={{ fontFamily: 'monospace' }}>
              {permission.path}
            </Tag>
          </div>
          <div>
            <Text type="secondary">{permission.description}</Text>
          </div>
        </Space>
      </Card>

      {/* 新增標準動作 */}
      {availableActions.length > 0 && (
        <Card 
          size="small" 
          title={
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              width: '100%'
            }}>
              <Space>
                <PlusOutlined style={{ color: '#1890ff' }} />
                <span>新增標準動作</span>
                <Tag color="blue" style={{ fontSize: '12px' }}>
                  {availableActions.length} 個可用
                </Tag>
                {state.ui.selectedBatchCodes.size > 0 && (
                  <Tag color="green" style={{ fontSize: '12px' }}>
                    <CheckOutlined style={{ marginRight: '4px' }} />
                    已選取 {state.ui.selectedBatchCodes.size} 個
                  </Tag>
                )}
              </Space>
              {state.ui.selectedBatchCodes.size > 0 && (
                <Space size="small">
                  <Button 
                    size="small" 
                    onClick={handleClearAllSelections}
                    style={{ 
                      borderColor: '#d9d9d9',
                      color: '#8c8c8c'
                    }}
                  >
                    清除選取
                  </Button>
                  <Button 
                    size="small" 
                    type="primary" 
                    onClick={handleBatchAdd}
                    style={{ 
                      background: '#1890ff',
                      borderColor: '#1890ff',
                      fontWeight: '500'
                    }}
                  >
                    批次新增 ({state.ui.selectedBatchCodes.size})
                  </Button>
                </Space>
              )}
            </div>
          }
          style={{ marginBottom: 16 }}
        >
          <div style={{ marginBottom: 12 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              點擊下方動作卡片可選取多個標準動作，然後批次新增到當前權限中
            </Text>
          </div>

          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: isMobile ? 'repeat(2, 1fr)' : 'repeat(4, 1fr)', 
            gap: '12px',
            marginBottom: '8px'
          }}>
            {availableActions.map(actionDef => {
              const isAdding = state.ui.addingActions.has(actionDef.code);
              const isSelected = state.ui.selectedBatchCodes.has(actionDef.code);
              
              return (
                <AvailableActionCard
                  key={actionDef.code}
                  action={actionDef}
                  isSelected={isSelected}
                  onToggle={() => handleToggleBatchSelection(actionDef.code)}
                />
              );
            })}
          </div>
        </Card>
      )}

      {/* 現有動作清單 */}
      <Card 
        size="small" 
        title={
          <Space>
            <SettingOutlined style={{ color: '#1890ff' }} />
            <span>現有動作</span>
            <Tag color="blue" style={{ fontSize: '12px' }}>
              {state.data.actions.length} 個
            </Tag>
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        {state.data.actions.length === 0 ? (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px 20px',
            color: '#8c8c8c'
          }}>
            <SettingOutlined style={{ fontSize: '48px', marginBottom: '16px', color: '#d9d9d9' }} />
            <div style={{ fontSize: '14px', marginBottom: '8px' }}>尚未建立任何動作</div>
            <div style={{ fontSize: '12px' }}>請使用上方功能新增標準動作</div>
          </div>
        ) : (
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: isMobile ? 'repeat(1, 1fr)' : 'repeat(2, 1fr)', 
            gap: '12px'
          }}>
            {state.data.actions.map((action) => {
              const isEditing = state.ui.editingAction === action.granularPermissionActionID;
              const editValues = state.ui.editingValues[action.granularPermissionActionID] || { 
                code: action.code, 
                name: action.name, 
                sortCode: action.sortCode 
              };
              
              return (
                <ActionCard
                  key={action.granularPermissionActionID}
                  action={action}
                  isEditing={isEditing}
                  editValues={editValues}
                  onStartEdit={() => handleStartEdit(action.granularPermissionActionID, action)}
                  onSaveEdit={() => handleSaveEdit(action.granularPermissionActionID)}
                  onCancelEdit={() => handleCancelEdit(action.granularPermissionActionID)}
                  onUpdateEditValues={(values) => handleUpdateEditValues(action.granularPermissionActionID, values)}
                />
              );
            })}
          </div>
        )}
      </Card>
    </div>
  );
};

export default ActionManagementCard;
