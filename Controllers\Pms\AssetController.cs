﻿using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Interfaces.Pms;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("財產資料管理")]
    public class AssetController : ControllerBase
    {
        private readonly IAssetService _Interface;

        public AssetController(IAssetService assetService)
        {
            _Interface = assetService;
        }

        [HttpGet]
        [Route("GetNewAssetNo")]
        [SwaggerOperation(Summary = "取得新的財產編號", Description = "根據科目、子目和類別生成新的財產編號")]
        public async Task<IActionResult> GetNewAssetNo([FromQuery] AssetNoRequestDTO request)
        {
            // ModelState.IsValid 會自動檢查驗證規則
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _Interface.GetNewAssetNoAsync(request.Subject, request.SubSubject, request.Category);
            if (!result.success)
                return BadRequest(result.message);

            return Ok(new { success = result.success, assetNo = result.assetNo, message = result.message });
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得所有資產", Description = "取得所有資產列表")]
        public async Task<IActionResult> GetAssets()
        {
            var assets = await _Interface.GetAssetAsync();
            return Ok(assets);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得資產詳細資料", Description = "根據ID取得資產詳細資料")]
        public async Task<IActionResult> GetAssetDetail(string id)
        {
            var asset = await _Interface.GetAssetDetailAsync(id);
            if (asset == null)
                return NotFound($"找不到編號為{id}的資產");

            return Ok(asset);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增資產", Description = "新增單一資產")]
        public async Task<IActionResult> AddAsset([FromBody] AssetWithAccessoriesDTO _data)
        {
            var (result, msg) = await _Interface.AddAssetAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯資產", Description = "編輯資產資料")]
        public async Task<IActionResult> EditAsset([FromBody] AssetWithAccessoriesDTO asset)
        {
            var result = await _Interface.EditAssetAsync(asset);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除資產", Description = "刪除資產資料")]
        public async Task<IActionResult> DeleteAsset([FromBody] AssetWithAccessoriesDTO asset)
        {
            var result = await _Interface.DeleteAssetAsync(asset);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("ValidateExcelFile")]
        [SwaggerOperation(Summary = "驗證批次轉檔Excel檔案", Description = "上傳Excel檔案進行格式驗證")]
        public async Task<IActionResult> ValidateExcelFile(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { success = false, message = "請選擇要上傳的檔案" });
                }

                // 檢查檔案格式
                var allowedExtensions = new[] { ".xlsx", ".xls" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest(new { success = false, message = "檔案格式不正確，請上傳Excel檔案(.xlsx或.xls)" });
                }

                // 檢查檔案大小（限制10MB）
                if (file.Length > 10 * 1024 * 1024)
                {
                    return BadRequest(new { success = false, message = "檔案大小不能超過10MB" });
                }

                using (var stream = file.OpenReadStream())
                {
                    var validationResult = await _Interface.ValidateExcelFileAsync(stream);
                    return Ok(new { success = validationResult.IsValid, data = validationResult });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"檔案驗證發生錯誤：{ex.Message}" });
            }
        }

        [HttpPost]
        [Route("BatchImport")]
        [SwaggerOperation(Summary = "批次匯入資產資料", Description = "上傳Excel檔案進行批次資產匯入")]
        public async Task<IActionResult> BatchImportAssets(IFormFile file, [FromQuery] string userId = "system")
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { success = false, message = "請選擇要上傳的檔案" });
                }

                // 檢查檔案格式
                var allowedExtensions = new[] { ".xlsx", ".xls" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest(new { success = false, message = "檔案格式不正確，請上傳Excel檔案(.xlsx或.xls)" });
                }

                // 檢查檔案大小（限制10MB）
                if (file.Length > 10 * 1024 * 1024)
                {
                    return BadRequest(new { success = false, message = "檔案大小不能超過10MB" });
                }

                using (var stream = file.OpenReadStream())
                {
                    var importResult = await _Interface.BatchImportAssetsAsync(stream, userId);
                    return Ok(new { success = importResult.Success, data = importResult });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"批次匯入發生錯誤：{ex.Message}" });
            }
        }

        [HttpGet]
        [Route("DownloadBatchTemplate")]
        [SwaggerOperation(Summary = "下載批次轉檔範本", Description = "下載Excel批次轉檔範本檔案")]
        public async Task<IActionResult> DownloadBatchTemplate()
        {
            try
            {
                var (fileBytes, fileName) = await _Interface.DownloadBatchTemplateAsync();

                return File(fileBytes,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    fileName);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"下載範本發生錯誤：{ex.Message}" });
            }
        }

        [HttpPost]
        [Route("CancelBatchImport")]
        [SwaggerOperation(Summary = "取消批次匯入", Description = "取消指定使用者的批次匯入作業")]
        public async Task<IActionResult> CancelBatchImport([FromQuery] string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
            {
                return BadRequest(new { success = false, message = "userId 不能為空" });
            }
            var ok = await _Interface.CancelBatchImportAsync(userId);
            return Ok(new { success = ok, message = ok ? "已發出取消請求" : "取消請求失敗" });
        }

        #region 財產統計 API

        /// <summary>
        /// 取得完整財產統計資料
        /// </summary>
        /// <param name="request">統計查詢參數</param>
        /// <returns>完整統計結果</returns>
        [HttpPost]
        [Route("statistics/all")]
        [SwaggerOperation(Summary = "取得完整財產統計資料", Description = "取得包含所有類型的財產統計資料，用於生成儀表板圖表")]
        public async Task<ActionResult<AssetStatisticsResultDTO>> GetAssetStatistics([FromBody] AssetStatisticsRequestDTO? request = null)
        {
            try
            {
                var result = await _Interface.GetAssetStatisticsAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"取得統計資料失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 取得財產統計總覽
        /// </summary>
        /// <returns>統計總覽資料</returns>
        [HttpGet]
        [Route("statistics/overview")]
        [SwaggerOperation(Summary = "取得財產統計總覽", Description = "取得財產總數、總價值、折舊等總覽統計資料")]
        public async Task<ActionResult<AssetStatisticsOverviewDTO>> GetAssetStatisticsOverview()
        {
            try
            {
                var result = await _Interface.GetAssetStatisticsOverviewAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"取得統計總覽失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 按部門取得財產統計
        /// </summary>
        /// <param name="departmentIds">部門ID篩選（可選）</param>
        /// <returns>部門統計資料</returns>
        [HttpGet]
        [Route("statistics/by-department")]
        [SwaggerOperation(Summary = "按部門取得財產統計", Description = "取得各部門的財產數量、價值分布統計")]
        public async Task<ActionResult<List<AssetStatisticsByDepartmentDTO>>> GetAssetStatisticsByDepartment([FromQuery] List<string>? departmentIds = null)
        {
            try
            {
                var result = await _Interface.GetAssetStatisticsByDepartmentAsync(departmentIds);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"取得部門統計失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 按狀態取得財產統計
        /// </summary>
        /// <param name="statusIds">狀態ID篩選（可選）</param>
        /// <returns>狀態統計資料</returns>
        [HttpGet]
        [Route("statistics/by-status")]
        [SwaggerOperation(Summary = "按狀態取得財產統計", Description = "取得各狀態（正常、維修中、閒置等）的財產分布統計")]
        public async Task<ActionResult<List<AssetStatisticsByStatusDTO>>> GetAssetStatisticsByStatus([FromQuery] List<Guid>? statusIds = null)
        {
            try
            {
                var result = await _Interface.GetAssetStatisticsByStatusAsync(statusIds);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"取得狀態統計失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 按科目取得財產統計
        /// </summary>
        /// <param name="accountIds">科目ID篩選（可選）</param>
        /// <returns>科目統計資料</returns>
        [HttpGet]
        [Route("statistics/by-account")]
        [SwaggerOperation(Summary = "按科目取得財產統計", Description = "取得各財產科目的資產分布和價值統計")]
        public async Task<ActionResult<List<AssetStatisticsByAccountDTO>>> GetAssetStatisticsByAccount([FromQuery] List<Guid>? accountIds = null)
        {
            try
            {
                var result = await _Interface.GetAssetStatisticsByAccountAsync(accountIds);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"取得科目統計失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 按年度取得財產統計
        /// </summary>
        /// <param name="startYear">起始年度（可選，預設為過去5年）</param>
        /// <param name="endYear">結束年度（可選，預設為當前年度）</param>
        /// <returns>年度統計資料</returns>
        [HttpGet]
        [Route("statistics/by-year")]
        [SwaggerOperation(Summary = "按年度取得財產統計", Description = "取得各年度的財產新增、報廢、折舊等趨勢統計")]
        public async Task<ActionResult<List<AssetStatisticsByYearDTO>>> GetAssetStatisticsByYear([FromQuery] int? startYear = null, [FromQuery] int? endYear = null)
        {
            try
            {
                var result = await _Interface.GetAssetStatisticsByYearAsync(startYear, endYear);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"取得年度統計失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 按月度取得財產統計
        /// </summary>
        /// <param name="year">年度（可選，預設為當前年度）</param>
        /// <param name="months">月份數量（可選，預設12個月）</param>
        /// <returns>月度統計資料</returns>
        [HttpGet]
        [Route("statistics/by-month")]
        [SwaggerOperation(Summary = "按月度取得財產統計", Description = "取得各月度的財產變動趨勢統計")]
        public async Task<ActionResult<List<AssetStatisticsByMonthDTO>>> GetAssetStatisticsByMonth([FromQuery] int? year = null, [FromQuery] int months = 12)
        {
            try
            {
                var result = await _Interface.GetAssetStatisticsByMonthAsync(year, months);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"取得月度統計失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 按廠牌取得財產統計
        /// </summary>
        /// <returns>廠牌統計資料</returns>
        [HttpGet]
        [Route("statistics/by-manufacturer")]
        [SwaggerOperation(Summary = "按廠牌取得財產統計", Description = "取得各廠牌的財產數量和價值分布統計")]
        public async Task<ActionResult<List<AssetStatisticsByManufacturerDTO>>> GetAssetStatisticsByManufacturer()
        {
            try
            {
                var result = await _Interface.GetAssetStatisticsByManufacturerAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"取得廠牌統計失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 按設備類型取得財產統計
        /// </summary>
        /// <returns>設備類型統計資料</returns>
        [HttpGet]
        [Route("statistics/by-equipment-type")]
        [SwaggerOperation(Summary = "按設備類型取得財產統計", Description = "取得各設備類型的財產分布統計")]
        public async Task<ActionResult<List<AssetStatisticsByEquipmentTypeDTO>>> GetAssetStatisticsByEquipmentType()
        {
            try
            {
                var result = await _Interface.GetAssetStatisticsByEquipmentTypeAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"取得設備類型統計失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 按使用年限取得財產統計
        /// </summary>
        /// <returns>使用年限統計資料</returns>
        [HttpGet]
        [Route("statistics/by-age")]
        [SwaggerOperation(Summary = "按使用年限取得財產統計", Description = "取得各使用年限區間的財產分布統計")]
        public async Task<ActionResult<List<AssetStatisticsByAgeDTO>>> GetAssetStatisticsByAge()
        {
            try
            {
                var result = await _Interface.GetAssetStatisticsByAgeAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"取得使用年限統計失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 取得折舊統計資料
        /// </summary>
        /// <param name="year">年度（可選，預設為當前年度）</param>
        /// <param name="months">月份數量（可選，預設12個月）</param>
        /// <returns>折舊統計資料</returns>
        [HttpGet]
        [Route("statistics/depreciation")]
        [SwaggerOperation(Summary = "取得折舊統計資料", Description = "取得各月度的折舊金額趨勢統計")]
        public async Task<ActionResult<List<AssetDepreciationStatisticsDTO>>> GetAssetDepreciationStatistics([FromQuery] int? year = null, [FromQuery] int months = 12)
        {
            try
            {
                var result = await _Interface.GetAssetDepreciationStatisticsAsync(year, months);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"取得折舊統計失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 按價值區間取得財產統計
        /// </summary>
        /// <returns>價值區間統計資料</returns>
        [HttpGet]
        [Route("statistics/by-value-range")]
        [SwaggerOperation(Summary = "按價值區間取得財產統計", Description = "取得各價值區間的財產數量分布統計")]
        public async Task<ActionResult<List<AssetStatisticsByValueRangeDTO>>> GetAssetStatisticsByValueRange()
        {
            try
            {
                var result = await _Interface.GetAssetStatisticsByValueRangeAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"取得價值區間統計失敗：{ex.Message}" });
            }
        }

        #endregion
    }
}