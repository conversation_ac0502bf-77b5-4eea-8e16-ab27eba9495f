﻿using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Tools;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Interfaces.Rms;
using FAST_ERP_Backend.Interfaces.Sms;
using FAST_ERP_Backend.Services.Common;
using FAST_ERP_Backend.Services.Pms;
using FAST_ERP_Backend.Services.Ims;
using FAST_ERP_Backend.Services.Rms;
using FAST_ERP_Backend.Tools;
using FAST_ERP_Backend.Services.Sms;
using System.Reflection;
using Microsoft.OpenApi.Models;
using FAST_ERP_Backend.Middlewares;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Extensions.FileProviders;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Services.Pas;
using AutoMapper;

var builder = WebApplication.CreateBuilder(args);

#region Logging Configuration
// 配置系統日誌
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

// 設置日誌級別
if (builder.Environment.IsDevelopment())
{
    builder.Logging.SetMinimumLevel(LogLevel.Debug);
}
else
{
    builder.Logging.SetMinimumLevel(LogLevel.Information);
}

// 註冊新的日誌系統組件
builder.Services.AddSingleton<IMongoDBConnectionManager, MongoDBConnectionManager>();
builder.Services.AddSingleton<ILogDataProcessor, LogDataProcessor>();
builder.Services.AddSingleton<EnhancedEntityChangeTracker>();
builder.Services.AddSingleton<ILoggerService, MongoDBLoggerService>();
#endregion

#region Core Services
// 註冊HttpContextAccessor
builder.Services.AddHttpContextAccessor();

// 註冊 AutoMapper
builder.Services.AddAutoMapper(typeof(Program).Assembly);

// 註冊記憶體快取服務
builder.Services.AddMemoryCache();
#endregion

// 註冊參數值處理服務
builder.Services.AddSingleton<IParameterValueService, ParameterValueService>();

builder.Services.AddScoped<AuditInterceptor>();




// 註冊DbContext綁定DefaultConnection
builder.Services.AddDbContext<ERPDbContext>((serviceProvider, options) =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"), sqlOptions =>
    {
        // 設定連線超時時間
        sqlOptions.CommandTimeout(30);
    })
    .AddInterceptors(serviceProvider.GetRequiredService<AuditInterceptor>()));

// 註冊 IConfiguration，讓 DI 可以提供配置
builder.Services.AddSingleton<IConfiguration>(builder.Configuration);

// 共用function.
builder.Services.AddTransient<Baseform>();
builder.Services.AddTransient<EmployeeClass>();
builder.Services.AddTransient<EncryptionHelper>();
builder.Services.AddTransient<IToolManager, ToolManager>();

// 註冊 TokenHandler 服務
builder.Services.AddScoped<TokenHandler>();

// 共用token解析服務
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();

// SignalR.
builder.Services.AddTransient<ISignalRMessageService, SignalRMessageService>();

#region Services
// Common
builder.Services.AddTransient<IUsersService, UsersService>();
builder.Services.AddTransient<IDepartmentService, DepartmentService>();
builder.Services.AddTransient<IEnterpriseGroupsService, EnterpriseGroupsService>();
builder.Services.AddTransient<IRolesService, RolesService>();
builder.Services.AddTransient<IRolesPermissionsService, RolesPermissionsService>();
builder.Services.AddTransient<IAuditLogsService, AuditLogsService>();
builder.Services.AddTransient<ISystemGroupsService, SystemGroupsService>();
builder.Services.AddTransient<ISystemMenuService, SystemMenuService>();

builder.Services.AddTransient<IPermissionValidationService, PermissionValidationService>();
builder.Services.AddTransient<ISystemParametersService, SystemParametersService>();
builder.Services.AddTransient<ISystemParametersItemService, SystemParametersItemService>();

builder.Services.AddTransient<IGranularPermissionService, GranularPermissionService>();
builder.Services.AddScoped<IGranularPermissionNotificationService, GranularPermissionNotificationService>();
builder.Services.AddTransient<IPositionService, PositionService>();
builder.Services.AddTransient<IDivisionService, DivisionService>();
builder.Services.AddTransient<IUnitService, UnitService>();
builder.Services.AddTransient<ICityService, CityService>();
builder.Services.AddTransient<IDistrictService, DistrictService>();
builder.Services.AddTransient<IEnterpriseImageService, EnterpriseImageService>();
builder.Services.AddTransient<IFileListService, FileListService>();
builder.Services.AddTransient<IFileUploadService, FileUploadService>();
builder.Services.AddTransient<IReportSignatureTemplateService, ReportSignatureTemplateService>();

builder.Services.AddTransient<IRsaService, RsaService>();

// PMS
builder.Services.AddTransient<IAssetService, AssetService>();
builder.Services.AddTransient<IInsuranceUnitService, InsuranceUnitService>();
builder.Services.AddTransient<IManufacturerService, ManufacturerService>();
builder.Services.AddTransient<IStorageLocationService, StorageLocationService>();
builder.Services.AddTransient<IDepreciationFormService, DepreciationFormService>();
builder.Services.AddTransient<IDepreciationFormDetailService, DepreciationFormDetailService>();
builder.Services.AddTransient<IAssetAccountService, AssetAccountService>();
builder.Services.AddTransient<IAssetSubAccountService, AssetSubAccountService>();
builder.Services.AddTransient<IAssetSourceService, AssetSourceService>();
builder.Services.AddTransient<IAssetCategoryService, AssetCategoryService>();
builder.Services.AddTransient<IAccessoryEquipmentService, AccessoryEquipmentService>();
builder.Services.AddTransient<IAmortizationSourceService, AmortizationSourceService>();
builder.Services.AddTransient<IAssetStatusService, AssetStatusService>();
builder.Services.AddTransient<IEquipmentTypeService, EquipmentTypeService>();
builder.Services.AddTransient<IPmsUserRoleService, PmsUserRoleService>();
builder.Services.AddTransient<IPmsSystemParameterService, PmsSystemParameterService>();
builder.Services.AddTransient<IAssetCarryOutService, AssetCarryOutService>();
builder.Services.AddTransient<IVendorMaintenanceService, VendorMaintenanceService>();
builder.Services.AddTransient<IAssetLocationTransferService, AssetLocationTransferService>();
builder.Services.AddTransient<IAssetChangeReportService, AssetChangeReportService>();
builder.Services.AddTransient<ICustodianRosterService, CustodianRosterService>();
builder.Services.AddTransient<IAssetScrapFormService, AssetScrapFormService>();
builder.Services.AddTransient<IAssetInventoryService, AssetInventoryService>();

// PAS
builder.Services.AddTransient<IEmployeeService, EmployeeService>();
builder.Services.AddTransient<IEducationService, EducationService>();
builder.Services.AddTransient<ITrainService, TrainService>();
builder.Services.AddTransient<IExaminationService, ExaminationService>();
builder.Services.AddTransient<ICertificationService, CertificationService>();
builder.Services.AddTransient<IUndergoService, UndergoService>();
builder.Services.AddTransient<IEnsureService, EnsureService>();
builder.Services.AddTransient<ISuspendService, SuspendService>();
builder.Services.AddTransient<ISalaryService, SalaryService>();
builder.Services.AddTransient<IHensureService, HensureService>();
builder.Services.AddTransient<IDependentService, DependentService>();
builder.Services.AddTransient<IPerformancePointGroupService, PerformancePointGroupService>();
builder.Services.AddTransient<IPerformancePointTypeService, PerformancePointTypeService>();
builder.Services.AddTransient<IPerformancePointRecordService, PerformancePointRecordService>();
builder.Services.AddTransient<IRegularSalaryItemService, RegularSalaryItemService>();
builder.Services.AddTransient<IEmployeeRegularSalaryService, EmployeeRegularSalaryService>();
builder.Services.AddTransient<ISalaryPointService, SalaryPointService>();
builder.Services.AddTransient<IDepartmentSalaryPointService, DepartmentSalaryPointService>();
builder.Services.AddTransient<IInsuranceGradeService, InsuranceGradeService>();
builder.Services.AddTransient<IInsuranceHistoryService, InsuranceHistoryService>();
builder.Services.AddTransient<IPromotionService, PromotionService>();
builder.Services.AddTransient<IServiceDepartmentChangeService, ServiceDepartmentChangeService>();
builder.Services.AddTransient<IExpenseDepartmentChangeService, ExpenseDepartmentChangeService>();
builder.Services.AddTransient<ITaxRateService, TaxRateService>();
builder.Services.AddTransient<IMonthlySalaryService, MonthlySalaryService>();
builder.Services.AddTransient<IBonusService, BonusService>();
builder.Services.AddTransient<IBackPayService, BackPayService>();

builder.Services.AddTransient<IPasOptionParameterService, PasOptionParameterService>();

// IMS
builder.Services.AddTransient<IPartnerService, PartnerService>();
builder.Services.AddTransient<IPartnerContactService, PartnerContactService>();
builder.Services.AddTransient<IPartnerAddressService, PartnerAddressService>();
builder.Services.AddTransient<IContactService, ContactService>();
builder.Services.AddTransient<IPriceTypeService, PriceTypeService>();
builder.Services.AddTransient<IItemService, ItemService>();
builder.Services.AddTransient<IItemPriceService, ItemPriceService>();
builder.Services.AddTransient<IItemCategoryService, ItemCategoryService>();
builder.Services.AddTransient<ICustomerCategoryService, CustomerCategoryService>();
builder.Services.AddTransient<ISupplierCategoryService, SupplierCategoryService>();
builder.Services.AddTransient<IWarehouseItemPriceService, WarehouseItemPriceService>();
builder.Services.AddTransient<IWarehouseService, WarehouseService>();
builder.Services.AddTransient<TestDataService>(); // 測試資料產生服務

// RMS
builder.Services.AddTransient<IPropertyService, PropertyService>();
builder.Services.AddTransient<ITenantService, TenantService>();
builder.Services.AddTransient<IContractService, ContractService>();
builder.Services.AddTransient<IFeeService, FeeService>();
builder.Services.AddTransient<IPaymentService, PaymentService>();

// SMS
builder.Services.AddTransient<ISmsWebServerService, SmsWebServerService>();
builder.Services.AddTransient<ISmsDbServerService, SmsDbServerService>();
builder.Services.AddTransient<ISmsSiteService, SmsSiteService>();


#endregion

//註冊Swagger註解文件
builder.Services.AddSwaggerGen(options =>
{
    // 設定分組邏輯
    options.DocInclusionPredicate((docName, apiDesc) =>
    {
        var actionDescriptor = apiDesc.ActionDescriptor as Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor;

        if (actionDescriptor == null)
            return false;

        // 取得控制器的命名空間
        var namespaceName = actionDescriptor.ControllerTypeInfo.Namespace;

        // 僅在命名空間完全相等時納入，避免根命名空間誤入
        return namespaceName != null && namespaceName.Equals(docName, StringComparison.OrdinalIgnoreCase);
    });

    // 動態為每個命名空間創建 Swagger 文檔
    var apiNamespaces = AppDomain.CurrentDomain.GetAssemblies()
        .SelectMany(a => a.GetTypes())
        .Where(t => t.IsClass && t.IsSubclassOf(typeof(ControllerBase)) && t.Namespace != null && !t.Namespace.StartsWith("Microsoft.AspNetCore.Mvc"))
        .Select(t => t.Namespace)
        // 僅包含模組命名空間，排除根命名空間
        .Where(ns => ns != null && ns.StartsWith("FAST_ERP_Backend.Controllers.") && !ns.Equals("FAST_ERP_Backend.Controllers", StringComparison.OrdinalIgnoreCase))
        .Distinct()
        .ToList();

    foreach (var ns in apiNamespaces)
    {
        options.SwaggerDoc(ns, new Microsoft.OpenApi.Models.OpenApiInfo
        {
            Title = $"{ns} API",
            Version = "v1"
        });
    }

    // 啟用註解功能
    options.EnableAnnotations();
    //Swagger JWT Token 驗證功能
    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Name = "Authorization",
        Type = SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT",
        In = ParameterLocation.Header,
        Description = "輸入 JWT Token"
    });

    options.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });
});

// 設定 Swagger 讀取 XML 檔案
var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
builder.Services.AddSwaggerGen(options =>
{
    options.IncludeXmlComments(xmlPath);
});

// 其他服務註冊.
builder.Services.AddHttpClient(); // 註冊 HttpClient (呼叫外部用api)
builder.Services.AddSignalR(); // SignalR

builder.Services.AddControllers(options =>
{
    // 添加模組化路由約定
    options.Conventions.Add(new FAST_ERP_Backend.Middlewares.ModuleRouteConvention());
});
builder.Services.AddCors(options =>
{
    //僅開啟https://localhost:3000的要求
    options.AddPolicy("AllowMyOrigin",
       policy =>
       {
           policy.WithOrigins("https://localhost:3000", "http://localhost:3000") // 允許來自這個來源的請求
                 .AllowAnyHeader()
                 .AllowAnyMethod()
                 .AllowCredentials()
                 .WithExposedHeaders("ETag", "X-Permissions-Version");
       });
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// Configure the HTTP request pipeline.
// 1. 全域例外處理 (最先執行)
app.UseGlobalExceptionHandler();

// 開發環境專用設定
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        //啟用 Swagger 中介軟體
        var apiNamespaces = AppDomain.CurrentDomain.GetAssemblies()
            .SelectMany(a => a.GetTypes())
            .Where(t => t.IsClass && t.IsSubclassOf(typeof(ControllerBase)) && t.Namespace != null && !t.Namespace.StartsWith("Microsoft.AspNetCore.Mvc"))
            .Select(t => t.Namespace)
            .Where(ns => ns != null && ns.StartsWith("FAST_ERP_Backend.Controllers.") && !ns.Equals("FAST_ERP_Backend.Controllers", StringComparison.OrdinalIgnoreCase))
            .Distinct()
            .OrderBy(ns => ns)  // 這裡改為按字母順序升序排列
            .ToList();

        foreach (var ns in apiNamespaces)
        {
            c.SwaggerEndpoint($"/swagger/{ns}/swagger.json", $"{ns} API v1");
        }
    });
}
else
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

// 2. HTTPS 重定向 (在靜態檔案之前)
app.UseHttpsRedirection();

// 3. 靜態檔案處理
// 確保 wwwroot 和 uploads 目錄存在
var wwwrootPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
if (!Directory.Exists(wwwrootPath))
{
    Directory.CreateDirectory(wwwrootPath);
}

var uploadsPath = Path.Combine(wwwrootPath, "uploads");
if (!Directory.Exists(uploadsPath))
{
    Directory.CreateDirectory(uploadsPath);
}

// 圖片存取權限
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(uploadsPath),
    RequestPath = "/uploads"
});

// 4. 路由
app.UseRouting();

// 5. CORS (在路由之後，驗證之前)
app.UseCors("AllowMyOrigin");

// 資料庫初始化 (應用程式啟動時立即執行)
/*using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<ERPDbContext>();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
    
    try
    {
        // 自動執行 Migration（會自動創建資料庫和表格）
        await context.Database.MigrateAsync();
        logger.LogInformation("資料庫 Migration 完成");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "資料庫初始化失敗");
        throw;
    }
}*/

// 6. 先解析 JWT/設定 HttpContext.User（自研中介層）
app.UsePasswordValidation();

// 7. 驗證和授權 (注意：需要先加入 Authentication 服務)
// app.UseAuthentication(); // 如果有 JWT 驗證，請取消註解
app.UseAuthorization();

// 8. 自定義中介軟體（群組/選單權限，可視需要開啟）
//app.UseGroupPermissions();
//app.UseMenuPermissions();

// 8. 端點映射
app.MapHub<SignalRHub>("/hubs/SignalRHub"); // SignalRHub導向位置
app.MapControllers();

app.Run();