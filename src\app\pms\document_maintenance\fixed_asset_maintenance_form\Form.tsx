import React, { useEffect, useState, useRef, useCallback } from "react";
import {
  Card,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Space,
  Divider,
  InputNumber,
  Tabs,
  Typography,
  Modal,
  Switch,
  Tooltip,
  Table,
  Descriptions,
  Tag,
  message,
} from "antd";
import {
  PlusOutlined,
  SaveOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
} from "@ant-design/icons";
import type { TabsProps } from "antd";
import { useAuth } from "@/contexts/AuthContext";
import { notifySuccess, notifyError } from "@/utils/notification";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import {
  formatTWCurrency,
  formatThousands,
  parseAmount,
} from "@/utils/formatUtils";
import dayjs from "dayjs";
import guidUtils from "@/utils/guidUtils";
import {
  AccessoryEquipment,
  InsuranceUnit,
  formInitialValues,
} from "./interface";
import {
  STATUS_COLORS,
  INSURANCE_STATUS_COLORS,
} from "@/constants/pms/statusColors";
import {
  AssetDetail,
  Asset,
  AssetSource as ServiceAssetSource,
  getAssets,
  addAsset,
  editAsset,
  deleteAsset,
  getAssetById,
  getNewAssetNo,
} from "@/services/pms/assetService";
import { AssetCategory } from "@/services/pms/assetCategoryService";
import { Department } from "@/services/common/departmentService";
import { Division } from "@/services/common/divisionService";
import { AssetAccount } from "@/services/pms/assetAccountService";
import { AmortizationSource } from "@/services/pms/amortizationSourceService";
import { AssetSubAccount } from "@/services/pms/assetSubAccountService";
import { Unit, createUnit } from "@/services/common/unitService";
import { AssetStatus } from "@/services/pms/assetStatusService";
import {
  EquipmentType,
  createEquipmentType,
} from "@/services/pms/equipmentTypeService";
import {
  Manufacturer,
  addManufacturer,
} from "@/services/pms/manufacturerService";
import { User } from "@/services/common/userService";
import {
  StorageLocation,
  createStorageLocation,
} from "@/services/pms/storageLocationService";
import { title } from "process";

const { TabPane } = Tabs;
const { Title } = Typography;
const { TextArea } = Input;

interface FixedAssetMaintenanceFormProps {
  editingAsset: AssetDetail | null;
  isViewMode: boolean;
  onCancel: () => void;
  onSuccess: (assetDetailData: AssetDetail) => void;
  assetStatusOptions: AssetStatus[];
  assetCategories: AssetCategory[];
  departments: Department[];
  divisions: Division[];
  assetAccounts: AssetAccount[];
  assetSources: ServiceAssetSource[];
  amortizationSources: AmortizationSource[];
  assetSubAccounts: AssetSubAccount[];
  units: Unit[];
  equipmentTypes: EquipmentType[];
  manufacturers: Manufacturer[];
  custodians: User[];
  assetUsers: User[];
  storageLocations: StorageLocation[];
  isMobile?: boolean;
}

// 新增表單按鈕組件
const FormButtons: React.FC<{
  onCancel: () => void;
  isViewMode: boolean;
  isMobile?: boolean;
  form: any;
}> = ({ onCancel, isViewMode, isMobile, form }) => {
  return (
    <div
      style={{
        position: "sticky",
        bottom: 0,
        left: 0,
        width: "100%",
        background: "#fff",
        zIndex: 10,
        padding: isMobile ? "12px 0 0 0" : "20px 0 0 0",
        textAlign: "right",
        boxShadow: "0 -2px 8px #f0f1f2",
        marginTop: "20px",
      }}
    >
      <Space>
        <Button onClick={onCancel}>取消</Button>
        <Button type="primary" onClick={form.submit} disabled={isViewMode}>
          {form.getFieldValue("assetId") ? "儲存" : "新增"}
        </Button>
      </Space>
    </div>
  );
};

// 檢視模式的CSS樣式
const ViewModeStyle = {
  standard: {
    padding: "8px 0",
    minHeight: "32px",
    lineHeight: "24px",
    borderBottom: "1px dashed #f0f0f0",
    fontSize: "14px",
  },
  title: {
    padding: "8px 0",
    minHeight: "32px",
    lineHeight: "32px",
    fontWeight: "bold",
    fontSize: "20px",
  },
  label: {
    color: "#888",
    fontSize: "15px",
    marginBottom: "4px",
    fontWeight: "bold",
    display: "block",
  },
  content: {
    padding: "4px 0",
    minHeight: "32px",
    lineHeight: "32px",
    fontWeight: "bold",
    fontSize: "16px",
  },
};

const FixedAssetMaintenanceForm: React.FC<FixedAssetMaintenanceFormProps> = ({
  editingAsset,
  isViewMode,
  onCancel,
  onSuccess,
  assetStatusOptions,
  assetCategories,
  departments,
  divisions,
  assetAccounts,
  assetSources,
  amortizationSources,
  assetSubAccounts,
  units,
  equipmentTypes,
  manufacturers,
  custodians,
  assetUsers,
  storageLocations,
  isMobile,
}) => {
  // 表單相關狀態
  const [form] = Form.useForm();
  const [accessoryForm] = Form.useForm();
  const [insuranceForm] = Form.useForm();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("1");

  // 附屬設備與保險單位狀態
  const [accessoryEquipments, setAccessoryEquipments] = useState<
    AccessoryEquipment[]
  >([]);
  const [insuranceUnits, setInsuranceUnits] = useState<InsuranceUnit[]>([]);

  // 選擇狀態
  const [selectedAssetSources, setSelectedAssetSources] = useState<string[]>(
    []
  );
  const [selectedAmortizationSources, setSelectedAmortizationSources] =
    useState<string[]>([]);
  const [selectedDepartmentId, setSelectedDepartmentId] = useState<string>("");
  const [selectedAssetAccountId, setSelectedAssetAccountId] =
    useState<string>("");
  const [selectedAssetStatus, setSelectedAssetStatus] = useState<string>("");

  // 過濾狀態
  const [filteredDivisions, setFilteredDivisions] = useState<Division[]>([]);
  const [filteredAssetSubAccounts, setFilteredAssetSubAccounts] = useState<
    AssetSubAccount[]
  >([]);

  // 附屬設備與保險單位模態框狀態
  const [isAccessoryModalVisible, setIsAccessoryModalVisible] = useState(false);
  const [isInsuranceModalVisible, setIsInsuranceModalVisible] = useState(false);
  const [editingAccessory, setEditingAccessory] =
    useState<AccessoryEquipment | null>(null);
  const [editingInsurance, setEditingInsurance] =
    useState<InsuranceUnit | null>(null);

  // 確認提交狀態
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false);
  const [confirmData, setConfirmData] = useState<AssetDetail | null>(null);

  // 刪除附屬設備和保險單位相關狀態
  const [deleteAccessoryModalVisible, setDeleteAccessoryModalVisible] =
    useState(false);
  const [deleteInsuranceModalVisible, setDeleteInsuranceModalVisible] =
    useState(false);
  const [accessoryToDelete, setAccessoryToDelete] =
    useState<AccessoryEquipment | null>(null);
  const [insuranceToDelete, setInsuranceToDelete] =
    useState<InsuranceUnit | null>(null);
  const [deleteAccessoryConfirmText, setDeleteAccessoryConfirmText] =
    useState("");
  const [deleteInsuranceConfirmText, setDeleteInsuranceConfirmText] =
    useState("");

  // 財務相關狀態
  const [deductSubsidy, setDeductSubsidy] = useState<boolean>(true);

  // 財產來源和攤提來源相關狀態
  const [localAssetSources, setLocalAssetSources] =
    useState<ServiceAssetSource[]>(assetSources);
  const [localAmortizationSources, setLocalAmortizationSources] =
    useState<AmortizationSource[]>(amortizationSources);
  const [isAddingAssetSource, setIsAddingAssetSource] = useState(false);
  const [isAddingAmortizationSource, setIsAddingAmortizationSource] =
    useState(false);
  const [newAssetSourceName, setNewAssetSourceName] = useState("");
  const [newAmortizationSourceName, setNewAmortizationSourceName] =
    useState("");
  const [
    newAmortizationSourceDescription,
    setNewAmortizationSourceDescription,
  ] = useState("");
  const [newAmortizationSourceAmount, setNewAmortizationSourceAmount] =
    useState<number>(0);
  const assetSourceInputRef = useRef<any>(null);
  const amortizationSourceInputRef = useRef<any>(null);

  // 廠牌型號新增功能相關狀態
  const [localManufacturers, setLocalManufacturers] =
    useState<Manufacturer[]>(manufacturers);
  const [isAddingManufacturer, setIsAddingManufacturer] = useState(false);
  const [newManufacturerName, setNewManufacturerName] = useState("");
  const manufacturerInputRef = useRef<any>(null);

  // 設備類型新增功能相關狀態
  const [localEquipmentTypes, setLocalEquipmentTypes] =
    useState<EquipmentType[]>(equipmentTypes);
  const [isAddingEquipmentType, setIsAddingEquipmentType] = useState(false);
  const [newEquipmentTypeName, setNewEquipmentTypeName] = useState("");
  const equipmentTypeInputRef = useRef<any>(null);

  // 單位新增功能相關狀態
  const [localUnits, setLocalUnits] = useState<Unit[]>(units);
  const [isAddingUnit, setIsAddingUnit] = useState(false);
  const [newUnitName, setNewUnitName] = useState("");
  const unitInputRef = useRef<any>(null);

  // 存放地點新增功能相關狀態
  const [localStorageLocations, setLocalStorageLocations] =
    useState<StorageLocation[]>(storageLocations);
  const [isAddingStorageLocation, setIsAddingStorageLocation] = useState(false);
  const [newStorageLocationName, setNewStorageLocationName] = useState("");
  const storageLocationInputRef = useRef<any>(null);

  // 謄寫功能相關狀態
  const [isCopyModalVisible, setIsCopyModalVisible] = useState(false);
  const [assetList, setAssetList] = useState<AssetDetail[]>([]);
  const [assetListLoading, setAssetListLoading] = useState(false);
  const [selectedAssetForCopy, setSelectedAssetForCopy] = useState<
    string | null
  >(null);

  // 當外部資料來源變更時更新本地狀態
  useEffect(() => {
    setLocalAssetSources(assetSources);
  }, [assetSources]);

  useEffect(() => {
    setLocalAmortizationSources(amortizationSources);
  }, [amortizationSources]);

  useEffect(() => {
    setLocalManufacturers(manufacturers);
  }, [manufacturers]);

  useEffect(() => {
    setLocalEquipmentTypes(equipmentTypes);
  }, [equipmentTypes]);

  useEffect(() => {
    setLocalUnits(units);
  }, [units]);

  useEffect(() => {
    setLocalStorageLocations(storageLocations);
  }, [storageLocations]);

  // 當編輯資產變更時，更新表單數據
  useEffect(() => {
    if (editingAsset) {
      // 設置表單初始值
      const formData: any = {
        ...editingAsset.asset,
        acquisitionDate: editingAsset.asset.acquisitionDate
          ? dayjs.unix(editingAsset.asset.acquisitionDate)
          : null,
        statusChangeDate: editingAsset.asset.statusChangeDate
          ? dayjs.unix(editingAsset.asset.statusChangeDate)
          : null,
      };

      // 處理報廢日期 (scrapDate而非discardDate)
      if (editingAsset.asset.scrapDate) {
        formData.discardDate = dayjs.unix(editingAsset.asset.scrapDate);
      }

      // 將後端報廢欄位帶入表單欄位
      if (editingAsset.asset.scrapReason) {
        (formData as any).discardReason = editingAsset.asset.scrapReason;
      }

      // 處理建築物相關日期
      if (editingAsset.asset.constructionDate) {
        formData.constructionDate = dayjs.unix(
          editingAsset.asset.constructionDate
        );
      }

      if (editingAsset.asset.usageExpiryDate) {
        formData.usageExpiryDate = dayjs.unix(
          editingAsset.asset.usageExpiryDate
        );
      }

      // 處理預計報廢年度
      if (editingAsset.asset.estimatedScrapYear) {
        formData.estimatedScrapYear = dayjs().year(
          editingAsset.asset.estimatedScrapYear
        );
      }

      // 設置當前選擇的財產科目ID
      const assetAccountId = editingAsset.asset.assetAccountId || "";
      setSelectedAssetAccountId(assetAccountId);

      // 根據選擇的財產科目過濾財產子目
      if (assetAccountId) {
        const filtered = assetSubAccounts.filter(
          (subAccount) => subAccount.assetAccountId === assetAccountId
        );
        setFilteredAssetSubAccounts(filtered);
      }

      // 設置當前選擇的部門ID和財產狀態
      setSelectedDepartmentId(editingAsset.asset.departmentId || "");
      setSelectedAssetStatus(editingAsset.asset.assetStatusId || "");

      // 根據選擇的部門過濾組別
      if (editingAsset.asset.departmentId) {
        const filtered = divisions.filter(
          (division) =>
            division.departmentId === editingAsset.asset.departmentId
        );
        setFilteredDivisions(filtered);
      }

      // 設置附屬設備和保險單位 - 使用類型斷言處理類型不匹配問題
      if (
        editingAsset.accessoryEquipments &&
        editingAsset.accessoryEquipments.length > 0
      ) {
        setAccessoryEquipments(editingAsset.accessoryEquipments as any[]);
      } else {
        setAccessoryEquipments([]);
      }

      if (
        editingAsset.insuranceUnits &&
        editingAsset.insuranceUnits.length > 0
      ) {
        setInsuranceUnits(editingAsset.insuranceUnits as any[]);
      } else {
        setInsuranceUnits([]);
      }

      // 設置已選擇的財產來源和攤提來源
      if (editingAsset.assetSources && editingAsset.assetSources.length > 0) {
        const assetSourceIds = editingAsset.assetSources.map(
          (source) => source.assetSourceId
        );
        setSelectedAssetSources(assetSourceIds);
        formData.assetSourceIds = assetSourceIds;
      }

      if (
        editingAsset.amortizationSources &&
        editingAsset.amortizationSources.length > 0
      ) {
        const amortizationSourceIds = editingAsset.amortizationSources.map(
          (source) => source.amortizationSourceId
        );
        setSelectedAmortizationSources(amortizationSourceIds);
        formData.amortizationSourceIds = amortizationSourceIds;
      }

      form.setFieldsValue(formData);
    } else {
      form.resetFields();
      form.setFieldsValue({
        ...formInitialValues,
        assetStatusId: assetStatusOptions[0]?.assetStatusId || undefined,
        purchaseAmount: 0,
        subsidyAmount: 0,
        estimatedResidualValue: 0,
        depreciationAmount: 0,
        accumulatedDepreciationAmount: 0,
      });
      setAccessoryEquipments([]);
      setInsuranceUnits([]);
      setSelectedAssetSources([]);
      setSelectedAmortizationSources([]);
      setSelectedDepartmentId("");
      setSelectedAssetAccountId("");
      setSelectedAssetStatus("");
      setFilteredDivisions([]);
      setFilteredAssetSubAccounts([]);
    }
  }, [editingAsset, assetSubAccounts, divisions, form, assetStatusOptions]);

  // 處理部門選擇變更
  const handleDepartmentChange = useCallback(
    (value: string) => {
      setSelectedDepartmentId(value);
      // 清空組別選擇
      form.setFieldsValue({ divisionId: undefined });

      // 根據選擇的部門過濾組別
      if (value) {
        const filtered = divisions.filter(
          (division) => division.departmentId === value
        );
        setFilteredDivisions(filtered);
      } else {
        setFilteredDivisions([]);
      }
    },
    [form, divisions]
  );

  // 處理財產科目選擇變更
  const handleAssetAccountChange = useCallback(
    (value: string) => {
      setSelectedAssetAccountId(value);
      // 清空財產子目的選擇
      form.setFieldsValue({ assetSubAccountId: undefined });

      // 根據選擇的財產科目過濾財產子目
      if (value) {
        const filtered = assetSubAccounts.filter(
          (subAccount) => subAccount.assetAccountId === value
        );
        setFilteredAssetSubAccounts(filtered);
      } else {
        setFilteredAssetSubAccounts([]);
      }

      // 檢查是否可以取得新財產編號
      checkAndGetNewAssetNo();
    },
    [assetSubAccounts, form]
  );

  // 處理財產子目選擇變更
  const handleAssetSubAccountChange = useCallback((value: string) => {
    // 檢查是否可以取得新財產編號
    checkAndGetNewAssetNo();
  }, []);

  // 處理財產類別選擇變更
  const handleAssetCategoryChange = useCallback((value: string) => {
    // 檢查是否可以取得新財產編號
    checkAndGetNewAssetNo();
  }, []);

  // 檢查並取得新財產編號
  const checkAndGetNewAssetNo = async () => {
    // 如果是編輯模式或檢視模式，不需要取得新財產編號
    if (editingAsset || isViewMode) return;

    const assetAccountId = form.getFieldValue("assetAccountId");
    const assetSubAccountId = form.getFieldValue("assetSubAccountId");
    const assetCategoryId = form.getFieldValue("assetCategoryId");

    // 如果財產科目、財產子目、財產類別都有值，則取得新財產編號
    if (assetAccountId && assetSubAccountId && assetCategoryId) {
      try {
        // 找到對應的科目資料
        const assetAccount = assetAccounts.find(
          (account) => account.assetAccountId === assetAccountId
        );
        // 找到對應的子目資料
        const assetSubAccount = assetSubAccounts.find(
          (subAccount) => subAccount.assetSubAccountId === assetSubAccountId
        );
        // 找到對應的類別資料
        const assetCategory = assetCategories.find(
          (category) => category.assetCategoryId === assetCategoryId
        );

        if (assetAccount && assetSubAccount && assetCategory) {
          const response = await getNewAssetNo(
            assetAccount.assetAccountNo,
            assetSubAccount.assetSubAccountNo,
            assetCategory.assetCategoryId
          );

          if (response.success && response.data && response.data.assetNo) {
            form.setFieldsValue({ assetNo: response.data.assetNo });
          }
        }
      } catch (error) {
        console.error("取得新財產編號失敗:", error);
      }
    }
  };

  // 處理財產狀態變更
  const handleAssetStatusChange = useCallback((value: string) => {
    setSelectedAssetStatus(value);
  }, []);

  // 處理財產來源變更
  const handleAssetSourceChange = useCallback((values: string[]) => {
    setSelectedAssetSources(values);
  }, []);

  // 處理攤提來源變更
  const handleAmortizationSourceChange = useCallback((values: string[]) => {
    setSelectedAmortizationSources(values);
  }, []);

  // 判斷是否為土地或房屋建築
  const isLandOrBuilding = () => {
    const selectedAccount = assetAccounts.find(
      (account) => account.assetAccountId === selectedAssetAccountId
    );
    if (!selectedAccount) return false;

    const accountName = selectedAccount.assetAccountName;
    return (
      accountName.includes("土地") ||
      accountName.includes("房屋") ||
      accountName.includes("建築物")
    );
  };

  // 判斷是否已報廢
  const isDiscarded = () => {
    const scrapStatus = assetStatusOptions.find(
      (status) => status.name === "已報廢"
    );
    const scrapUsableStatus = assetStatusOptions.find(
      (status) => status.name === "報廢後堪用"
    );
    return (
      selectedAssetStatus === scrapStatus?.assetStatusId ||
      selectedAssetStatus === scrapUsableStatus?.assetStatusId
    );
  };

  // 附屬設備相關處理函數
  const handleAddAccessory = () => {
    setEditingAccessory(null);
    accessoryForm.resetFields();

    // 設置預設使用狀態
    if (assetStatusOptions.length > 0) {
      accessoryForm.setFieldsValue({
        usageStatus: assetStatusOptions[0].assetStatusId,
      });
    }

    setIsAccessoryModalVisible(true);
  };

  const handleEditAccessory = (record: AccessoryEquipment) => {
    setEditingAccessory(record);
    const formData: any = { ...record };
    if (formData.purchaseDate) {
      formData.purchaseDate = dayjs.unix(formData.purchaseDate);
    }
    accessoryForm.setFieldsValue(formData);
    setIsAccessoryModalVisible(true);
  };

  const handleDeleteAccessory = (id: string) => {
    const accessory = accessoryEquipments.find(
      (item) => item.accessoryEquipmentId === id
    );
    if (accessory) {
      setAccessoryToDelete(accessory);
      setDeleteAccessoryConfirmText("");
      setDeleteAccessoryModalVisible(true);
    }
  };

  const executeDeleteAccessory = () => {
    if (!accessoryToDelete) return;

    setAccessoryEquipments(
      accessoryEquipments.filter(
        (item) =>
          item.accessoryEquipmentId !== accessoryToDelete.accessoryEquipmentId
      )
    );
    setDeleteAccessoryModalVisible(false);
    setAccessoryToDelete(null);
    setDeleteAccessoryConfirmText("");
  };

  const handleAccessorySubmit = (values: any) => {
    const submitData = { ...values };

    // 處理日期
    if (submitData.purchaseDate && submitData.purchaseDate.unix) {
      submitData.purchaseDate = submitData.purchaseDate.unix();
    }

    if (editingAccessory) {
      // 更新現有設備
      const updatedAccessories = accessoryEquipments.map((item) =>
        item.accessoryEquipmentId === editingAccessory.accessoryEquipmentId
          ? { ...item, ...submitData }
          : item
      );
      setAccessoryEquipments(updatedAccessories);
    } else {
      // 新增設備 - 使用Guid作為臨時ID
      const newAccessory = {
        ...submitData,
        accessoryEquipmentId: guidUtils(),
        createTime: Date.now(),
        createUserId: user?.userId || "",
        updateTime: 0,
        updateUserId: "",
        deleteTime: 0,
        deleteUserId: "",
      } as AccessoryEquipment;
      setAccessoryEquipments([...accessoryEquipments, newAccessory]);
    }

    // 關閉模態框並重置表單
    setIsAccessoryModalVisible(false);
    accessoryForm.resetFields();
    setEditingAccessory(null);
  };

  // 保險單位相關處理函數
  const handleAddInsurance = () => {
    setEditingInsurance(null);
    insuranceForm.resetFields();
    setIsInsuranceModalVisible(true);
  };

  const handleEditInsurance = (record: InsuranceUnit) => {
    setEditingInsurance(record);
    const formData = {
      ...record,
      insuranceStartDate: record.insuranceStartDate
        ? dayjs.unix(record.insuranceStartDate)
        : null,
      insuranceExpiryDate: record.insuranceExpiryDate
        ? dayjs.unix(record.insuranceExpiryDate)
        : null,
    };
    insuranceForm.setFieldsValue(formData);
    setIsInsuranceModalVisible(true);
  };

  const handleDeleteInsurance = (id: string) => {
    const insurance = insuranceUnits.find(
      (item) => item.insuranceUnitId === id
    );
    if (insurance) {
      setInsuranceToDelete(insurance);
      setDeleteInsuranceConfirmText("");
      setDeleteInsuranceModalVisible(true);
    }
  };

  const executeDeleteInsurance = () => {
    if (!insuranceToDelete) return;

    setInsuranceUnits(
      insuranceUnits.filter(
        (item) => item.insuranceUnitId !== insuranceToDelete.insuranceUnitId
      )
    );
    setDeleteInsuranceModalVisible(false);
    setInsuranceToDelete(null);
    setDeleteInsuranceConfirmText("");
  };

  const handleInsuranceSubmit = (values: any) => {
    const submitData = { ...values };

    // 處理日期
    if (submitData.insuranceStartDate && submitData.insuranceStartDate.unix) {
      submitData.insuranceStartDate = submitData.insuranceStartDate.unix();
    }

    if (submitData.insuranceExpiryDate && submitData.insuranceExpiryDate.unix) {
      submitData.insuranceExpiryDate = submitData.insuranceExpiryDate.unix();
    }

    if (editingInsurance) {
      // 更新現有保險單位
      const updatedInsurances = insuranceUnits.map((item) =>
        item.insuranceUnitId === editingInsurance.insuranceUnitId
          ? { ...item, ...submitData }
          : item
      );
      setInsuranceUnits(updatedInsurances);
    } else {
      // 新增保險單位 - 使用Guid作為臨時ID
      const newInsurance = {
        ...submitData,
        insuranceUnitId: guidUtils(),
        createTime: Date.now(),
        createUserId: user?.userId || "",
        updateTime: 0,
        updateUserId: "",
        deleteTime: 0,
        deleteUserId: "",
        sortCode: 0,
      } as InsuranceUnit;
      setInsuranceUnits([...insuranceUnits, newInsurance]);
    }

    // 關閉模態框並重置表單
    setIsInsuranceModalVisible(false);
    insuranceForm.resetFields();
    setEditingInsurance(null);
  };

  // 財務相關處理函數
  const handlePurchaseAmountChange = (value: number | null) => {
    // 如果購入金額為空或0，則清空折舊金額
    if (!value) {
      form.setFieldsValue({ depreciationAmount: null });
      return;
    }

    // 取得補助金額
    const subsidyAmount = deductSubsidy
      ? form.getFieldValue("subsidyAmount") || 0
      : 0;

    // 計算折舊金額（購入金額 - 補助金額，如果deductSubsidy為true）
    const depreciationAmount = value - subsidyAmount;

    // 更新折舊金額欄位
    form.setFieldsValue({
      depreciationAmount: depreciationAmount > 0 ? depreciationAmount : 0,
    });
  };

  const handleSubsidyAmountChange = (value: number | null) => {
    // 如果不需要扣除補助金額，則不進行計算
    if (!deductSubsidy) {
      return;
    }

    // 取得購入金額
    const purchaseAmount = form.getFieldValue("purchaseAmount") || 0;

    // 如果購入金額為0，不進行計算
    if (!purchaseAmount) {
      return;
    }

    // 計算折舊金額（購入金額 - 補助金額）
    const subsidyAmount = value || 0;
    const depreciationAmount = purchaseAmount - subsidyAmount;

    // 更新折舊金額欄位
    form.setFieldsValue({
      depreciationAmount: depreciationAmount > 0 ? depreciationAmount : 0,
    });
  };

  const handleDeductSubsidyChange = (checked: boolean) => {
    setDeductSubsidy(checked);

    // 重新計算折舊金額
    const purchaseAmount = form.getFieldValue("purchaseAmount");
    const subsidyAmount = form.getFieldValue("subsidyAmount");

    if (purchaseAmount) {
      const depreciationAmount =
        checked && subsidyAmount
          ? purchaseAmount - subsidyAmount
          : purchaseAmount;

      form.setFieldsValue({
        depreciationAmount: depreciationAmount > 0 ? depreciationAmount : 0,
      });
    }
  };

  // 處理新增財產來源
  const addItem = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    setIsAddingAssetSource(true);
    setTimeout(() => {
      assetSourceInputRef.current?.focus();
    }, 0);
  };

  // 處理新增攤提來源
  const addAmortizationItem = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    setIsAddingAmortizationSource(true);
    setTimeout(() => {
      amortizationSourceInputRef.current?.focus();
    }, 0);
  };

  // 處理新增財產來源
  const handleAddAssetSource = async () => {
    if (!newAssetSourceName) return;

    try {
      // 臨時 ID - 使用Guid
      const tempId = guidUtils();

      // 財產來源
      const newSource = {
        assetSourceId: tempId,
        assetSourceName: newAssetSourceName,
        sortCode: 0,
        createTime: Date.now(),
        createUserId: user?.userId || "",
        assetAssetSources: [],
      } as ServiceAssetSource;

      // 更新財產來源列表
      setLocalAssetSources([...localAssetSources, newSource]);

      // 選擇該項目
      setSelectedAssetSources([...selectedAssetSources, tempId]);
      form.setFieldsValue({
        assetSourceIds: [...selectedAssetSources, tempId],
      });

      notifySuccess("新增財產來源成功", newAssetSourceName);
    } catch (error) {
      console.error("新增財產來源失敗:", error);
      notifyError("新增財產來源失敗", "請稍後再試");
    } finally {
      // 清空輸入框並關閉新增介面
      setNewAssetSourceName("");
      setIsAddingAssetSource(false);
    }
  };

  // 處理新增攤提來源
  const handleAddAmortizationSource = async () => {
    if (!newAmortizationSourceName) return;

    try {
      // 臨時 ID - 使用Guid
      const tempId = guidUtils();

      // 攤提來源
      const newSource = {
        amortizationSourceId: tempId,
        sourceName: newAmortizationSourceName,
        description: newAmortizationSourceDescription,
        amount: newAmortizationSourceAmount,
        departmentId: selectedDepartmentId || "",
        assetId: editingAsset?.asset?.assetId || "",
        createTime: Date.now(),
        createUserId: user?.userId || "",
        createUserName: user?.name || "",
        updateTime: 0,
        updateUserId: "",
        updateUserName: "",
        deleteTime: 0,
        deleteUserId: "",
        deleteUserName: "",
        assetAmortizationSources: [],
      } as unknown as AmortizationSource;

      // 更新攤提來源列表
      setLocalAmortizationSources([...localAmortizationSources, newSource]);

      // 選擇該項目
      setSelectedAmortizationSources([...selectedAmortizationSources, tempId]);
      form.setFieldsValue({
        amortizationSourceIds: [...selectedAmortizationSources, tempId],
      });

      notifySuccess("新增攤提來源成功", newAmortizationSourceName);
    } catch (error) {
      console.error("新增攤提來源失敗:", error);
      notifyError("新增攤提來源失敗", "請稍後再試");
    } finally {
      // 清空輸入框並關閉新增介面
      setNewAmortizationSourceName("");
      setNewAmortizationSourceDescription("");
      setNewAmortizationSourceAmount(0);
      setIsAddingAmortizationSource(false);
    }
  };

  // 處理新增廠牌型號
  const addManufacturerItem = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    setIsAddingManufacturer(true);
    setTimeout(() => {
      manufacturerInputRef.current?.focus();
    }, 0);
  };

  const handleAddManufacturer = async () => {
    if (!newManufacturerName) return;

    try {
      // 準備API數據
      const manufacturerData = {
        manufacturerId: guidUtils(),
        name: newManufacturerName,
        model: "",
        manufacturerName: newManufacturerName,
        supplier: "",
        contactPerson: "",
        contactPhone: "",
        contactEmail: "",
        description: "",
        sortCode: 0,
        createTime: 0,
        createUserId: user?.userId || "",
        updateTime: 0,
        updateUserId: "",
        deleteTime: 0,
        deleteUserId: "",
        isDeleted: false,
      };

      // 調用API新增廠牌型號
      const response = await addManufacturer(manufacturerData);

      if (response.success) {
        // 更新本地廠牌型號列表
        setLocalManufacturers([...localManufacturers, manufacturerData]);

        // 選擇該項目
        form.setFieldsValue({
          manufacturerId: manufacturerData.manufacturerId,
        });

        notifySuccess("新增廠牌型號成功", newManufacturerName);
      } else {
        notifyError("新增廠牌型號失敗", response.message || "請稍後再試");
      }
    } catch (error) {
      console.error("新增廠牌型號失敗:", error);
      notifyError("新增廠牌型號失敗", "請稍後再試");
    } finally {
      // 清空輸入框並關閉新增介面
      setNewManufacturerName("");
      setIsAddingManufacturer(false);
    }
  };

  // 處理新增設備類型
  const addEquipmentTypeItem = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    setIsAddingEquipmentType(true);
    setTimeout(() => {
      equipmentTypeInputRef.current?.focus();
    }, 0);
  };

  const handleAddEquipmentType = async () => {
    if (!newEquipmentTypeName) return;

    try {
      // 新增設備類型
      const equipmentTypeData = {
        equipmentTypeId: guidUtils(),
        equipmentTypeNo: "",
        name: newEquipmentTypeName,
        sortCode: 0,
        createTime: 0,
        createUserId: user?.userId || "",
        updateTime: 0,
        updateUserId: "",
        deleteTime: 0,
        deleteUserId: "",
      };

      // 調用API新增設備類型
      const response = await createEquipmentType(equipmentTypeData);

      if (response.success) {
        // 更新本地設備類型列表
        setLocalEquipmentTypes([...localEquipmentTypes, equipmentTypeData]);

        // 選擇該項目
        form.setFieldsValue({
          equipmentTypeId: equipmentTypeData.equipmentTypeId,
        });

        notifySuccess("新增設備類型成功", newEquipmentTypeName);
      } else {
        notifyError("新增設備類型失敗", response.message || "請稍後再試");
      }
    } catch (error) {
      console.error("新增設備類型失敗:", error);
      notifyError("新增設備類型失敗", "請稍後再試");
    } finally {
      // 清空輸入框並關閉新增介面
      setNewEquipmentTypeName("");
      setIsAddingEquipmentType(false);
    }
  };

  // 處理新增單位
  const addUnitItem = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    setIsAddingUnit(true);
    setTimeout(() => {
      unitInputRef.current?.focus();
    }, 0);
  };

  const handleAddUnit = async () => {
    if (!newUnitName) return;

    try {
      // 新增單位
      const unitData = {
        unitId: guidUtils(),
        unitNo: "",
        name: newUnitName,
        sortCode: 0,
        createTime: 0,
        createUserId: user?.userId || "",
        updateTime: 0,
        updateUserId: "",
        deleteTime: 0,
        deleteUserId: "",
      };

      // 新增單位
      const response = await createUnit(unitData);

      if (response.success) {
        // 更新本地單位列表
        setLocalUnits([...localUnits, unitData]);

        // 選擇該項目
        form.setFieldsValue({
          unitId: unitData.unitId,
        });

        notifySuccess("新增單位成功", newUnitName);
      } else {
        notifyError("新增單位失敗", response.message || "請稍後再試");
      }
    } catch (error) {
      console.error("新增單位失敗:", error);
      notifyError("新增單位失敗", "請稍後再試");
    } finally {
      // 清空輸入框並關閉新增介面
      setNewUnitName("");
      setIsAddingUnit(false);
    }
  };

  // 處理新增存放地點
  const addStorageLocationItem = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    setIsAddingStorageLocation(true);
    setTimeout(() => {
      storageLocationInputRef.current?.focus();
    }, 0);
  };

  const handleAddStorageLocation = async () => {
    if (!newStorageLocationName) return;

    try {
      // 新增存放地點
      const storageLocationData = {
        storageLocationId: guidUtils(),
        name: newStorageLocationName,
        address: "",
        description: "",
        sortCode: 0,
        createTime: 0,
        createUserId: user?.userId || "",
        updateTime: null,
        updateUserId: null,
        deleteTime: null,
        deleteUserId: null,
      };

      // 調用API新增存放地點
      const response = await createStorageLocation(storageLocationData);

      if (response.success) {
        // 更新本地存放地點列表
        setLocalStorageLocations([
          ...localStorageLocations,
          storageLocationData,
        ]);

        // 選擇該項目
        form.setFieldsValue({
          storageLocationId: storageLocationData.storageLocationId,
        });

        notifySuccess("新增存放地點成功", newStorageLocationName);
      } else {
        notifyError("新增存放地點失敗", response.message || "請稍後再試");
      }
    } catch (error) {
      console.error("新增存放地點失敗:", error);
      notifyError("新增存放地點失敗", "請稍後再試");
    } finally {
      // 清空輸入框並關閉新增介面
      setNewStorageLocationName("");
      setIsAddingStorageLocation(false);
    }
  };

  // 處理新增/編輯表單提交
  const handleSubmit = async (values: any) => {
    try {
      // 檢核所有表單的必填欄位
      const basicFormFields = [
        { name: "assetAccountId", label: "財產科目" },
        { name: "assetSubAccountId", label: "財產子目" },
        { name: "assetCategoryId", label: "財產類別" },
        { name: "assetName", label: "財產名稱" },
        { name: "acquisitionDate", label: "取得日期" },
        { name: "quantity", label: "數量" },
        { name: "unitId", label: "單位" },
        { name: "departmentId", label: "所屬部門" },
        /*         { name: "divisionId", label: "所屬組別" },
        { name: "manufacturerId", label: "廠牌型號" }, */
        { name: "assetStatusId", label: "使用狀態" },
        { name: "equipmentTypeId", label: "設備類型" },
        { name: "custodianId", label: "保管人" },
        { name: "userId", label: "使用人" },
      ];

      const financeFormFields = [
        { name: "purchaseAmount", label: "購入金額" },
        { name: "subsidyAmount", label: "補助金額" },
        { name: "estimatedResidualValue", label: "估計殘值" },
      ];

      // 如果是已報廢狀態，加入報廢相關必填欄位
      if (isDiscarded()) {
        basicFormFields.push(
          { name: "discardReason", label: "報廢原因" },
          { name: "discardDate", label: "報廢日期" },
          { name: "isStillUsable", label: "報廢後堪用" }
        );
      }

      // 取得所有表單的值
      const allFormValues = form.getFieldsValue(true);

      // 檢查基本資訊必填欄位
      const basicErrors: string[] = [];
      basicFormFields.forEach((field) => {
        if (!allFormValues[field.name]) {
          basicErrors.push(field.label);
        }
      });

      // 檢查財務資訊必填欄位
      const financeErrors: string[] = [];
      financeFormFields.forEach((field) => {
        if (
          allFormValues[field.name] === undefined ||
          allFormValues[field.name] === null
        ) {
          financeErrors.push(field.label);
        }
      });

      // 如果有錯誤，顯示錯誤訊息並返回
      if (basicErrors.length > 0 || financeErrors.length > 0) {
        let errorMessage = "";
        if (basicErrors.length > 0) {
          errorMessage += `基本資訊頁籤中的以下欄位為必填：\n${basicErrors.join(
            "、"
          )}\n\n`;
        }
        if (financeErrors.length > 0) {
          errorMessage += `財務資訊頁籤中的以下欄位為必填：\n${financeErrors.join(
            "、"
          )}`;
        }

        Modal.error({
          title: "請填寫必填欄位",
          content: errorMessage,
          onOk: () => {
            // 如果有基本資訊的錯誤，切換到基本資訊頁籤
            if (basicErrors.length > 0) {
              setActiveTab("1");
            }
            // 如果只有財務資訊的錯誤，切換到財務資訊頁籤
            else if (financeErrors.length > 0) {
              setActiveTab("2");
            }
          },
        });
        return;
      }

      // 獲取主表單所有值，確保即使在其他標籤頁提交也有基本資訊
      const mainFormValues = form.getFieldsValue(true);
      const submitData = { ...mainFormValues, ...values };

      // 將日期轉換為 Unix 時間戳（秒）
      if (submitData.acquisitionDate && submitData.acquisitionDate.unix) {
        submitData.acquisitionDate = submitData.acquisitionDate.unix();
      } else if (
        submitData.acquisitionDate === null ||
        submitData.acquisitionDate === undefined
      ) {
        submitData.acquisitionDate = 0;
      }

      // 確保 statusChangeDate 永遠是數字，而不是 null
      if (submitData.statusChangeDate && submitData.statusChangeDate.unix) {
        submitData.statusChangeDate = submitData.statusChangeDate.unix();
      } else {
        submitData.statusChangeDate = 0; // 如果是 null，設置為 0
      }

      if (submitData.constructionDate && submitData.constructionDate.unix) {
        submitData.constructionDate = submitData.constructionDate.unix();
      } else if (
        submitData.constructionDate === null ||
        submitData.constructionDate === undefined
      ) {
        submitData.constructionDate = 0;
      }

      if (submitData.usageExpiryDate && submitData.usageExpiryDate.unix) {
        submitData.usageExpiryDate = submitData.usageExpiryDate.unix();
      } else if (
        submitData.usageExpiryDate === null ||
        submitData.usageExpiryDate === undefined
      ) {
        submitData.usageExpiryDate = 0;
      }

      if (submitData.discardDate && submitData.discardDate.unix) {
        submitData.scrapDate = submitData.discardDate.unix();
      } else if (
        submitData.discardDate === null ||
        submitData.discardDate === undefined
      ) {
        submitData.scrapDate = 0;
      }

      // 將表單的報廢欄位映射回資產欄位
      submitData.scrapReason = submitData.discardReason || "";

      // 處理預計報廢年度 - 轉換 dayjs 對象為年份數值
      if (submitData.estimatedScrapYear && submitData.estimatedScrapYear.year) {
        submitData.estimatedScrapYear = submitData.estimatedScrapYear.year();
      } else if (
        submitData.estimatedScrapYear === null ||
        submitData.estimatedScrapYear === undefined
      ) {
        submitData.estimatedScrapYear = null;
      }

      // 設置財產來源和攤提來源
      const assetId = editingAsset?.asset?.assetId || guidUtils();
      const currentTime = Date.now();
      const userId = user?.userId || "";

      // 財產
      const formattedAsset: Asset = {
        ...editingAsset?.asset,
        ...submitData,
        assetId: assetId,
        createTime: editingAsset ? editingAsset.asset.createTime : currentTime,
        createUserId: editingAsset
          ? editingAsset.asset.createUserId || ""
          : userId,
        updateTime: editingAsset ? currentTime : 0,
        updateUserId: editingAsset ? userId : "",
        deleteTime: 0,
        deleteUserId: "",
        accessoryEquipments: [],
        assetInsuranceUnits: [],
        assetAmortizationSources: [],
        assetAssetSources: [],
      };

      // 處理附屬設備
      const formattedAccessoryEquipments = accessoryEquipments.map(
        (equipment) => ({
          ...equipment,
          assetId: assetId,
          asset: formattedAsset,
          createTime: equipment.createTime || currentTime,
          createUserId: equipment.createUserId || userId,
          updateTime: equipment.updateTime || 0,
          updateUserId: equipment.updateUserId || "",
          deleteTime: 0,
          deleteUserId: "",
        })
      );

      // 處理保險單位
      const formattedInsuranceUnits = insuranceUnits.map((unit) => ({
        ...unit,
        insuranceStartDate: unit.insuranceStartDate || 0,
        insuranceExpiryDate: unit.insuranceExpiryDate || 0,
        createTime: unit.createTime || currentTime,
        createUserId: unit.createUserId || userId,
        updateTime: unit.updateTime || 0,
        updateUserId: unit.updateUserId || "",
        deleteTime: 0,
        deleteUserId: "",
        assetInsuranceUnits: [],
      }));

      // 處理攤提來源
      const formattedAmortizationSources = localAmortizationSources
        .filter((source) =>
          selectedAmortizationSources.includes(source.amortizationSourceId)
        )
        .map((source) => ({
          amortizationSourceId: source.amortizationSourceId,
          departmentId: source.departmentId || submitData.departmentId || "",
          sourceName: source.sourceName,
          description: source.description || "",
          amount: source.amount || 0,
          assetId: assetId,
          asset: formattedAsset,
          createTime: source.createTime || currentTime,
          createUserId: source.createUserId || userId,
          updateTime: source.updateTime || 0,
          updateUserId: source.updateUserId || "",
          deleteTime: 0,
          deleteUserId: "",
          assetAmortizationSources: [],
        }));

      // 處理財產來源
      const formattedAssetSources = localAssetSources
        .filter((source) => selectedAssetSources.includes(source.assetSourceId))
        .map(
          (source) =>
            ({
              assetSourceId: source.assetSourceId,
              assetSourceName: source.assetSourceName,
              sortCode: source.sortCode || 0,
              createTime: source.createTime || currentTime,
              createUserId: source.createUserId || userId,
              updateTime: source.updateTime || 0,
              updateUserId: source.updateUserId || "",
              deleteTime: 0,
              deleteUserId: "",
              assetAssetSources: [],
            } as ServiceAssetSource)
        );

      // 組合最終提交資料
      const assetDetailData: AssetDetail = {
        asset: formattedAsset,
        accessoryEquipments: formattedAccessoryEquipments,
        insuranceUnits: formattedInsuranceUnits,
        amortizationSources: formattedAmortizationSources,
        assetSources: formattedAssetSources,
      };

      // 設置確認資料並顯示確認模態框
      setConfirmData(assetDetailData);
      setIsConfirmModalVisible(true);
    } catch (error) {
      console.error("準備資料失敗:", error);
      notifyError("準備資料失敗", "請稍後再試");
    }
  };

  // 處理確認提交
  const handleConfirmSubmit = () => {
    if (!confirmData) return;

    // 關閉確認對話框
    setIsConfirmModalVisible(false);

    // 將資料傳遞給父組件
    onSuccess(confirmData);

    // 重置所有資料
    if (!editingAsset) {
      resetAllFormData();
    }

    // 重置到第一個標籤頁
    setActiveTab("1");
  };

  // 重置所有表單資料的函數
  const resetAllFormData = () => {
    // 重置主表單
    form.resetFields();
    form.setFieldsValue({
      ...formInitialValues,
      assetStatus: assetStatusOptions[0]?.assetStatusId || undefined,
      purchaseAmount: 0,
      subsidyAmount: 0,
      estimatedResidualValue: 0,
      depreciationAmount: 0,
      accumulatedDepreciationAmount: 0,
    });

    // 重置附屬設備和保險單位
    setAccessoryEquipments([]);
    setInsuranceUnits([]);

    // 重置選擇狀態
    setSelectedAssetSources([]);
    setSelectedAmortizationSources([]);
    setSelectedDepartmentId("");
    setSelectedAssetAccountId("");
    setSelectedAssetStatus("");

    // 重置過濾資料
    setFilteredDivisions([]);
    setFilteredAssetSubAccounts([]);

    // 重置確認資料
    setConfirmData(null);
  };

  // 更新 dropdownRender 的類型定義
  const renderManufacturerDropdownMenu = (menu: React.ReactNode) => (
    <>
      {menu}
      {!isViewMode && (
        <>
          <Divider style={{ margin: "8px 0" }} />
          <Space style={{ padding: "0 8px 4px" }}>
            {isAddingManufacturer ? (
              <>
                <Input
                  ref={manufacturerInputRef}
                  value={newManufacturerName}
                  onChange={(e) => setNewManufacturerName(e.target.value)}
                  placeholder="輸入新廠牌型號名稱"
                  style={{ width: "200px" }}
                />
                <Button
                  type="text"
                  icon={<PlusOutlined />}
                  onClick={handleAddManufacturer}
                >
                  新增
                </Button>
              </>
            ) : (
              <a onClick={addManufacturerItem} style={{ whiteSpace: "nowrap" }}>
                <PlusOutlined /> 新增廠牌型號
              </a>
            )}
          </Space>
        </>
      )}
    </>
  );

  const renderEquipmentTypeDropdownMenu = (menu: React.ReactNode) => (
    <>
      {menu}
      {!isViewMode && (
        <>
          <Divider style={{ margin: "8px 0" }} />
          <Space style={{ padding: "0 8px 4px" }}>
            {isAddingEquipmentType ? (
              <>
                <Input
                  ref={equipmentTypeInputRef}
                  value={newEquipmentTypeName}
                  onChange={(e) => setNewEquipmentTypeName(e.target.value)}
                  placeholder="輸入新設備類型名稱"
                  style={{ width: "200px" }}
                />
                <Button
                  type="text"
                  icon={<PlusOutlined />}
                  onClick={handleAddEquipmentType}
                >
                  新增
                </Button>
              </>
            ) : (
              <a
                onClick={addEquipmentTypeItem}
                style={{ whiteSpace: "nowrap" }}
              >
                <PlusOutlined /> 新增設備類型
              </a>
            )}
          </Space>
        </>
      )}
    </>
  );

  const renderUnitDropdownMenu = (menu: React.ReactNode) => (
    <>
      {menu}
      {!isViewMode && (
        <>
          <Divider style={{ margin: "8px 0" }} />
          <Space style={{ padding: "0 8px 4px" }}>
            {isAddingUnit ? (
              <>
                <Input
                  ref={unitInputRef}
                  value={newUnitName}
                  onChange={(e) => setNewUnitName(e.target.value)}
                  placeholder="輸入新單位名稱"
                  style={{ width: "200px" }}
                />
                <Button
                  type="text"
                  icon={<PlusOutlined />}
                  onClick={handleAddUnit}
                >
                  新增
                </Button>
              </>
            ) : (
              <a onClick={addUnitItem} style={{ whiteSpace: "nowrap" }}>
                <PlusOutlined /> 新增單位
              </a>
            )}
          </Space>
        </>
      )}
    </>
  );

  const renderStorageLocationDropdownMenu = (menu: React.ReactNode) => (
    <>
      {menu}
      {!isViewMode && (
        <>
          <Divider style={{ margin: "8px 0" }} />
          <Space style={{ padding: "0 8px 4px" }}>
            {isAddingStorageLocation ? (
              <>
                <Input
                  ref={storageLocationInputRef}
                  value={newStorageLocationName}
                  onChange={(e) => setNewStorageLocationName(e.target.value)}
                  placeholder="輸入新存放地點名稱"
                  style={{ width: "200px" }}
                />
                <Button
                  type="text"
                  icon={<PlusOutlined />}
                  onClick={handleAddStorageLocation}
                >
                  新增
                </Button>
              </>
            ) : (
              <a
                onClick={addStorageLocationItem}
                style={{ whiteSpace: "nowrap" }}
              >
                <PlusOutlined /> 新增存放地點
              </a>
            )}
          </Space>
        </>
      )}
    </>
  );

  const renderDropdownMenu = (menu: React.ReactNode) => (
    <>
      {menu}
      {!isViewMode && (
        <>
          <Divider style={{ margin: "8px 0" }} />
          <Space style={{ padding: "0 8px 4px" }}>
            {isAddingAssetSource ? (
              <>
                <Input
                  ref={assetSourceInputRef}
                  value={newAssetSourceName}
                  onChange={(e) => setNewAssetSourceName(e.target.value)}
                  placeholder="輸入新財產來源名稱"
                  style={{ width: "200px" }}
                />
                <Button
                  type="text"
                  icon={<PlusOutlined />}
                  onClick={handleAddAssetSource}
                >
                  新增
                </Button>
              </>
            ) : (
              <a onClick={addItem} style={{ whiteSpace: "nowrap" }}>
                <PlusOutlined /> 新增財產來源
              </a>
            )}
          </Space>
        </>
      )}
    </>
  );

  // 處理表單取消
  const handleFormCancel = () => {
    // 如果不是在編輯模式下，則重置表單數據
    if (!editingAsset) {
      resetAllFormData();
    }

    // 重置到第一個標籤頁
    setActiveTab("1");

    // 調用父組件的 onCancel 方法關閉 Modal
    onCancel();
  };

  // 謄寫功能 - 載入財產列表
  const fetchAssetList = async () => {
    setAssetListLoading(true);
    try {
      const response = await getAssets();
      if (response.success && response.data) {
        // 過濾掉已刪除的資產
        const filteredAssets = response.data.filter(
          (asset) => !asset.asset.deleteTime
        );
        setAssetList(filteredAssets);
      } else {
        message.error(response.message || "無法取得財產列表");
      }
    } catch (error) {
      console.error("取得財產列表失敗:", error);
      message.error("取得財產列表失敗");
    } finally {
      setAssetListLoading(false);
    }
  };

  // 謄寫功能 - 開啟模態框
  const showCopyModal = () => {
    fetchAssetList();
    setIsCopyModalVisible(true);
  };

  // 謄寫功能 - 關閉模態框
  const handleCopyModalCancel = () => {
    setIsCopyModalVisible(false);
    setSelectedAssetForCopy(null);
  };

  // 謄寫功能 - 確認謄寫
  const handleConfirmCopy = async () => {
    if (!selectedAssetForCopy) {
      message.error("請先選擇一筆財產資料");
      return;
    }

    try {
      // 取得選擇的財產詳細資料
      const response = await getAssetById(selectedAssetForCopy);
      if (response.success && response.data) {
        const sourceAsset = response.data.asset;

        // 需要保留的欄位
        const copyFormData: any = {
          // 不複製金額相關欄位
          // assetName: sourceAsset.assetName,
          // assetShortName: sourceAsset.assetShortName,
          // purchaseAmount: sourceAsset.purchaseAmount,
          // subsidyAmount: sourceAsset.subsidyAmount,
          // estimatedResidualValue: sourceAsset.estimatedResidualValue,
          // depreciationAmount: sourceAsset.depreciationAmount,
          // accumulatedDepreciationAmount: sourceAsset.accumulatedDepreciationAmount,
          departmentId: sourceAsset.departmentId,
          divisionId: sourceAsset.divisionId,
          custodianId: sourceAsset.custodianId,
          userId: sourceAsset.userId,
          assetStatusId: sourceAsset.assetStatusId,
          usage: sourceAsset.usage,
          notes: sourceAsset.notes,
          // 不複製耐用年限
          // serviceLife: sourceAsset.serviceLife,
          // insurancePeriod: sourceAsset.insurancePeriod,
          manufacturerId: sourceAsset.manufacturerId,
          specification: sourceAsset.specification,
          buildingAddress: sourceAsset.buildingAddress,
          buildingStructure: sourceAsset.buildingStructure,
          floorArea: sourceAsset.floorArea,
          publicArea: sourceAsset.publicArea,
          usageLicenseNo: sourceAsset.usageLicenseNo,
          buildingTaxItem: sourceAsset.buildingTaxItem,
          landSection: sourceAsset.landSection,
          landLocation: sourceAsset.landLocation,
          landNumber: sourceAsset.landNumber,
          landArea: sourceAsset.landArea,
          assetAccountId: sourceAsset.assetAccountId,
          assetSubAccountId: sourceAsset.assetSubAccountId,
          assetCategoryId: sourceAsset.assetCategoryId,
          equipmentTypeId: sourceAsset.equipmentTypeId,
          //customAssetNo1: sourceAsset.customAssetNo1,
          //customAssetNo2: sourceAsset.customAssetNo2,
          estimatedScrapYear: sourceAsset.estimatedScrapYear,
        };

        // 處理部門變更
        if (copyFormData.departmentId) {
          handleDepartmentChange(copyFormData.departmentId);
        }

        // 處理科目變更
        if (copyFormData.assetAccountId) {
          handleAssetAccountChange(copyFormData.assetAccountId);
        }

        // 處理資產狀態變更
        if (copyFormData.assetStatus) {
          handleAssetStatusChange(copyFormData.assetStatus);
        }

        // 複製資產來源
        if (
          response.data.assetSources &&
          response.data.assetSources.length > 0
        ) {
          const assetSourceIds = response.data.assetSources.map(
            (source) => source.assetSourceId
          );
          copyFormData.assetSourceIds = assetSourceIds;
          setSelectedAssetSources(assetSourceIds);
        }

        // 處理預計報廢年度的轉換
        if (copyFormData.estimatedScrapYear) {
          copyFormData.estimatedScrapYear = dayjs().year(
            copyFormData.estimatedScrapYear
          );
        }

        // 設置表單值
        form.setFieldsValue(copyFormData);

        // 複製附屬設備
        if (
          response.data.accessoryEquipments &&
          response.data.accessoryEquipments.length > 0
        ) {
          const copiedAccessories = response.data.accessoryEquipments.map(
            (equipment) => ({
              ...equipment,
              accessoryEquipmentId: guidUtils(), // 產生新的ID
              createTime: Date.now(),
              createUserId: user?.userId || "",
              updateTime: 0,
              updateUserId: "",
              deleteTime: 0,
              deleteUserId: "",
              asset: null, // 添加必要屬性
            })
          ) as AccessoryEquipment[];
          setAccessoryEquipments(copiedAccessories);
        }

        // 複製保險單位
        if (
          response.data.insuranceUnits &&
          response.data.insuranceUnits.length > 0
        ) {
          const copiedInsuranceUnits = response.data.insuranceUnits.map(
            (unit) => ({
              ...unit,
              insuranceUnitId: guidUtils(), // 產生新的ID
              createTime: Date.now(),
              createUserId: user?.userId || "",
              updateTime: 0,
              updateUserId: "",
              deleteTime: 0,
              deleteUserId: "",
              assetInsuranceUnits: [],
            })
          ) as InsuranceUnit[];
          setInsuranceUnits(copiedInsuranceUnits);
        }
        // 檢查是否可以取得新財產編號
        checkAndGetNewAssetNo();

        message.success("謄寫完成");
        setIsCopyModalVisible(false);
        setSelectedAssetForCopy(null);
      } else {
        message.error(response.message || "無法取得財產詳細資料");
      }
    } catch (error) {
      console.error("謄寫財產資料失敗:", error);
      message.error("謄寫財產資料失敗");
    }
  };

  return (
    <>
      <div className="custom-tabs" style={{ marginBottom: 16 }}>
        {/* 謄寫Modal */}
        <Modal
          title="選擇要謄寫的財產資料"
          open={isCopyModalVisible}
          onCancel={handleCopyModalCancel}
          onOk={handleConfirmCopy}
          width={800}
          okText="確認謄寫"
          okButtonProps={{
            disabled: !selectedAssetForCopy,
            type: "primary",
          }}
          cancelText="取消"
        >
          <div style={{ marginBottom: 16 }}>
            <p>選擇財產資料進行謄寫，將複製到新的財產資料中</p>
          </div>
          <Table
            dataSource={assetList}
            loading={assetListLoading}
            rowKey={(record) => record.asset.assetId}
            pagination={{ pageSize: 10 }}
            scroll={{ y: 400 }}
            size="small"
            rowSelection={{
              type: "radio",
              onChange: (selectedRowKeys) => {
                setSelectedAssetForCopy(selectedRowKeys[0] as string);
              },
            }}
            columns={[
              {
                title: "財產編號",
                dataIndex: ["asset", "assetNo"],
                key: "assetNo",
                width: 150,
              },
              {
                title: "財產科目",
                key: "assetAccount",
                width: 200,
                render: (_, record) => {
                  const account = assetAccounts.find(
                    (a) => a.assetAccountId === record.asset.assetAccountId
                  );
                  return account ? account.assetAccountName : "-";
                },
              },
              {
                title: "財產名稱",
                dataIndex: ["asset", "assetName"],
                key: "assetName",
                width: 200,
              },
              {
                title: "取得日期",
                dataIndex: ["asset", "acquisitionDate"],
                key: "acquisitionDate",
                width: 120,
                render: (date) =>
                  date ? dayjs.unix(date).format("YYYY-MM-DD") : "-",
              },
              {
                title: "所屬部門",
                key: "department",
                width: 150,
                render: (_, record) => {
                  const dept = departments.find(
                    (d) => d.departmentId === record.asset.departmentId
                  );
                  return dept ? dept.name : "-";
                },
              },
            ]}
          />
        </Modal>

        <div
          role="tablist"
          className="tab-nav"
          style={{
            display: "flex",
            borderBottom: "1px solid #f0f0f0",
            marginBottom: "16px",
          }}
        >
          <button
            role="tab"
            aria-selected={activeTab === "1"}
            id="tab-basic"
            style={{
              padding: "8px 16px",
              marginRight: "8px",
              background: activeTab === "1" ? "#fff" : "#fafafa",
              border: "1px solid #d9d9d9",
              borderBottom: activeTab === "1" ? "1px solid #fff" : "none",
              borderRadius: "4px 4px 0 0",
              cursor: "pointer",
              fontSize: "16px",
              transition: "all 0.3s",
              position: "relative",
              top: "1px",
              outline: "none",
              color: activeTab === "1" ? "#1890ff" : "inherit",
              fontWeight: activeTab === "1" ? 500 : "normal",
            }}
            onClick={() => setActiveTab("1")}
          >
            基本資訊
          </button>
          <button
            role="tab"
            aria-selected={activeTab === "2"}
            id="tab-finance"
            style={{
              padding: "8px 16px",
              marginRight: "8px",
              background: activeTab === "2" ? "#fff" : "#fafafa",
              border: "1px solid #d9d9d9",
              borderBottom: activeTab === "2" ? "1px solid #fff" : "none",
              borderRadius: "4px 4px 0 0",
              cursor: "pointer",
              fontSize: "16px",
              transition: "all 0.3s",
              position: "relative",
              top: "1px",
              outline: "none",
              color: activeTab === "2" ? "#1890ff" : "inherit",
              fontWeight: activeTab === "2" ? 500 : "normal",
            }}
            onClick={() => setActiveTab("2")}
          >
            財務資訊
          </button>
          <button
            role="tab"
            aria-selected={activeTab === "3"}
            id="tab-accessory"
            style={{
              padding: "8px 16px",
              marginRight: "8px",
              background: activeTab === "3" ? "#fff" : "#fafafa",
              border: "1px solid #d9d9d9",
              borderBottom: activeTab === "3" ? "1px solid #fff" : "none",
              borderRadius: "4px 4px 0 0",
              cursor: "pointer",
              fontSize: "16px",
              transition: "all 0.3s",
              position: "relative",
              top: "1px",
              outline: "none",
              color: activeTab === "3" ? "#1890ff" : "inherit",
              fontWeight: activeTab === "3" ? 500 : "normal",
            }}
            onClick={() => setActiveTab("3")}
          >
            附屬設備
          </button>
          <button
            role="tab"
            aria-selected={activeTab === "4"}
            id="tab-insurance"
            style={{
              padding: "8px 16px",
              marginRight: "8px",
              background: activeTab === "4" ? "#fff" : "#fafafa",
              border: "1px solid #d9d9d9",
              borderBottom: activeTab === "4" ? "1px solid #fff" : "none",
              borderRadius: "4px 4px 0 0",
              cursor: "pointer",
              fontSize: "16px",
              transition: "all 0.3s",
              position: "relative",
              top: "1px",
              outline: "none",
              color: activeTab === "4" ? "#1890ff" : "inherit",
              fontWeight: activeTab === "4" ? 500 : "normal",
            }}
            onClick={() => setActiveTab("4")}
          >
            保險單位
          </button>
        </div>

        <div style={{ marginTop: 21 }}>
          {/* 基本資訊 */}
          {activeTab === "1" && (
            <div
              role="tabpanel"
              aria-labelledby="tab-basic"
              style={{ paddingTop: "10px" }}
            >
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={formInitialValues}
              >
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                    columnGap: "16px",
                  }}
                >
                  <Form.Item
                    name="assetNo"
                    label={isViewMode ? "" : "財產編號"}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.title}>
                        <span style={ViewModeStyle.label}>財產編號</span>
                        <div style={ViewModeStyle.content}>
                          {form.getFieldValue("assetNo") || "-"}
                        </div>
                      </div>
                    ) : (
                      <Input
                        disabled={true}
                        style={{
                          backgroundColor: "#ffffff",
                          fontWeight: "bold",
                          fontSize: "20px",
                          color: "#000000",
                          border: "none",
                        }}
                      />
                    )}
                  </Form.Item>

                  {/* 謄寫按鈕 */}
                  {!editingAsset && !isViewMode && (
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "flex-end",
                        alignItems: "flex-end",
                        height: "100%",
                        marginBottom: "24px",
                      }}
                    >
                      <Button
                        type="default"
                        icon={<CopyOutlined />}
                        onClick={showCopyModal}
                      >
                        謄寫
                      </Button>
                    </div>
                  )}

                  <Form.Item
                    name="customAssetNo1"
                    label={isViewMode ? "" : "自訂財產編號一"}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>自訂財產編號一</span>
                        <div style={ViewModeStyle.content}>
                          {form.getFieldValue("customAssetNo1") || "-"}
                        </div>
                      </div>
                    ) : (
                      <Input placeholder="請輸入自訂財產編號一" />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="customAssetNo2"
                    label={isViewMode ? "" : "自訂財產編號二"}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>自訂財產編號二</span>
                        <div style={ViewModeStyle.content}>
                          {form.getFieldValue("customAssetNo2") || "-"}
                        </div>
                      </div>
                    ) : (
                      <Input placeholder="請輸入自訂財產編號二" />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="assetAccountId"
                    label={isViewMode ? "" : "財產科目"}
                    rules={[{ required: true, message: "請選擇財產科目" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>財產科目</span>
                        <div style={ViewModeStyle.content}>
                          {assetAccounts.find(
                            (account) =>
                              account.assetAccountId ===
                              form.getFieldValue("assetAccountId")
                          )?.assetAccountName || "-"}
                        </div>
                      </div>
                    ) : (
                      <Select
                        placeholder="請選擇財產科目"
                        onChange={handleAssetAccountChange}
                        disabled={!!editingAsset}
                      >
                        {assetAccounts.map((account) => (
                          <Select.Option
                            key={account.assetAccountId}
                            value={account.assetAccountId}
                          >
                            {account.assetAccountName}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>

                  <Form.Item
                    name="assetSubAccountId"
                    label={isViewMode ? "" : "財產子目"}
                    rules={[{ required: true, message: "請選擇財產子目" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>財產子目</span>
                        <div style={ViewModeStyle.content}>
                          {assetSubAccounts.find(
                            (subAccount) =>
                              subAccount.assetSubAccountId ===
                              form.getFieldValue("assetSubAccountId")
                          )?.assetSubAccountName || "-"}
                        </div>
                      </div>
                    ) : (
                      <Select
                        placeholder="請選擇財產子目"
                        onChange={handleAssetSubAccountChange}
                        disabled={!!editingAsset || !selectedAssetAccountId}
                      >
                        {filteredAssetSubAccounts.map((subAccount) => (
                          <Select.Option
                            key={subAccount.assetSubAccountId}
                            value={subAccount.assetSubAccountId}
                          >
                            {subAccount.assetSubAccountName}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>

                  <Form.Item
                    name="assetCategoryId"
                    label={isViewMode ? "" : "財產類別"}
                    rules={[{ required: true, message: "請選擇財產類別" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>財產類別</span>
                        <div style={ViewModeStyle.content}>
                          {assetCategories.find(
                            (category) =>
                              category.assetCategoryId ===
                              form.getFieldValue("assetCategoryId")
                          )?.assetCategoryName || "-"}
                        </div>
                      </div>
                    ) : (
                      <Select
                        placeholder="請選擇財產類別"
                        onChange={handleAssetCategoryChange}
                        disabled={!!editingAsset}
                      >
                        {assetCategories.map((category) => (
                          <Select.Option
                            key={category.assetCategoryId}
                            value={category.assetCategoryId}
                          >
                            {category.assetCategoryName}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>

                  <Form.Item
                    name="assetName"
                    label={isViewMode ? "" : "財產名稱"}
                    rules={[{ required: true, message: "請輸入財產名稱" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <span style={ViewModeStyle.label}>財產名稱</span>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("assetName") || "-"}
                            </div>
                          </div>
                        ) : (
                          <Input placeholder="請輸入財產簡稱" />
                        )}
                      </div>
                    ) : (
                      <Input placeholder="請輸入財產名稱" maxLength={50} />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="assetShortName"
                    label={isViewMode ? "" : "財產簡稱"}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>財產簡稱</span>
                        <div style={ViewModeStyle.content}>
                          {form.getFieldValue("assetShortName") || "-"}
                        </div>
                      </div>
                    ) : (
                      <Input placeholder="請輸入財產簡稱" />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="specification"
                    label={isViewMode ? "" : "規格"}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>規格</span>
                        <div style={ViewModeStyle.content}>
                          {form.getFieldValue("specification") || "-"}
                        </div>
                      </div>
                    ) : (
                      <Input placeholder="請輸入規格" />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="acquisitionDate"
                    label={isViewMode ? "" : "取得日期"}
                    rules={[{ required: true, message: "請選擇取得日期" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>取得日期</span>
                        <div style={ViewModeStyle.content}>
                          {form.getFieldValue("acquisitionDate")
                            ? dayjs.isDayjs(
                                form.getFieldValue("acquisitionDate")
                              )
                              ? form
                                  .getFieldValue("acquisitionDate")
                                  .format("YYYY-MM-DD")
                              : form.getFieldValue("acquisitionDate")
                            : "-"}
                        </div>
                      </div>
                    ) : (
                      <DatePicker
                        placeholder="請選擇取得日期"
                        style={{ width: "100%" }}
                        format="YYYY-MM-DD"
                      />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="quantity"
                    label={isViewMode ? "" : "數量"}
                    rules={[{ required: true, message: "請輸入數量" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>數量</span>
                        <div style={ViewModeStyle.content}>
                          {form.getFieldValue("quantity") || "-"}
                        </div>
                      </div>
                    ) : (
                      <Input placeholder="請輸入數量" />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="unitId"
                    label={isViewMode ? "" : "單位"}
                    rules={[{ required: true, message: "請選擇單位" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>單位</span>
                        <div style={ViewModeStyle.content}>
                          {localUnits.find(
                            (unit) =>
                              unit.unitId === form.getFieldValue("unitId")
                          )?.name || "-"}
                        </div>
                      </div>
                    ) : (
                      <Select
                        placeholder="請選擇單位"
                        style={{ width: "100%" }}
                        popupRender={renderUnitDropdownMenu}
                      >
                        {localUnits.map((unit) => (
                          <Select.Option key={unit.unitId} value={unit.unitId}>
                            {unit.name}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>

                  <Form.Item
                    name="departmentId"
                    label={isViewMode ? "" : "所屬部門"}
                    rules={[{ required: true, message: "請選擇所屬部門" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>所屬部門</span>
                        <div style={ViewModeStyle.content}>
                          {departments.find(
                            (dept) =>
                              dept.departmentId ===
                              form.getFieldValue("departmentId")
                          )?.name || "-"}
                        </div>
                      </div>
                    ) : (
                      <Select
                        placeholder="請選擇所屬部門"
                        onChange={handleDepartmentChange}
                      >
                        {departments.map((department) => (
                          <Select.Option
                            key={department.departmentId}
                            value={department.departmentId}
                          >
                            {department.name}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>

                  <Form.Item
                    name="divisionId"
                    label={isViewMode ? "" : "所屬組別"}
                    rules={[{ message: "請選擇所屬組別" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>所屬組別</span>
                        <div style={ViewModeStyle.content}>
                          {divisions.find(
                            (div) =>
                              div.divisionId ===
                              form.getFieldValue("divisionId")
                          )?.name || "-"}
                        </div>
                      </div>
                    ) : (
                      <Select
                        disabled={!selectedDepartmentId}
                        placeholder={
                          selectedDepartmentId
                            ? "請選擇所屬組別"
                            : "請先選擇部門"
                        }
                      >
                        {filteredDivisions.map((division) => (
                          <Select.Option
                            key={division.divisionId}
                            value={division.divisionId}
                          >
                            {division.name}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>

                  <Form.Item
                    name="manufacturerId"
                    label={isViewMode ? "" : "廠牌型號"}
                    rules={[{ message: "請選擇廠牌型號" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>廠牌型號</span>
                        <div style={ViewModeStyle.content}>
                          {localManufacturers.find(
                            (m) =>
                              m.manufacturerId ===
                              form.getFieldValue("manufacturerId")
                          )?.name || "-"}
                        </div>
                      </div>
                    ) : (
                      <Select
                        placeholder="請選擇廠牌型號"
                        style={{ width: "100%" }}
                        showSearch
                        filterOption={(input, option) =>
                          (option?.label as string)
                            ?.toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        options={localManufacturers.map((manufacturer) => ({
                          value: manufacturer.manufacturerId,
                          label: manufacturer.name,
                        }))}
                        popupRender={renderManufacturerDropdownMenu}
                      />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="assetStatusId"
                    label={isViewMode ? "" : "使用狀態"}
                    rules={[{ required: true, message: "請選擇使用狀態" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>使用狀態</span>
                        <div style={ViewModeStyle.content}>
                          {(() => {
                            const status = assetStatusOptions.find(
                              (s) =>
                                s.assetStatusId ===
                                form.getFieldValue("assetStatus")
                            );
                            return status ? (
                              <Tag
                                color={STATUS_COLORS[status.name] || "default"}
                              >
                                {status.name}
                              </Tag>
                            ) : (
                              "-"
                            );
                          })()}
                        </div>
                      </div>
                    ) : (
                      <Select
                        placeholder="請選擇使用狀態"
                        onChange={handleAssetStatusChange}
                      >
                        {assetStatusOptions.map((status) => (
                          <Select.Option
                            key={status.assetStatusId}
                            value={status.assetStatusId}
                          >
                            {status.name}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>

                  <Form.Item
                    name="equipmentTypeId"
                    label={isViewMode ? "" : "設備類型"}
                    rules={[{ required: true, message: "請選擇設備類型" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>設備類型</span>
                        <div style={ViewModeStyle.content}>
                          {localEquipmentTypes.find(
                            (type) =>
                              type.equipmentTypeId ===
                              form.getFieldValue("equipmentTypeId")
                          )?.name || "-"}
                        </div>
                      </div>
                    ) : (
                      <Select
                        placeholder="請選擇設備類型"
                        popupRender={renderEquipmentTypeDropdownMenu}
                      >
                        {localEquipmentTypes.map((type) => (
                          <Select.Option
                            key={type.equipmentTypeId}
                            value={type.equipmentTypeId}
                          >
                            {type.name}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>

                  <Form.Item
                    name="custodianId"
                    label={isViewMode ? "" : "保管人"}
                    rules={[{ required: true, message: "請選擇保管人" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>保管人</span>
                        <div style={ViewModeStyle.content}>
                          {(() => {
                            const user = custodians.find(
                              (u) =>
                                u.userId === form.getFieldValue("custodianId")
                            );
                            return user ? `${user.name}` : "-";
                          })()}
                        </div>
                      </div>
                    ) : (
                      <Select
                        showSearch
                        placeholder="請選擇保管人"
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          String(option?.label ?? "")
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        options={custodians.map((user) => ({
                          value: user.userId,
                          label: `${user.name}`,
                        }))}
                      />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="userId"
                    label={isViewMode ? "" : "使用人"}
                    rules={[{ required: true, message: "請選擇使用人" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>使用人</span>
                        <div style={ViewModeStyle.content}>
                          {(() => {
                            const user = assetUsers.find(
                              (u) => u.userId === form.getFieldValue("userId")
                            );
                            return user ? `${user.name}` : "-";
                          })()}
                        </div>
                      </div>
                    ) : (
                      <Select
                        showSearch
                        placeholder="請選擇使用人"
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          String(option?.label ?? "")
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        options={assetUsers.map((user) => ({
                          value: user.userId,
                          label: `${user.name}`,
                        }))}
                      />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="storageLocationId"
                    label={isViewMode ? "" : "存放地點"}
                    rules={[{ message: "請選擇存放地點" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>存放地點</span>
                        <div style={ViewModeStyle.content}>
                          {(() => {
                            const location = localStorageLocations.find(
                              (loc) =>
                                loc.storageLocationId ===
                                form.getFieldValue("storageLocationId")
                            );
                            return location ? location.name : "-";
                          })()}
                        </div>
                      </div>
                    ) : (
                      <Select
                        showSearch
                        placeholder="請選擇存放地點"
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          String(option?.label ?? "")
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        options={localStorageLocations.map((location) => ({
                          value: location.storageLocationId,
                          label: location.name,
                        }))}
                        popupRender={renderStorageLocationDropdownMenu}
                      />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="serviceLife"
                    label={isViewMode ? "" : "耐用年限"}
                    rules={[{ required: true, message: "請輸入耐用年限" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>耐用年限</span>
                        <div style={ViewModeStyle.content}>
                          {form.getFieldValue("serviceLife") != null
                            ? `${form.getFieldValue("serviceLife")} 年`
                            : "-"}
                        </div>
                      </div>
                    ) : (
                      <InputNumber
                        placeholder="請輸入耐用年限"
                        disabled={!!editingAsset || !!editingAccessory}
                        min={0}
                        style={{ width: "100%" }}
                      />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="insurancePeriod"
                    label={isViewMode ? "" : "保固年限"}
                    rules={[{ required: true, message: "請輸入保固年限" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>保固年限</span>
                        <div style={ViewModeStyle.content}>
                          {form.getFieldValue("insurancePeriod") != null
                            ? `${form.getFieldValue("insurancePeriod")} 年`
                            : "-"}
                        </div>
                      </div>
                    ) : (
                      <InputNumber
                        placeholder="請輸入保固年限"
                        min={0}
                        style={{ width: "100%" }}
                      />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="estimatedScrapYear"
                    label={isViewMode ? "" : "預計報廢年度"}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>預計報廢年度</span>
                        <div style={ViewModeStyle.content}>
                          {form.getFieldValue("estimatedScrapYear")
                            ? dayjs()
                                .year(form.getFieldValue("estimatedScrapYear"))
                                .format("YYYY")
                            : "-"}
                        </div>
                      </div>
                    ) : (
                      <DatePicker
                        placeholder="請選擇預計報廢年度"
                        picker="year"
                        style={{ width: "100%" }}
                        format="YYYY"
                        value={form.getFieldValue("estimatedScrapYear")}
                        onChange={(date) => {
                          form.setFieldValue("estimatedScrapYear", date);
                        }}
                      />
                    )}
                  </Form.Item>

                  <Form.Item name="usage" label={isViewMode ? "" : "使用用途"}>
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>使用用途</span>
                        <div style={ViewModeStyle.content}>
                          {form.getFieldValue("usage") || "-"}
                        </div>
                      </div>
                    ) : (
                      <Input.TextArea
                        placeholder="請輸入使用用途"
                        maxLength={200}
                        autoSize={{ minRows: 2, maxRows: 4 }}
                        showCount
                      />
                    )}
                  </Form.Item>

                  <Form.Item name="notes" label={isViewMode ? "" : "備註"}>
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <span style={ViewModeStyle.label}>備註</span>
                        <div style={ViewModeStyle.content}>
                          {form.getFieldValue("notes") || "-"}
                        </div>
                      </div>
                    ) : (
                      <Input.TextArea
                        placeholder="請輸入備註"
                        maxLength={200}
                        autoSize={{ minRows: 2, maxRows: 4 }}
                        showCount
                      />
                    )}
                  </Form.Item>
                </div>

                {/* 土地或房屋建築相關欄位 */}
                {isLandOrBuilding() && (
                  <div className="land-building-fields">
                    <Divider style={{ margin: "16px 0" }} />
                    <Title level={5}>土地/房屋建築資訊</Title>
                    <div
                      style={{
                        display: "grid",
                        gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                        columnGap: "16px",
                      }}
                    >
                      <Form.Item
                        name="buildingAddress"
                        label={isViewMode ? "" : "建物地址"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>建物地址</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("buildingAddress") || "-"}
                            </div>
                          </div>
                        ) : (
                          <Input placeholder="請輸入建物地址" maxLength={100} />
                        )}
                      </Form.Item>

                      <Form.Item
                        name="buildingStructure"
                        label={isViewMode ? "" : "建物構造"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>建物構造</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("buildingStructure") || "-"}
                            </div>
                          </div>
                        ) : (
                          <Input placeholder="請輸入建物構造" maxLength={100} />
                        )}
                      </Form.Item>
                    </div>

                    <div
                      style={{
                        display: "grid",
                        gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                        columnGap: "16px",
                      }}
                    >
                      <Form.Item
                        name="constructionDate"
                        label={isViewMode ? "" : "使用執照日期"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>使用執照日期</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("constructionDate")
                                ? dayjs.isDayjs(
                                    form.getFieldValue("constructionDate")
                                  )
                                  ? form
                                      .getFieldValue("constructionDate")
                                      .format("YYYY-MM-DD")
                                  : form.getFieldValue("constructionDate")
                                : "-"}
                            </div>
                          </div>
                        ) : (
                          <DatePicker
                            placeholder="請選擇使用執照日期"
                            style={{ width: "100%" }}
                            format="YYYY-MM-DD"
                          />
                        )}
                      </Form.Item>

                      <Form.Item
                        name="floorArea"
                        label={isViewMode ? "" : "面積(m²)"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>面積(m²)</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("floorArea") || "-"}
                            </div>
                          </div>
                        ) : (
                          <InputNumber
                            placeholder="請輸入面積"
                            min={0}
                            style={{ width: "100%" }}
                          />
                        )}
                      </Form.Item>
                    </div>

                    <div
                      style={{
                        display: "grid",
                        gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                        columnGap: "16px",
                      }}
                    >
                      <Form.Item
                        name="publicArea"
                        label={isViewMode ? "" : "公設(m²)"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>公設(m²)</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("publicArea") || "-"}
                            </div>
                          </div>
                        ) : (
                          <InputNumber
                            placeholder="公設(m²)"
                            min={0}
                            style={{ width: "100%" }}
                          />
                        )}
                      </Form.Item>

                      <Form.Item
                        name="usageExpiryDate"
                        label={isViewMode ? "" : "使用執照日期"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>使用執照日期</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("usageExpiryDate")
                                ? dayjs.isDayjs(
                                    form.getFieldValue("usageExpiryDate")
                                  )
                                  ? form
                                      .getFieldValue("usageExpiryDate")
                                      .format("YYYY-MM-DD")
                                  : form.getFieldValue("usageExpiryDate")
                                : "-"}
                            </div>
                          </div>
                        ) : (
                          <DatePicker
                            placeholder="請選擇使用執照日期"
                            style={{ width: "100%" }}
                            format="YYYY-MM-DD"
                          />
                        )}
                      </Form.Item>
                    </div>

                    <div
                      style={{
                        display: "grid",
                        gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                        columnGap: "16px",
                      }}
                    >
                      <Form.Item
                        name="usageLicenseNo"
                        label={isViewMode ? "" : "使用執照號碼"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>使用執照號碼</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("usageLicenseNo") || "-"}
                            </div>
                          </div>
                        ) : (
                          <Input placeholder="請輸入使用執照號碼" />
                        )}
                      </Form.Item>

                      <Form.Item
                        name="buildingTaxItem"
                        label={isViewMode ? "" : "適用房屋稅目"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>適用房屋稅目</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("buildingTaxItem") || "-"}
                            </div>
                          </div>
                        ) : (
                          <Input placeholder="請輸入適用房屋稅目" />
                        )}
                      </Form.Item>
                    </div>

                    <div
                      style={{
                        display: "grid",
                        gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                        columnGap: "16px",
                      }}
                    >
                      <Form.Item
                        name="publicValue"
                        label={isViewMode ? "" : "公告現值"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>公告現值</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("publicValue") || "-"}
                            </div>
                          </div>
                        ) : (
                          <InputNumber
                            placeholder="請輸入公告現值"
                            min={0}
                            defaultValue={0}
                            style={{ width: "100%" }}
                            formatter={formatThousands}
                            parser={parseAmount}
                          />
                        )}
                      </Form.Item>

                      <Form.Item
                        name="landSection"
                        label={isViewMode ? "" : "地目"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>地目</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("landSection") || "-"}
                            </div>
                          </div>
                        ) : (
                          <Input placeholder="請輸入地目" />
                        )}
                      </Form.Item>
                    </div>
                    <div
                      style={{
                        display: "grid",
                        gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                        columnGap: "16px",
                      }}
                    >
                      <Form.Item
                        name="certificateNo"
                        label={isViewMode ? "" : "權狀號碼"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>權狀號碼</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("certificateNo") || "-"}
                            </div>
                          </div>
                        ) : (
                          <Input placeholder="請輸入權狀號碼" maxLength={50} />
                        )}
                      </Form.Item>

                      <Form.Item
                        name="landArea"
                        label={isViewMode ? "" : "面積(m²)"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>面積(m²)</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("landArea") || "-"}
                            </div>
                          </div>
                        ) : (
                          <InputNumber
                            placeholder="請輸入面積"
                            min={0}
                            style={{ width: "100%" }}
                          />
                        )}
                      </Form.Item>
                    </div>

                    <div
                      style={{
                        display: "grid",
                        gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                        columnGap: "16px",
                      }}
                    >
                      <Form.Item
                        name="landSection"
                        label={isViewMode ? "" : "地段"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>地段</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("landSection") || "-"}
                            </div>
                          </div>
                        ) : (
                          <Input placeholder="請輸入地段" />
                        )}
                      </Form.Item>

                      <Form.Item
                        name="landNumber"
                        label={isViewMode ? "" : "地號"}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>地號</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("landNumber") || "-"}
                            </div>
                          </div>
                        ) : (
                          <Input
                            disabled={isViewMode}
                            placeholder="請輸入地號"
                          />
                        )}
                      </Form.Item>
                    </div>
                  </div>
                )}

                {/* 報廢相關欄位 - 只有在已報廢或報廢後堪用狀態才顯示 */}
                {isDiscarded() && (
                  <div className="discard-fields">
                    <Divider style={{ margin: "16px 0" }} />
                    <Title level={5}>報廢資訊</Title>
                    <div
                      style={{
                        display: "grid",
                        gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                        columnGap: "16px",
                      }}
                    >
                      <Form.Item
                        name="discardReason"
                        label={isViewMode ? "" : "報廢原因"}
                        rules={[{ required: true, message: "請輸入報廢原因" }]}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <div style={ViewModeStyle.label}>報廢原因</div>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("scrapReason") || "-"}
                            </div>
                          </div>
                        ) : (
                          <Input.TextArea
                            disabled={isViewMode}
                            placeholder="請輸入報廢原因"
                            autoSize={{ minRows: 2, maxRows: 4 }}
                          />
                        )}
                      </Form.Item>

                      <Form.Item
                        name="discardDate"
                        label={isViewMode ? "" : "報廢日期"}
                        rules={[{ required: true, message: "請選擇報廢日期" }]}
                      >
                        {isViewMode ? (
                          <div style={ViewModeStyle.standard}>
                            <span style={ViewModeStyle.label}>報廢日期</span>
                            <div style={ViewModeStyle.content}>
                              {form.getFieldValue("discardDate")
                                ? dayjs.isDayjs(
                                    form.getFieldValue("discardDate")
                                  )
                                  ? form
                                      .getFieldValue("discardDate")
                                      .format("YYYY-MM-DD")
                                  : form.getFieldValue("discardDate")
                                : "-"}
                            </div>
                          </div>
                        ) : (
                          <DatePicker
                            disabled={isViewMode}
                            placeholder="請選擇報廢日期"
                            style={{ width: "100%" }}
                            format="YYYY-MM-DD"
                          />
                        )}
                      </Form.Item>
                    </div>
                  </div>
                )}

                <FormButtons
                  onCancel={handleFormCancel}
                  isViewMode={isViewMode}
                  isMobile={isMobile}
                  form={form}
                />
              </Form>
            </div>
          )}

          {/* 財務資訊 */}
          {activeTab === "2" && (
            <div
              role="tabpanel"
              aria-labelledby="tab-finance"
              style={{ paddingTop: "10px" }}
            >
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={formInitialValues}
              >
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr 1fr",
                    columnGap: "16px",
                  }}
                >
                  <Form.Item
                    name="purchaseAmount"
                    label={isViewMode ? "" : "購入金額"}
                    rules={[{ required: true, message: "請輸入購入金額" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <div style={ViewModeStyle.label}>購入金額</div>
                        <div style={ViewModeStyle.content}>
                          {(() => {
                            const value = form.getFieldValue("purchaseAmount");
                            return value !== null &&
                              value !== undefined &&
                              value !== ""
                              ? formatThousands(value)
                              : formatThousands("0");
                          })()}
                        </div>
                      </div>
                    ) : (
                      <InputNumber
                        disabled={isViewMode || !!editingAsset}
                        placeholder="請輸入購入金額"
                        min={0}
                        style={{ width: "100%" }}
                        formatter={formatThousands}
                        parser={parseAmount}
                        onChange={handlePurchaseAmountChange}
                      />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="subsidyAmount"
                    label={isViewMode ? "" : "補助金額"}
                    rules={[{ required: true, message: "請輸入補助金額" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <div style={ViewModeStyle.label}>補助金額</div>
                        <div style={ViewModeStyle.content}>
                          {(() => {
                            const value = form.getFieldValue("subsidyAmount");
                            return value !== null &&
                              value !== undefined &&
                              value !== ""
                              ? formatThousands(value)
                              : formatThousands("0");
                          })()}
                        </div>
                      </div>
                    ) : (
                      <InputNumber
                        disabled={isViewMode || !!editingAsset}
                        placeholder="請輸入補助金額"
                        min={0}
                        style={{ width: "100%" }}
                        formatter={formatThousands}
                        parser={parseAmount}
                        onChange={handleSubsidyAmountChange}
                      />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="estimatedResidualValue"
                    label={isViewMode ? "" : "估計殘值"}
                    rules={[{ required: true, message: "請輸入估計殘值" }]}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <div style={ViewModeStyle.label}>估計殘值</div>
                        <div style={ViewModeStyle.content}>
                          {(() => {
                            const value = form.getFieldValue(
                              "estimatedResidualValue"
                            );
                            return value !== null &&
                              value !== undefined &&
                              value !== ""
                              ? formatThousands(value)
                              : formatThousands("0");
                          })()}
                        </div>
                      </div>
                    ) : (
                      <InputNumber
                        disabled={isViewMode || !!editingAsset}
                        placeholder="請輸入估計殘值"
                        min={0}
                        style={{ width: "100%" }}
                        formatter={formatThousands}
                        parser={parseAmount}
                      />
                    )}
                  </Form.Item>
                </div>

                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                    columnGap: "16px",
                  }}
                >
                  <Form.Item
                    name="depreciationAmount"
                    label={isViewMode ? "" : "折舊金額"}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <div style={ViewModeStyle.label}>折舊金額</div>
                        <div style={ViewModeStyle.content}>
                          {(() => {
                            const value =
                              form.getFieldValue("depreciationAmount");
                            return value !== null &&
                              value !== undefined &&
                              value !== ""
                              ? formatThousands(value)
                              : formatThousands("0");
                          })()}
                        </div>
                      </div>
                    ) : (
                      <InputNumber
                        disabled={isViewMode || !!editingAsset}
                        placeholder="請輸入折舊金額"
                        min={0}
                        style={{ width: "100%" }}
                        formatter={formatThousands}
                        parser={parseAmount}
                      />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="deductSubsidy"
                    label={isViewMode ? "" : "扣除補助金額"}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <div style={ViewModeStyle.label}>扣除補助金額</div>
                        <div style={ViewModeStyle.content}>
                          {deductSubsidy ? "是" : "否"}
                        </div>
                      </div>
                    ) : (
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "8px",
                        }}
                      >
                        <Switch
                          checkedChildren="扣除補助金額"
                          unCheckedChildren="不扣除補助金額"
                          checked={deductSubsidy}
                          onChange={handleDeductSubsidyChange}
                          disabled={isViewMode || !!editingAsset}
                          size="small"
                        />
                        <Tooltip
                          title={
                            deductSubsidy
                              ? "計算時扣除補助金額"
                              : "計算時不扣除補助金額"
                          }
                        >
                          <span style={{ color: "#666", fontSize: "12px" }}>
                            {deductSubsidy
                              ? "計算時扣除補助金額"
                              : "計算時不扣除補助金額"}
                          </span>
                        </Tooltip>
                      </div>
                    )}
                  </Form.Item>
                </div>

                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                    columnGap: "16px",
                  }}
                >
                  <Form.Item
                    name="accumulatedDepreciationAmount"
                    label={isViewMode ? "" : "累計折舊金額"}
                    initialValue={0}
                  >
                    {isViewMode ? (
                      <div style={ViewModeStyle.standard}>
                        <div style={ViewModeStyle.label}>累計折舊金額</div>
                        <div style={ViewModeStyle.content}>
                          {(() => {
                            const value = form.getFieldValue(
                              "accumulatedDepreciationAmount"
                            );
                            return value !== null &&
                              value !== undefined &&
                              value !== ""
                              ? formatThousands(value)
                              : formatThousands("0");
                          })()}
                        </div>
                      </div>
                    ) : (
                      <InputNumber
                        disabled={true}
                        min={0}
                        style={{ width: "100%" }}
                        formatter={formatThousands}
                        parser={parseAmount}
                      />
                    )}
                  </Form.Item>
                </div>

                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                    columnGap: "16px",
                  }}
                >
                  <Form.Item
                    name="assetSourceIds"
                    label={isViewMode ? "" : "財產來源"}
                  >
                    {isViewMode ? (
                      <div>
                        <div style={ViewModeStyle.label}>財產來源</div>
                        <Descriptions size="small" bordered column={1}>
                          {selectedAssetSources.map((sourceId, index) => {
                            const source = localAssetSources.find(
                              (s) => s.assetSourceId === sourceId
                            );
                            return source ? (
                              <Descriptions.Item
                                key={sourceId}
                                label={`${index + 1}.`}
                              >
                                {source.assetSourceName}
                              </Descriptions.Item>
                            ) : null;
                          })}
                        </Descriptions>
                      </div>
                    ) : (
                      <Select
                        mode="multiple"
                        placeholder="請選擇財產來源"
                        style={{ width: "100%" }}
                        onChange={handleAssetSourceChange}
                        value={selectedAssetSources}
                        popupRender={(menu: React.ReactNode) => (
                          <>
                            {menu}
                            {!isViewMode && (
                              <>
                                <Divider style={{ margin: "8px 0" }} />
                                <Space style={{ padding: "0 8px 4px" }}>
                                  {isAddingAssetSource ? (
                                    <>
                                      <Input
                                        ref={assetSourceInputRef}
                                        value={newAssetSourceName}
                                        onChange={(e) =>
                                          setNewAssetSourceName(e.target.value)
                                        }
                                        placeholder="輸入新財產來源名稱"
                                        style={{ width: "200px" }}
                                      />
                                      <Button
                                        type="text"
                                        icon={<PlusOutlined />}
                                        onClick={handleAddAssetSource}
                                      >
                                        新增
                                      </Button>
                                    </>
                                  ) : (
                                    <a
                                      onClick={addItem}
                                      style={{ whiteSpace: "nowrap" }}
                                    >
                                      <PlusOutlined /> 新增財產來源
                                    </a>
                                  )}
                                </Space>
                              </>
                            )}
                          </>
                        )}
                        options={localAssetSources.map((source) => ({
                          value: source.assetSourceId,
                          label: source.assetSourceName,
                        }))}
                      />
                    )}
                  </Form.Item>

                  <Form.Item
                    name="amortizationSourceIds"
                    label={isViewMode ? "" : "攤提來源"}
                  >
                    {isViewMode ? (
                      <div>
                        <div style={ViewModeStyle.label}>攤提來源</div>
                        <Descriptions size="small" bordered column={1}>
                          {selectedAmortizationSources.map(
                            (sourceId, index) => {
                              const source = localAmortizationSources.find(
                                (s) => s.amortizationSourceId === sourceId
                              );
                              return source ? (
                                <Descriptions.Item
                                  key={sourceId}
                                  label={`${index + 1}.`}
                                >
                                  {source.sourceName}
                                  {source.description && (
                                    <div
                                      style={{
                                        fontSize: "12px",
                                        color: "#888",
                                      }}
                                    >
                                      {source.description}
                                    </div>
                                  )}
                                  {source.amount > 0 && (
                                    <div
                                      style={{
                                        textAlign: "right",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      {formatThousands(source.amount)}
                                    </div>
                                  )}
                                </Descriptions.Item>
                              ) : null;
                            }
                          )}
                        </Descriptions>
                      </div>
                    ) : (
                      <Select
                        mode="multiple"
                        placeholder="請選擇攤提來源"
                        style={{ width: "100%" }}
                        onChange={handleAmortizationSourceChange}
                        value={selectedAmortizationSources}
                        popupRender={(menu: React.ReactNode) => (
                          <>
                            {menu}
                            {!isViewMode && (
                              <>
                                <Divider style={{ margin: "8px 0" }} />
                                {isAddingAmortizationSource ? (
                                  <div style={{ padding: "0 8px 8px" }}>
                                    <Space
                                      direction="vertical"
                                      style={{ width: "100%" }}
                                    >
                                      <Input
                                        ref={amortizationSourceInputRef}
                                        value={newAmortizationSourceName}
                                        onChange={(e) =>
                                          setNewAmortizationSourceName(
                                            e.target.value
                                          )
                                        }
                                        placeholder="輸入新攤提來源名稱"
                                      />
                                      <Input.TextArea
                                        value={newAmortizationSourceDescription}
                                        onChange={(e) =>
                                          setNewAmortizationSourceDescription(
                                            e.target.value
                                          )
                                        }
                                        placeholder="輸入說明"
                                        rows={2}
                                      />
                                      <InputNumber
                                        value={newAmortizationSourceAmount}
                                        onChange={(value) =>
                                          setNewAmortizationSourceAmount(
                                            Number(value) || 0
                                          )
                                        }
                                        placeholder="輸入金額"
                                        style={{ width: "100%" }}
                                        formatter={formatThousands}
                                        parser={parseAmount}
                                      />
                                      <Button
                                        type="primary"
                                        onClick={handleAddAmortizationSource}
                                      >
                                        新增攤提來源
                                      </Button>
                                    </Space>
                                  </div>
                                ) : (
                                  <Space style={{ padding: "0 8px 4px" }}>
                                    <a
                                      onClick={addAmortizationItem}
                                      style={{ whiteSpace: "nowrap" }}
                                    >
                                      <PlusOutlined /> 新增攤提來源
                                    </a>
                                  </Space>
                                )}
                              </>
                            )}
                          </>
                        )}
                        options={localAmortizationSources.map((source) => ({
                          value: source.amortizationSourceId,
                          label: source.sourceName,
                        }))}
                      />
                    )}
                  </Form.Item>
                </div>

                <FormButtons
                  onCancel={handleFormCancel}
                  isViewMode={isViewMode}
                  isMobile={isMobile}
                  form={form}
                />
              </Form>
            </div>
          )}

          {/* 附屬設備 */}
          {activeTab === "3" && (
            <div
              role="tabpanel"
              aria-labelledby="tab-accessory"
              style={{ paddingTop: "10px" }}
            >
              <Form form={accessoryForm} style={{ display: "none" }} />
              {!isViewMode && (
                <div style={{ marginBottom: 16 }}>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleAddAccessory}
                  >
                    新增附屬設備
                  </Button>
                </div>
              )}
              <Table
                dataSource={accessoryEquipments}
                rowKey="accessoryEquipmentId"
                pagination={false}
                size="small"
                columns={[
                  {
                    title: "設備編號",
                    dataIndex: "equipmentNo",
                    key: "equipmentNo",
                    render: (text, record) => (
                      <Tooltip
                        title={
                          <div style={{ padding: "8px" }}>
                            <Descriptions size="small" column={1} bordered>
                              <Descriptions.Item label="設備編號">
                                {record.equipmentNo}
                              </Descriptions.Item>
                              <Descriptions.Item label="設備名稱">
                                {record.equipmentName}
                              </Descriptions.Item>
                              <Descriptions.Item label="規格/型號">
                                {record.specification || "-"}
                              </Descriptions.Item>
                              <Descriptions.Item label="購入日期">
                                {DateTimeExtensions.formatDateFromTimestamp(
                                  record.purchaseDate
                                )}
                              </Descriptions.Item>
                              <Descriptions.Item label="購入價格">
                                {formatTWCurrency(record.purchasePrice)}
                              </Descriptions.Item>
                              <Descriptions.Item label="備註">
                                {record.remarks || "-"}
                              </Descriptions.Item>
                            </Descriptions>
                          </div>
                        }
                        color="#fff"
                        placement="right"
                        styles={{
                          root: { maxWidth: "400px" },
                          body: { padding: "0" },
                        }}
                      >
                        <span style={{ cursor: "pointer", color: "#1890ff" }}>
                          {text}
                        </span>
                      </Tooltip>
                    ),
                  },
                  {
                    title: "設備名稱",
                    dataIndex: "equipmentName",
                    key: "equipmentName",
                  },
                  {
                    title: "規格/型號",
                    dataIndex: "specification",
                    key: "specification",
                  },
                  {
                    title: "購入日期",
                    dataIndex: "purchaseDate",
                    key: "purchaseDate",
                    render: (text) => (
                      <span>
                        {DateTimeExtensions.formatDateFromTimestamp(text)}
                      </span>
                    ),
                  },
                  {
                    title: "購入價格",
                    dataIndex: "purchasePrice",
                    key: "purchasePrice",
                    render: (text) => (
                      <Tag color="red">{formatTWCurrency(text)}</Tag>
                    ),
                  },
                  {
                    title: "使用狀態",
                    dataIndex: "assetStatusId",
                    key: "assetStatusId",
                    render: (statusId: string) => {
                      const status = assetStatusOptions.find(
                        (s) => s.assetStatusId === statusId
                      );
                      if (!status) return <Tag>未知狀態</Tag>;

                      const statusName = status.name;
                      const color = STATUS_COLORS[statusName] || "default";
                      return <Tag color={color}>{statusName}</Tag>;
                    },
                  },
                  {
                    title: "操作",
                    key: "action",
                    render: (_, record) =>
                      !isViewMode && (
                        <Space size="small">
                          <Button
                            type="link"
                            size="small"
                            icon={<EditOutlined />}
                            onClick={() => handleEditAccessory(record)}
                          >
                            編輯
                          </Button>
                          <Button
                            type="link"
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() =>
                              handleDeleteAccessory(record.accessoryEquipmentId)
                            }
                          >
                            刪除
                          </Button>
                        </Space>
                      ),
                  },
                ]}
              />

              {/* 新增/編輯附屬設備的彈出表單 */}
              <Modal
                title={editingAccessory ? "編輯附屬設備" : "新增附屬設備"}
                open={isAccessoryModalVisible}
                onOk={accessoryForm.submit}
                onCancel={() => {
                  setIsAccessoryModalVisible(false);
                  accessoryForm.resetFields();
                  setEditingAccessory(null);
                }}
                okText="確認"
                cancelText="取消"
                width={isMobile ? "100%" : 700}
                style={isMobile ? { top: 0, margin: 0, maxWidth: "100vw" } : {}}
                styles={{
                  body: isMobile
                    ? {
                        padding: "12px",
                        maxHeight: "80vh",
                        overflowY: "auto",
                      }
                    : {},
                }}
                destroyOnClose={true}
              >
                <Form
                  form={accessoryForm}
                  layout="vertical"
                  onFinish={handleAccessorySubmit}
                  initialValues={{
                    equipmentType: "",
                    specification: "",
                    purchaseDate: null,
                    purchasePrice: 0,
                    usageStatus: assetStatusOptions[0]?.assetStatusId || "",
                    remarks: "",
                  }}
                >
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: "1fr 1fr",
                      columnGap: "16px",
                    }}
                  >
                    <Form.Item
                      name="equipmentNo"
                      label="設備編號"
                      rules={[{ required: true, message: "請輸入設備編號" }]}
                    >
                      <Input placeholder="請輸入設備編號" />
                    </Form.Item>

                    <Form.Item
                      name="equipmentName"
                      label="設備名稱"
                      rules={[{ required: true, message: "請輸入設備名稱" }]}
                    >
                      <Input placeholder="請輸入設備名稱" />
                    </Form.Item>
                  </div>

                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: "1fr 1fr",
                      columnGap: "16px",
                    }}
                  >
                    <Form.Item
                      name="equipmentType"
                      label="設備類型"
                      rules={[{ required: true, message: "請選擇設備類型" }]}
                    >
                      <Select placeholder="請選擇設備類型">
                        {equipmentTypes.map((type) => (
                          <Select.Option
                            key={type.equipmentTypeId}
                            value={type.name}
                          >
                            {type.name}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>

                    <Form.Item
                      name="specification"
                      label="規格/型號"
                      rules={[{ required: true, message: "請輸入規格/型號" }]}
                    >
                      <Input placeholder="請輸入規格/型號" />
                    </Form.Item>
                  </div>

                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: "1fr 1fr",
                      columnGap: "16px",
                    }}
                  >
                    <Form.Item
                      name="purchaseDate"
                      label="購入日期"
                      rules={[{ required: true, message: "請選擇購入日期" }]}
                    >
                      <DatePicker
                        placeholder="請選擇購入日期"
                        style={{ width: "100%" }}
                        format="YYYY-MM-DD"
                      />
                    </Form.Item>

                    <Form.Item
                      name="purchasePrice"
                      label="購入價格"
                      rules={[{ required: true, message: "請輸入購入價格" }]}
                    >
                      <InputNumber
                        placeholder="請輸入購入價格"
                        min={0}
                        style={{ width: "100%" }}
                        formatter={formatThousands}
                        parser={parseAmount}
                      />
                    </Form.Item>
                  </div>

                  <Form.Item
                    name="usageStatus"
                    label="使用狀態"
                    rules={[{ required: true, message: "請選擇使用狀態" }]}
                  >
                    <Select placeholder="請選擇使用狀態">
                      {assetStatusOptions.map((status) => (
                        <Select.Option
                          key={status.assetStatusId}
                          value={status.assetStatusId}
                        >
                          {status.name}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item name="remarks" label="備註">
                    <Input.TextArea
                      placeholder="請輸入備註"
                      maxLength={100}
                      autoSize={{ minRows: 3, maxRows: 6 }}
                      showCount
                    />
                  </Form.Item>
                </Form>
              </Modal>

              {/* 刪除附屬設備確認對話框 */}
              <Modal
                title="確認刪除附屬設備"
                open={deleteAccessoryModalVisible}
                onCancel={() => {
                  setDeleteAccessoryModalVisible(false);
                  setAccessoryToDelete(null);
                  setDeleteAccessoryConfirmText("");
                }}
                onOk={executeDeleteAccessory}
                okText="確認刪除"
                cancelText="取消"
                okButtonProps={{
                  danger: true,
                  disabled:
                    deleteAccessoryConfirmText !==
                    (accessoryToDelete?.equipmentName || ""),
                }}
              >
                <div>
                  <p>
                    請輸入
                    <strong>「{accessoryToDelete?.equipmentName}」</strong>
                    以確認刪除：
                  </p>
                  <Input
                    placeholder="請輸入附屬設備名稱"
                    value={deleteAccessoryConfirmText}
                    onChange={(e) =>
                      setDeleteAccessoryConfirmText(e.target.value)
                    }
                  />
                </div>
              </Modal>

              {/* 添加主表單提交按鈕，確保在附屬設備頁面也能提交完整資料 */}
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={formInitialValues}
                style={{ marginTop: "20px" }}
              >
                <FormButtons
                  onCancel={handleFormCancel}
                  isViewMode={isViewMode}
                  isMobile={isMobile}
                  form={form}
                />
              </Form>
            </div>
          )}

          {/* 保險單位 */}
          {activeTab === "4" && (
            <div
              role="tabpanel"
              aria-labelledby="tab-insurance"
              style={{ paddingTop: "10px" }}
            >
              <Form form={insuranceForm} style={{ display: "none" }} />
              {!isViewMode && (
                <div style={{ marginBottom: 16 }}>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleAddInsurance}
                  >
                    新增保險單位
                  </Button>
                </div>
              )}
              <Table
                dataSource={insuranceUnits}
                rowKey="insuranceUnitId"
                pagination={false}
                size="small"
                columns={[
                  {
                    title: "單位名稱",
                    dataIndex: "name",
                    key: "name",
                    render: (text, record) => (
                      <Tooltip
                        title={
                          <div style={{ padding: "8px" }}>
                            <Descriptions size="small" column={1} bordered>
                              <Descriptions.Item label="保險單位">
                                {record.name}
                              </Descriptions.Item>
                              <Descriptions.Item label="統一編號">
                                {record.companyNo}
                              </Descriptions.Item>
                              <Descriptions.Item label="聯絡人">
                                {record.contactPerson}
                              </Descriptions.Item>
                              <Descriptions.Item label="聯絡電話">
                                {record.contactPhone}
                              </Descriptions.Item>
                              <Descriptions.Item label="地址">
                                {record.address}
                              </Descriptions.Item>
                              <Descriptions.Item label="備註">
                                {record.description}
                              </Descriptions.Item>
                            </Descriptions>
                          </div>
                        }
                        color="#fff"
                        placement="right"
                        styles={{
                          root: { maxWidth: "400px" },
                          body: { padding: "0" },
                        }}
                      >
                        <span style={{ cursor: "pointer", color: "#1890ff" }}>
                          {text}
                        </span>
                      </Tooltip>
                    ),
                  },
                  {
                    title: "聯絡人",
                    dataIndex: "contactPerson",
                    key: "contactPerson",
                  },
                  {
                    title: "投保金額",
                    dataIndex: "insuranceAmount",
                    key: "insuranceAmount",
                    render: (text) => (
                      <Tag color="blue">{formatTWCurrency(text)}</Tag>
                    ),
                  },
                  {
                    title: "投保期間",
                    key: "insurancePeriod",
                    render: (_, record: any) => (
                      <span>
                        {record.insuranceStartDate
                          ? DateTimeExtensions.formatDateFromTimestamp(
                              record.insuranceStartDate
                            )
                          : "-"}
                        {" ~ "}
                        {record.insuranceExpiryDate
                          ? DateTimeExtensions.formatDateFromTimestamp(
                              record.insuranceExpiryDate
                            )
                          : "-"}
                      </span>
                    ),
                  },
                  {
                    title: "投保狀態",
                    key: "insuranceStatus",
                    render: (_, record: any) => {
                      const now = Math.floor(Date.now() / 1000); // 轉換為 Unix timestamp
                      const startDate = record.insuranceStartDate;
                      const expiryDate = record.insuranceExpiryDate;

                      let status = "";

                      if (!startDate || !expiryDate) {
                        status = "未知";
                      } else if (now < startDate) {
                        status = "未開始";
                      } else if (now > expiryDate) {
                        status = "已到期";
                      } else {
                        status = "投保中";
                      }

                      const color = INSURANCE_STATUS_COLORS[status];
                      return <Tag color={color}>{status}</Tag>;
                    },
                  },
                  {
                    title: "操作",
                    key: "action",
                    render: (_, record) =>
                      !isViewMode && (
                        <Space size="small">
                          <Button
                            type="link"
                            size="small"
                            icon={<EditOutlined />}
                            onClick={() => handleEditInsurance(record)}
                          >
                            編輯
                          </Button>
                          <Button
                            type="link"
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() =>
                              handleDeleteInsurance(record.insuranceUnitId)
                            }
                          >
                            刪除
                          </Button>
                        </Space>
                      ),
                  },
                ]}
              />

              {/* 新增/編輯保險單位的彈出表單 */}
              <Modal
                title={editingInsurance ? "編輯保險單位" : "新增保險單位"}
                open={isInsuranceModalVisible}
                onOk={insuranceForm.submit}
                onCancel={() => {
                  setIsInsuranceModalVisible(false);
                  insuranceForm.resetFields();
                  setEditingInsurance(null);
                }}
                okText="確認"
                cancelText="取消"
                width={isMobile ? "100%" : 700}
                style={isMobile ? { top: 0, margin: 0, maxWidth: "100vw" } : {}}
                styles={{
                  body: isMobile
                    ? {
                        padding: "12px",
                        maxHeight: "80vh",
                        overflowY: "auto",
                      }
                    : {},
                }}
                destroyOnClose={true}
              >
                <Form
                  form={insuranceForm}
                  layout="vertical"
                  onFinish={handleInsuranceSubmit}
                  initialValues={{
                    name: "",
                    companyNo: "",
                    contactPerson: "",
                    contactPhone: "",
                    insuranceAmount: 0,
                    insuranceStartDate: null,
                    insuranceExpiryDate: null,
                    contactEmail: "",
                    website: "",
                    address: "",
                    description: "",
                  }}
                >
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                      columnGap: "16px",
                    }}
                  >
                    <Form.Item
                      name="name"
                      label="保險單位名稱"
                      rules={[
                        { required: true, message: "請輸入保險單位名稱" },
                      ]}
                    >
                      <Input placeholder="請輸入保險單位名稱" />
                    </Form.Item>

                    <Form.Item
                      name="companyNo"
                      label="公司統編"
                      rules={[{ required: true, message: "請輸入公司統編" }]}
                    >
                      <Input placeholder="請輸入公司統編" />
                    </Form.Item>
                  </div>

                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                      columnGap: "16px",
                    }}
                  >
                    <Form.Item
                      name="contactPerson"
                      label="聯絡人"
                      rules={[{ required: true, message: "請輸入聯絡人" }]}
                    >
                      <Input placeholder="請輸入聯絡人" />
                    </Form.Item>

                    <Form.Item
                      name="contactPhone"
                      label="聯絡電話"
                      rules={[{ required: true, message: "請輸入聯絡電話" }]}
                    >
                      <Input placeholder="請輸入聯絡電話" />
                    </Form.Item>
                  </div>

                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                      columnGap: "16px",
                    }}
                  >
                    <Form.Item
                      name="insuranceAmount"
                      label="投保金額"
                      rules={[{ required: true, message: "請輸入投保金額" }]}
                    >
                      <InputNumber
                        placeholder="請輸入投保金額"
                        min={0}
                        style={{ width: "100%" }}
                        formatter={formatThousands}
                        parser={parseAmount}
                      />
                    </Form.Item>
                  </div>

                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
                      columnGap: "16px",
                    }}
                  >
                    <Form.Item
                      name="insuranceStartDate"
                      label="投保起日"
                      rules={[{ required: true, message: "請選擇投保起日" }]}
                    >
                      <DatePicker
                        placeholder="請選擇投保起日"
                        style={{ width: "100%" }}
                        format="YYYY-MM-DD"
                      />
                    </Form.Item>

                    <Form.Item
                      name="insuranceExpiryDate"
                      label="投保迄日"
                      rules={[{ required: true, message: "請選擇投保迄日" }]}
                    >
                      <DatePicker
                        placeholder="請選擇投保迄日"
                        style={{ width: "100%" }}
                        format="YYYY-MM-DD"
                      />
                    </Form.Item>
                  </div>

                  <Form.Item name="contactEmail" label="電子郵件">
                    <Input placeholder="請輸入電子郵件" />
                  </Form.Item>

                  <Form.Item name="website" label="網站">
                    <Input placeholder="請輸入網站" />
                  </Form.Item>

                  <Form.Item name="address" label="地址">
                    <Input placeholder="請輸入地址" />
                  </Form.Item>

                  <Form.Item name="description" label="說明">
                    <Input.TextArea
                      placeholder="請輸入說明"
                      maxLength={200}
                      autoSize={{ minRows: 3, maxRows: 6 }}
                      showCount
                    />
                  </Form.Item>
                </Form>
              </Modal>

              {/* 刪除保險單位確認對話框 */}
              <Modal
                title="確認刪除保險單位"
                open={deleteInsuranceModalVisible}
                onCancel={() => {
                  setDeleteInsuranceModalVisible(false);
                  setInsuranceToDelete(null);
                  setDeleteInsuranceConfirmText("");
                }}
                onOk={executeDeleteInsurance}
                okText="確認刪除"
                cancelText="取消"
                okButtonProps={{
                  danger: true,
                  disabled:
                    deleteInsuranceConfirmText !==
                    (insuranceToDelete?.name || ""),
                }}
              >
                <div>
                  <p>
                    請輸入<strong>「{insuranceToDelete?.name}」</strong>
                    以確認刪除：
                  </p>
                  <Input
                    placeholder="請輸入保險公司名稱"
                    value={deleteInsuranceConfirmText}
                    onChange={(e) =>
                      setDeleteInsuranceConfirmText(e.target.value)
                    }
                  />
                </div>
              </Modal>

              {/* 添加主表單提交按鈕，確保在保險單位頁面也能提交完整資料 */}
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={formInitialValues}
                style={{ marginTop: "20px" }}
              >
                <FormButtons
                  onCancel={handleFormCancel}
                  isViewMode={isViewMode}
                  isMobile={isMobile}
                  form={form}
                />
              </Form>
            </div>
          )}
        </div>
      </div>

      {/* 確認提交模態框 */}
      <Modal
        title={editingAsset ? "確認修改固定資產" : "確認新增固定資產"}
        open={isConfirmModalVisible}
        onCancel={() => setIsConfirmModalVisible(false)}
        onOk={handleConfirmSubmit}
        width={800}
        okText="確認"
        cancelText="返回修改"
      >
        {confirmData && (
          <div style={{ maxHeight: "70vh", overflowY: "auto" }}>
            {/* 基本資料 */}
            <Descriptions title="基本資料" bordered size="small">
              <Descriptions.Item label="財產編號">
                {confirmData.asset.assetNo || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="財產名稱">
                {confirmData.asset.assetName || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="財產簡稱">
                {confirmData.asset.assetShortName || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="規格">
                {confirmData.asset.specification || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="自訂財產編號一">
                {confirmData.asset.customAssetNo1 || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="自訂財產編號二">
                {confirmData.asset.customAssetNo2 || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="財產類別">
                {assetCategories.find(
                  (category) =>
                    category.assetCategoryId ===
                    confirmData.asset.assetCategoryId
                )?.assetCategoryName || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="財產狀態">
                <Tag
                  color={
                    STATUS_COLORS[
                      assetStatusOptions.find(
                        (status) =>
                          status.assetStatusId ===
                          confirmData.asset.assetStatusId
                      )?.name || "default"
                    ]
                  }
                >
                  {assetStatusOptions.find(
                    (status) =>
                      status.assetStatusId === confirmData.asset.assetStatusId
                  )?.name || "-"}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="部門">
                {departments.find(
                  (dept) => dept.departmentId === confirmData.asset.departmentId
                )?.name || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="組別">
                {divisions.find(
                  (div) => div.divisionId === confirmData.asset.divisionId
                )?.name || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="財產科目">
                {assetAccounts.find(
                  (account) =>
                    account.assetAccountId === confirmData.asset.assetAccountId
                )?.assetAccountName || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="財產子目">
                {assetSubAccounts.find(
                  (subAccount) =>
                    subAccount.assetSubAccountId ===
                    confirmData.asset.assetSubAccountId
                )?.assetSubAccountName || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="數量" span={1}>
                {confirmData.asset.quantity}{" "}
                {units.find((unit) => unit.unitId === confirmData.asset.unitId)
                  ?.name || ""}
              </Descriptions.Item>
              <Descriptions.Item label="取得日期" span={1}>
                {confirmData.asset.acquisitionDate
                  ? dayjs
                      .unix(confirmData.asset.acquisitionDate)
                      .format("YYYY-MM-DD")
                  : "-"}
              </Descriptions.Item>
              <Descriptions.Item label="使用用途">
                {confirmData.asset.usage || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="廠牌型號">
                {localManufacturers.find(
                  (m) => m.manufacturerId === confirmData.asset.manufacturerId
                )?.name || "-"}
                {localManufacturers.find(
                  (m) => m.manufacturerId === confirmData.asset.manufacturerId
                )?.model
                  ? `(${
                      localManufacturers.find(
                        (m) =>
                          m.manufacturerId === confirmData.asset.manufacturerId
                      )?.model
                    })`
                  : ""}
              </Descriptions.Item>
              <Descriptions.Item label="設備類型">
                {localEquipmentTypes.find(
                  (type) =>
                    type.equipmentTypeId === confirmData.asset.equipmentTypeId
                )?.name || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="保管人">
                {custodians.find(
                  (user) => user.userId === confirmData.asset.custodianId
                )?.name || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="使用人">
                {assetUsers.find(
                  (user) => user.userId === confirmData.asset.userId
                )?.name || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="存放地點">
                {localStorageLocations.find(
                  (location) =>
                    location.storageLocationId ===
                    confirmData.asset.storageLocationId
                )?.name || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="耐用年限">
                {confirmData.asset.serviceLife
                  ? `${confirmData.asset.serviceLife} 年`
                  : "-"}
              </Descriptions.Item>
              <Descriptions.Item label="保固年限">
                {confirmData.asset.insurancePeriod
                  ? `${confirmData.asset.insurancePeriod} 年`
                  : "-"}
              </Descriptions.Item>
              <Descriptions.Item label="預計報廢年度">
                {confirmData.asset.estimatedScrapYear || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="備註">
                {confirmData.asset.notes || "-"}
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Descriptions
              title="財務資訊"
              bordered
              size="small"
              style={{ marginTop: 16 }}
            >
              <Descriptions.Item label="購入金額">
                {formatTWCurrency(confirmData.asset.purchaseAmount)}
              </Descriptions.Item>
              <Descriptions.Item label="補助金額">
                {formatTWCurrency(confirmData.asset.subsidyAmount)}
              </Descriptions.Item>
              <Descriptions.Item label="折舊金額">
                {formatTWCurrency(confirmData.asset.depreciationAmount)}
              </Descriptions.Item>
              <Descriptions.Item label="折舊後殘值">
                {formatTWCurrency(confirmData.asset.estimatedResidualValue)}
              </Descriptions.Item>
              <Descriptions.Item label="累計折舊">
                {formatTWCurrency(
                  confirmData.asset.accumulatedDepreciationAmount
                )}
              </Descriptions.Item>
            </Descriptions>

            {/* 財產來源資訊 */}
            {confirmData.assetSources &&
              confirmData.assetSources.length > 0 && (
                <>
                  <Divider />
                  <Title level={5}>財產來源</Title>
                  <Table
                    dataSource={confirmData.assetSources}
                    columns={[
                      {
                        title: "財產來源名稱",
                        dataIndex: "assetSourceName",
                        key: "assetSourceName",
                      },
                    ]}
                    pagination={false}
                    size="small"
                    rowKey="assetSourceId"
                  />
                </>
              )}

            {/* 攤提來源資訊 */}
            {confirmData.amortizationSources &&
              confirmData.amortizationSources.length > 0 && (
                <>
                  <Divider />
                  <Title level={5}>攤提來源</Title>
                  <Table
                    dataSource={confirmData.amortizationSources}
                    columns={[
                      {
                        title: "來源名稱",
                        dataIndex: "sourceName",
                        key: "sourceName",
                      },
                      {
                        title: "說明",
                        dataIndex: "description",
                        key: "description",
                        render: (text) => text || "-",
                      },
                      {
                        title: "金額",
                        dataIndex: "amount",
                        key: "amount",
                        render: (amount) =>
                          amount?.toLocaleString("zh-TW", {
                            style: "currency",
                            currency: "TWD",
                          }) || "0",
                      },
                    ]}
                    pagination={false}
                    size="small"
                    rowKey="amortizationSourceId"
                  />
                </>
              )}

            {/* 土地或建築物特有資訊 */}
            {(confirmData.asset.buildingAddress ||
              confirmData.asset.buildingStructure ||
              confirmData.asset.constructionDate ||
              confirmData.asset.floorArea ||
              confirmData.asset.publicArea ||
              confirmData.asset.usageExpiryDate ||
              confirmData.asset.usageLicenseNo ||
              confirmData.asset.buildingTaxItem ||
              (confirmData.asset.publicValue &&
                confirmData.asset.publicValue !== 0) ||
              confirmData.asset.landSection ||
              confirmData.asset.landNumber ||
              confirmData.asset.landArea ||
              (confirmData.asset.certificateNo &&
                confirmData.asset.certificateNo !== "0")) && (
              <>
                <Divider />
                <Descriptions
                  title="土地/建築物資訊"
                  bordered
                  column={{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }}
                  size="small"
                  style={{ marginTop: 16 }}
                >
                  {confirmData.asset.buildingAddress && (
                    <Descriptions.Item label="建物地址">
                      {confirmData.asset.buildingAddress}
                    </Descriptions.Item>
                  )}
                  {confirmData.asset.buildingStructure && (
                    <Descriptions.Item label="建物構造">
                      {confirmData.asset.buildingStructure}
                    </Descriptions.Item>
                  )}
                  {confirmData.asset.constructionDate && (
                    <Descriptions.Item label="興建日期">
                      {dayjs
                        .unix(confirmData.asset.constructionDate)
                        .format("YYYY-MM-DD")}
                    </Descriptions.Item>
                  )}
                  {confirmData.asset.floorArea && (
                    <Descriptions.Item label="面積(m²)">
                      {confirmData.asset.floorArea}
                    </Descriptions.Item>
                  )}
                  {confirmData.asset.publicArea && (
                    <Descriptions.Item label="公設(m²)">
                      {confirmData.asset.publicArea}
                    </Descriptions.Item>
                  )}
                  {confirmData.asset.usageExpiryDate && (
                    <Descriptions.Item label="使用執照日期">
                      {dayjs
                        .unix(confirmData.asset.usageExpiryDate)
                        .format("YYYY-MM-DD")}
                    </Descriptions.Item>
                  )}
                  {confirmData.asset.usageLicenseNo && (
                    <Descriptions.Item label="使用執照號碼">
                      {confirmData.asset.usageLicenseNo}
                    </Descriptions.Item>
                  )}
                  {confirmData.asset.buildingTaxItem && (
                    <Descriptions.Item label="適用房屋稅目">
                      {confirmData.asset.buildingTaxItem}
                    </Descriptions.Item>
                  )}
                  {confirmData.asset.publicValue &&
                    confirmData.asset.publicValue !== 0 && (
                      <Descriptions.Item label="公告現值">
                        {formatThousands(confirmData.asset.publicValue)}
                      </Descriptions.Item>
                    )}
                  {confirmData.asset.certificateNo &&
                    confirmData.asset.certificateNo !== "0" && (
                      <Descriptions.Item label="權狀號碼">
                        {confirmData.asset.certificateNo}
                      </Descriptions.Item>
                    )}
                  {confirmData.asset.landSection && (
                    <Descriptions.Item label="地段">
                      {confirmData.asset.landSection}
                    </Descriptions.Item>
                  )}
                  {confirmData.asset.landNumber && (
                    <Descriptions.Item label="地號">
                      {confirmData.asset.landNumber}
                    </Descriptions.Item>
                  )}
                  {confirmData.asset.landArea && (
                    <Descriptions.Item label="土地面積(m²)">
                      {confirmData.asset.landArea}
                    </Descriptions.Item>
                  )}
                </Descriptions>
              </>
            )}

            {/* 報廢資訊 */}
            {confirmData.asset.scrapDate > 0 && (
              <>
                <Divider />
                <Descriptions
                  title="報廢資訊"
                  bordered
                  size="small"
                  style={{ marginTop: 16 }}
                >
                  <Descriptions.Item label="報廢日期">
                    {dayjs
                      .unix(confirmData.asset.scrapDate)
                      .format("YYYY-MM-DD")}
                  </Descriptions.Item>
                  <Descriptions.Item label="報廢原因">
                    {confirmData.asset.scrapReason || "-"}
                  </Descriptions.Item>
                </Descriptions>
              </>
            )}

            {/* 附屬設備 */}
            <Divider />
            <Title level={5}>
              附屬設備 ({confirmData.accessoryEquipments.length})
            </Title>
            {confirmData.accessoryEquipments.length > 0 ? (
              <Table
                dataSource={confirmData.accessoryEquipments}
                columns={[
                  {
                    title: "設備編號",
                    dataIndex: "equipmentNo",
                    key: "equipmentNo",
                  },
                  {
                    title: "設備名稱",
                    dataIndex: "equipmentName",
                    key: "equipmentName",
                  },
                  {
                    title: "設備類型",
                    dataIndex: "equipmentType",
                    key: "equipmentType",
                  },
                  {
                    title: "規格/型號",
                    dataIndex: "specification",
                    key: "specification",
                  },
                  {
                    title: "購入日期",
                    dataIndex: "purchaseDate",
                    key: "purchaseDate",
                    render: (date) =>
                      date ? dayjs.unix(date).format("YYYY-MM-DD") : "-",
                  },
                  {
                    title: "購入價格",
                    dataIndex: "purchasePrice",
                    key: "purchasePrice",
                    render: (price) => formatTWCurrency(price),
                  },
                  {
                    title: "使用狀態",
                    dataIndex: "usageStatusId",
                    key: "usageStatusId",
                    render: (statusId) => {
                      const status = assetStatusOptions.find(
                        (s) => s.assetStatusId === statusId
                      );
                      return status ? (
                        <Tag color={STATUS_COLORS[status.name] || "default"}>
                          {status.name}
                        </Tag>
                      ) : (
                        "-"
                      );
                    },
                  },
                ]}
                pagination={false}
                size="small"
                rowKey="accessoryEquipmentId"
              />
            ) : (
              <div style={{ textAlign: "center", padding: "20px 0" }}>
                無附屬設備資料
              </div>
            )}

            {/* 保險單位 */}
            <Divider />
            <Title level={5}>
              保險單位 ({confirmData.insuranceUnits.length})
            </Title>
            {confirmData.insuranceUnits.length > 0 ? (
              <Table
                dataSource={confirmData.insuranceUnits}
                columns={[
                  {
                    title: "保險單位",
                    dataIndex: "name",
                    key: "name",
                  },
                  {
                    title: "聯絡人",
                    dataIndex: "contactPerson",
                    key: "contactPerson",
                  },
                  {
                    title: "聯絡電話",
                    dataIndex: "contactPhone",
                    key: "contactPhone",
                  },
                  {
                    title: "投保金額",
                    dataIndex: "insuranceAmount",
                    key: "insuranceAmount",
                    render: (amount) => formatTWCurrency(amount),
                  },
                  {
                    title: "投保期間",
                    key: "insurancePeriod",
                    render: (_, record) => (
                      <>
                        {record.insuranceStartDate
                          ? dayjs
                              .unix(record.insuranceStartDate)
                              .format("YYYY-MM-DD")
                          : ""}{" "}
                        至{" "}
                        {record.insuranceExpiryDate
                          ? dayjs
                              .unix(record.insuranceExpiryDate)
                              .format("YYYY-MM-DD")
                          : ""}
                      </>
                    ),
                  },
                  {
                    title: "投保狀態",
                    key: "insuranceStatus",
                    render: (_, record) => {
                      const now = Math.floor(Date.now() / 1000);
                      const startDate = record.insuranceStartDate;
                      const expiryDate = record.insuranceExpiryDate;

                      let status = "";

                      if (!startDate || !expiryDate) {
                        status = "未知";
                      } else if (now < startDate) {
                        status = "未開始";
                      } else if (now > expiryDate) {
                        status = "已到期";
                      } else {
                        status = "投保中";
                      }

                      return (
                        <Tag color={INSURANCE_STATUS_COLORS[status]}>
                          {status}
                        </Tag>
                      );
                    },
                  },
                ]}
                pagination={false}
                size="small"
                rowKey="insuranceUnitId"
              />
            ) : (
              <div style={{ textAlign: "center", padding: "20px 0" }}>
                無保險單位資料
              </div>
            )}

            <Divider />
            <div
              style={{ textAlign: "right", color: "#ff4d4f", marginTop: 16 }}
            >
              <Typography.Text type="danger">
                請確認以上資料無誤後，點擊「確認」按鈕送出資料。
              </Typography.Text>
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};

export default FixedAssetMaintenanceForm;
