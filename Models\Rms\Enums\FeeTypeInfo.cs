using System.Collections.Generic;
using System.Linq;

namespace FAST_ERP_Backend.Models.Rms.Enums
{
    /// <summary>
    /// 費用類型（物件化定義）
    /// </summary>
    public sealed class FeeTypeInfo
    {
        public string Code { get; }
        public string Name { get; }

        private FeeTypeInfo(string code, string name)
        {
            Code = code;
            Name = name;
        }

        public static readonly FeeTypeInfo Electricity   = new("2", "電費");
        public static readonly FeeTypeInfo Water         = new("3", "水費");
        public static readonly FeeTypeInfo Parking       = new("4", "停車費");
        public static readonly FeeTypeInfo FireInsurance = new("5", "火險費");
        public static readonly FeeTypeInfo Rent          = new("1", "月租費");

        public static IEnumerable<FeeTypeInfo> All => new[] { Electricity, Water, Parking, FireInsurance, Rent };

        public static FeeTypeInfo FromCode(string code) => All.First(x => x.Code == code);
    }
}


