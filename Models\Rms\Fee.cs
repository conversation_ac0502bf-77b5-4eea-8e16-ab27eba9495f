using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
using FAST_ERP_Backend.Attributes;
using System.Collections.Generic;
using FAST_ERP_Backend.Models;

namespace FAST_ERP_Backend.Models.Rms
{
    /// <summary>
    /// 費用資料表
    /// </summary>
    public class Fee : ModelBaseEntity
    {
        [Key]
        [Comment("費用編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string FeeId { get; set; } // 費用編號

        [Comment("合約編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string ContractId { get; set; } // 合約編號 (FK)

        [Comment("費用類型")]
        [Column(TypeName = "nvarchar(50)")]
        public string FeeType { get; set; } // 費用類型（電費/水費/停車費/火險費等）

        [Comment("計費期間(YYYYMM)")]
        [Column(TypeName = "nvarchar(6)")]
        public string BillingPeriod { get; set; } // 計費期間，例如 202501

        [Comment("費用金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } // 費用金額

        [Comment("費用應繳日期")]
        [Column(TypeName = "bigint")]
        public long FeeDate { get; set; } // 費用應繳日期

        [Comment("已繳金額累計")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } // 已繳金額

        [Comment("費用狀態")]
        [Column(TypeName = "nvarchar(50)")]
        public string Status { get; set; } // 費用狀態（未繳(0)/部分已繳(1)/已繳(2)/作廢(9)）

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Note { get; set; } // 備註

        // Navigation Properties
        public virtual Contract Contract { get; set; }
        public virtual ICollection<PaymentAllocation> PaymentAllocations { get; set; }

        public Fee()
        {
            PaymentAllocations = new HashSet<PaymentAllocation>();
        }

    }

    /// <summary>
    /// 費用資料傳輸物件（回應用）
    /// </summary>
    public class FeeDTO : ModelBaseEntityDTO
    {
        /// <summary>
        /// 費用編號
        /// </summary>
        public string FeeId { get; set; }
        
        /// <summary>
        /// 合約編號
        /// </summary>
        public string ContractId { get; set; }
        
        /// <summary>
        /// 費用類型
        /// </summary>
        public string FeeType { get; set; }
        
        /// <summary>
        /// 計費期間
        /// </summary>
        public string BillingPeriod { get; set; }
        
        /// <summary>
        /// 費用金額
        /// </summary>
        public decimal Amount { get; set; }
        
        /// <summary>
        /// 費用應繳日期
        /// </summary>
        public long FeeDate { get; set; }
        
        /// <summary>
        /// 已繳金額
        /// </summary>
        public decimal PaidAmount { get; set; }
        
        /// <summary>
        /// 費用狀態
        /// </summary>
        public string Status { get; set; }
        
        /// <summary>
        /// 備註
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// 初始化費用資料傳輸物件
        /// </summary>
        public FeeDTO()
        {
        }
    }

    

    /// <summary>
    /// 費用新增請求 DTO（API 驗證用）
    /// </summary>
    public class FeeCreateRequestDTO : IValidatableObject
    {
        /// <summary>
        /// 合約編號
        /// </summary>
        [Required(ErrorMessage = "合約編號不能為空")]
        public string ContractId { get; set; }

        /// <summary>
        /// 費用類型
        /// </summary>
        [Required(ErrorMessage = "費用類型不能為空")]
        public string FeeType { get; set; }

        /// <summary>
        /// 計費期間(YYYYMM)
        /// </summary>
        [Required(ErrorMessage = "計費期間(YYYYMM) 不能為空")]
        [RegularExpression(@"^\d{6}$", ErrorMessage = "格式須為YYYYMM")]
        [FeeCreateValidation]
        public string BillingPeriod { get; set; }

        /// <summary>
        /// 費用金額
        /// </summary>
        [Required(ErrorMessage = "費用金額不能為空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "費用金額必須大於0")]
        public decimal Amount { get; set; }

        /// <summary>
        /// 費用應繳日期
        /// </summary>
        [Required(ErrorMessage = "費用應繳日期不能為空")]
        public long FeeDate { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        [StringLength(500, ErrorMessage = "備註長度不能超過500字元")]
        public string? Note { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (!string.IsNullOrWhiteSpace(BillingPeriod) && BillingPeriod.Length == 6)
            {
                if (!int.TryParse(BillingPeriod.Substring(0, 4), out var year) ||
                    !int.TryParse(BillingPeriod.Substring(4, 2), out var month) ||
                    year < 1900 || year > 9999 || month < 1 || month > 12)
                {
                    yield return new ValidationResult(
                        "計費期間需為合法年月（YYYYMM，月份01-12）",
                        new[] { nameof(BillingPeriod) }
                    );
                }
            }
        }
    }

    /// <summary>
    /// 費用更新請求 DTO（API 驗證用）
    /// </summary>
    public class FeeUpdateRequestDTO : IValidatableObject
    {
        [Required(ErrorMessage = "費用編號不能為空")]
        [FeeUpdateValidation]
        [FeeEditable]
        public string FeeId { get; set; }

        [Required(ErrorMessage = "費用類型不能為空")]
        public string FeeType { get; set; }

        [Required(ErrorMessage = "計費期間(YYYYMM) 不能為空")]
        [RegularExpression(@"^\d{6}$", ErrorMessage = "格式須為YYYYMM")]
        public string BillingPeriod { get; set; }

        /// <summary>
        /// 費用金額
        /// </summary>
        [Required(ErrorMessage = "費用金額不能為空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "費用金額必須大於0")]
        public decimal Amount { get; set; }

        /// <summary>
        /// 費用應繳日期
        /// </summary>
        [Required(ErrorMessage = "費用應繳日期不能為空")]
        public long FeeDate { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        [StringLength(500, ErrorMessage = "備註長度不能超過500字元")]
        public string? Note { get; set; }

        /// <summary>
        /// 驗證計費期間格式
        /// </summary>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果集合</returns>
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (!string.IsNullOrWhiteSpace(BillingPeriod) && BillingPeriod.Length == 6)
            {
                if (!int.TryParse(BillingPeriod.Substring(0, 4), out var year) ||
                    !int.TryParse(BillingPeriod.Substring(4, 2), out var month) ||
                    year < 1900 || year > 9999 || month < 1 || month > 12)
                {
                    yield return new ValidationResult(
                        "計費期間需為合法年月（YYYYMM，月份01-12）",
                        new[] { nameof(BillingPeriod) }
                    );
                }
            }
        }
    }

    /// <summary>
    /// 費用刪除請求 DTO（API 驗證用）
    /// </summary>
    public class FeeDeleteRequestDTO
    {
        /// <summary>
        /// 費用編號
        /// </summary>
        [Required(ErrorMessage = "費用編號不能為空")]
        [FeeDeletable]
        public string FeeId { get; set; }
    }

    /// <summary>
    /// 費用新增複合驗證屬性（同步 DB 檢查唯一性：ContractId + FeeType + BillingPeriod）
    /// </summary>
    public class FeeCreateValidationAttribute : ValidationAttribute
    {
        /// <summary>
        /// 初始化費用建立驗證屬性
        /// </summary>
        public FeeCreateValidationAttribute()
        {
            ErrorMessage = "同期間同類型費用已存在";
        }

        /// <summary>
        /// 驗證費用建立資料的唯一性
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success; // 空值由其他驗證處理
            }

            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            if (validationContext.ObjectInstance is FeeCreateRequestDTO create)
            {
                var exists = context.Rms_Fees.Any(f =>
                    f.ContractId == create.ContractId &&
                    f.FeeType == create.FeeType &&
                    f.BillingPeriod == create.BillingPeriod);
                if (exists)
                {
                    return new ValidationResult(ErrorMessage);
                }
            }

            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 費用更新複合驗證屬性（同步 DB 檢查唯一性：ContractId + FeeType + BillingPeriod，排除自己）
    /// </summary>
    public class FeeUpdateValidationAttribute : ValidationAttribute
    {
        /// <summary>
        /// 初始化費用更新驗證屬性
        /// </summary>
        public FeeUpdateValidationAttribute()
        {
            ErrorMessage = "同期間同類型費用已存在";
        }

        /// <summary>
        /// 驗證費用更新資料的唯一性
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success; // 空值由其他驗證處理
            }

            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            if (validationContext.ObjectInstance is FeeUpdateRequestDTO update)
            {
                var current = context.Rms_Fees.FirstOrDefault(f => f.FeeId == update.FeeId);
                if (current == null)
                {
                    return new ValidationResult("費用不存在");
                }

                var exists = context.Rms_Fees.Any(f =>
                    f.ContractId == current.ContractId &&
                    f.FeeType == update.FeeType &&
                    f.BillingPeriod == update.BillingPeriod &&
                    f.FeeId != update.FeeId);
                if (exists)
                {
                    return new ValidationResult(ErrorMessage);
                }
            }

            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 檢查費用是否可編輯（若已有繳費分配則不可編輯）
    /// </summary>
    public class FeeEditableAttribute : ValidationAttribute
    {
        /// <summary>
        /// 初始化費用可編輯驗證屬性
        /// </summary>
        public FeeEditableAttribute()
        {
            ErrorMessage = "此費用已有繳費分配紀錄，無法修改";
        }

        /// <summary>
        /// 驗證費用是否可以編輯
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success;
            }

            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            var feeId = value.ToString();
            var hasAlloc = context.Rms_PaymentAllocations.Any(a => a.FeeId == feeId);
            return hasAlloc ? new ValidationResult(ErrorMessage) : ValidationResult.Success;
        }
    }

    /// <summary>
    /// 檢查費用是否可刪除（若已有繳費分配則不可刪除）
    /// </summary>
    public class FeeDeletableAttribute : ValidationAttribute
    {
        /// <summary>
        /// 初始化費用可刪除驗證屬性
        /// </summary>
        public FeeDeletableAttribute()
        {
            ErrorMessage = "此費用已有繳費分配紀錄，無法刪除";
        }

        /// <summary>
        /// 驗證費用是否可以刪除
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success;
            }

            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            var feeId = value.ToString();
            var hasAlloc = context.Rms_PaymentAllocations.Any(a => a.FeeId == feeId);
            return hasAlloc ? new ValidationResult(ErrorMessage) : ValidationResult.Success;
        }
    }
} 