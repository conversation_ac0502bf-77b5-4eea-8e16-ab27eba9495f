using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Models.Ims;
using FAST_ERP_Backend.Models;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Ims;

/// <summary> 商業夥伴聯絡人管理 </summary>
[ApiController]
[Route("api/ims/[controller]")]
[SwaggerTag("商業夥伴聯絡人管理")]
public class PartnerContactController(IPartnerContactService _partnerContactService) : ControllerBase
{
    /// <summary> 取得商業夥伴的所有聯絡人 </summary>
    [HttpGet("{PartnerID}")]
    [SwaggerOperation(Summary = "取得商業夥伴的所有聯絡人", Description = "取得指定商業夥伴的所有聯絡人")]
    public async Task<IActionResult> Get(Guid PartnerID)
    {
        try
        {
            if (PartnerID == Guid.Empty)
            {
                return BadRequest(ApiResponse<object>.ErrorResult("商業夥伴ID不能為空", 400));
            }

            var contacts = await _partnerContactService.GetAsync(PartnerID);
            return Ok(ApiResponse<List<PartnerContactDTO>>.SuccessResult(contacts, "取得商業夥伴聯絡人列表成功"));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResult($"取得聯絡人列表失敗: {ex.Message}", 400));
        }
    }

    /// <summary> 取得特定聯絡人關聯 </summary>
    [HttpGet("{PartnerID}/{ContactID}")]
    [SwaggerOperation(Summary = "取得特定聯絡人關聯", Description = "取得指定商業夥伴的特定聯絡人關聯")]
    public async Task<IActionResult> Get(Guid PartnerID, Guid ContactID)
    {
        try
        {
            if (PartnerID == Guid.Empty || ContactID == Guid.Empty)
            {
                return BadRequest(ApiResponse<object>.ErrorResult("商業夥伴ID或聯絡人ID不能為空", 400));
            }

            var contact = await _partnerContactService.GetAsync(PartnerID, ContactID);
            if (contact == null)
            {
                return NotFound(ApiResponse<object>.ErrorResult("找不到指定的聯絡人關聯", 404));
            }

            return Ok(ApiResponse<PartnerContactDTO>.SuccessResult(contact, "取得聯絡人關聯成功"));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResult($"取得聯絡人關聯失敗: {ex.Message}", 400));
        }
    }

    /// <summary> 新增聯絡人關聯 </summary>
    [HttpPost]
    [SwaggerOperation(Summary = "新增聯絡人關聯", Description = "新增商業夥伴聯絡人關聯")]
    public async Task<IActionResult> Add([FromBody] PartnerContactDTO data)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();
                return BadRequest(ApiResponse<object>.ErrorResult($"資料驗證失敗: {string.Join(", ", errors)}", 400));
            }

            var (result, message) = await _partnerContactService.AddAsync(data);

            if (result)
            {
                return Ok(ApiResponse<object>.SuccessResult(null, message));
            }
            else
            {
                return BadRequest(ApiResponse<object>.ErrorResult(message, 400));
            }
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResult(ex.Message, 400));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<object>.ErrorResult($"新增聯絡人關聯失敗: {ex.Message}", 500));
        }
    }

    /// <summary> 更新聯絡人關聯 </summary>
    [HttpPut]
    [SwaggerOperation(Summary = "更新聯絡人關聯", Description = "更新商業夥伴聯絡人關聯")]
    public async Task<IActionResult> Update([FromBody] PartnerContactDTO dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();
                return BadRequest(ApiResponse<object>.ErrorResult($"資料驗證失敗: {string.Join(", ", errors)}", 400));
            }

            if (dto.PartnerID == Guid.Empty || dto.ContactID == Guid.Empty)
            {
                return BadRequest(ApiResponse<object>.ErrorResult("商業夥伴ID或聯絡人ID不能為空", 400));
            }

            var (result, message) = await _partnerContactService.UpdateAsync(dto);

            if (result)
            {
                return Ok(ApiResponse<object>.SuccessResult(null, message));
            }
            else
            {
                return BadRequest(ApiResponse<object>.ErrorResult(message, 400));
            }
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResult(ex.Message, 400));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<object>.ErrorResult($"更新聯絡人關聯失敗: {ex.Message}", 500));
        }
    }

    /// <summary> 刪除聯絡人關聯 </summary>
    [HttpDelete("{PartnerID}/{ContactID}")]
    [SwaggerOperation(Summary = "刪除聯絡人關聯", Description = "刪除商業夥伴聯絡人關聯（硬刪除）")]
    public async Task<IActionResult> Delete(Guid PartnerID, Guid ContactID)
    {
        try
        {
            if (PartnerID == Guid.Empty || ContactID == Guid.Empty)
            {
                return BadRequest(ApiResponse<object>.ErrorResult("商業夥伴ID或聯絡人ID不能為空", 400));
            }

            var (success, message) = await _partnerContactService.DeleteAsync(PartnerID, ContactID);
            if (!success)
            {
                return BadRequest(ApiResponse<object>.ErrorResult(message, 400));
            }

            return Ok(ApiResponse<object>.SuccessResult(null, message));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResult($"刪除聯絡人關聯失敗: {ex.Message}", 400));
        }
    }
} 