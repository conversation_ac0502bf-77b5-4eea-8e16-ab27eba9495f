# FAST ERP 租金管理系統（Rms）規則總覽

建立者: 系統自動產生  
建立時間: 2025-07-19  
最後更新: 2025-12-19

---

## 📋 快速參考

### 核心狀態值（Smart Enum 管理）

- **房源狀態**：空置(1)、出租(2)、維修(3)、停用(4)
- **合約狀態**：草稿(0)、啟用(1)、終止(2)
- **房型類別**：辦公(1)、住宅(2)、店面(3)、倉庫(4)、其他(5)
- **費用狀態（Fee）**：未繳(0)、部分已繳(1)、已繳(2)、作廢(9)
- **繳費狀態（Payment）**：待確認(0)、已入帳(1)、作廢(9)

### 關鍵業務規則

- 新房源預設「空置」狀態
- 出租狀態僅由合約系統自動控制
- 合約啟用時建立房源/租戶快照
- 出租狀態房源資料不可異動

---

## 1. 系統架構

### 主要模組

- **Ims**：進銷存 | **Pms**：財產管理 | **Pas**：人事薪資 | **Rms**：租金管理

### Rms 功能模組

- **Property**：房源資料維護、狀態查詢
- **Tenant**：租戶資料、聯絡人、身分證驗證
- **Contract**：合約建立、狀態管理、快照機制
- **Fee/Payment**：費用管理、繳費記錄

---

## 2. 開發規則與約定

### 2.1 基礎規則

- **ID 生成**：統一使用 `Guid.NewGuid().ToString()`
- **軟刪除**：EF Core 全域查詢過濾器自動處理 `IsDeleted`
- **API 格式**：統一使用 `ApiResponse<T>`，含 `Success`、`Message`、`Data`、`Paginate`
- **分頁處理**：使用 `PaginateInfo`，包含總數、頁碼、頁大小等

### 2.2 資料驗證（統一策略）

- **必填與格式**：`[Required]`、`[StringLength]`、`[RegularExpression]`
- **自訂驗證（全部放在 DTO 層）**：
  - 共用：`[UniformNumber]`、`[PhoneNumber]`、`[Email]`
  - 存在性/可刪除：`[PropertyExists]`、`[TenantExists]`、`[CanDelete]`
  - 情境化複合驗證：
    - Property：`[PropertyCreateValidation]`、`[PropertyUpdateValidation]`
    - Tenant：`[TenantCreateValidation]`、`[TenantUpdateValidation]`
    - Contract：`[ContractCreateValidation]`、`[ContractUpdateValidation]`、`[ContractCanActivate]`、`[ContractCanTerminate]`、`[ContractCanDelete]`
    - Fee：`[FeeCreateValidation]`、`[FeeUpdateValidation]`（唯一性：`ContractId + FeeType + BillingPeriod`）
- **原則**：
  - DTO 層為主；`[ApiController]` 自動執行 ModelState 驗證
  - Service 專注「交易、一致性、跨表/狀態」等業務，不重複欄位/唯一性檢核
  - Model 層精簡，除非必要的領域不變條件
  - 自訂 Attribute 為同步檢查，如需異步/重度查詢改由 Service 處理

**Fee 模組：**
- `FeeCreateValidationAttribute`：建立時檢查唯一性（`ContractId + FeeType + BillingPeriod`）
- `FeeUpdateValidationAttribute`：更新時檢查唯一性（排除自己）

### 2.3 狀態管理與業務邏輯

#### 狀態值定義（內建 Smart Enum 管理）

- **房源狀態**：空置(1)、出租(2)、維修(3)、停用(4)
- **房型類別**：辦公(1)、住宅(2)、店面(3)、倉庫(4)、其他(5)
- **合約狀態**：草稿(0)、啟用(1)、終止(2)

#### 房源狀態規則

- **建立**：新房源預設「空置」(1)
- **手動調整**：空置↔維修↔停用（三者可互調）
- **系統控制**：出租(2)僅由合約系統自動管理

#### 合約業務規則

- **草稿階段**：新建合約預設草稿(0)，可自由編輯，不影響房源
- **啟用條件**：房源必須空置、無日期重疊的其他啟用合約
- **啟用效果**：合約→啟用(1)、房源→出租(2)、建立快照
- **終止條件**：僅啟用合約可終止、需確認過期
- **終止效果**：合約→終止(2)、若無其他啟用合約則房源→空置(1)
- **狀態同步**：確保房源與合約狀態完全一致

#### 費用與繳費業務規則

- **費用建立**：系統人員按月為合約建立費用（租金/水/電/停車/火險等）
- **唯一性**：同合約 + 同費用類型 + 同計費期間（BillingPeriod，例 202501）不得重複
- **核銷機制**：一筆繳費可分配到多筆費用（關聯表 `PaymentAllocation`）
- **狀態演進（Fee）**：未繳(0) → 部分已繳(1) → 已繳(2)；任意 → 作廢(9)
- **狀態演進（Payment）**：待確認(0) → 已入帳(1) → 作廢(9)
- **金額欄位**：`Fee.PaidAmount`（已繳累計）
- **分配原則（明細主導，無餘額）**：使用者輸入各費用分配金額，`Payment.Amount = Σ allocations` 由後端計算（不自動分配、不保留餘額）
- **交易一致性**：建立 Payment 與分配（Allocation）與 Fee 狀態更新須在同一交易內完成

##### 架構圖（費用/繳費/分配關係）

```mermaid
erDiagram
  CONTRACT ||--o{ FEE : "has"
  PAYMENT ||--o{ PAYMENT_ALLOCATION : "allocates"
  FEE ||--o{ PAYMENT_ALLOCATION : "is allocated by"

  CONTRACT {
    string "ContractId PK"
  }
  FEE {
    string "FeeId PK"
    string "ContractId FK"
    string "FeeType"
    string "BillingPeriod"
    decimal "Amount"
    decimal "PaidAmount"
    string "Status"
  }
  PAYMENT {
    string "PaymentId PK"
    string "ContractId FK"
    decimal "Amount"
    string "Status"
  }
  PAYMENT_ALLOCATION {
    string "PaymentAllocationId PK"
    string "PaymentId FK"
    string "FeeId FK"
    decimal "Amount"
  }
```

##### 典型流程（摘要）

1) 月費建立：依合約批次產生當月 `Fee`（防重：ContractId + FeeType + BillingPeriod）
2) 客戶繳費：使用者於各筆 `Fee` 輸入本次分配金額（可多筆）
3) 分配核銷：後端計算 `Payment.Amount = Σ allocations`，建立 `Payment` 與多筆 `PaymentAllocation`，同步更新 `Fee.PaidAmount/Status`

#### 合約快照機制

- **建立時機**：合約啟用時自動建立房源與租戶快照
- **儲存方式**：JSON格式存於合約表，含版本標記
- **資料結構**：包含房源資料（ID、名稱、地址、坪數等）與租戶資料（ID、姓名、聯絡資訊等）
- **應用場景**：法律證據、稽核追蹤、歷史比較、合約糾紛處理
- **技術特點**：原子性操作、版本兼容、效能最佳化

---

## 3. 資料表與實作狀態

### 主要資料表（精簡）

- **Property**：房源資料表
- **Tenant**：租戶資料表  
- **Contract**：合約資料表（含JSON快照欄位）
- **Fee**：費用資料表（含 `BillingPeriod`、`PaidAmount`）
- **Payment**：繳費記錄資料表（`Amount` 為後端依 allocations 加總，不留未分配餘額）
- **PaymentAllocation**：繳費分配關聯表（`PaymentId`、`FeeId`、`Amount`）

### 實作狀態

- ✅ **已完成**：Property、Tenant、Contract（含完整CRUD、驗證、狀態管理）
- 🛠️ **進行中**：Fee、Payment、PaymentAllocation（文件規則已更新）

### 技術架構

- **後端**：ASP.NET Core Web API + EF Core + SQL Server
- **文件**：Swagger自動生成 + TableLayout規範
- **詳細設計**：參考 `TableLayout/Rms/` 目錄下各 .md 文件

---

## 4. 更新記錄

### 2025-12-19

- ✅ **Contract模組完成**：實體、服務、控制器、驗證、分頁
- ✅ **狀態管理規則**：房源/合約狀態定義與業務邏輯
- ✅ **快照機制設計**：JSON格式存儲、版本兼容、稽核追蹤
- ✅ **Tenant模組完成**：CRUD、身分證/電話驗證、業務檢查
- ✅ **驗證優化**：統一錯誤訊息、簡化驗證規則
- ✅ **編譯修復**：ApiResponse重複定義問題解決

### 2025-12-19 (下午)

- ✅ **驗證邏輯重構**：將合約業務驗證邏輯從 Service 層移至 Model 層
- ✅ **驗證設計原則**：新增 Model 層驗證優先的設計原則
- ✅ **程式碼簡化**：移除 Service 層重複驗證，提高程式碼重用性
- ✅ **外鍵約束修復**：解決 RMS 模組外鍵約束循環引用問題

### 2025-12-19 (晚上)

- ✅ **驗證效能優化**：實作複合驗證屬性，解決 Property/Tenant 多次資料庫查詢問題
- ✅ **Property 模組優化**：新增 `PropertyCreateValidationAttribute` 和 `PropertyUpdateValidationAttribute`
- ✅ **Tenant 模組優化**：新增 `TenantCreateValidationAttribute` 和 `TenantUpdateValidationAttribute`
- ✅ **查詢次數優化**：Property 建立從 3次查詢優化為 1次，Tenant 建立從 4次查詢優化為 1次
- ✅ **驗證策略統一**：確保 RMS 模組所有驗證邏輯遵循相同的效能最佳化原則

### 2025-07-19

- 初始建立規則總覽文件與Property模組

---

## 參考說明

本文件為RMS模組開發規範總覽，詳細設計請參考TableLayout目錄
