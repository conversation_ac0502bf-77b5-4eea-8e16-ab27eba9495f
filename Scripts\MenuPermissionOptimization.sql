-- =====================================================
-- FastERP 選單權限優化腳本
-- 目的：清理冗餘的父節點權限，只保留末節點權限
-- 作者：FastERP 開發團隊
-- 日期：2024-12-19
-- =====================================================

-- 設定安全模式（防止意外執行）
DECLARE @SafetyMode BIT = 1; -- 設為 0 才會執行實際清理

PRINT '=== FastERP 選單權限優化分析 ===';
PRINT '執行時間: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '';

-- =====================================================
-- 第一步：資料備份
-- =====================================================
PRINT '1. 建立備份表...';

-- 備份角色權限表
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Common_RolesPermissions_Backup]') AND type in (N'U'))
BEGIN
    SELECT * 
    INTO Common_RolesPermissions_Backup
    FROM Common_RolesPermissions
    WHERE IsDeleted = 0;
    
    PRINT '✓ 已建立 Common_RolesPermissions_Backup 備份表';
    PRINT '  備份記錄數: ' + CAST(@@ROWCOUNT AS VARCHAR);
END
ELSE
BEGIN
    PRINT '⚠ 備份表已存在，跳過備份步驟';
END

PRINT '';

-- =====================================================
-- 第二步：分析現有權限結構
-- =====================================================
PRINT '2. 分析現有權限結構...';

-- 統計總體資料
DECLARE @TotalMenus INT, @TotalPermissions INT, @TotalRoles INT;

SELECT @TotalMenus = COUNT(*) FROM Common_SystemMenu WHERE IsDeleted = 0;
SELECT @TotalPermissions = COUNT(*) FROM Common_RolesPermissions WHERE IsDeleted = 0;
SELECT @TotalRoles = COUNT(DISTINCT RolesId) FROM Common_RolesPermissions WHERE IsDeleted = 0;

PRINT '總選單數量: ' + CAST(@TotalMenus AS VARCHAR);
PRINT '總權限記錄數: ' + CAST(@TotalPermissions AS VARCHAR);
PRINT '涉及角色數: ' + CAST(@TotalRoles AS VARCHAR);
PRINT '';

-- 識別選單樹結構
PRINT '3. 識別選單樹結構...';

-- 建立臨時表存儲選單分析結果
IF OBJECT_ID('tempdb..#MenuAnalysis') IS NOT NULL DROP TABLE #MenuAnalysis;

CREATE TABLE #MenuAnalysis (
    SystemMenuId NVARCHAR(100),
    Label NVARCHAR(50),
    ParentId NVARCHAR(100),
    IsLeafNode BIT,
    ChildrenCount INT,
    PermissionCount INT,
    IsRedundant BIT DEFAULT 0
);

-- 分析每個選單的子節點數量
INSERT INTO #MenuAnalysis (SystemMenuId, Label, ParentId, ChildrenCount)
SELECT 
    m.SystemMenuId,
    m.Label,
    m.ParentId,
    (SELECT COUNT(*) FROM Common_SystemMenu c 
     WHERE c.ParentId = m.SystemMenuId AND c.IsDeleted = 0) as ChildrenCount
FROM Common_SystemMenu m
WHERE m.IsDeleted = 0;

-- 標記末節點
UPDATE #MenuAnalysis 
SET IsLeafNode = CASE WHEN ChildrenCount = 0 THEN 1 ELSE 0 END;

-- 統計每個選單的權限數量
UPDATE ma
SET PermissionCount = (
    SELECT COUNT(*) 
    FROM Common_RolesPermissions rp 
    WHERE rp.SystemMenuId = ma.SystemMenuId AND rp.IsDeleted = 0
)
FROM #MenuAnalysis ma;

-- 顯示統計結果
DECLARE @LeafNodes INT, @ParentNodes INT, @LeafWithPermissions INT, @ParentWithPermissions INT;

SELECT @LeafNodes = COUNT(*) FROM #MenuAnalysis WHERE IsLeafNode = 1;
SELECT @ParentNodes = COUNT(*) FROM #MenuAnalysis WHERE IsLeafNode = 0;
SELECT @LeafWithPermissions = COUNT(*) FROM #MenuAnalysis WHERE IsLeafNode = 1 AND PermissionCount > 0;
SELECT @ParentWithPermissions = COUNT(*) FROM #MenuAnalysis WHERE IsLeafNode = 0 AND PermissionCount > 0;

PRINT '末節點數量: ' + CAST(@LeafNodes AS VARCHAR);
PRINT '父節點數量: ' + CAST(@ParentNodes AS VARCHAR);
PRINT '有權限的末節點: ' + CAST(@LeafWithPermissions AS VARCHAR);
PRINT '有權限的父節點: ' + CAST(@ParentWithPermissions AS VARCHAR);
PRINT '';

-- =====================================================
-- 第四步：識別冗餘的父節點權限
-- =====================================================
PRINT '4. 識別冗餘的父節點權限...';

-- 使用遞歸 CTE 識別冗餘父節點
WITH MenuHierarchy AS (
    -- 錨點：末節點
    SELECT SystemMenuId, ParentId, Label, 0 as Level
    FROM #MenuAnalysis 
    WHERE IsLeafNode = 1 AND PermissionCount > 0
    
    UNION ALL
    
    -- 遞歸：向上查找父節點
    SELECT p.SystemMenuId, p.ParentId, p.Label, mh.Level + 1
    FROM #MenuAnalysis p
    INNER JOIN MenuHierarchy mh ON p.SystemMenuId = mh.ParentId
    WHERE p.IsLeafNode = 0
),
RedundantParents AS (
    SELECT DISTINCT 
        rp.RolesPermissionsId,
        rp.RolesId,
        rp.SystemMenuId,
        ma.Label,
        'Parent node with all children having permissions' as Reason
    FROM Common_RolesPermissions rp
    INNER JOIN #MenuAnalysis ma ON rp.SystemMenuId = ma.SystemMenuId
    WHERE rp.IsDeleted = 0
      AND ma.IsLeafNode = 0  -- 只檢查父節點
      AND ma.PermissionCount > 0  -- 有權限的父節點
      AND NOT EXISTS (
          -- 檢查是否所有子節點都沒有權限
          SELECT 1 
          FROM Common_SystemMenu child
          LEFT JOIN Common_RolesPermissions child_rp ON child.SystemMenuId = child_rp.SystemMenuId 
                                                     AND child_rp.RolesId = rp.RolesId 
                                                     AND child_rp.IsDeleted = 0
          WHERE child.ParentId = rp.SystemMenuId 
            AND child.IsDeleted = 0
            AND child_rp.SystemMenuId IS NULL  -- 子節點沒有權限
      )
)
SELECT 
    rp.RolesPermissionsId,
    rp.RolesId,
    rp.SystemMenuId,
    rp.Label,
    rp.Reason
INTO #RedundantPermissions
FROM RedundantParents rp;

DECLARE @RedundantCount INT = @@ROWCOUNT;
PRINT '識別到冗餘父節點權限: ' + CAST(@RedundantCount AS VARCHAR) + ' 筆';

-- 顯示冗餘權限詳情（前10筆）
IF @RedundantCount > 0
BEGIN
    PRINT '';
    PRINT '冗餘權限範例（前10筆）:';
    SELECT TOP 10 
        RolesId as '角色ID',
        SystemMenuId as '選單ID', 
        Label as '選單名稱',
        Reason as '冗餘原因'
    FROM #RedundantPermissions
    ORDER BY RolesId, SystemMenuId;
END

PRINT '';

-- =====================================================
-- 第五步：執行清理（安全模式檢查）
-- =====================================================
IF @SafetyMode = 1
BEGIN
    PRINT '⚠ 安全模式啟用，不會執行實際清理';
    PRINT '如要執行清理，請將腳本開頭的 @SafetyMode 設為 0';
    PRINT '';
    PRINT '預計清理效果:';
    PRINT '• 將刪除 ' + CAST(@RedundantCount AS VARCHAR) + ' 筆冗餘父節點權限';
    PRINT '• 權限記錄將從 ' + CAST(@TotalPermissions AS VARCHAR) + ' 筆減少到約 ' + CAST(@TotalPermissions - @RedundantCount AS VARCHAR) + ' 筆';
    PRINT '• 預計節省 ' + CAST(ROUND((@RedundantCount * 100.0 / @TotalPermissions), 2) AS VARCHAR) + '% 的儲存空間';
END
ELSE
BEGIN
    PRINT '5. 執行權限清理...';
    
    BEGIN TRANSACTION CleanupTransaction;
    
    BEGIN TRY
        -- 軟刪除冗餘權限
        UPDATE rp
        SET IsDeleted = 1,
            DeleteTime = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()),
            DeleteUserId = 'SYSTEM_OPTIMIZATION'
        FROM Common_RolesPermissions rp
        INNER JOIN #RedundantPermissions red ON rp.RolesPermissionsId = red.RolesPermissionsId;
        
        DECLARE @CleanedCount INT = @@ROWCOUNT;
        
        PRINT '✓ 成功清理 ' + CAST(@CleanedCount AS VARCHAR) + ' 筆冗餘權限';
        
        COMMIT TRANSACTION CleanupTransaction;
        
        PRINT '✓ 權限優化完成';
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION CleanupTransaction;
        
        PRINT '✗ 清理過程發生錯誤:';
        PRINT ERROR_MESSAGE();
        
    END CATCH
END

-- =====================================================
-- 清理臨時表
-- =====================================================
IF OBJECT_ID('tempdb..#MenuAnalysis') IS NOT NULL DROP TABLE #MenuAnalysis;
IF OBJECT_ID('tempdb..#RedundantPermissions') IS NOT NULL DROP TABLE #RedundantPermissions;

PRINT '';
PRINT '=== 權限優化分析完成 ===';
PRINT '執行完成時間: ' + CONVERT(VARCHAR, GETDATE(), 120);

-- =====================================================
-- 驗證腳本（可選執行）
-- =====================================================
/*
-- 驗證清理效果
SELECT 
    '清理前' as 狀態,
    COUNT(*) as 權限數量
FROM Common_RolesPermissions_Backup

UNION ALL

SELECT 
    '清理後' as 狀態,
    COUNT(*) as 權限數量
FROM Common_RolesPermissions 
WHERE IsDeleted = 0;

-- 如需回滾，執行以下語句：
-- TRUNCATE TABLE Common_RolesPermissions;
-- INSERT INTO Common_RolesPermissions SELECT * FROM Common_RolesPermissions_Backup;
*/
