import { useState, useCallback, useMemo } from 'react';
import { message } from 'antd';
import { ActionManagerService } from '../services/actionManagerService';
import { createContextLogger, SYMBOLS } from '@/utils/logger';
import { ActionManagerState, initialState } from '../types/actionManager.types';
import { GranularPermission, GranularPermissionAction, ActionDefinition } from '@/services/common/granularPermissionService';

// 遵循 FastERP 日誌記錄策略
const actionManagerLogger = createContextLogger({ module: 'useActionManager' });

export const useActionManager = (permission: GranularPermission | null) => {
  const [state, setState] = useState<ActionManagerState>(initialState);

  // 更新狀態的通用方法
  const updateState = useCallback((updates: Partial<ActionManagerState>) => {
    setState(prev => ({
      ...prev,
      ...updates,
      data: { ...prev.data, ...updates.data },
      ui: { ...prev.ui, ...updates.ui }
    }));
  }, []);

  // 更新 UI 狀態
  const updateUI = useCallback((uiUpdates: Partial<ActionManagerState['ui']>) => {
    setState(prev => ({
      ...prev,
      ui: { ...prev.ui, ...uiUpdates }
    }));
  }, []);

  // 載入資料
  const loadData = useCallback(async () => {
    if (!permission) return;

    updateState({ data: { ...state.data, loading: true } });

    try {
      actionManagerLogger.log(SYMBOLS.DEBUG, '開始載入動作管理資料', { 
        permissionId: permission.granularPermissionID 
      });

      const [actions, validActions] = await Promise.all([
        ActionManagerService.loadPermissionActions(permission.granularPermissionID),
        ActionManagerService.loadValidActions()
      ]);

      updateState({
        data: {
          actions,
          validActions,
          loading: false
        }
      });

      actionManagerLogger.log(SYMBOLS.SUCCESS, '動作管理資料載入完成', { 
        permissionId: permission.granularPermissionID,
        actionCount: actions.length,
        validActionCount: validActions.length
      });
    } catch (error) {
      actionManagerLogger.log(SYMBOLS.ERROR, '動作管理資料載入失敗', { 
        permissionId: permission?.granularPermissionID, 
        error 
      });
      
      updateState({ data: { ...state.data, loading: false } });
      message.error('載入資料失敗');
    }
  }, [permission, updateState]);

  // 快速新增動作
  const handleQuickAddAction = useCallback(async (actionDef: ActionDefinition) => {
    if (!permission) return;

    updateUI({ addingActions: new Set(state.ui.addingActions).add(actionDef.code) });

    try {
      const result = await ActionManagerService.addPermissionAction({
        granularPermissionID: permission.granularPermissionID,
        code: actionDef.code,
        name: actionDef.name,
        sortCode: 0,
      });

      if (result.success) {
        message.success(`成功新增「${actionDef.name}」動作`);
        await loadData();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      actionManagerLogger.log(SYMBOLS.ERROR, '快速新增動作失敗', { actionDef, error });
      message.error('新增動作失敗');
    } finally {
      updateUI({
        addingActions: (() => {
          const newSet = new Set(state.ui.addingActions);
          newSet.delete(actionDef.code);
          return newSet;
        })()
      });
    }
  }, [permission, loadData, updateUI]);

  // 開始編輯動作
  const handleStartEdit = useCallback((actionId: string, action: GranularPermissionAction) => {
    actionManagerLogger.log(SYMBOLS.DEBUG, '開始編輯動作', { actionId });
    updateUI({
      editingAction: actionId,
      editingValue: action.name,
      editingValues: {
        ...state.ui.editingValues,
        [actionId]: {
          code: action.code,
          name: action.name,
          sortCode: action.sortCode
        }
      }
    });
  }, [updateUI]);

  // 取消編輯動作
  const handleCancelEdit = useCallback((actionId: string) => {
    actionManagerLogger.log(SYMBOLS.DEBUG, '取消編輯動作', { actionId });
    updateUI({
      editingAction: null,
      editingValue: '',
      editingValues: (() => {
        const newValues = { ...state.ui.editingValues };
        delete newValues[actionId];
        return newValues;
      })()
    });
  }, [updateUI]);

  // 儲存編輯動作
  const handleSaveEdit = useCallback(async (actionId: string) => {
    const editValues = state.ui.editingValues[actionId];
    if (!editValues || !editValues.name.trim()) {
      message.warning('請輸入動作名稱');
      return;
    }

    try {
      const action = state.data.actions.find(a => a.granularPermissionActionID === actionId);
      if (!action) {
        message.error('找不到要編輯的動作');
        return;
      }

      const result = await ActionManagerService.updatePermissionAction({
        ...action,
        name: editValues.name.trim()
      });

      if (result.success) {
        message.success('動作名稱更新成功');
        updateUI({
          editingAction: null,
          editingValue: '',
          editingValues: (() => {
            const newValues = { ...state.ui.editingValues };
            delete newValues[actionId];
            return newValues;
          })()
        });
        await loadData();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      actionManagerLogger.log(SYMBOLS.ERROR, '動作編輯失敗', { actionId, error });
      message.error('更新失敗');
    }
  }, [loadData, updateUI]);

  // 切換批次選取
  const toggleBatchSelection = useCallback((actionCode: string) => {
    setState(prev => {
      const newSelected = new Set(prev.ui.selectedBatchCodes);
      if (newSelected.has(actionCode)) {
        newSelected.delete(actionCode);
      } else {
        newSelected.add(actionCode);
      }
      return {
        ...prev,
        ui: { ...prev.ui, selectedBatchCodes: newSelected }
      };
    });
  }, []);

  // 清除所有選取
  const clearAllSelections = useCallback(() => {
    actionManagerLogger.log(SYMBOLS.DEBUG, '清除所有批次選取');
    updateUI({ selectedBatchCodes: new Set() });
  }, [updateUI]);

  // 批次新增確認
  const handleBatchAddConfirm = useCallback(async () => {
    if (!permission || state.ui.selectedBatchCodes.size === 0) {
      actionManagerLogger.log(SYMBOLS.WARNING, '批次新增條件不滿足', { 
        hasPermission: !!permission,
        selectedCount: state.ui.selectedBatchCodes.size 
      });
      return;
    }

    const selectedCodes = Array.from(state.ui.selectedBatchCodes);
    actionManagerLogger.log(SYMBOLS.DEBUG, '開始批次新增動作', { 
      permissionId: permission.granularPermissionID,
      selectedCodes 
    });

    try {
      const result = await ActionManagerService.batchAddPermissionActions(
        permission.granularPermissionID,
        selectedCodes
      );

      if (result.success) {
        message.success(result.message);
        updateUI({ selectedBatchCodes: new Set() });
        await loadData();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      actionManagerLogger.log(SYMBOLS.ERROR, '批次新增動作失敗', { 
        permissionId: permission.granularPermissionID,
        selectedCodes,
        error 
      });
      message.error('批次新增失敗');
    }
  }, [permission, state.ui.selectedBatchCodes, loadData, updateUI]);

  // 計算可用的標準動作
  const availableActions = useMemo(() => {
    const existingCodes = state.data.actions.map(a => a.code);
    return state.data.validActions.filter(va => !existingCodes.includes(va.code));
  }, [state.data.actions, state.data.validActions]);

  return {
    state,
    availableActions,
    loadData,
    handleQuickAddAction,
    handleStartEdit,
    handleCancelEdit,
    handleSaveEdit,
    toggleBatchSelection,
    clearAllSelections,
    handleBatchAddConfirm,
    updateUI
  };
};
