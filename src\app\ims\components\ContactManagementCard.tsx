"use client";

import React, { useState, useEffect } from "react";
import { Card, Button, Row, Col, Typography, Tag, Space } from "antd";
import {
  ContactsOutlined,
  PlusOutlined,
  UnorderedListOutlined,
  UserOutlined,
} from "@ant-design/icons";

// Services
import { getContactList } from "@/services/ims/ContactService";
import { Contact } from "@/services/ims/ContactService";

// Types
interface ContactManagementCardProps {
  /** 完整新增聯絡人回調 */
  onAddContact: () => void;
  /** 快速新增聯絡人回調 */
  onQuickAddContact?: () => void;
  /** 查看聯絡人列表回調 */
  onViewContactList: () => void;
  /** 是否為移動端 */
  isMobile: boolean;
  /** 是否顯示快速新增按鈕 */
  showQuickAdd?: boolean;
}

interface ContactStats {
  totalContacts: number;
  activeContacts: number;
  inactiveContacts: number;
  contactsByType: Record<string, number>;
}

const { Text } = Typography;

const ContactManagementCard: React.FC<ContactManagementCardProps> = ({
  onAddContact,
  onQuickAddContact,
  onViewContactList,
  isMobile,
  showQuickAdd = true,
}) => {
  const [stats, setStats] = useState<ContactStats>({
    totalContacts: 0,
    activeContacts: 0,
    inactiveContacts: 0,
    contactsByType: {},
  });
  const [loading, setLoading] = useState(false);

  // 載入聯絡人統計資料
  const loadContactStats = async () => {
    setLoading(true);
    try {
      const response = await getContactList();
      if (response.success && response.data) {
        const contacts = response.data;
        
        // 計算統計資料
        const totalContacts = contacts.length;
        const activeContacts = contacts.filter(c => c.isActive).length;
        const inactiveContacts = totalContacts - activeContacts;
        
        // 按類型分組統計
        const contactsByType: Record<string, number> = {};
        contacts.forEach(contact => {
          const type = contact.contactType || "其他";
          contactsByType[type] = (contactsByType[type] || 0) + 1;
        });

        setStats({
          totalContacts,
          activeContacts,
          inactiveContacts,
          contactsByType,
        });
      }
    } catch (error) {
      console.error("載入聯絡人統計失敗:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadContactStats();
  }, []);

  return (
    <Card
      size={isMobile ? "small" : "default"}
      title={
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <ContactsOutlined style={{ color: "#52c41a" }} />
          <span>聯絡人管理</span>
        </div>
      }
      className="unified-card"
      styles={{
        header: {
          backgroundColor: "#fafafa",
          borderBottom: "1px solid #f0f0f0",
          padding: isMobile ? "12px 16px" : "16px 20px",
        },
        body: {
          padding: isMobile ? "12px 16px" : "16px 20px",
        },
      }}
    >
      <Row gutter={[8, 8]}>
        {/* 新增聯絡人按鈕 */}
        <Col xs={24} sm={showQuickAdd ? 12 : 24}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={onAddContact}
            className="unified-button"
            style={{ width: "100%" }}
            loading={loading}
          >
            {showQuickAdd ? "完整新增" : "新增聯絡人"}
          </Button>
        </Col>

        {/* 快速新增按鈕（可選） */}
        {showQuickAdd && onQuickAddContact && (
          <Col xs={24} sm={12}>
            <Button
              type="default"
              icon={<PlusOutlined />}
              onClick={onQuickAddContact}
              className="unified-button"
              style={{ width: "100%" }}
              loading={loading}
            >
              快速新增
            </Button>
          </Col>
        )}

        {/* 聯絡人列表按鈕 */}
        <Col xs={24}>
          <Button
            type="default"
            icon={<UnorderedListOutlined />}
            onClick={onViewContactList}
            className="unified-button"
            style={{ width: "100%" }}
            loading={loading}
          >
            聯絡人列表
          </Button>
        </Col>
      </Row>

      {/* 統計資訊 */}
      <div style={{ marginTop: 16 }}>
        <Row gutter={[8, 8]} justify="center">
          <Col>
            <Space direction="vertical" size="small" align="center">
              <Text type="secondary" style={{ fontSize: "12px" }}>
                總聯絡人數
              </Text>
              <Tag color="blue" style={{ fontSize: "14px", padding: "4px 8px" }}>
                {stats.totalContacts}
              </Tag>
            </Space>
          </Col>
          <Col>
            <Space direction="vertical" size="small" align="center">
              <Text type="secondary" style={{ fontSize: "12px" }}>
                啟用
              </Text>
              <Tag color="green" style={{ fontSize: "14px", padding: "4px 8px" }}>
                {stats.activeContacts}
              </Tag>
            </Space>
          </Col>
          <Col>
            <Space direction="vertical" size="small" align="center">
              <Text type="secondary" style={{ fontSize: "12px" }}>
                停用
              </Text>
              <Tag color="red" style={{ fontSize: "14px", padding: "4px 8px" }}>
                {stats.inactiveContacts}
              </Tag>
            </Space>
          </Col>
        </Row>

        {/* 按類型統計 */}
        {Object.keys(stats.contactsByType).length > 0 && (
          <div style={{ marginTop: 12, textAlign: "center" }}>
            <Text type="secondary" style={{ fontSize: "12px" }}>
              按類型分佈
            </Text>
            <div style={{ marginTop: 4 }}>
              <Space wrap size="small">
                {Object.entries(stats.contactsByType).map(([type, count]) => (
                  <Tag key={type} color="cyan" style={{ fontSize: "11px" }}>
                    {type}: {count}
                  </Tag>
                ))}
              </Space>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default ContactManagementCard;
