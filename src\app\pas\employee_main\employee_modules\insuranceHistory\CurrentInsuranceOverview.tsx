import React, { useEffect, useState, useRef } from "react";
import {
    Card,
    Row,
    Col,
    Typography,
    Space,
    Tag,
    message
} from "antd";
import {
    SafetyOutlined,
    MedicineBoxOutlined,
    ToolOutlined,
    CalendarOutlined,
    InfoCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

import {
    getAllEffectiveInsuranceGradesFromAPI,
    getAllEffectiveInsuranceGrades,
    InsuranceHistory
} from "@/services/pas/Insurance/InsuranceHistoryService";
import {
    getInsuranceGradeList,
    InsuranceGrade as BaseInsuranceGrade
} from '@/services/pas/Insurance/InsuranceGradeService';
import {
    INSURANCE_TYPES,
    getInsuranceTypeName,
} from "@/services/pas/Insurance/constants/insuranceConstants";

const { Title, Text } = Typography;

// 保險類型配置
const INSURANCE_TYPE_CONFIG = [
    {
        type: INSURANCE_TYPES.LABOR,
        name: '勞保',
        icon: <SafetyOutlined />,
        color: '#1890ff',
    },
    {
        type: INSURANCE_TYPES.HEALTH,
        name: '健保',
        icon: <MedicineBoxOutlined />,
        color: '#52c41a',
    },
    {
        type: INSURANCE_TYPES.ACCIDENT,
        name: '職災',
        icon: <ToolOutlined />,
        color: '#fa8c16',
    }
];

// 格式化日期顯示
const formatDateDisplay = (dateStr: string | null, emptyText = '持續中') => {
    return dateStr ? dayjs(dateStr).format('YYYY-MM-DD') : emptyText;
};

interface ExtendedInsuranceGrade extends BaseInsuranceGrade {
    gradeName: string;
    amount: number;
}

interface CurrentInsuranceOverviewProps {
    userId: string;
    title?: string;
    showTitle?: boolean;
    size?: 'default' | 'small';
    style?: React.CSSProperties;
    className?: string;
    // 新增：強制使用容器寬度自適應佈局
    useContainerWidth?: boolean;
    // 新增：父組件觸發重新載入的 prop
    refreshTrigger?: number;
}

const CurrentInsuranceOverview: React.FC<CurrentInsuranceOverviewProps> = ({
    userId,
    title = "當前生效保險級距",
    showTitle = true,
    size = 'default',
    style = { marginBottom: 16, borderRadius: '8px' },
    className = "shadow-sm",
    useContainerWidth = false,
    refreshTrigger
}) => {
    const [loading, setLoading] = useState(false);
    const [insuranceGrades, setInsuranceGrades] = useState<ExtendedInsuranceGrade[]>([]);
    const [containerWidth, setContainerWidth] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);

    // 當前生效保險級距的 state
    const [currentActiveInsurance, setCurrentActiveInsurance] = useState<Record<number, InsuranceHistory | null>>({
        [INSURANCE_TYPES.LABOR]: null,
        [INSURANCE_TYPES.HEALTH]: null,
        [INSURANCE_TYPES.ACCIDENT]: null
    });

    // 獲取保險級距選項
    const fetchInsuranceGrades = async () => {
        try {
            // 獲取所有類型的保險級距
            const allGrades: ExtendedInsuranceGrade[] = [];
            for (const type of [INSURANCE_TYPES.LABOR, INSURANCE_TYPES.HEALTH, INSURANCE_TYPES.ACCIDENT]) {
                const res = await getInsuranceGradeList(type);
                if (res.success && res.data) {
                    allGrades.push(...res.data.map(grade => ({
                        ...grade,
                        gradeName: `${getInsuranceTypeName(type)} - NT$ ${grade.monthlySalary?.toLocaleString() || 0}`,
                        amount: grade.monthlySalary || 0
                    })));
                }
            }
            setInsuranceGrades(allGrades);
        } catch (error: any) {
            console.error('獲取保險級距失敗:', error);
        }
    };

    // 獲取當前生效保險級距的方法 - 使用後端API
    const fetchCurrentActiveInsurance = async (targetDate?: dayjs.Dayjs) => {
        if (!userId) return;

        setLoading(true);
        try {
            // 將日期轉換為 YYYY-MM-DD 格式的字串
            const targetDateStr = targetDate ? targetDate.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD');

            // 使用後端的一次性查詢方法，提高效能
            const result = await getAllEffectiveInsuranceGradesFromAPI(userId, targetDateStr);

            if (result.success && result.data) {
                // 將陣列轉換為 Record<number, InsuranceHistory | null>
                const record: Record<number, InsuranceHistory | null> = {
                    [INSURANCE_TYPES.LABOR]: null,
                    [INSURANCE_TYPES.HEALTH]: null,
                    [INSURANCE_TYPES.ACCIDENT]: null
                };
                result.data.forEach(item => {
                    record[item.insuranceType] = item;
                });
                setCurrentActiveInsurance(record);
            } else {
                // 如果後端API失敗，使用前端便利方法作為後備
                const backupResult = await getAllEffectiveInsuranceGrades(userId, targetDateStr);

                if (backupResult.success && backupResult.data) {
                    setCurrentActiveInsurance(backupResult.data);
                } else {
                    message.error(backupResult.message || '獲取當前生效保險級距失敗');
                }
            }
        } catch (error: any) {
            console.error('獲取當前生效保險級距失敗:', error);
            message.error('獲取當前生效保險級距失敗');
        } finally {
            setLoading(false);
        }
    };

    // 容器寬度檢測
    useEffect(() => {
        if (!useContainerWidth) return;

        const updateContainerWidth = () => {
            if (containerRef.current) {
                setContainerWidth(containerRef.current.offsetWidth);
            }
        };

        // 初始檢測
        updateContainerWidth();

        // 監聽視窗大小變化
        const resizeObserver = new ResizeObserver(updateContainerWidth);
        if (containerRef.current) {
            resizeObserver.observe(containerRef.current);
        }

        return () => {
            resizeObserver.disconnect();
        };
    }, [useContainerWidth]);

    // 當 userId 改變時載入資料
    useEffect(() => {
        if (userId) {
            fetchInsuranceGrades();
            fetchCurrentActiveInsurance();
        }
    }, [userId]);

    // 監聽父組件的重新載入觸發
    useEffect(() => {
        if (refreshTrigger && refreshTrigger > 0 && userId) {
            fetchCurrentActiveInsurance();
        }
    }, [refreshTrigger, userId]);

    // 暴露重新載入方法給父組件 (如果需要的話可以透過 ref 使用)
    const refreshData = React.useCallback(() => {
        if (userId) {
            fetchCurrentActiveInsurance();
        }
    }, [userId]);

    // 根據容器寬度計算 Col span
    const getColSpan = () => {
        if (!useContainerWidth) {
            // 使用預設的響應式佈局
            return { xs: 24, sm: 12, lg: 8 };
        }

        // 根據容器寬度動態計算
        if (containerWidth === 0) {
            return { span: 8 }; // 預設三欄
        }

        // 容器寬度小於 600px 時一欄，600-900px 時兩欄，大於 900px 時三欄
        if (containerWidth < 600) {
            return { span: 24 }; // 一欄
        } else if (containerWidth < 900) {
            return { span: 12 }; // 兩欄
        } else {
            return { span: 8 };  // 三欄
        }
    };

    const cardTitle = showTitle ? (
        <Space>
            <InfoCircleOutlined />
            <Title level={4} style={{ margin: 0 }}>{title}</Title>
        </Space>
    ) : null;

    return (
        <div ref={containerRef}>
            <Card
                title={cardTitle}
                loading={loading}
                className={className}
                style={style}
                size={size}
            >
                <Row gutter={[16, 16]}>
                    {INSURANCE_TYPE_CONFIG.map((config) => {
                        const activeRecord = currentActiveInsurance[config.type];
                        const grade = activeRecord ? insuranceGrades.find(g => g.uid === activeRecord.insuranceGradeUid) : null;
                        const colSpan = getColSpan();

                        return (
                            <Col {...colSpan} key={config.type}>
                                <Card
                                    size="small"
                                    style={{
                                        borderLeft: `4px solid ${config.color}`,
                                        backgroundColor: activeRecord ? '#f6ffed' : '#f5f5f5'
                                    }}
                                >
                                    <Space direction="vertical" style={{ width: '100%' }}>
                                        <Space>
                                            <span style={{ color: config.color }}>{config.icon}</span>
                                            <Text strong style={{ color: config.color }}>{config.name}</Text>
                                        </Space>

                                        {activeRecord ? (
                                            <>
                                                <div>
                                                    <Text type="secondary">級距：</Text>
                                                    <Text strong>
                                                        NT$ {grade?.monthlySalary?.toLocaleString() || '未知'}
                                                    </Text>
                                                </div>
                                                <div>
                                                    <Text type="secondary">生效日期：</Text>
                                                    <Text>{formatDateDisplay(activeRecord.startDate, '-')}</Text>
                                                </div>
                                                <div>
                                                    <Text type="secondary">結束日期：</Text>
                                                    <Text style={{ color: activeRecord.endDate ? '#fa8c16' : '#52c41a' }}>
                                                        {formatDateDisplay(activeRecord.endDate || null, '持續生效中')}
                                                    </Text>
                                                </div>
                                                <Tag color="green">
                                                    <CalendarOutlined style={{ marginRight: 4 }} />
                                                    生效中
                                                </Tag>
                                            </>
                                        ) : (
                                            <>
                                                <Text type="secondary">目前無生效記錄</Text>
                                                <Tag color="default">
                                                    未投保
                                                </Tag>
                                            </>
                                        )}
                                    </Space>
                                </Card>
                            </Col>
                        );
                    })}
                </Row>
            </Card>
        </div>
    );
};

export default CurrentInsuranceOverview;
