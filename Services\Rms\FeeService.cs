using FAST_ERP_Backend.Interfaces.Rms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Rms;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Rms
{
    /// <summary>
    /// 費用管理服務
    /// </summary>
    public class FeeService : IFeeService
    {
        private readonly ERPDbContext _context;
        private readonly ILogger<FeeService> _logger;

        /// <summary>
        /// 初始化費用服務
        /// </summary>
        /// <param name="context">資料庫上下文</param>
        /// <param name="logger">日誌服務</param>
        public FeeService(ERPDbContext context, ILogger<FeeService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 取得費用列表
        /// </summary>
        /// <param name="contractId">合約編號</param>
        /// <param name="status">費用狀態</param>
        /// <param name="feeType">費用類型</param>
        /// <param name="billingPeriod">計費期間</param>
        /// <returns>費用列表回應</returns>
        public async Task<ApiResponse<List<FeeDTO>>> GetFeeListAsync(string contractId, string? status, string? feeType, string? billingPeriod)
        {
            try
            {
                var q = _context.Rms_Fees.AsNoTracking().Where(f => f.ContractId == contractId);
                if (!string.IsNullOrEmpty(status)) q = q.Where(f => f.Status == status);
                if (!string.IsNullOrEmpty(feeType)) q = q.Where(f => f.FeeType == feeType);
                if (!string.IsNullOrEmpty(billingPeriod)) q = q.Where(f => f.BillingPeriod == billingPeriod);

                var list = await q
                    .OrderBy(f => f.FeeDate)
                    .Select(f => new FeeDTO
                    {
                        FeeId = f.FeeId,
                        ContractId = f.ContractId,
                        FeeType = f.FeeType,
                        BillingPeriod = f.BillingPeriod,
                        Amount = f.Amount,
                        FeeDate = f.FeeDate,
                        PaidAmount = f.PaidAmount,
                        Status = f.Status,
                        Note = f.Note,
                        CreateTime = f.CreateTime ?? 0,
                        CreateUserId = f.CreateUserId,
                        UpdateTime = f.UpdateTime ?? 0,
                        UpdateUserId = f.UpdateUserId
                    }).ToListAsync();

                return ApiResponse<List<FeeDTO>>.SuccessResult(list, "取得費用列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得費用列表時發生錯誤");
                return ApiResponse<List<FeeDTO>>.ErrorResult("取得費用列表時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 建立費用
        /// </summary>
        /// <param name="request">費用建立請求</param>
        /// <param name="tokenUid">使用者識別碼</param>
        /// <returns>費用建立回應</returns>
        public async Task<ApiResponse<FeeDTO>> CreateFeeAsync(FeeCreateRequestDTO request, string tokenUid)
        {
            try
            {
                // 唯一性檢核已移至 DTO 自訂 Attribute：FeeCreateValidation

                var fee = new Fee
                {
                    FeeId = Guid.NewGuid().ToString(),
                    ContractId = request.ContractId,
                    FeeType = request.FeeType,
                    BillingPeriod = request.BillingPeriod,
                    Amount = request.Amount,
                    FeeDate = request.FeeDate,
                    PaidAmount = 0,
                    Status = "0", // 未繳
                    Note = request.Note ?? string.Empty,
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    CreateUserId = tokenUid
                };
                _context.Rms_Fees.Add(fee);
                await _context.SaveChangesAsync();

                var dto = new FeeDTO
                {
                    FeeId = fee.FeeId,
                    ContractId = fee.ContractId,
                    FeeType = fee.FeeType,
                    BillingPeriod = fee.BillingPeriod,
                    Amount = fee.Amount,
                    FeeDate = fee.FeeDate,
                    PaidAmount = fee.PaidAmount,
                    Status = fee.Status,
                    Note = fee.Note,
                    CreateTime = fee.CreateTime ?? 0,
                    CreateUserId = fee.CreateUserId
                };
                return ApiResponse<FeeDTO>.SuccessResult(dto, "新增費用成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "新增費用時發生錯誤");
                return ApiResponse<FeeDTO>.ErrorResult("新增費用時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 更新費用
        /// </summary>
        /// <param name="request">費用更新請求</param>
        /// <param name="tokenUid">使用者識別碼</param>
        /// <returns>更新結果</returns>
        public async Task<ApiResponse<string>> UpdateFeeAsync(FeeUpdateRequestDTO request, string tokenUid)
        {
            try
            {
                var fee = await _context.Rms_Fees.FirstOrDefaultAsync(f => f.FeeId == request.FeeId);
                fee.FeeType = request.FeeType;
                fee.BillingPeriod = request.BillingPeriod;
                fee.Amount = request.Amount;
                fee.FeeDate = request.FeeDate;
                fee.Note = request.Note ?? string.Empty;
                fee.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                fee.UpdateUserId = tokenUid;
                await _context.SaveChangesAsync();
                return ApiResponse<string>.SuccessResult("更新費用成功", "更新費用成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新費用時發生錯誤");
                return ApiResponse<string>.ErrorResult("更新費用時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 刪除費用
        /// </summary>
        /// <param name="request">費用刪除請求</param>
        /// <param name="tokenUid">使用者識別碼</param>
        /// <returns>刪除結果</returns>
        public async Task<ApiResponse<string>> DeleteFeeAsync(FeeDeleteRequestDTO request, string tokenUid)
        {
            try
            {
                var fee = await _context.Rms_Fees.FirstOrDefaultAsync(f => f.FeeId == request.FeeId);
                fee.IsDeleted = true;
                await _context.SaveChangesAsync();
                return ApiResponse<string>.SuccessResult("刪除費用成功", "刪除費用成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刪除費用時發生錯誤");
                return ApiResponse<string>.ErrorResult("刪除費用時發生錯誤", 500);
            }
        }
    }
}


