using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Interfaces.Pms;
using Swashbuckle.AspNetCore.Annotations;
using System.Text.RegularExpressions;
using System.ComponentModel.DataAnnotations;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("財產折舊單")]
    public class DepreciationFormController : ControllerBase
    {
        private readonly IDepreciationFormService _depreciationFormService;
        private readonly Regex _dateFormatRegex = new Regex(@"^\d{4}-\d{2}-\d{2}$");

        public DepreciationFormController(IDepreciationFormService depreciationFormService)
        {
            _depreciationFormService = depreciationFormService;
        }

        /// <summary>
        /// 取得折舊單資料
        /// </summary>
        /// <returns>折舊單資料列表</returns>
        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得折舊單資料列表", Description = "取得所有折舊單資料")]
        public async Task<ActionResult<List<DepreciationFormDTO>>> GetDepreciationForms()
        {
            var result = await _depreciationFormService.GetDepreciationFormsAsync();
            return Ok(result);
        }

        /// <summary>
        /// 新增折舊單
        /// </summary>
        /// <param name="_data">折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增折舊單", Description = "新增折舊單")]
        public async Task<ActionResult<string>> AddDepreciationForm([FromBody] DepreciationFormDTO _data)
        {
            var (success, message) = await _depreciationFormService.AddDepreciationFormAsync(_data);
            if (success)
                return Ok(message);
            return BadRequest(message);
        }

        /// <summary>
        /// 編輯折舊單
        /// </summary>
        /// <param name="_data">折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯折舊單", Description = "編輯折舊單")]
        public async Task<ActionResult<string>> EditDepreciationForm([FromBody] DepreciationFormDTO _data)
        {
            var (success, message) = await _depreciationFormService.EditDepreciationFormAsync(_data);
            if (success)
                return Ok(message);
            return BadRequest(message);
        }

        /// <summary>
        /// 刪除折舊單
        /// </summary>
        /// <param name="_data">折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除折舊單", Description = "刪除折舊單")]
        public async Task<ActionResult<string>> DeleteDepreciationForm([FromBody] DepreciationFormDTO _data)
        {
            var (success, message) = await _depreciationFormService.DeleteDepreciationFormAsync(_data);
            if (success)
                return Ok(message);
            return BadRequest(message);
        }

        /// <summary>
        /// 取得折舊單明細
        /// </summary>
        /// <param name="depreciationFormId">折舊單編號</param>
        /// <returns>折舊單明細資料</returns>
        [HttpGet("details/{depreciationFormId}")]
        [SwaggerOperation(Summary = "取得折舊單明細", Description = "取得折舊單明細")]
        public async Task<ActionResult<DepreciationFormDTO>> GetDepreciationFormDetails(string depreciationFormId)
        {
            var result = await _depreciationFormService.GetDepreciationFormDetailsAsync(depreciationFormId);
            return Ok(result);
        }

        /// <summary>
        /// 檢查折舊紀錄是否已被使用
        /// </summary>
        /// <param name="_depreciationId">折舊紀錄編號</param>
        /// <returns>是否已被使用</returns>
        [HttpGet("check/{_depreciationId}")]
        [SwaggerOperation(Summary = "檢查折舊紀錄是否已被使用", Description = "檢查折舊紀錄是否已被使用")]
        public async Task<ActionResult<bool>> IsDepreciationUsed(string _depreciationId)
        {
            var result = await _depreciationFormService.IsDepreciationUsedAsync(_depreciationId);
            return Ok(result);
        }

        /// <summary>
        /// 產生財產折舊表
        /// </summary>
        /// <param name="request">查詢參數</param>
        /// <returns>財產折舊表報告</returns>
        [HttpPost("report/generate")]
        [SwaggerOperation("產生財產折舊表")]
        public async Task<ActionResult<AssetDepreciationReportResponseDTO>> GenerateAssetDepreciationReport(
            [FromBody] AssetDepreciationReportRequestDTO request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (!_dateFormatRegex.IsMatch(request.StartDate) || !_dateFormatRegex.IsMatch(request.EndDate))
            {
                return BadRequest("日期格式必須為YYYY-MM-DD");
            }

            var start = DateTime.Parse(request.StartDate);
            var end = DateTime.Parse(request.EndDate);
            if (start > end)
            {
                return BadRequest("起始日期必須小於或等於結束日期");
            }

            var result = await _depreciationFormService.GenerateAssetDepreciationReportAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 審核折舊單
        /// </summary>
        /// <param name="depreciationFormId">折舊單編號</param>
        /// <param name="isApproved">是否核准</param>
        /// <param name="approverId">審核人員ID</param>
        /// <param name="comment">審核意見</param>
        /// <returns>執行結果及訊息</returns>
        [HttpPost("approve/{depreciationFormId}")]
        [SwaggerOperation(Summary = "審核折舊單", Description = "審核折舊單 (核准或駁回)")]
        public async Task<ActionResult<string>> ApproveDepreciationForm(
            string depreciationFormId,
            [FromQuery] bool isApproved,
            [FromQuery] string approverId,
            [FromQuery] string? comment = null)
        {
            if (string.IsNullOrEmpty(approverId))
            {
                return BadRequest("審核人員ID不能為空");
            }

            var (success, message) = await _depreciationFormService.ApproveDepreciationFormAsync(depreciationFormId, isApproved, approverId, comment);
            if (success)
                return Ok(message);
            return BadRequest(message);
        }

        /// <summary>
        /// 執行折舊單
        /// </summary>
        /// <param name="depreciationFormId">折舊單編號</param>
        /// <param name="operatorId">操作人員ID</param>
        /// <returns>執行結果及訊息</returns>
        [HttpPost("execute/{depreciationFormId}")]
        [SwaggerOperation(Summary = "執行折舊單", Description = "執行已核准的折舊單，更新資產累計折舊金額")]
        public async Task<ActionResult<string>> ExecuteDepreciationForm(
            string depreciationFormId,
            [FromQuery] string operatorId)
        {
            if (string.IsNullOrEmpty(operatorId))
            {
                return BadRequest("操作人員ID不能為空");
            }

            var (success, message) = await _depreciationFormService.ExecuteDepreciationFormAsync(depreciationFormId, operatorId);
            if (success)
                return Ok(message);
            return BadRequest(message);
        }

        /// <summary>
        /// 批次處理折舊單
        /// </summary>
        /// <param name="batchDto">批次處理資料</param>
        /// <returns>執行結果及訊息</returns>
        [HttpPost("batch")]
        [SwaggerOperation(Summary = "批次處理折舊單", Description = "批次審核或執行折舊單")]
        public async Task<ActionResult<string>> BatchProcessDepreciationForms([FromBody] DepreciationFormBatchDTO batchDto)
        {
            if (batchDto == null || batchDto.DepreciationFormIds == null || !batchDto.DepreciationFormIds.Any())
            {
                return BadRequest("折舊單編號列表不能為空");
            }

            if (string.IsNullOrEmpty(batchDto.Action))
            {
                return BadRequest("操作類型不能為空");
            }

            if (string.IsNullOrEmpty(batchDto.OperatorId))
            {
                return BadRequest("操作人員ID不能為空");
            }

            string[] validActions = { "APPROVE", "REJECT", "EXECUTE" };
            if (!validActions.Contains(batchDto.Action.ToUpper()))
            {
                return BadRequest($"無效的操作類型。支援的類型：{string.Join(", ", validActions)}");
            }

            var (success, message) = await _depreciationFormService.BatchProcessDepreciationFormsAsync(batchDto);
            if (success)
                return Ok(message);
            return BadRequest(message);
        }

        /// <summary>
        /// 取得折舊單統計資料
        /// </summary>
        /// <param name="userId">使用者ID (可選，用於個人統計)</param>
        /// <returns>統計資料</returns>
        [HttpGet("statistics")]
        [SwaggerOperation(Summary = "取得折舊單統計資料", Description = "取得折舊單統計資料，支援個人統計")]
        public async Task<ActionResult<object>> GetDepreciationFormStatistics([FromQuery] string? userId = null)
        {
            var result = await _depreciationFormService.GetDepreciationFormStatisticsAsync(userId);
            return Ok(result);
        }

    }
}