"use client";
import React, { useState } from "react";
import {
  Table,
  But<PERSON>,
  Card,
  Alert,
  Tabs,
  Space,
  Typography,
  Tag,
  Pagination,
  Modal,
} from "antd";
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseOutlined,
  FileTextOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import { AssetBatchData } from "../page";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

dayjs.extend(customParseFormat);

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { confirm } = Modal;

interface DataPreviewProps {
  data: AssetBatchData[];
  validationResult: any;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export function DataPreview({
  data,
  validationResult,
  onConfirm,
  onCancel,
  isLoading = false,
}: DataPreviewProps) {
  // 當前頁面
  const [currentPage, setCurrentPage] = useState(1);
  // 每頁顯示筆數
  const pageSize = 10;
  // 分頁資料
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  // 確保 data 是陣列
  const dataArray = Array.isArray(data) ? data : [];
  const currentData = dataArray.slice(startIndex, endIndex);

  // 表格欄位定義
  const formatDate = (value: string | number): string => {
    if (!value || value === "0") return "-";

    let parsed: any = null;

    if (typeof value === "number") {
      const num = value;
      if (num > 1e12) {
        parsed = dayjs(num);
      } else if (num > 1e9) {
        parsed = dayjs.unix(num);
      } else {
        parsed = dayjs(num);
      }
    } else {
      const trimmed = value.trim();
      if (/^\d+$/.test(trimmed)) {
        const num = parseInt(trimmed, 10);
        if (num > 1e12) {
          parsed = dayjs(num);
        } else if (num > 1e9) {
          parsed = dayjs.unix(num);
        } else {
          // 可能是年或其他無效數值
          return "-";
        }
      } else {
        const formats = [
          "YYYY/M/D",
          "YYYY/MM/DD",
          "YYYY-M-D",
          "YYYY-MM-DD",
          "YYYY/M/D HH:mm:ss",
          "YYYY-MM-DD HH:mm:ss",
        ];
        for (const f of formats) {
          const d = dayjs(trimmed, f, true);
          if (d.isValid()) {
            parsed = d;
            break;
          }
        }
        if (!parsed) {
          const d = dayjs(trimmed);
          if (d.isValid()) parsed = d;
        }
      }
    }

    return parsed && parsed.isValid() ? parsed.format("YYYY/MM/DD") : "-";
  };

  const columns = [
    {
      title: "序號",
      dataIndex: "index",
      key: "index",
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: "財產編號",
      dataIndex: "財產編號",
      key: "財產編號",
      width: 120,
      fixed: "left" as const,
    },
    {
      title: "財產名稱",
      dataIndex: "財產名稱",
      key: "財產名稱",
      width: 200,
    },
    {
      title: "財產簡稱",
      dataIndex: "財產簡稱",
      key: "財產簡稱",
      width: 150,
    },
    {
      title: "取得日期",
      dataIndex: "取得日期",
      key: "取得日期",
      width: 120,
      render: (value: string | number) => formatDate(value),
    },
    {
      title: "數量",
      dataIndex: "數量",
      key: "數量",
      width: 80,
      render: (value: number) => value?.toLocaleString() || 0,
    },
    {
      title: "購入金額",
      dataIndex: "購入金額",
      key: "購入金額",
      width: 120,
      render: (value: number) => `$${value?.toLocaleString() || 0}`,
    },
    {
      title: "部門",
      dataIndex: "部門",
      key: "部門",
      width: 120,
      render: (value: string) => value || "-",
    },
    {
      title: "保管人",
      dataIndex: "保管人",
      key: "保管人",
      width: 120,
      render: (value: string) => value || "-",
    },
    {
      title: "使用人",
      dataIndex: "使用人",
      key: "使用人",
      width: 120,
      render: (value: string) => value || "-",
    },
    {
      title: "財產狀態",
      dataIndex: "財產狀態",
      key: "財產狀態",
      width: 120,
      render: (value: string) => value || "-",
    },
    {
      title: "使用狀態",
      dataIndex: "使用狀態",
      key: "使用狀態",
      width: 120,
      render: (value: string) => value || "-",
    },
    {
      title: "存放地點",
      dataIndex: "存放地點",
      key: "存放地點",
      width: 120,
      render: (value: string) => value || "-",
    },
    {
      title: "耐用年限",
      dataIndex: "耐用年限",
      key: "耐用年限",
      width: 100,
      render: (value: number) => (value > 0 ? `${value}年` : "-"),
    },
    {
      title: "備註",
      dataIndex: "備註",
      key: "備註",
      width: 150,
      render: (value: string) => value || "-",
    },
  ];

  // 驗證結果統計
  const validationStats = {
    total: validationResult?.totalRows || dataArray.length,
    valid:
      validationResult?.validCount ||
      validationResult?.validRows ||
      dataArray.length,
    invalid: validationResult?.invalidCount || validationResult?.errorRows || 0,
    warnings: validationResult?.warningCount || 0,
  };

  const tabItems = [
    {
      key: "preview",
      label: "資料預覽",
      children: (
        <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
          {dataArray.length > 0 ? (
            <>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Text>
                  共 {dataArray.length} 筆資料，顯示第 {startIndex + 1} -{" "}
                  {Math.min(endIndex, dataArray.length)} 筆
                </Text>
                <Pagination
                  current={currentPage}
                  pageSize={pageSize}
                  total={dataArray.length}
                  showSizeChanger={false}
                  showQuickJumper
                  onChange={setCurrentPage}
                  size="small"
                />
              </div>

              <Table
                dataSource={currentData.map((item, index) => ({
                  ...item,
                  key: startIndex + index,
                }))}
                columns={columns}
                pagination={false}
                scroll={{ x: 1200, y: 400 }}
                size="middle"
                bordered
                locale={{
                  emptyText: dataArray.length === 0 ? "無資料" : "此頁面無資料",
                }}
              />
            </>
          ) : (
            <Alert
              type="info"
              showIcon
              message="無可預覽的資料"
              description="由於檔案中存在錯誤，無法顯示預覽資料。請查看下方的驗證詳情來修正問題。"
            />
          )}
        </div>
      ),
    },
  ];

  // 驗證詳情
  if (validationResult?.details && validationResult.details.length > 0) {
    tabItems.push({
      key: "validation",
      label: `驗證詳情 (${validationResult.details.length})`,
      children: (
        <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
          <div style={{ marginBottom: "16px" }}>
            <Text strong>錯誤清單：</Text>
            <Text type="secondary" style={{ marginLeft: "8px" }}>
              共發現 {validationResult.details.length} 個錯誤需要修正
            </Text>
          </div>

          <div style={{ maxHeight: "400px", overflowY: "auto" }}>
            {validationResult.details.map((detail: any, index: number) => (
              <Alert
                key={index}
                type={detail.type === "error" ? "error" : "warning"}
                showIcon
                message={`第 ${detail.row} 列 - ${detail.field}`}
                description={
                  <div>
                    <div style={{ marginBottom: "4px" }}>
                      <strong>錯誤訊息：</strong>
                      {detail.message}
                    </div>
                    {detail.value && (
                      <div>
                        <strong>問題值：</strong>
                        <code
                          style={{
                            backgroundColor: "#f5f5f5",
                            padding: "2px 4px",
                            borderRadius: "3px",
                          }}
                        >
                          {detail.value}
                        </code>
                      </div>
                    )}
                  </div>
                }
                style={{ marginBottom: "8px" }}
              />
            ))}
          </div>
        </div>
      ),
    });
  }

  // 無資料
  if (
    (!dataArray || dataArray.length === 0) &&
    (!validationResult || !validationResult.details)
  ) {
    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
        <Alert
          type="warning"
          showIcon
          message="無法讀取資料"
          description="檔案中沒有找到有效的資料行，請檢查檔案格式是否正確。"
        />
        <div
          style={{ display: "flex", justifyContent: "flex-end", gap: "16px" }}
        >
          <Button size="large" onClick={onCancel}>
            重新上傳
          </Button>
        </div>
      </div>
    );
  }

  // 處理確認匯入
  const handleConfirmImport = () => {
    // 顯示確認對話框
    confirm({
      title: "確認匯入",
      icon: <QuestionCircleOutlined style={{ color: "#1890ff" }} />,
      content: (
        <div>
          <h3>
            確定要匯入
            <strong
              style={{
                color: "#ff4d4f",
                margin: "0 4px",
                textDecoration: "underline",
              }}
            >
              {dataArray.length}
            </strong>
            筆資料？
          </h3>
          <h4>匯入後將無法取消，請確認資料正確性。</h4>
        </div>
      ),
      okText: "確認匯入",
      cancelText: "取消",
      okButtonProps: {
        type: "primary",
        danger: true,
      },
      cancelButtonProps: {
        type: "default",
      },
      onOk: onConfirm,
    });
  };

  // 處理取消
  const handleCancel = () => {
    // 顯示確認對話框
    confirm({
      title: "確認取消",
      icon: <QuestionCircleOutlined style={{ color: "#faad14" }} />,
      content: "確定要取消匯入嗎？已上傳的資料將不會被保存。",
      okText: "確認取消",
      cancelText: "返回",
      onOk() {
        onCancel();
      },
    });
  };

  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
      {/* 驗證結果摘要 */}
      <Card>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            marginBottom: "16px",
          }}
        >
          <Title level={4} style={{ margin: 0 }}>
            檔案驗證結果
          </Title>
          <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <Tag color="success">
                <CheckCircleOutlined /> 有效:{" "}
                <strong>{validationStats.valid}</strong> 筆
              </Tag>
            </div>
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <Tag color="warning">
                <ExclamationCircleOutlined /> 警告:{" "}
                <strong>{validationStats.warnings}</strong> 筆
              </Tag>
            </div>
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <Tag color="error">
                <CloseOutlined /> 錯誤:{" "}
                <strong>{validationStats.invalid}</strong> 筆
              </Tag>
            </div>
          </div>
        </div>

        {/* 驗證狀態 */}
        {validationStats.invalid > 0 ? (
          <Alert
            type="error"
            showIcon
            message="發現資料錯誤"
            description={`檔案中有 ${validationStats.invalid} 筆資料存在錯誤，請修正後重新上傳。`}
          />
        ) : validationStats.warnings > 0 ? (
          <Alert
            type="warning"
            showIcon
            message="發現資料警告"
            description={`檔案中有 ${validationStats.warnings} 筆資料存在警告，建議檢查後再匯入。`}
          />
        ) : (
          <Alert
            type="success"
            showIcon
            message="資料檢核成功"
            description={`所有 ${validationStats.total} 筆資料均檢核成功，可以進行匯入。`}
          />
        )}
      </Card>

      {/* 資料預覽 */}
      <Card>
        <Tabs
          defaultActiveKey={
            validationStats.invalid > 0 ? "validation" : "preview"
          }
          items={tabItems}
        />
      </Card>

      {/* 操作按鈕 */}
      <div style={{ display: "flex", justifyContent: "flex-end", gap: "16px" }}>
        <Button size="large" onClick={handleCancel} disabled={isLoading}>
          取消
        </Button>

        <Button
          type="default"
          danger
          size="large"
          onClick={handleConfirmImport}
          disabled={isLoading || validationStats.invalid > 0}
          icon={<FileTextOutlined />}
          loading={isLoading}
        >
          {isLoading ? "處理中..." : "匯入"}
        </Button>
      </div>

      {/* 匯入須知 */}
      <Card size="small" title="匯入須知">
        <ul
          style={{
            margin: 0,
            paddingLeft: "16px",
          }}
        >
          <li style={{ marginBottom: "8px" }}>
            匯入過程中請勿關閉瀏覽器或離開此頁面
          </li>
          <li style={{ marginBottom: "8px" }}>
            匯入完成後將顯示詳細的處理結果
          </li>
          <li>如有錯誤資料，請修正後重新匯入</li>
        </ul>
      </Card>
    </div>
  );
}
