# SmsSites API 規格書

## 概述

SmsSites 提供站台的管理功能，包括查詢、新增、更新、刪除和批次操作。站台是 Web 伺服器和資料庫伺服器的組合，用於部署和管理應用程式。

## API 端點

### 1. 查詢站台

**GET** `/api/sms/SmsSites`

#### 查詢參數

| 參數名稱 | 類型 | 必填 | 說明 |
|---------|------|------|------|
| keyword | string | 否 | 關鍵字搜尋 |
| webServerId | string(uuid) | 否 | Web 伺服器 ID |
| dbServerId | string(uuid) | 否 | 資料庫伺服器 ID |
| ownerUserId | string | 否 | 擁有者使用者 ID |

#### 回應

- **200 OK**: 成功查詢站台列表

### 2. 新增站台

**POST** `/api/sms/SmsSites`

#### 請求內容

```json
{
  "createTime": 0,
  "createUserId": "string",
  "updateTime": 0,
  "updateUserId": "string",
  "deleteTime": 0,
  "deleteUserId": "string",
  "isDeleted": false,
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "siteName": "string",
  "webServerId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "dbServerId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "purposeDescription": "string",
  "ownerUserId": "string",
  "coOwnerUserId1": "string",
  "coOwnerUserId2": "string",
  "enableDate": 0,
  "disableDate": 0,
  "webServer": {
    "createTime": 0,
    "createUserId": "string",
    "updateTime": 0,
    "updateUserId": "string",
    "deleteTime": 0,
    "deleteUserId": "string",
    "isDeleted": false,
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "serverName": "string",
    "location": "string",
    "ipAddress": "string",
    "operatingSystem": "string",
    "environment": "string",
    "remarks": "string",
    "vmPort": 0,
    "dockerPort": 0,
    "enableDate": 0,
    "disableDate": 0
  },
  "dbServer": {
    "createTime": 0,
    "createUserId": "string",
    "updateTime": 0,
    "updateUserId": "string",
    "deleteTime": 0,
    "deleteUserId": "string",
    "isDeleted": false,
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "serverName": "string",
    "location": "string",
    "ipAddress": "string",
    "dbEngine": "string",
    "version": "string",
    "environment": "string",
    "remarks": "string",
    "vmPort": 0,
    "dockerPort": 0,
    "enableDate": 0,
    "disableDate": 0
  },
  "owner": {
    "createTime": 0,
    "createUserId": "string",
    "updateTime": 0,
    "updateUserId": "string",
    "deleteTime": 0,
    "deleteUserId": "string",
    "isDeleted": false,
    "userId": "string",
    "account": "string",
    "password": "string",
    "name": "string",
    "enterpriseGroupId": "string",
    "rolesId": "string",
    "positionId": "string",
    "eMail": "string",
    "permanentAddress": "string",
    "mailingAddress": "string",
    "telNo": "string",
    "phone": "string",
    "altPhone": "string",
    "sortCode": 0,
    "unlockTime": 0
  },
  "coOwner1": {
    "createTime": 0,
    "createUserId": "string",
    "updateTime": 0,
    "updateUserId": "string",
    "deleteTime": 0,
    "deleteUserId": "string",
    "isDeleted": false,
    "userId": "string",
    "account": "string",
    "password": "string",
    "name": "string",
    "enterpriseGroupId": "string",
    "rolesId": "string",
    "positionId": "string",
    "eMail": "string",
    "permanentAddress": "string",
    "mailingAddress": "string",
    "telNo": "string",
    "phone": "string",
    "altPhone": "string",
    "sortCode": 0,
    "unlockTime": 0
  },
  "coOwner2": {
    "createTime": 0,
    "createUserId": "string",
    "updateTime": 0,
    "updateUserId": "string",
    "deleteTime": 0,
    "deleteUserId": "string",
    "isDeleted": false,
    "userId": "string",
    "account": "string",
    "password": "string",
    "name": "string",
    "enterpriseGroupId": "string",
    "rolesId": "string",
    "positionId": "string",
    "eMail": "string",
    "permanentAddress": "string",
    "mailingAddress": "string",
    "telNo": "string",
    "phone": "string",
    "altPhone": "string",
    "sortCode": 0,
    "unlockTime": 0
  }
}
```

#### 回應

- **200 OK**: 成功新增站台

### 3. 取得站台明細

**GET** `/api/sms/SmsSites/{id}`

#### 路徑參數

| 參數名稱 | 類型 | 必填 | 說明 |
|---------|------|------|------|
| id | string(uuid) | 是 | 站台 ID |

#### 回應

- **200 OK**: 成功取得站台明細

### 4. 更新站台

**PUT** `/api/sms/SmsSites/{id}`

#### 路徑參數

| 參數名稱 | 類型 | 必填 | 說明 |
|---------|------|------|------|
| id | string(uuid) | 是 | 站台 ID |

#### 請求內容

同新增站台的請求內容格式

#### 回應

- **200 OK**: 成功更新站台

### 5. 刪除站台

**DELETE** `/api/sms/SmsSites/{id}`

#### 路徑參數

| 參數名稱 | 類型 | 必填 | 說明 |
|---------|------|------|------|
| id | string(uuid) | 是 | 站台 ID |

#### 回應

- **200 OK**: 成功刪除站台

### 6. 批次新增站台

**POST** `/api/sms/SmsSites/batch`

#### 請求內容

```json
[
  {
    "createTime": 0,
    "createUserId": "string",
    "updateTime": 0,
    "updateUserId": "string",
    "deleteTime": 0,
    "deleteUserId": "string",
    "isDeleted": false,
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "siteName": "string",
    "webServerId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "dbServerId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "purposeDescription": "string",
    "ownerUserId": "string",
    "coOwnerUserId1": "string",
    "coOwnerUserId2": "string",
    "enableDate": 0,
    "disableDate": 0
  }
]
```

#### 回應

- **200 OK**: 成功批次新增站台

## 資料模型

### SmsSite

| 欄位名稱 | 類型 | 說明 |
|---------|------|------|
| id | string(uuid) | 唯一識別碼 |
| siteName | string | 站台名稱 |
| webServerId | string(uuid) | Web 伺服器 ID |
| dbServerId | string(uuid) | 資料庫伺服器 ID |
| purposeDescription | string | 用途描述 |
| ownerUserId | string | 擁有者使用者 ID |
| coOwnerUserId1 | string | 共同擁有者 1 使用者 ID |
| coOwnerUserId2 | string | 共同擁有者 2 使用者 ID |
| enableDate | number | 啟用日期 |
| disableDate | number | 停用日期 |
| createTime | number | 建立時間 |
| createUserId | string | 建立使用者 ID |
| updateTime | number | 更新時間 |
| updateUserId | string | 更新使用者 ID |
| deleteTime | number | 刪除時間 |
| deleteUserId | string | 刪除使用者 ID |
| isDeleted | boolean | 是否已刪除 |

### 關聯物件

#### WebServer

| 欄位名稱 | 類型 | 說明 |
|---------|------|------|
| id | string(uuid) | 唯一識別碼 |
| serverName | string | 伺服器名稱 |
| location | string | 位置 |
| ipAddress | string | IP 位址 |
| operatingSystem | string | 作業系統 |
| environment | string | 環境 |
| remarks | string | 備註 |
| vmPort | number | VM 連接埠 |
| dockerPort | number | Docker 連接埠 |
| enableDate | number | 啟用日期 |
| disableDate | number | 停用日期 |

#### DbServer

| 欄位名稱 | 類型 | 說明 |
|---------|------|------|
| id | string(uuid) | 唯一識別碼 |
| serverName | string | 伺服器名稱 |
| location | string | 位置 |
| ipAddress | string | IP 位址 |
| dbEngine | string | 資料庫引擎 |
| version | string | 版本 |
| environment | string | 環境 |
| remarks | string | 備註 |
| vmPort | number | VM 連接埠 |
| dockerPort | number | Docker 連接埠 |
| enableDate | number | 啟用日期 |
| disableDate | number | 停用日期 |

#### User

| 欄位名稱 | 類型 | 說明 |
|---------|------|------|
| userId | string | 使用者 ID |
| account | string | 帳號 |
| name | string | 姓名 |
| eMail | string | 電子郵件 |
| phone | string | 電話 |
| positionId | string | 職位 ID |

## 錯誤處理

- **400 Bad Request**: 請求參數錯誤
- **404 Not Found**: 站台不存在
- **409 Conflict**: 站台名稱重複或伺服器已被使用
- **500 Internal Server Error**: 伺服器內部錯誤

## 使用範例

### 查詢站台

```bash
curl -X GET "https://api.example.com/api/sms/SmsSites?keyword=production&webServerId=123e4567-e89b-12d3-a456-************"
```

### 新增站台

```bash
curl -X POST "https://api.example.com/api/sms/SmsSites" \
  -H "Content-Type: application/json" \
  -d '{
    "siteName": "Production-Site-01",
    "webServerId": "123e4567-e89b-12d3-a456-************",
    "dbServerId": "987fcdeb-51a2-43d1-b789-123456789abc",
    "purposeDescription": "主要生產環境站台",
    "ownerUserId": "user001",
    "enableDate": *************
  }'
```

### 更新站台

```bash
curl -X PUT "https://api.example.com/api/sms/SmsSites/123e4567-e89b-12d3-a456-************" \
  -H "Content-Type: application/json" \
  -d '{
    "siteName": "Production-Site-01-Updated",
    "purposeDescription": "更新後的主要生產環境站台"
  }'
```
