"use client";
/* 系統參數設定
    /app/pms/parameter_settings/system_parameter_setting/page.tsx
  功能說明
    1. 選擇參數類型
    2. 選擇參數
    3. 新增參數
    4. 編輯參數
    5. 刪除參數
*/
import React, { useState, useEffect } from "react";
import {
  Card,
  Tabs,
  Table,
  Button,
  Modal,
  Form,
  Input,
  message,
  Spin,
  Switch,
  InputNumber,
  Typography,
  Divider,
  Select,
  Space,
  Popconfirm,
  Radio,
  List,
  Tag,
  Tooltip,
  Alert,
  Descriptions,
  Steps,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  CalculatorOutlined,
  PlusCircleOutlined,
  MinusCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import {
  SystemParameter,
  getSystemParameters,
  getSystemParametersByType,
  addSystemParameter,
  editSystemParameter,
  deleteSystemParameter,
  getDepreciationMethods,
  setDefaultDepreciationMethod,
  getDecliningBalanceRates,
  setDecliningBalanceRate,
  getInitializationStatus,
  setInitializationStatus,
  getAssetAccountDepreciationMethods,
  setAssetAccountDepreciationMethod,
  AssetAccountDepreciationMethod,
  SetAssetAccountDepreciationMethodRequest,
} from "@/services/pms/systemParameterSettingService";
import { useAuth } from "@/contexts/AuthContext";
import DepreciationSimulationModal from "./DepreciationSimulationModal";

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { Step } = Steps;

// 折舊法設定流程配置
interface DepreciationMethodStep {
  title: string;
  description: string;
  status: "wait" | "process" | "finish" | "error";
  icon?: React.ReactNode;
}

// 獲取折舊法設定流程步驟
const getDepreciationMethodSteps = (
  selectedMethod: string,
  status: {
    isStraightLineComplete: boolean;
    isDecliningBalanceComplete: boolean;
    isMixedMethodComplete: boolean;
  }
): DepreciationMethodStep[] => {
  const baseSteps: DepreciationMethodStep[] = [
    {
      title: "選擇折舊法",
      description: "選擇適合的折舊法",
      status: "wait",
      icon: <InfoCircleOutlined />,
    },
    {
      title: "完成相關設定",
      description: "根據選擇的折舊法完成必要設定",
      status: "wait",
      icon: <ClockCircleOutlined />,
    },
    {
      title: "設定默認選項",
      description: "將選擇的折舊法設為系統默認",
      status: "wait",
      icon: <CheckCircleOutlined />,
    },
  ];

  // 根據選擇的折舊法調整步驟
  if (selectedMethod === "直線法") {
    baseSteps[0] = {
      title: "確認折舊法",
      description: "確認折舊法為「直線法」",
      status: "finish",
      icon: <CheckCircleOutlined />,
    };
    baseSteps[1] = {
      title: "直線法設定",
      description: "直線法無需額外設定，可直接設定默認選項",
      status: "finish",
      icon: <CheckCircleOutlined />,
    };
    baseSteps[2] = {
      title: "設定默認選項",
      description: "將直線法設為系統默認折舊法",
      status: status.isStraightLineComplete ? "finish" : "process",
      icon: status.isStraightLineComplete ? (
        <CheckCircleOutlined />
      ) : (
        <ClockCircleOutlined />
      ),
    };
  } else if (selectedMethod === "餘額遞減法") {
    baseSteps[0] = {
      title: "確認折舊法",
      description: "確認折舊法為「餘額遞減法」",
      status: "finish",
      icon: <CheckCircleOutlined />,
    };
    baseSteps[1] = {
      title: "餘額遞減法折舊率設定",
      description: status.isDecliningBalanceComplete
        ? "已完成各財產科目的餘額遞減法折舊率設定"
        : "需設定各財產科目的餘額遞減法折舊率",
      status: status.isDecliningBalanceComplete ? "finish" : "process",
      icon: status.isDecliningBalanceComplete ? (
        <CheckCircleOutlined />
      ) : (
        <ClockCircleOutlined />
      ),
    };
    baseSteps[2] = {
      title: "設定默認選項",
      description: status.isDecliningBalanceComplete
        ? "完成折舊率設定後，將餘額遞減法設為系統默認"
        : "需先完成折舊率設定才能設定默認選項",
      status: status.isDecliningBalanceComplete ? "process" : "wait",
      icon: status.isDecliningBalanceComplete ? (
        <ClockCircleOutlined />
      ) : (
        <InfoCircleOutlined />
      ),
    };
  } else if (selectedMethod === "依財產科目設定折舊法") {
    baseSteps[0] = {
      title: "確認折舊法",
      description: "確認折舊法為「依財產科目設定折舊法」",
      status: "finish",
      icon: <CheckCircleOutlined />,
    };
    baseSteps[1] = {
      title: "依財產科目設定折舊法設定",
      description: status.isMixedMethodComplete
        ? "已完成為各財產科目設定適用的折舊方法"
        : "需為各財產科目設定適用的折舊方法（直線法或餘額遞減法）",
      status: status.isMixedMethodComplete ? "finish" : "process",
      icon: status.isMixedMethodComplete ? (
        <CheckCircleOutlined />
      ) : (
        <ClockCircleOutlined />
      ),
    };
    baseSteps[2] = {
      title: "設定默認選項",
      description: status.isMixedMethodComplete
        ? "完成依財產科目設定折舊法設定後，將其設為系統默認"
        : "需先完成依財產科目設定折舊法設定才能設定默認選項",
      status: status.isMixedMethodComplete ? "process" : "wait",
      icon: status.isMixedMethodComplete ? (
        <ClockCircleOutlined />
      ) : (
        <InfoCircleOutlined />
      ),
    };
  }

  return baseSteps;
};

interface ParameterTypeOption {
  value: string;
  label: string;
}

const SystemParameterSettingPage: React.FC = () => {
  const [parameters, setParameters] = useState<SystemParameter[]>([]);
  const [depreciationMethods, setDepreciationMethods] = useState<
    SystemParameter[]
  >([]);
  const [decliningBalanceRates, setDecliningBalanceRates] = useState<
    SystemParameter[]
  >([]);
  const [assetAccountDepreciationMethods, setAssetAccountDepreciationMethods] =
    useState<AssetAccountDepreciationMethod[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [modalTitle, setModalTitle] = useState<string>("");
  const [currentParameter, setCurrentParameter] =
    useState<SystemParameter | null>(null);
  const [activeTab, setActiveTab] = useState<string>("general");
  const [selectedDepreciationMethod, setSelectedDepreciationMethod] =
    useState<string>("直線法");
  const [form] = Form.useForm();
  const { user } = useAuth();
  const [rateSetting, setRateSetting] = useState<{
    assetAccountId: string;
    rate: number;
    userId: string;
  }>({
    assetAccountId: "",
    rate: 0,
    userId: "",
  });
  const [isMobile, setIsMobile] = useState(false);
  const [canEditDecliningBalanceRate, setCanEditDecliningBalanceRate] =
    useState<boolean>(false);
  const [editingRateKey, setEditingRateKey] = useState<string>("");
  const [rateEditForm] = Form.useForm();
  const [depreciationSimulationVisible, setDepreciationSimulationVisible] =
    useState(false);
  const [simulationRate, setSimulationRate] = useState(0);
  const [simulationTitle, setSimulationTitle] = useState("");
  const [isInitialized, setIsInitialized] = useState<boolean>(true);
  const [initializationMessage, setInitializationMessage] =
    useState<string>("");
  const [confirmMethodName, setConfirmMethodName] = useState<string>("");
  const [confirmModalVisible, setConfirmModalVisible] =
    useState<boolean>(false);
  const [selectedMethodId, setSelectedMethodId] = useState<string>("");
  const [selectedMethodName, setSelectedMethodName] = useState<string>("");
  const [jsonKeyValues, setJsonKeyValues] = useState<
    { key: string; value: string }[]
  >([]);
  const [isJsonValue, setIsJsonValue] = useState<boolean>(false);
  const [isConfirmModalVisible, setIsConfirmModalVisible] =
    useState<boolean>(false);
  const [confirmData, setConfirmData] = useState<any>(null);
  const [simulationAssetAccountId, setSimulationAssetAccountId] =
    useState<string>("");

  // 檢查是否已設定默認折舊法
  const isDefaultDepreciationMethodSet = () => {
    return (
      isInitialized ||
      depreciationMethods.some((method) => {
        try {
          const jsonObj = JSON.parse(method.parameterValue);
          return jsonObj.isDefault === true;
        } catch {
          return false;
        }
      })
    );
  };

  // 系統參數類型選項
  const parameterTypeOptions: ParameterTypeOption[] = [
    { value: "general", label: "一般參數" },
  ];

  // 一般參數設定表格列定義
  const columns = [
    {
      title: "參數名稱",
      dataIndex: "parameterName",
      key: "parameterName",
      width: "15%",
    },
    {
      title: "參數值",
      dataIndex: "parameterValue",
      key: "parameterValue",
      width: "30%",
      render: (text: string) => {
        try {
          // 嘗試解析 JSON
          const jsonObj = JSON.parse(text);
          return <pre>{JSON.stringify(jsonObj, null, 2)}</pre>;
        } catch (e) {
          // 如果不是 JSON 就直接顯示文字
          return text;
        }
      },
    },
    {
      title: "參數說明",
      dataIndex: "parameterDescription",
      key: "parameterDescription",
      width: "25%",
    },
    {
      title: "參數類型",
      dataIndex: "parameterType",
      key: "parameterType",
      width: "10%",
      render: (text: string) => {
        const option = parameterTypeOptions.find((opt) => opt.value === text);
        return option ? option.label : text;
      },
    },
    {
      title: "啟用狀態",
      dataIndex: "isEnabled",
      key: "isEnabled",
      width: "10%",
      render: (isEnabled: boolean) => (
        <Tag color={isEnabled ? "success" : "error"}>
          {isEnabled ? "啟用" : "停用"}
        </Tag>
      ),
    },
    {
      title: "操作",
      key: "action",
      width: "10%",
      render: (_: any, record: SystemParameter) => (
        <Space size="middle">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          {/* <Popconfirm
            title="確定要刪除此參數嗎？"
            onConfirm={() => handleDelete(record)}
            okText="確定"
            cancelText="取消"
          >
            <Button type="link" size="small" danger icon={<DeleteOutlined />}>
              刪除
            </Button>
          </Popconfirm> */}
        </Space>
      ),
    },
  ];

  // 加載所有系統參數
  const loadSystemParameters = async () => {
    setLoading(true);
    try {
      const response = await getSystemParameters();
      if (response.success && response.data) {
        setParameters(response.data);
      } else {
        message.error(response.message || "獲取系統參數失敗");
      }
    } catch (error) {
      message.error("獲取系統參數時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 加載折舊法設定
  const loadDepreciationMethods = async () => {
    setLoading(true);
    try {
      const response = await getDepreciationMethods();
      if (response.success && response.data) {
        setDepreciationMethods(response.data);
        // 檢查是否有任何折舊法設為默認選項
        const hasDefaultMethod = response.data.find((method) => {
          try {
            const jsonObj = JSON.parse(method.parameterValue);
            return jsonObj.isDefault;
          } catch (e) {
            return false;
          }
        });
        // 如果沒有設定默認折舊法，則開放編輯餘額遞減法折舊率
        setCanEditDecliningBalanceRate(!hasDefaultMethod);
      } else {
        message.error(response.message || "獲取折舊法設定失敗");
      }
    } catch (error) {
      message.error("獲取折舊法設定時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 加載餘額遞減法折舊率設定
  const loadDecliningBalanceRates = async () => {
    setLoading(true);
    try {
      const response = await getDecliningBalanceRates();
      if (response.success && response.data) {
        setDecliningBalanceRates(response.data);
      } else {
        message.error(response.message || "獲取餘額遞減法折舊率設定失敗");
      }
    } catch (error) {
      message.error("獲取餘額遞減法折舊率設定時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 載入資產科目折舊方法設定
  const loadAssetAccountDepreciationMethods = async () => {
    setLoading(true);
    try {
      const response = await getAssetAccountDepreciationMethods();
      if (response.success && response.data) {
        // 將 SystemParameter[] 轉換為 AssetAccountDepreciationMethod[]
        const convertedData: AssetAccountDepreciationMethod[] =
          response.data.map((item: SystemParameter) => {
            try {
              const jsonObj = JSON.parse(item.parameterValue);
              return {
                assetAccountId: jsonObj.assetAccountId,
                assetAccountName: jsonObj.assetAccountName,
                depreciationMethodId: jsonObj.depreciationMethodId,
                depreciationMethodName: jsonObj.depreciationMethodName,
                depreciationMethodCode: jsonObj.depreciationMethodKey,
                isDefault: false, // 這個欄位在 API 回應中可能不存在
                createTime: item.createTime,
                createUserId: item.createUserId,
                updateTime: item.updateTime,
                updateUserId: item.updateUserId,
              };
            } catch (error) {
              console.error("解析資產科目折舊方法資料失敗:", error);
              return {
                assetAccountId: item.parameterId,
                assetAccountName: item.parameterName,
                depreciationMethodId: "",
                depreciationMethodName: "",
                depreciationMethodCode: "",
                isDefault: false,
                createTime: item.createTime,
                createUserId: item.createUserId,
                updateTime: item.updateTime,
                updateUserId: item.updateUserId,
              };
            }
          });

        setAssetAccountDepreciationMethods(convertedData);
      } else {
        message.error(response.message || "載入資產科目折舊方法設定失敗");
      }
    } catch (error) {
      console.error("載入資產科目折舊方法設定失敗:", error);
      message.error("載入資產科目折舊方法設定失敗");
    } finally {
      setLoading(false);
    }
  };

  // 設定資產科目折舊方法
  const handleSetAssetAccountDepreciationMethod = async (
    assetAccountId: string,
    depreciationMethodId: string
  ) => {
    // 檢查是否已設定默認折舊法
    if (isDefaultDepreciationMethodSet()) {
      message.warning("系統已設定默認折舊法，無法變更折舊方法設定");
      return;
    }

    try {
      setLoading(true);
      const request: SetAssetAccountDepreciationMethodRequest = {
        assetAccountId: assetAccountId,
        depreciationMethodId: depreciationMethodId,
        userId: user?.userId || "",
      };

      const response = await setAssetAccountDepreciationMethod(request);
      if (response.success) {
        message.success("設定資產科目折舊方法成功");
        // 更新本地數據
        const updatedMethods = assetAccountDepreciationMethods.map((item) => {
          if (item.assetAccountId === assetAccountId) {
            return {
              ...item,
              depreciationMethodId: depreciationMethodId,
            };
          }
          return item;
        });
        setAssetAccountDepreciationMethods(updatedMethods);
      } else {
        message.error(response.message || "設定資產科目折舊方法失敗");
        // 重新加載數據
        loadAssetAccountDepreciationMethods();
      }
    } catch (error) {
      message.error("設定資產科目折舊方法時發生錯誤");
      // 重新加載數據
      loadAssetAccountDepreciationMethods();
    } finally {
      setLoading(false);
    }
  };

  // 依類型加載系統參數
  const loadParametersByType = async (type: string) => {
    setLoading(true);
    try {
      const response = await getSystemParametersByType(type);
      if (response.success && response.data) {
        setParameters(response.data);
      } else {
        message.error(response.message || `獲取${type}類型參數失敗`);
      }
    } catch (error) {
      message.error(`獲取${type}類型參數時發生錯誤`);
    } finally {
      setLoading(false);
    }
  };

  // 檢查系統初始化狀態
  const checkInitializationStatus = async () => {
    try {
      const response = await getInitializationStatus();
      if (response.success) {
        setIsInitialized(response.data.isInitialized);
        setInitializationMessage(response.data.message);
      } else {
        message.error(response.message || "檢查系統初始化狀態失敗");
      }
    } catch (error) {
      message.error("檢查系統初始化狀態時發生錯誤");
    }
  };

  // 設定系統初始化狀態
  const handleSetInitializationStatus = async () => {
    try {
      const response = await setInitializationStatus(true);
      if (response.success) {
        setIsInitialized(true);
        checkInitializationStatus(); // 重新檢查狀態以獲取最新訊息
      } else {
        message.error(response.message || "設定系統初始化狀態失敗");
      }
    } catch (error) {
      message.error("設定系統初始化狀態時發生錯誤");
    }
  };

  // 載入初始資料
  const loadInitialData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        checkInitializationStatus(),
        loadSystemParameters(),
        loadDepreciationMethods(),
        loadDecliningBalanceRates(),
        loadAssetAccountDepreciationMethods(),
      ]);
    } catch (error) {
      console.error("載入初始資料失敗:", error);
      message.error("載入初始資料失敗");
    } finally {
      setLoading(false);
    }
  };

  // 檢查手機版
  const checkMobile = () => {
    setIsMobile(window.innerWidth <= 768);
  };

  useEffect(() => {
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  useEffect(() => {
    loadInitialData();
  }, [activeTab]);

  // 處理 Tab 切換
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  // 新增系統參數
  const handleAdd = () => {
    setModalTitle("新增系統參數");
    setCurrentParameter(null);
    form.resetFields(); // 重置表單
    setJsonKeyValues([{ key: "", value: "" }]);
    setIsJsonValue(false);
    setIsModalVisible(true);
  };

  // 編輯系統參數
  const handleEdit = (record: SystemParameter) => {
    setModalTitle("編輯系統參數");
    setCurrentParameter(record);

    // 嘗試解析參數值是否為JSON格式
    try {
      const jsonObj = JSON.parse(record.parameterValue);
      if (typeof jsonObj === "object" && jsonObj !== null) {
        // 將JSON物件轉換為鍵值對數組
        const keyValues = Object.entries(jsonObj).map(([key, value]) => ({
          key,
          value:
            typeof value === "object" ? JSON.stringify(value) : String(value),
        }));
        setJsonKeyValues(keyValues);
        setIsJsonValue(true);
      } else {
        setIsJsonValue(false);
      }
    } catch (e) {
      // 不是有效的JSON，視為純文字
      setIsJsonValue(false);
    }

    form.setFieldsValue({
      parameterId: record.parameterId,
      parameterName: record.parameterName,
      parameterValue: record.parameterValue,
      parameterDescription: record.parameterDescription,
      parameterType: record.parameterType,
      isEnabled: record.isEnabled,
      sortOrder: record.sortOrder,
    });
    setIsModalVisible(true);
  };

  // 刪除系統參數
  const handleDelete = async (record: SystemParameter) => {
    try {
      setLoading(true);
      const response = await deleteSystemParameter(record);
      if (response.success) {
        message.success("刪除系統參數成功");
        // 重新加載數據
        if (activeTab === "general") {
          loadSystemParameters();
        } else if (activeTab === "depreciation_method") {
          loadDepreciationMethods();
        } else if (activeTab === "declining_balance_rate") {
          loadDecliningBalanceRates();
        } else if (activeTab === "asset_account_depreciation_method") {
          loadAssetAccountDepreciationMethods();
        }
      } else {
        message.error(response.message || "刪除系統參數失敗");
      }
    } catch (error) {
      message.error("刪除系統參數時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 設定默認折舊法
  const handleSetDefaultDepreciationMethod = async (methodId: string) => {
    if (isInitialized) {
      message.warning("系統已初始化，無法變更折舊法設定");
      return;
    }

    try {
      setLoading(true);
      const response = await setDefaultDepreciationMethod(methodId);
      if (response.success) {
        message.success("設定默認折舊法成功");
        // 檢查是否設定了默認折舊法
        const method = depreciationMethods.find(
          (m) => m.parameterId === methodId
        );
        if (method) {
          try {
            const jsonObj = JSON.parse(method.parameterValue);
            // 如果設定了默認折舊法，則關閉編輯餘額遞減法折舊率
            setCanEditDecliningBalanceRate(false);
            // 如果設定了默認折舊法，自動設定系統為已初始化
            await handleSetInitializationStatus();
          } catch (e) {
            setCanEditDecliningBalanceRate(true);
          }
        }
        loadDepreciationMethods();
      } else {
        message.error(response.message || "設定默認折舊法失敗");
      }
    } catch (error) {
      message.error("設定默認折舊法時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 判斷折舊率是否正在編輯
  const isEditingRate = (record: SystemParameter) =>
    record.parameterId === editingRateKey;

  // 開始編輯折舊率
  const editRate = (record: SystemParameter) => {
    // 檢查是否已設定默認折舊法
    if (isDefaultDepreciationMethodSet()) {
      message.warning("系統已設定默認折舊法，無法編輯折舊率");
      return;
    }

    try {
      const jsonObj = JSON.parse(record.parameterValue);
      rateEditForm.setFieldsValue({ rate: jsonObj.rate });
      setEditingRateKey(record.parameterId);
    } catch (e) {
      console.error("解析折舊率數據錯誤:", e);
    }
  };

  // 保存編輯的折舊率
  const saveRate = async (record: SystemParameter) => {
    try {
      const values = await rateEditForm.validateFields();
      const rate = values.rate;

      await handleSetDecliningBalanceRate(record, rate);
      setEditingRateKey("");
    } catch (errInfo) {
      console.error("驗證失敗:", errInfo);
    }
  };

  // 設定餘額遞減法折舊率
  const handleSetDecliningBalanceRate = async (
    record: SystemParameter,
    rate: number
  ) => {
    // 檢查是否已設定默認折舊法
    if (isDefaultDepreciationMethodSet()) {
      message.warning("系統已設定默認折舊法，無法變更折舊率設定");
      return;
    }

    try {
      setLoading(true);
      const jsonObj = JSON.parse(record.parameterValue);
      const request = {
        assetAccountId: jsonObj.assetAccountId,
        rate: rate,
        userId: user?.userId || "",
      };

      const response = await setDecliningBalanceRate(request);
      if (response.success) {
        message.success("設定餘額遞減法折舊率成功");
        // 更新本地數據
        const updatedRates = decliningBalanceRates.map((item) => {
          if (item.parameterId === record.parameterId) {
            return {
              ...item,
              parameterValue: JSON.stringify({
                ...jsonObj,
                rate: rate,
              }),
            };
          }
          return item;
        });
        setDecliningBalanceRates(updatedRates);
      } else {
        message.error(response.message || "設定餘額遞減法折舊率失敗");
        // 重新加載數據
        loadDecliningBalanceRates();
      }
    } catch (error) {
      message.error("設定餘額遞減法折舊率時發生錯誤");
      // 重新加載數據
      loadDecliningBalanceRates();
    } finally {
      setLoading(false);
    }
  };

  // 檢查折舊法設定狀態
  const checkDepreciationMethodStatus = () => {
    // 這裡可以根據實際的資料狀態來判斷各折舊法的完成狀態
    // 目前先返回預設值，實際實作時可以根據 API 資料來判斷
    return {
      isStraightLineComplete: true, // 直線法通常不需要額外設定
      isDecliningBalanceComplete: decliningBalanceRates.length > 0, // 有設定折舊率
      isMixedMethodComplete: assetAccountDepreciationMethods.length > 0, // 有設定依財產科目設定折舊法
    };
  };

  // 處理表單提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 處理JSON格式參數
      if (isJsonValue && jsonKeyValues.length > 0) {
        // 過濾掉空鍵值對
        const validKeyValues = jsonKeyValues.filter(
          (item) => item.key.trim() !== ""
        );
        // 創建一個空對象來存儲解析後的鍵值對
        const jsonObj: Record<string, any> = {};
        // 遍歷過濾後的鍵值對
        validKeyValues.forEach((item) => {
          // 嘗試將值轉換為適當的數據類型（數字、布爾值或保持字符串）
          let parsedValue: any = item.value;
          if (parsedValue === "true") {
            parsedValue = true;
          } else if (parsedValue === "false") {
            parsedValue = false;
          } else if (!isNaN(Number(parsedValue)) && parsedValue.trim() !== "") {
            parsedValue = Number(parsedValue);
          }
          jsonObj[item.key] = parsedValue;
        });

        values.parameterValue = JSON.stringify(jsonObj);
      }

      // 顯示確認畫面
      if (currentParameter) {
        // 如果是編輯
        setConfirmData({
          ...currentParameter,
          ...values,
          updateUserId: user?.userId || "",
          isEdit: true,
        });
      } else {
        // 如果是新增
        setConfirmData({
          parameterId: "", // 由後端生成
          ...values,
          createUserId: user?.userId || "",
          isDeleted: false,
          isEdit: false,
        });
      }
      setIsConfirmModalVisible(true);
    } catch (error) {
      message.error("表單驗證失敗，請檢查輸入資料");
    }
  };

  // 確認後提交
  const handleConfirmSubmit = async () => {
    setIsConfirmModalVisible(false);
    setLoading(true);

    try {
      if (confirmData.isEdit) {
        // 編輯模式
        const { isEdit, ...parameterData } = confirmData;
        const response = await editSystemParameter(parameterData);
        if (response.success) {
          message.success("編輯系統參數成功");
          setIsModalVisible(false);
          // 重新加載數據
          if (activeTab === "general") {
            loadSystemParameters();
          } else if (activeTab === "depreciation_method") {
            loadDepreciationMethods();
          } else if (activeTab === "declining_balance_rate") {
            loadDecliningBalanceRates();
          } else if (activeTab === "asset_account_depreciation_method") {
            loadAssetAccountDepreciationMethods();
          }
        } else {
          message.error(response.message || "編輯系統參數失敗");
        }
      } else {
        // 新增模式
        const { isEdit, ...parameterData } = confirmData;
        const response = await addSystemParameter(parameterData);
        if (response.success) {
          message.success("新增系統參數成功");
          setIsModalVisible(false);
          // 重新加載數據
          if (activeTab === "general") {
            loadSystemParameters();
          } else if (activeTab === "depreciation_method") {
            loadDepreciationMethods();
          } else if (activeTab === "declining_balance_rate") {
            loadDecliningBalanceRates();
          } else if (activeTab === "asset_account_depreciation_method") {
            loadAssetAccountDepreciationMethods();
          }
        } else {
          message.error(response.message || "新增系統參數失敗");
        }
      }
    } catch (error) {
      message.error("提交資料時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 開啟確認視窗
  const showConfirmModal = (methodId: string, methodName: string) => {
    setSelectedMethodId(methodId);
    setSelectedMethodName(methodName);
    setConfirmMethodName("");
    setConfirmModalVisible(true);
  };

  // 確認設定默認折舊法
  const confirmSetDefaultMethod = async () => {
    if (confirmMethodName !== selectedMethodName) {
      message.error("輸入的折舊法名稱不正確");
      return;
    }
    setConfirmModalVisible(false);
    await handleSetDefaultDepreciationMethod(selectedMethodId);
  };

  // 折舊法表格列定義
  const depreciationMethodColumns = [
    {
      title: "折舊法",
      dataIndex: "parameterName",
      key: "parameterName",
      width: "10%",
    },
    {
      title: "設定值",
      dataIndex: "parameterValue",
      key: "parameterValue",
      width: "40%",
      render: (text: string) => {
        try {
          const jsonObj = JSON.parse(text);
          return (
            <div>
              <p>
                <strong>代碼:</strong> {jsonObj.key}
              </p>
              <p>
                <strong>說明:</strong> {jsonObj.description}
              </p>
              <p>
                <strong>公式:</strong> {jsonObj.formula}
              </p>
              {jsonObj.rate && (
                <p>
                  <strong>折舊率:</strong> {jsonObj.rate}
                </p>
              )}
            </div>
          );
        } catch (e) {
          return text;
        }
      },
    },
    {
      title: "參數說明",
      dataIndex: "parameterDescription",
      key: "parameterDescription",
      width: "25%",
      ellipsis: false,
      wordWrap: true,
    },
    {
      title: "默認選項",
      dataIndex: "parameterValue",
      key: "isDefault",
      width: "10%",
      render: (text: string, record: SystemParameter) => {
        try {
          const jsonObj = JSON.parse(text);
          return (
            <Space direction="vertical" size="small">
              {isInitialized ? (
                <>
                  <Radio checked={jsonObj.isDefault} disabled />
                  {jsonObj.isDefault && <Tag color="green">已設為默認</Tag>}
                </>
              ) : (
                <Radio
                  checked={jsonObj.isDefault}
                  onChange={() =>
                    showConfirmModal(record.parameterId, record.parameterName)
                  }
                />
              )}
            </Space>
          );
        } catch (e) {
          return <Radio disabled />;
        }
      },
    },
  ];

  // 將 decliningBalanceRates 轉換為 assetAccounts 陣列
  const assetAccounts = decliningBalanceRates.map((item) => {
    try {
      const jsonObj = JSON.parse(item.parameterValue);
      return {
        assetAccountId: item.parameterId,
        assetAccountName: jsonObj.assetAccountName || "",
        rate: jsonObj.rate || 0,
      };
    } catch {
      return {
        assetAccountId: item.parameterId,
        assetAccountName: "",
        rate: 0,
      };
    }
  });

  // 修改 showDepreciationSimulation，傳入 assetAccountId
  const showDepreciationSimulation = (assetAccountId: string) => {
    setSimulationAssetAccountId(assetAccountId);
    const found = assetAccounts.find(
      (a) => a.assetAccountId === assetAccountId
    );
    setSimulationRate(found ? found.rate : 0);
    setSimulationTitle(
      `${found ? found.assetAccountName : ""} - 餘額遞減法折舊試算`
    );
    setDepreciationSimulationVisible(true);
  };

  // 餘額遞減法折舊率表格列定義
  const decliningBalanceRateColumns = [
    {
      title: "財產科目",
      dataIndex: "parameterValue",
      key: "assetAccountName",
      width: "10%",
      render: (text: string) => {
        try {
          const jsonObj = JSON.parse(text);
          const assetAccountName = jsonObj.assetAccountName || "";
          return assetAccountName;
        } catch (e) {
          return "";
        }
      },
    },
    {
      title: "折舊率 (%)",
      dataIndex: "parameterValue",
      key: "rate",
      width: "10%",
      render: (text: string, record: SystemParameter) => {
        const editable = isEditingRate(record);
        try {
          const jsonObj = JSON.parse(text);
          return editable ? (
            <Form form={rateEditForm} component={false}>
              <Form.Item
                name="rate"
                style={{ margin: 0 }}
                rules={[
                  { required: true, message: "請輸入折舊率" },
                  {
                    type: "number",
                    min: 0,
                    message: "折舊率為%數",
                  },
                ]}
                initialValue={jsonObj.rate}
              >
                <InputNumber
                  min={0}
                  step={10}
                  precision={2}
                  formatter={(value) => `${value}%`}
                  style={{ width: "100px" }}
                  placeholder="請輸入折舊率"
                />
              </Form.Item>
            </Form>
          ) : (
            <span>{jsonObj.rate}%</span>
          );
        } catch (e) {
          return 0;
        }
      },
    },
    {
      title: "參數說明",
      dataIndex: "parameterDescription",
      key: "parameterDescription",
      width: "60%",
    },
    {
      title: "折舊試算",
      width: "20%",
      render: (_: any, record: SystemParameter) => {
        try {
          const jsonObj = JSON.parse(record.parameterValue);
          const assetAccountId = record.parameterId;
          const assetName = jsonObj.assetAccountName || "";
          const rate = jsonObj.rate;
          if (assetName === "土地" || assetName === "未完工程" || rate <= 0) {
            return <Text type="secondary">不適用</Text>;
          }
          return (
            <Button
              type="link"
              icon={<CalculatorOutlined />}
              onClick={() => showDepreciationSimulation(assetAccountId)}
            >
              查看試算
            </Button>
          );
        } catch (e) {
          return null;
        }
      },
    },
    {
      title: "操作",
      key: "action",
      width: "20%",
      render: (_: any, record: SystemParameter) => {
        const editable = isEditingRate(record);

        // 如果已設定默認折舊法，顯示純文字
        if (isDefaultDepreciationMethodSet()) {
          return <Text type="secondary">已鎖定</Text>;
        }

        return editable ? (
          <Space>
            <Button
              type="link"
              onClick={() => saveRate(record)}
              style={{ marginRight: 8 }}
            >
              儲存
            </Button>
            <Button type="link" onClick={() => setEditingRateKey("")}>
              取消
            </Button>
          </Space>
        ) : (
          <Button
            type="link"
            disabled={editingRateKey !== "" || !canEditDecliningBalanceRate}
            icon={<EditOutlined />}
            onClick={() => editRate(record)}
          >
            編輯
          </Button>
        );
      },
    },
  ];

  // 資產科目折舊方法表格列定義
  const assetAccountDepreciationMethodColumns = [
    {
      title: "財產科目",
      dataIndex: "assetAccountName",
      key: "assetAccountName",
      width: "20%",
    },
    {
      title: "折舊方法",
      key: "depreciationMethodSelection",
      width: "40%",
      render: (_: any, record: AssetAccountDepreciationMethod) => {
        // 土地和未完工程不適用折舊
        if (
          record.assetAccountName === "土地" ||
          record.assetAccountName === "未完工程"
        ) {
          return <Text type="secondary">不適用</Text>;
        }

        const availableMethods = depreciationMethods.filter(
          (method) =>
            method.isEnabled && method.parameterName !== "依財產科目設定折舊法"
        );

        // 如果已設定默認折舊法，顯示純文字
        if (isDefaultDepreciationMethodSet()) {
          const currentMethod = availableMethods.find(
            (method) => method.parameterId === record.depreciationMethodId
          );
          if (currentMethod) {
            try {
              const methodData = JSON.parse(currentMethod.parameterValue);
              return (
                <Text>{methodData.name || currentMethod.parameterName}</Text>
              );
            } catch {
              return <Text>{currentMethod.parameterName}</Text>;
            }
          }
          return <Text type="secondary">未設定</Text>;
        }

        // 否則顯示Select下拉選單
        return (
          <Space>
            <Select
              value={record.depreciationMethodId}
              style={{ width: 180 }}
              onChange={(value) =>
                handleSetAssetAccountDepreciationMethod(
                  record.assetAccountId,
                  value
                )
              }
              placeholder="選擇折舊方法"
            >
              {availableMethods.map((method) => {
                try {
                  const methodData = JSON.parse(method.parameterValue);
                  const isCurrentMethod =
                    method.parameterId === record.depreciationMethodId;
                  return (
                    <Option key={method.parameterId} value={method.parameterId}>
                      {methodData.name || method.parameterName}
                    </Option>
                  );
                } catch {
                  const isCurrentMethod =
                    method.parameterId === record.depreciationMethodId;
                  return (
                    <Option key={method.parameterId} value={method.parameterId}>
                      {method.parameterName}
                    </Option>
                  );
                }
              })}
            </Select>
          </Space>
        );
      },
    },
    {
      title: "說明",
      key: "description",
      width: "40%",
      render: (_: any, record: AssetAccountDepreciationMethod) => {
        // 土地和未完工程不適用折舊
        if (
          record.assetAccountName === "土地" ||
          record.assetAccountName === "未完工程"
        ) {
          return "此科目不提列折舊";
        }

        const method = depreciationMethods.find(
          (m) => m.parameterId === record.depreciationMethodId
        );
        if (method) {
          try {
            const methodData = JSON.parse(method.parameterValue);
            return methodData.description || method.parameterDescription || "";
          } catch {
            return method.parameterDescription || "";
          }
        }
        return "";
      },
    },
  ];

  // 手機版折舊法列表
  const renderMobileDepreciationList = () => {
    return (
      <List
        loading={loading}
        dataSource={depreciationMethods}
        renderItem={(method) => {
          const jsonObj = JSON.parse(method.parameterValue);
          return (
            <List.Item
              key={method.parameterId}
              style={{
                padding: "12px",
                borderBottom: "1px solid #f0f0f0",
              }}
            >
              <div style={{ width: "100%" }}>
                <div style={{ marginBottom: "8px" }}>
                  <Text strong>{method.parameterName}</Text>
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">代碼：</Text>
                  <div>{jsonObj.code}</div>
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">說明：</Text>
                  <div>{jsonObj.description}</div>
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">公式：</Text>
                  <div>{jsonObj.formula}</div>
                </div>
                {jsonObj.rate && (
                  <div style={{ marginBottom: "8px" }}>
                    <Text type="secondary">折舊率：</Text>
                    <div>{jsonObj.rate}</div>
                  </div>
                )}
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">參數說明：</Text>
                  <div>{method.parameterDescription}</div>
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">默認選項：</Text>
                  {isInitialized ? (
                    <>
                      <Radio checked={jsonObj.isDefault} disabled />
                      {jsonObj.isDefault && (
                        <Tag color="green" style={{ marginLeft: 8 }}>
                          已設為默認
                        </Tag>
                      )}
                    </>
                  ) : (
                    <Radio
                      checked={jsonObj.isDefault}
                      onChange={() =>
                        showConfirmModal(
                          method.parameterId,
                          method.parameterName
                        )
                      }
                    />
                  )}
                </div>
              </div>
            </List.Item>
          );
        }}
        pagination={{
          pageSize: 10,
          size: "small",
        }}
      />
    );
  };

  // 手機版餘額遞減法折舊率列表
  const renderMobileDecliningBalanceList = () => {
    return (
      <List
        loading={loading}
        dataSource={decliningBalanceRates}
        renderItem={(rate) => {
          const jsonObj = JSON.parse(rate.parameterValue);
          const editable = isEditingRate(rate);
          const assetName = jsonObj.assetAccountName || "";
          const rateValue = jsonObj.rate;
          return (
            <List.Item
              key={rate.parameterId}
              style={{
                padding: "12px",
                borderBottom: "1px solid #f0f0f0",
              }}
            >
              <div style={{ width: "100%" }}>
                <div style={{ marginBottom: "8px" }}>
                  <Text strong>{jsonObj.assetAccountName}</Text>
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">折舊率：</Text>
                  {editable ? (
                    <Form form={rateEditForm} component={false}>
                      <Form.Item
                        name="rate"
                        style={{ margin: 0 }}
                        rules={[
                          { required: true, message: "請輸入折舊率" },
                          {
                            type: "number",
                            min: 0,
                            message: "折舊率為%數",
                          },
                        ]}
                        initialValue={jsonObj.rate}
                      >
                        <InputNumber
                          min={0}
                          step={10}
                          precision={2}
                          formatter={(value) => `${value}%`}
                          style={{ width: "100px" }}
                          placeholder="請輸入折舊率"
                        />
                      </Form.Item>
                    </Form>
                  ) : (
                    <span>{jsonObj.rate}%</span>
                  )}
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">說明：</Text>
                  <div>{rate.parameterDescription}</div>
                </div>
                {!canEditDecliningBalanceRate && (
                  <div style={{ marginBottom: "8px" }}>
                    <Text type="warning">
                      已設定默認折舊法，無法編輯餘額遞減法折舊率
                    </Text>
                  </div>
                )}
                <Space style={{ marginTop: "12px" }} wrap>
                  {isDefaultDepreciationMethodSet() ? (
                    <Text type="secondary">已鎖定</Text>
                  ) : editable ? (
                    <>
                      <Button type="link" onClick={() => saveRate(rate)}>
                        儲存
                      </Button>
                      <Button type="link" onClick={() => setEditingRateKey("")}>
                        取消
                      </Button>
                    </>
                  ) : (
                    <Button
                      type="link"
                      disabled={
                        editingRateKey !== "" || !canEditDecliningBalanceRate
                      }
                      icon={<EditOutlined />}
                      onClick={() => editRate(rate)}
                    >
                      編輯
                    </Button>
                  )}

                  {/* 折舊試算按鈕 */}
                  {assetName !== "土地" &&
                    assetName !== "未完工程" &&
                    rateValue > 0 && (
                      <Button
                        type="link"
                        icon={<CalculatorOutlined />}
                        onClick={() =>
                          showDepreciationSimulation(rate.parameterId)
                        }
                      >
                        查看試算
                      </Button>
                    )}
                </Space>
              </div>
            </List.Item>
          );
        }}
        pagination={{
          pageSize: 10,
          size: "small",
        }}
      />
    );
  };

  // 切換參數值輸入方式
  const toggleValueInputType = () => {
    if (!isJsonValue) {
      // 從純文字切換到JSON格式
      try {
        // 取得當前文字區域的值並嘗試解析為JSON
        const currentValue = form.getFieldValue("parameterValue");

        if (currentValue && currentValue.trim() !== "") {
          try {
            const parsedJson = JSON.parse(currentValue);
            if (typeof parsedJson === "object" && parsedJson !== null) {
              // 將JSON物件轉換為鍵值對數組
              const keyValues = Object.entries(parsedJson).map(
                ([key, value]) => ({
                  key,
                  value:
                    typeof value === "object"
                      ? JSON.stringify(value)
                      : String(value),
                })
              );

              if (keyValues.length > 0) {
                setJsonKeyValues(keyValues);
                setIsJsonValue(true);
                return;
              }
            }
          } catch (error) {
            console.error("無法解析JSON格式:", error);
            // 如果解析失敗，顯示提示訊息
            message.warning("現有值不是有效的JSON格式，已切換為空白鍵值對");
          }
        }

        // 如果沒有有效的JSON或解析失敗，添加一個空白鍵值對
        setJsonKeyValues([{ key: "", value: "" }]);
      } catch (error) {
        // 出現異常時添加一個空白鍵值對
        setJsonKeyValues([{ key: "", value: "" }]);
        console.error("切換到JSON格式時出錯:", error);
      }
    } else {
      // 從JSON格式切換到純文字
      try {
        if (jsonKeyValues.length > 0) {
          const jsonObj: Record<string, any> = {};

          // 只處理有效的鍵值對（鍵名不為空）
          jsonKeyValues
            .filter((item) => item.key.trim() !== "")
            .forEach((item) => {
              // 嘗試將值轉換為適當的數據類型
              let parsedValue: any = item.value;
              if (parsedValue === "true") {
                parsedValue = true;
              } else if (parsedValue === "false") {
                parsedValue = false;
              } else if (
                !isNaN(Number(parsedValue)) &&
                parsedValue.trim() !== ""
              ) {
                parsedValue = Number(parsedValue);
              }
              jsonObj[item.key] = parsedValue;
            });

          // 美化JSON格式以便於閱讀
          form.setFieldsValue({
            parameterValue: JSON.stringify(jsonObj, null, 2),
          });
        } else {
          form.setFieldsValue({ parameterValue: "{}" });
        }
      } catch (error) {
        console.error("轉換JSON格式時出錯:", error);
        // 發生錯誤時至少保留一個空的JSON對象
        form.setFieldsValue({ parameterValue: "{}" });
      }
    }
    setIsJsonValue(!isJsonValue);
  };

  // 新增鍵值對
  const addKeyValuePair = () => {
    setJsonKeyValues([...jsonKeyValues, { key: "", value: "" }]);
  };

  // 移除鍵值對
  const removeKeyValuePair = (index: number) => {
    const newKeyValues = [...jsonKeyValues];
    newKeyValues.splice(index, 1);
    setJsonKeyValues(newKeyValues);
  };

  // 更新鍵值對
  const updateKeyValue = (
    index: number,
    field: "key" | "value",
    value: string
  ) => {
    const newKeyValues = [...jsonKeyValues];
    newKeyValues[index][field] = value;
    setJsonKeyValues(newKeyValues);
  };

  // 格式化參數值顯示
  const formatParameterValue = (value: string) => {
    try {
      const jsonObj = JSON.parse(value);
      return (
        <pre style={{ maxHeight: "200px", overflow: "auto" }}>
          {JSON.stringify(jsonObj, null, 2)}
        </pre>
      );
    } catch (e) {
      return value;
    }
  };

  // 取得參數類型名稱
  const getParameterTypeName = (type: string) => {
    const option = parameterTypeOptions.find((opt) => opt.value === type);
    return option ? option.label : type;
  };

  return (
    <div className="system-parameter-setting">
      <Card
        title={<Title level={4}>系統參數設定</Title>}
        extra={
          activeTab === "general" && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
              style={{ width: isMobile ? "100%" : "auto" }}
            >
              新增參數
            </Button>
          )
        }
        styles={{
          body: { padding: isMobile ? "12px" : "24px" },
        }}
      >
        <Spin spinning={loading}>
          <Tabs
            activeKey={activeTab}
            onChange={handleTabChange}
            items={[
              {
                key: "general",
                label: "一般參數設定",
                children: (
                  <>
                    {isInitialized && initializationMessage && (
                      <Alert
                        message={initializationMessage}
                        type="info"
                        showIcon
                        style={{ marginBottom: 16 }}
                      />
                    )}
                    <Table
                      columns={columns}
                      dataSource={parameters}
                      rowKey="parameterId"
                      pagination={{
                        defaultPageSize: 10,
                        showSizeChanger: true,
                        pageSizeOptions: ["10", "20", "50", "100"],
                        showTotal: (total) => `共 ${total} 筆資料`,
                      }}
                    />
                  </>
                ),
              },
              {
                key: "depreciation_method",
                label: "折舊法設定",
                children: (
                  <>
                    <Card
                      title="折舊法設定流程提示"
                      style={{ marginBottom: 16 }}
                    >
                      <Alert
                        message="折舊法設定流程"
                        description="請根據您的需求選擇適合的折舊法，並按照流程完成相關設定。"
                        type="info"
                        showIcon
                        style={{ marginBottom: 16 }}
                      />

                      <div style={{ marginBottom: 16 }}>
                        <Text
                          strong
                          style={{ marginBottom: 8, display: "block" }}
                        >
                          選擇折舊法：
                        </Text>
                        <Radio.Group
                          value={selectedDepreciationMethod}
                          onChange={(e) =>
                            setSelectedDepreciationMethod(e.target.value)
                          }
                          style={{ marginBottom: 16 }}
                        >
                          <Radio.Button value="直線法">直線法</Radio.Button>
                          <Radio.Button value="餘額遞減法">
                            餘額遞減法
                          </Radio.Button>
                          <Radio.Button value="依財產科目設定折舊法">
                            依財產科目設定折舊法
                          </Radio.Button>
                        </Radio.Group>
                      </div>

                      <Steps
                        current={1} // 顯示當前步驟
                        direction={isMobile ? "vertical" : "horizontal"}
                        items={getDepreciationMethodSteps(
                          selectedDepreciationMethod,
                          checkDepreciationMethodStatus()
                        ).map((step, index) => ({
                          title: step.title,
                          description: step.description,
                          status: step.status,
                          icon: step.icon,
                        }))}
                        style={{ marginBottom: 16 }}
                      />

                      <Alert
                        message="流程說明"
                        description={
                          <div>
                            <p>
                              <strong>直線法：</strong>
                              無需額外設定，可直接設定默認選項
                            </p>
                            <p>
                              <strong>餘額遞減法：</strong>
                              需完成「餘額遞減法折舊率設定」後才能設定默認選項
                            </p>
                            <p>
                              <strong>依財產科目設定折舊法：</strong>
                              需完成「依財產科目設定折舊法設定」後才能設定默認選項
                            </p>
                          </div>
                        }
                        type="warning"
                        showIcon
                      />
                    </Card>

                    <Divider />

                    {isMobile ? (
                      renderMobileDepreciationList()
                    ) : (
                      <Table
                        columns={depreciationMethodColumns}
                        dataSource={depreciationMethods}
                        rowKey="parameterId"
                        pagination={{
                          defaultPageSize: 10,
                          showSizeChanger: true,
                          pageSizeOptions: ["10", "20", "50", "100"],
                          showTotal: (total) => `共 ${total} 筆資料`,
                        }}
                      />
                    )}
                  </>
                ),
              },
              {
                key: "declining_balance_rate",
                label: "餘額遞減法折舊率設定",
                children: (
                  <>
                    {isDefaultDepreciationMethodSet() && (
                      <Alert
                        message="系統已設定默認折舊法"
                        description="由於系統已設定默認折舊法，此頁籤中的折舊率設定已被鎖定，無法變更。如需修改，請先取消默認折舊法設定。"
                        type="warning"
                        showIcon
                        style={{ marginBottom: 16 }}
                      />
                    )}
                    {isMobile ? (
                      renderMobileDecliningBalanceList()
                    ) : (
                      <Table
                        columns={decliningBalanceRateColumns}
                        dataSource={decliningBalanceRates}
                        rowKey="parameterId"
                        pagination={{
                          defaultPageSize: 10,
                          showSizeChanger: true,
                          pageSizeOptions: ["10", "20", "50", "100"],
                          showTotal: (total) => `共 ${total} 筆資料`,
                        }}
                        bordered
                        size="middle"
                      />
                    )}
                  </>
                ),
              },
              {
                key: "asset_account_depreciation_method",
                label: "依財產科目設定折舊法",
                children: (
                  <div>
                    <Card
                      title="資產科目折舊方法設定"
                      style={{ marginBottom: 16 }}
                    >
                      {isDefaultDepreciationMethodSet() && (
                        <Alert
                          message="系統已設定默認折舊法"
                          description="由於系統已設定默認折舊法，此頁籤中的折舊方法設定已被鎖定，無法變更。如需修改，請先取消默認折舊法設定。"
                          type="warning"
                          showIcon
                          style={{ marginBottom: 16 }}
                        />
                      )}
                      <Alert
                        message="依財產科目設定折舊法"
                        description="為每個財產科目設定適用的折舊方法（直線法或餘額遞減法）。系統會根據此設定來計算各科目的折舊。"
                        type="info"
                        showIcon
                        style={{ marginBottom: 16 }}
                      />
                      {isMobile ? (
                        <div>
                          {/* 手機版顯示 */}
                          {assetAccountDepreciationMethods.map((item) => (
                            <Card
                              key={item.assetAccountId}
                              size="small"
                              style={{ marginBottom: 8 }}
                            >
                              <div style={{ marginBottom: 8 }}>
                                <Text strong>{item.assetAccountName}</Text>
                              </div>
                              <div>
                                {/* 土地和未完工程不適用折舊 */}
                                {item.assetAccountName === "土地" ||
                                item.assetAccountName === "未完工程" ? (
                                  <div>
                                    <Text type="secondary">不適用</Text>
                                    <div style={{ marginTop: 4 }}>
                                      <Text
                                        type="secondary"
                                        style={{ fontSize: "12px" }}
                                      >
                                        此科目不提列折舊
                                      </Text>
                                    </div>
                                  </div>
                                ) : (
                                  <div>
                                    {isDefaultDepreciationMethodSet() ? (
                                      // 如果已設定默認折舊法，顯示純文字
                                      <div>
                                        {(() => {
                                          const currentMethod =
                                            depreciationMethods.find(
                                              (method) =>
                                                method.parameterId ===
                                                item.depreciationMethodId
                                            );
                                          if (currentMethod) {
                                            try {
                                              const methodData = JSON.parse(
                                                currentMethod.parameterValue
                                              );
                                              return (
                                                <Text>
                                                  {methodData.name ||
                                                    currentMethod.parameterName}
                                                </Text>
                                              );
                                            } catch {
                                              return (
                                                <Text>
                                                  {currentMethod.parameterName}
                                                </Text>
                                              );
                                            }
                                          }
                                          return (
                                            <Text type="secondary">未設定</Text>
                                          );
                                        })()}
                                      </div>
                                    ) : (
                                      // 否則顯示Select下拉選單
                                      <Select
                                        value={item.depreciationMethodId}
                                        style={{ width: "100%" }}
                                        onChange={(value) =>
                                          handleSetAssetAccountDepreciationMethod(
                                            item.assetAccountId,
                                            value
                                          )
                                        }
                                        placeholder="選擇折舊方法"
                                      >
                                        {depreciationMethods
                                          .filter(
                                            (method) =>
                                              method.isEnabled &&
                                              method.parameterName !==
                                                "依財產科目設定折舊法"
                                          )
                                          .map((method) => {
                                            try {
                                              const methodData = JSON.parse(
                                                method.parameterValue
                                              );
                                              const isCurrentMethod =
                                                method.parameterId ===
                                                item.depreciationMethodId;
                                              return (
                                                <Option
                                                  key={method.parameterId}
                                                  value={method.parameterId}
                                                >
                                                  {methodData.name ||
                                                    method.parameterName}
                                                </Option>
                                              );
                                            } catch {
                                              const isCurrentMethod =
                                                method.parameterId ===
                                                item.depreciationMethodId;
                                              return (
                                                <Option
                                                  key={method.parameterId}
                                                  value={method.parameterId}
                                                >
                                                  {method.parameterName}
                                                </Option>
                                              );
                                            }
                                          })}
                                      </Select>
                                    )}
                                  </div>
                                )}
                              </div>
                            </Card>
                          ))}
                        </div>
                      ) : (
                        <Table
                          columns={assetAccountDepreciationMethodColumns}
                          dataSource={assetAccountDepreciationMethods}
                          loading={loading}
                          pagination={false}
                          rowKey="assetAccountId"
                          bordered
                        />
                      )}
                    </Card>
                  </div>
                ),
              },
            ]}
          />
        </Spin>

        <Modal
          title={modalTitle}
          open={isModalVisible}
          onOk={handleSubmit}
          onCancel={() => setIsModalVisible(false)}
          confirmLoading={loading}
          width={isMobile ? "100%" : 700}
          style={isMobile ? { top: 0 } : {}}
          okText="確認"
          cancelText="取消"
        >
          <Form form={form} layout="vertical">
            <Form.Item name="parameterId" hidden>
              <Input />
            </Form.Item>

            <Form.Item
              name="parameterName"
              label="參數名稱"
              rules={[{ required: true, message: "請輸入參數名稱" }]}
            >
              <Input placeholder="請輸入參數名稱" />
            </Form.Item>

            <Form.Item label="參數值" required>
              <div style={{ marginBottom: 8 }}>
                <Switch
                  checkedChildren="JSON"
                  unCheckedChildren="純文字"
                  checked={isJsonValue}
                  onChange={toggleValueInputType}
                />
              </div>

              {isJsonValue ? (
                <div>
                  {jsonKeyValues.map((item, index) => (
                    <div
                      key={index}
                      style={{ display: "flex", marginBottom: 8 }}
                    >
                      <Input
                        placeholder="參數名稱"
                        value={item.key}
                        onChange={(e) =>
                          updateKeyValue(index, "key", e.target.value)
                        }
                        style={{ width: "30%", marginRight: 8 }}
                      />
                      <Input
                        placeholder="參數值"
                        value={item.value}
                        onChange={(e) =>
                          updateKeyValue(index, "value", e.target.value)
                        }
                        style={{ width: "60%" }}
                      />
                      <Button
                        type="text"
                        icon={<MinusCircleOutlined />}
                        onClick={() => removeKeyValuePair(index)}
                        disabled={jsonKeyValues.length <= 1}
                        style={{ marginLeft: 8 }}
                      />
                    </div>
                  ))}
                  <Button
                    type="dashed"
                    onClick={addKeyValuePair}
                    icon={<PlusCircleOutlined />}
                    style={{ width: "100%" }}
                  >
                    新增系統參數
                  </Button>
                </div>
              ) : (
                <Form.Item
                  name="parameterValue"
                  noStyle
                  rules={[{ required: true, message: "請輸入參數值" }]}
                >
                  <TextArea
                    rows={4}
                    placeholder="請輸入參數值 (JSON 格式或純文字)"
                  />
                </Form.Item>
              )}
            </Form.Item>

            <Form.Item
              name="parameterDescription"
              label="參數說明"
              rules={[{ required: true, message: "請輸入參數說明" }]}
            >
              <TextArea rows={2} placeholder="請輸入參數說明" />
            </Form.Item>

            <Form.Item
              name="parameterType"
              label="參數類型"
              rules={[{ required: true, message: "請選擇參數類型" }]}
            >
              <Select placeholder="請選擇參數類型">
                {parameterTypeOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="isEnabled"
              label="啟用狀態"
              valuePropName="checked"
              initialValue={true}
            >
              <Switch />
            </Form.Item>
          </Form>
        </Modal>

        {/* 折舊試算 Modal */}
        <DepreciationSimulationModal
          visible={depreciationSimulationVisible}
          onClose={() => setDepreciationSimulationVisible(false)}
          title={simulationTitle}
          assetAccounts={assetAccounts}
          initialAssetAccountId={simulationAssetAccountId}
        />

        {/* 設定默認折舊法確認 Modal */}
        <Modal
          title="確認設定折舊法"
          open={confirmModalVisible}
          onCancel={() => setConfirmModalVisible(false)}
          footer={[
            <Button key="cancel" onClick={() => setConfirmModalVisible(false)}>
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              danger
              onClick={confirmSetDefaultMethod}
              disabled={confirmMethodName !== selectedMethodName}
            >
              設定
            </Button>,
          ]}
        >
          <Alert
            message="注意：設定後系統將自動初始化，之後將無法變更折舊法"
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <p>
            您即將設定「
            <strong style={{ color: "red" }}>{selectedMethodName}</strong>
            」為折舊法
          </p>
          <Input
            placeholder="確認變更請輸入折舊法名稱"
            value={confirmMethodName}
            onChange={(e) => setConfirmMethodName(e.target.value)}
            style={{ marginTop: 8 }}
          />
          {confirmMethodName && confirmMethodName !== selectedMethodName && (
            <Text type="danger" style={{ display: "block", marginTop: 8 }}>
              輸入的折舊法名稱不正確
            </Text>
          )}
        </Modal>

        {/* 確認提交的系統參數 Modal */}
        <Modal
          title="確認系統參數資料"
          open={isConfirmModalVisible}
          onOk={handleConfirmSubmit}
          onCancel={() => setIsConfirmModalVisible(false)}
          width={isMobile ? "100%" : 700}
          style={isMobile ? { top: 0 } : {}}
          confirmLoading={loading}
          okButtonProps={{
            danger: confirmData?.isEdit,
          }}
          okText={confirmData?.isEdit ? "儲存" : "新增"}
          cancelText="取消"
        >
          {confirmData && (
            <Descriptions bordered column={1} size="small">
              <Descriptions.Item label="參數名稱">
                {confirmData.parameterName}
              </Descriptions.Item>
              <Descriptions.Item label="參數值">
                {formatParameterValue(confirmData.parameterValue)}
              </Descriptions.Item>
              <Descriptions.Item label="參數說明">
                {confirmData.parameterDescription}
              </Descriptions.Item>
              <Descriptions.Item label="參數類型">
                {getParameterTypeName(confirmData.parameterType)}
              </Descriptions.Item>
              <Descriptions.Item label="啟用狀態">
                <Tag color={confirmData.isEnabled ? "success" : "error"}>
                  {confirmData.isEnabled ? "啟用" : "停用"}
                </Tag>
              </Descriptions.Item>
            </Descriptions>
          )}
          <div style={{ marginTop: 16 }}>
            <Alert
              message={
                confirmData?.isEdit
                  ? "請確認以上資料是否正確，確認後將更新系統參數"
                  : "請確認以上資料是否正確，確認後將新增系統參數"
              }
              type="error"
              showIcon
            />
          </div>
        </Modal>
      </Card>
    </div>
  );
};

export default SystemParameterSettingPage;
