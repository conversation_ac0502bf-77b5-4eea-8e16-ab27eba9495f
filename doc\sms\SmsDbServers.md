# SmsDbServers API 規格書

## 概述

SmsDbServers 提供資料庫伺服器的管理功能，包括查詢、新增、更新、刪除和批次操作。

## API 端點

### 1. 查詢資料庫伺服器

**GET** `/api/sms/SmsDbServers`

#### 查詢參數

| 參數名稱 | 類型 | 必填 | 說明 |
|---------|------|------|------|
| keyword | string | 否 | 關鍵字搜尋 |
| dbEngine | string | 否 | 資料庫引擎 |
| environment | string | 否 | 環境 |

#### 回應

- **200 OK**: 成功查詢資料庫伺服器列表

### 2. 新增資料庫伺服器

**POST** `/api/sms/SmsDbServers`

#### 請求內容

```json
{
  "createTime": 0,
  "createUserId": "string",
  "updateTime": 0,
  "updateUserId": "string",
  "deleteTime": 0,
  "deleteUserId": "string",
  "isDeleted": false,
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "serverName": "string",
  "location": "string",
  "ipAddress": "string",
  "dbEngine": "string",
  "version": "string",
  "environment": "string",
  "remarks": "string",
  "vmPort": 0,
  "dockerPort": 0,
  "enableDate": 0,
  "disableDate": 0
}
```

#### 回應

- **200 OK**: 成功新增資料庫伺服器

### 3. 取得資料庫伺服器明細

**GET** `/api/sms/SmsDbServers/{id}`

#### 路徑參數

| 參數名稱 | 類型 | 必填 | 說明 |
|---------|------|------|------|
| id | string(uuid) | 是 | 資料庫伺服器 ID |

#### 回應

- **200 OK**: 成功取得資料庫伺服器明細

### 4. 更新資料庫伺服器

**PUT** `/api/sms/SmsDbServers/{id}`

#### 路徑參數

| 參數名稱 | 類型 | 必填 | 說明 |
|---------|------|------|------|
| id | string(uuid) | 是 | 資料庫伺服器 ID |

#### 請求內容

同新增資料庫伺服器的請求內容格式

#### 回應

- **200 OK**: 成功更新資料庫伺服器

### 5. 刪除資料庫伺服器

**DELETE** `/api/sms/SmsDbServers/{id}`

#### 路徑參數

| 參數名稱 | 類型 | 必填 | 說明 |
|---------|------|------|------|
| id | string(uuid) | 是 | 資料庫伺服器 ID |

#### 回應

- **200 OK**: 成功刪除資料庫伺服器

### 6. 批次新增資料庫伺服器

**POST** `/api/sms/SmsDbServers/batch`

#### 請求內容

```json
[
  {
    "createTime": 0,
    "createUserId": "string",
    "updateTime": 0,
    "updateUserId": "string",
    "deleteTime": 0,
    "deleteUserId": "string",
    "isDeleted": false,
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "serverName": "string",
    "location": "string",
    "ipAddress": "string",
    "dbEngine": "string",
    "version": "string",
    "environment": "string",
    "remarks": "string",
    "vmPort": 0,
    "dockerPort": 0,
    "enableDate": 0,
    "disableDate": 0
  }
]
```

#### 回應

- **200 OK**: 成功批次新增資料庫伺服器

## 資料模型

### SmsDbServer

| 欄位名稱 | 類型 | 說明 |
|---------|------|------|
| id | string(uuid) | 唯一識別碼 |
| serverName | string | 伺服器名稱 |
| location | string | 位置 |
| ipAddress | string | IP 位址 |
| dbEngine | string | 資料庫引擎 |
| version | string | 版本 |
| environment | string | 環境 |
| remarks | string | 備註 |
| vmPort | number | VM 連接埠 |
| dockerPort | number | Docker 連接埠 |
| enableDate | number | 啟用日期 |
| disableDate | number | 停用日期 |
| createTime | number | 建立時間 |
| createUserId | string | 建立使用者 ID |
| updateTime | number | 更新時間 |
| updateUserId | string | 更新使用者 ID |
| deleteTime | number | 刪除時間 |
| deleteUserId | string | 刪除使用者 ID |
| isDeleted | boolean | 是否已刪除 |

## 錯誤處理

- **400 Bad Request**: 請求參數錯誤
- **404 Not Found**: 資料庫伺服器不存在
- **500 Internal Server Error**: 伺服器內部錯誤

## 使用範例

### 查詢資料庫伺服器

```bash
curl -X GET "https://api.example.com/api/sms/SmsDbServers?keyword=mysql&environment=production"
```

### 新增資料庫伺服器

```bash
curl -X POST "https://api.example.com/api/sms/SmsDbServers" \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "MySQL-Prod-01",
    "location": "Taipei",
    "ipAddress": "*************",
    "dbEngine": "MySQL",
    "version": "8.0",
    "environment": "production",
    "vmPort": 3306,
    "dockerPort": 3306
  }'
```
