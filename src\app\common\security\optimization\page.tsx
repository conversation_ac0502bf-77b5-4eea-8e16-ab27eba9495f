"use client";

/* 選單權限優化測試頁面
  /app/common/security/optimization/page.tsx
  用於測試和驗證末節點權限儲存策略的效果
*/
import React, { useState, useEffect } from "react";
import {
  Card, Button, Table, Statistic, Row, Col, Alert, 
  Spin, message, Typography, Divider, Tag, Progress
} from "antd";
import {
  AnalysisOutlined, CheckCircleOutlined, WarningOutlined,
  LeafOutlined, BranchesOutlined, DatabaseOutlined
} from "@ant-design/icons";
import { httpClient } from "@/services/http";
import { createContextLogger } from "@/utils/logger";

const { Title, Text } = Typography;
const logger = createContextLogger("MenuPermissionOptimization" as any);

interface AnalysisData {
  Summary: {
    TotalMenus: number;
    TotalPermissions: number;
    TotalRoles: number;
    LeafNodeCount: number;
    ParentNodeCount: number;
    LeafNodesWithPermissions: number;
    ParentNodesWithPermissions: number;
    RedundantPermissionCount: number;
  };
  OptimizationPotential: {
    CanRemovePermissions: number;
    StorageSavingPercentage: number;
    EstimatedFinalPermissionCount: number;
  };
  RedundantPermissionSamples: Array<{
    RolesPermissionsId: string;
    RolesId: string;
    SystemMenuId: string;
    Label: string;
    Reason: string;
    ChildrenCount: number;
  }>;
}

/**
 * 選單權限優化測試頁面
 */
const MenuPermissionOptimizationPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
  const [leafNodes, setLeafNodes] = useState<any[]>([]);

  // 執行權限結構分析
  const analyzePermissionStructure = async () => {
    try {
      setLoading(true);
      logger.info("開始分析權限結構");

      const response = await httpClient("/api/MenuPermissionOptimization/analyze", {
        method: "GET",
      });

      if (response.success) {
        setAnalysisData(response.data);
        message.success("權限結構分析完成");
        logger.info("權限結構分析完成", response.data);
      } else {
        message.error(response.message || "分析失敗");
      }
    } catch (error) {
      logger.error("權限結構分析失敗", error);
      message.error("分析失敗");
    } finally {
      setLoading(false);
    }
  };

  // 獲取末節點列表
  const getLeafNodes = async () => {
    try {
      setLoading(true);
      logger.info("獲取末節點列表");

      const response = await httpClient("/api/MenuPermissionOptimization/leaf-nodes", {
        method: "GET",
      });

      if (response.success) {
        setLeafNodes(response.data.LeafMenus || []);
        message.success("末節點列表載入完成");
      } else {
        message.error(response.message || "載入失敗");
      }
    } catch (error) {
      logger.error("載入末節點列表失敗", error);
      message.error("載入失敗");
    } finally {
      setLoading(false);
    }
  };

  // 頁面載入時執行分析
  useEffect(() => {
    analyzePermissionStructure();
    getLeafNodes();
  }, []);

  // 冗餘權限表格列定義
  const redundantColumns = [
    {
      title: "角色ID",
      dataIndex: "RolesId",
      key: "RolesId",
      width: 120,
    },
    {
      title: "選單名稱",
      dataIndex: "Label",
      key: "Label",
    },
    {
      title: "子節點數",
      dataIndex: "ChildrenCount",
      key: "ChildrenCount",
      width: 100,
      render: (count: number) => <Tag color="blue">{count}</Tag>,
    },
    {
      title: "冗餘原因",
      dataIndex: "Reason",
      key: "Reason",
      render: (reason: string) => <Text type="secondary">{reason}</Text>,
    },
  ];

  // 末節點表格列定義
  const leafNodeColumns = [
    {
      title: "選單名稱",
      dataIndex: "Label",
      key: "Label",
    },
    {
      title: "選單鍵",
      dataIndex: "Key",
      key: "Key",
      render: (key: string) => <Text code>{key}</Text>,
    },
    {
      title: "是否為功能選單",
      dataIndex: "IsMenu",
      key: "IsMenu",
      width: 120,
      render: (isMenu: boolean) => (
        <Tag color={isMenu ? "green" : "orange"}>
          {isMenu ? "功能選單" : "分類節點"}
        </Tag>
      ),
    },
    {
      title: "權限數量",
      dataIndex: "PermissionCount",
      key: "PermissionCount",
      width: 100,
      render: (count: number) => (
        <Tag color={count > 0 ? "green" : "default"}>{count}</Tag>
      ),
    },
  ];

  return (
    <div style={{ padding: "24px" }}>
      {/* 頁面標題 */}
      <div style={{ marginBottom: "24px" }}>
        <Title level={2} style={{ margin: 0, display: "flex", alignItems: "center", gap: "8px" }}>
          <AnalysisOutlined /> 選單權限優化分析
        </Title>
        <Text type="secondary">
          分析當前權限儲存結構，驗證末節點權限儲存策略的優化效果
        </Text>
      </div>

      {/* 操作按鈕 */}
      <div style={{ marginBottom: "24px" }}>
        <Button
          type="primary"
          icon={<AnalysisOutlined />}
          onClick={analyzePermissionStructure}
          loading={loading}
          style={{ marginRight: "12px" }}
        >
          重新分析
        </Button>
        <Button
          icon={<LeafOutlined />}
          onClick={getLeafNodes}
          loading={loading}
        >
          重新載入末節點
        </Button>
      </div>

      <Spin spinning={loading}>
        {/* 分析結果概覽 */}
        {analysisData && (
          <>
            <Card title="權限結構概覽" style={{ marginBottom: "24px" }}>
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title="總選單數"
                    value={analysisData.Summary.TotalMenus}
                    prefix={<BranchesOutlined />}
                  />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title="總權限記錄"
                    value={analysisData.Summary.TotalPermissions}
                    prefix={<DatabaseOutlined />}
                  />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title="末節點數"
                    value={analysisData.Summary.LeafNodeCount}
                    prefix={<LeafOutlined />}
                  />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title="涉及角色數"
                    value={analysisData.Summary.TotalRoles}
                    prefix={<CheckCircleOutlined />}
                  />
                </Col>
              </Row>

              <Divider />

              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <Card size="small" title="末節點權限">
                    <Statistic
                      title="有權限的末節點"
                      value={analysisData.Summary.LeafNodesWithPermissions}
                      suffix={`/ ${analysisData.Summary.LeafNodeCount}`}
                      valueStyle={{ color: "#52c41a" }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12}>
                  <Card size="small" title="父節點權限">
                    <Statistic
                      title="有權限的父節點"
                      value={analysisData.Summary.ParentNodesWithPermissions}
                      suffix={`/ ${analysisData.Summary.ParentNodeCount}`}
                      valueStyle={{ color: "#faad14" }}
                    />
                  </Card>
                </Col>
              </Row>
            </Card>

            {/* 優化潛力分析 */}
            <Card title="優化潛力分析" style={{ marginBottom: "24px" }}>
              <Alert
                message="末節點權限儲存策略優化效果"
                description={
                  <div>
                    <p>
                      通過只儲存末節點權限，可以減少{" "}
                      <strong>{analysisData.OptimizationPotential.CanRemovePermissions}</strong>{" "}
                      筆冗餘的父節點權限記錄
                    </p>
                    <Progress
                      percent={analysisData.OptimizationPotential.StorageSavingPercentage}
                      status="active"
                      strokeColor="#52c41a"
                      format={(percent) => `節省 ${percent}% 儲存空間`}
                    />
                    <p style={{ marginTop: "12px" }}>
                      權限記錄將從{" "}
                      <strong>{analysisData.Summary.TotalPermissions}</strong> 筆減少到約{" "}
                      <strong>{analysisData.OptimizationPotential.EstimatedFinalPermissionCount}</strong> 筆
                    </p>
                  </div>
                }
                type={analysisData.OptimizationPotential.CanRemovePermissions > 0 ? "warning" : "success"}
                icon={analysisData.OptimizationPotential.CanRemovePermissions > 0 ? <WarningOutlined /> : <CheckCircleOutlined />}
                showIcon
              />
            </Card>

            {/* 冗餘權限詳情 */}
            {analysisData.RedundantPermissionSamples.length > 0 && (
              <Card title="冗餘權限範例" style={{ marginBottom: "24px" }}>
                <Table
                  columns={redundantColumns}
                  dataSource={analysisData.RedundantPermissionSamples}
                  rowKey="RolesPermissionsId"
                  size="small"
                  pagination={false}
                />
              </Card>
            )}
          </>
        )}

        {/* 末節點列表 */}
        {leafNodes.length > 0 && (
          <Card title="末節點選單列表">
            <Table
              columns={leafNodeColumns}
              dataSource={leafNodes}
              rowKey="SystemMenuId"
              size="small"
              pagination={{
                defaultPageSize: 10,
                showSizeChanger: true,
                pageSizeOptions: ["10", "20", "50"],
                showTotal: (total) => `共 ${total} 個末節點`,
              }}
            />
          </Card>
        )}
      </Spin>
    </div>
  );
};

export default MenuPermissionOptimizationPage;
