'use client';

import React, { useState, useEffect } from 'react';
import {
    Table,
    Button,
    Modal,
    Form,
    Input,
    Select,
    DatePicker,
    InputNumber,
    message,
    Popconfirm,
    Tabs,
    Space,
    Typography,
    Row,
    Col,
    Checkbox,
    Card
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, UserOutlined, ClockCircleOutlined, SettingOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { TabsProps } from 'antd';
import dayjs from 'dayjs';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import {
    InsuranceGrade,
    createEmptyInsuranceGrade,
    getInsuranceGradeList,
    addInsuranceGrade,
    editInsuranceGrade,
    deleteInsuranceGrade,
} from '@/services/pas/Insurance/InsuranceGradeService';

import InsuranceGradeEmployeeDetailModal from './InsuranceGradeEmployeeDetailModal';

import {
    INSURANCE_TYPES,
    getInsuranceTypeName,
    DATE_FORMAT,
} from "@/services/pas/Insurance/constants/insuranceConstants";

const { Title } = Typography;

// 從圖片中提取的投保級距數據
const TEST_SALARY_GRADES = [
    28590, 28800, 30300, 31800, 33300, 34800, 36300, 38200, 40100, 42000,
    43900, 45800, 48200, 50600, 53000, 55400, 57800, 60800, 63800, 66800,
    69800, 72800, 76500, 80200, 83900, 87600, 92100, 96600, 101100, 105600,
    110100, 115500, 120900, 126300, 131700, 137100, 142500, 147900, 150000
];

/**
 * 保險級距管理組件
 * 提供勞保、健保、職災三種保險級距的增刪改查功能
 */
export default function InsuranceGradeManagement() {
    // ========== 樣式定義 ==========
    const customStyles = `
        .row-expired {
            background-color: #f5f5f5 !important;
            color: #999 !important;
            opacity: 0.7;
        }
        .row-expired:hover {
            background-color: #f0f0f0 !important;
        }
        .row-expired .ant-btn {
            opacity: 0.6;
        }
        .row-expired .ant-btn:hover {
            opacity: 1;
        }
    `;

    // 動態注入樣式
    React.useEffect(() => {
        const styleElement = document.createElement('style');
        styleElement.innerHTML = customStyles;
        document.head.appendChild(styleElement);

        return () => {
            document.head.removeChild(styleElement);
        };
    }, []);
    // ========== 表單與狀態管理 ==========
    const [form] = Form.useForm();

    // 載入狀態
    const [loading, setLoading] = useState(false);

    // 彈窗狀態
    const [modalVisible, setModalVisible] = useState(false);

    // 編輯中的記錄
    const [editingRecord, setEditingRecord] = useState<InsuranceGrade | null>(null);

    // 目前激活的標籤頁（保險類型）
    const [activeTab, setActiveTab] = useState('1'); // 1=勞保, 2=健保, 3=職災

    // 表格資料源
    const [dataSource, setDataSource] = useState<InsuranceGrade[]>([]);

    // 待刪除的記錄 uid
    const [deleteUid, setDeleteUid] = useState<string | null>(null);

    // 是否顯示已過期資料
    const [showExpired, setShowExpired] = useState(false);

    // 原始資料源（未過濾）
    const [originalDataSource, setOriginalDataSource] = useState<InsuranceGrade[]>([]);

    // 員工詳細資訊彈窗狀態
    const [employeeDetailModal, setEmployeeDetailModal] = useState({
        visible: false,
        gradeUid: '',
        status: 'current' as 'current' | 'pending',
        insuranceType: 1,
        monthlySalary: 0
    });



    // ========== 資料載入相關 ==========

    /**
     * 判斷保險級距是否已過期
     * @param record 保險級距記錄
     * @returns 是否已過期
     */
    const isExpired = (record: InsuranceGrade): boolean => {
        if (!record.endDate) return false;
        // 當結束日期在今天之前時，才視為已過期（當天仍然生效）
        return dayjs().isAfter(dayjs(record.endDate), 'day');
    };

    /**
     * 過濾資料源
     * @param data 原始資料
     * @returns 過濾後的資料
     */
    const filterDataSource = (data: InsuranceGrade[]): InsuranceGrade[] => {
        if (showExpired) {
            return data; // 顯示所有資料
        }
        return data.filter(record => !isExpired(record)); // 只顯示生效中的資料
    };

    /**
     * 載入指定保險類型的級距資料
     * @param insuranceType 保險類型 (1=勞保, 2=健保, 3=職災)
     */
    const loadData = async (insuranceType: number) => {
        setLoading(true);
        try {
            // 載入級距資料（已包含員工統計）
            const gradeResponse = await getInsuranceGradeList(insuranceType);

            if (gradeResponse.success && gradeResponse.data) {
                setOriginalDataSource(gradeResponse.data);
                setDataSource(filterDataSource(gradeResponse.data));
            } else {
                message.error(gradeResponse.message || '載入級距資料失敗');
                setOriginalDataSource([]);
                setDataSource([]);
            }
        } catch (error: any) {
            message.error('載入資料失敗：' + error.message);
            setOriginalDataSource([]);
            setDataSource([]);
        } finally {
            setLoading(false);
        }
    };

    /**
     * 當標籤頁切換時重新載入資料
     */
    useEffect(() => {
        loadData(parseInt(activeTab));
    }, [activeTab]);

    /**
     * 當顯示過期資料選項改變時重新過濾資料
     */
    useEffect(() => {
        setDataSource(filterDataSource(originalDataSource));
    }, [showExpired, originalDataSource]);



    // ========== 表格配置 ==========

    /**
     * 表格欄位定義
     */
    const columns: ColumnsType<InsuranceGrade> = [
        {
            title: '月投保薪資',
            dataIndex: 'monthlySalary',
            key: 'monthlySalary',
            sorter: (a, b) => a.monthlySalary - b.monthlySalary,
        },
        {
            title: '生效日期',
            dataIndex: 'startDate',
            key: 'startDate',
            render: (date: string) => date ? dayjs(date).format(DATE_FORMAT) : '-',
        },
        {
            title: '結束日期',
            dataIndex: 'endDate',
            key: 'endDate',
            render: (date: string, record: InsuranceGrade) => {
                if (!date) return '-';

                const formattedDate = dayjs(date).format(DATE_FORMAT);
                const expired = isExpired(record);

                return (
                    <span style={{
                        color: expired ? '#ff4d4f' : undefined,
                        fontWeight: expired ? 'bold' : 'normal'
                    }}>
                        {formattedDate}
                        {expired && <span style={{ marginLeft: '4px' }}>（已過期）</span>}
                    </span>
                );
            },
        },
        {
            title: '目前生效員工',
            key: 'currentEmployees',
            align: 'center' as const,
            width: 130,
            render: (_, record: InsuranceGrade) => {
                const count = record.currentEmployeeCount || 0;
                return (
                    <Space>
                        <UserOutlined style={{ color: '#52c41a' }} />
                        <span
                            style={{
                                fontWeight: count > 0 ? 'bold' : 'normal',
                                color: count > 0 ? '#52c41a' : '#999',
                                cursor: count > 0 ? 'pointer' : 'default',
                                textDecoration: count > 0 ? 'underline' : 'none'
                            }}
                            onClick={() => count > 0 && handleShowEmployeeDetail(record, 'current')}
                            title={count > 0 ? '點擊查看員工詳細資訊' : ''}
                        >
                            {count} 人
                        </span>
                    </Space>
                );
            },
        },
        {
            title: '待生效員工',
            key: 'pendingEmployees',
            align: 'center' as const,
            width: 130,
            render: (_, record: InsuranceGrade) => {
                const count = record.pendingEmployeeCount || 0;
                return (
                    <Space>
                        <ClockCircleOutlined style={{ color: '#1890ff' }} />
                        <span
                            style={{
                                fontWeight: count > 0 ? 'bold' : 'normal',
                                color: count > 0 ? '#1890ff' : '#999',
                                cursor: count > 0 ? 'pointer' : 'default',
                                textDecoration: count > 0 ? 'underline' : 'none'
                            }}
                            onClick={() => count > 0 && handleShowEmployeeDetail(record, 'pending')}
                            title={count > 0 ? '點擊查看員工詳細資訊' : ''}
                        >
                            {count} 人
                        </span>
                    </Space>
                );
            },
        },
        {
            title: '備註',
            dataIndex: 'remark',
            key: 'remark',
            ellipsis: true,
            render: (text: string) => text || '-',
        },
        {
            title: '操作',
            key: 'action',
            fixed: 'right',
            render: (_, record) => (
                <Space size="small">
                    <Button
                        type="link"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    >
                        編輯
                    </Button>
                    <Popconfirm
                        title="確定要刪除此保險級距嗎？"
                        onConfirm={() => setDeleteUid(record.uid)}
                        okText="確定"
                        cancelText="取消"
                    >
                        <Button
                            type="link"
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                        >
                            刪除
                        </Button>
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    // ========== 操作處理函數 ==========

    /**
     * 生成測試保險級距數據
     * 為勞保、健保、職災三種保險類型分別生成測試數據
     */
    const handleGenerateTestData = async (): Promise<void> => {
        try {
            setLoading(true);

            const startDate = '2025-01-01';
            const endDate = '2025-12-31';

            // 確認對話框
            Modal.confirm({
                title: '確認生成測試數據',
                content: (
                    <div>
                        <p>此操作將為勞保、健保、職災三種保險類型分別生成 {TEST_SALARY_GRADES.length} 筆測試級距數據。</p>
                        <p><strong>生效期間：</strong>{startDate} ~ {endDate}</p>
                        <p><strong>投保級距：</strong>從 NT$ {TEST_SALARY_GRADES[0].toLocaleString()} 到 NT$ {TEST_SALARY_GRADES[TEST_SALARY_GRADES.length - 1].toLocaleString()}</p>
                        <p style={{ color: '#ff4d4f' }}>請確認是否要繼續？</p>
                    </div>
                ),
                okText: '確認生成',
                cancelText: '取消',
                onOk: async () => {
                    try {
                        let successCount = 0;
                        let errorCount = 0;
                        const errors: string[] = [];

                        // 為三種保險類型分別生成數據
                        for (const insuranceType of [INSURANCE_TYPES.LABOR, INSURANCE_TYPES.HEALTH, INSURANCE_TYPES.ACCIDENT]) {
                            const typeName = getInsuranceTypeName(insuranceType);

                            for (const monthlySalary of TEST_SALARY_GRADES) {
                                try {
                                    const testData = {
                                        insuranceType,
                                        monthlySalary,
                                        startDate,
                                        endDate,
                                        remark: `${typeName}測試數據 - 自動生成於 ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`
                                    };

                                    const response = await addInsuranceGrade(testData);

                                    if (response.success) {
                                        successCount++;
                                    } else {
                                        errorCount++;
                                        errors.push(`${typeName} - NT$ ${monthlySalary.toLocaleString()}: ${response.message}`);
                                    }
                                } catch (error: any) {
                                    errorCount++;
                                    errors.push(`${typeName} - NT$ ${monthlySalary.toLocaleString()}: ${error.message}`);
                                }
                            }
                        }

                        // 顯示結果
                        if (errorCount === 0) {
                            message.success(`測試數據生成完成！成功創建 ${successCount} 筆級距數據`);
                        } else {
                            Modal.info({
                                title: '測試數據生成結果',
                                content: (
                                    <div>
                                        <p><strong>成功：</strong>{successCount} 筆</p>
                                        <p><strong>失敗：</strong>{errorCount} 筆</p>
                                        {errors.length > 0 && (
                                            <div style={{ marginTop: '12px' }}>
                                                <p><strong>錯誤詳情：</strong></p>
                                                <div style={{ maxHeight: '200px', overflow: 'auto', fontSize: '12px' }}>
                                                    {errors.map((error, index) => (
                                                        <div key={index} style={{ color: '#ff4d4f', marginBottom: '4px' }}>
                                                            {error}
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                ),
                                width: 600,
                            });
                        }

                        // 重新載入當前標籤頁的數據
                        await loadData(parseInt(activeTab));

                    } catch (error: any) {
                        message.error('生成測試數據失敗：' + error.message);
                    } finally {
                        setLoading(false);
                    }
                }
            });

        } catch (error: any) {
            message.error('操作失敗：' + error.message);
            setLoading(false);
        }
    };

    /**
     * 處理新增級距
     * 重置表單並設定預設值
     */
    const handleAdd = () => {
        setEditingRecord(null);
        form.resetFields();

        // 設定預設值
        form.setFieldsValue({
            monthlySalary: 0,
            startDate: dayjs(),
        });

        setModalVisible(true);
    };

    /**
     * 處理編輯級距
     * 將選中記錄的資料填入表單
     * @param record 要編輯的保險級距記錄
     */
    const handleEdit = (record: InsuranceGrade) => {
        setEditingRecord(record);

        // 準備表單數據，將日期字符串轉換為 dayjs 對象
        const formValues = {
            monthlySalary: record.monthlySalary,
            startDate: record.startDate ? dayjs(record.startDate) : null,
            endDate: record.endDate ? dayjs(record.endDate) : null,
            remark: record.remark,
        };

        form.setFieldsValue(formValues);
        setModalVisible(true);
    };

    /**
     * 處理刪除級距
     * @param uid 要刪除的記錄 uid
     */
    const handleDelete = async (uid: string): Promise<void> => {
        try {
            const response = await deleteInsuranceGrade(uid);

            if (response.success) {
                message.success('刪除成功');
                await loadData(parseInt(activeTab));
            } else {
                message.error(response.message || '刪除失敗');
            }
        } catch (error: any) {
            message.error('刪除失敗：' + error.message);
        }
    };

    /**
     * 處理表單提交（新增或編輯）
     */
    const handleSubmit = async (): Promise<void> => {
        try {
            // 驗證表單
            const values = await form.validateFields();

            // 格式化日期數據並添加當前選中的保險類型
            const formData = {
                ...values,
                insuranceType: parseInt(activeTab), // 自動使用當前標籤頁的保險類型
                startDate: values.startDate ? values.startDate.format(DATE_FORMAT) : '',
                endDate: values.endDate ? values.endDate.format(DATE_FORMAT) : null,
            };

            let response;

            if (editingRecord) {
                // 編輯模式
                response = await editInsuranceGrade({
                    ...formData,
                    uid: editingRecord.uid,
                });
            } else {
                // 新增模式
                response = await addInsuranceGrade(formData);
            }

            if (response.success) {
                message.success(editingRecord ? '編輯成功' : '新增成功');
                setModalVisible(false);
                await loadData(parseInt(activeTab));
            } else {
                message.error(response.message || (editingRecord ? '編輯失敗' : '新增失敗'));
            }
        } catch (error: any) {
            message.error('操作失敗：' + error.message);
        }
    };

    /**
     * 關閉彈窗並重置表單
     */
    const handleModalCancel = (): void => {
        setModalVisible(false);
        form.resetFields();
    };

    /**
     * 處理刪除倒計時完成
     */
    const handleDeleteComplete = async (): Promise<void> => {
        if (!deleteUid) return;

        try {
            await handleDelete(deleteUid);
            setDeleteUid(null);
        } catch (error) {
            message.error('刪除失敗，請稍後再試');
            setDeleteUid(null);
        }
    };

    /**
     * 取消刪除操作
     */
    const handleDeleteCancel = (): void => {
        setDeleteUid(null);
    };

    /**
     * 打開員工詳細資訊彈窗
     */
    const handleShowEmployeeDetail = (record: InsuranceGrade, status: 'current' | 'pending'): void => {
        setEmployeeDetailModal({
            visible: true,
            gradeUid: record.uid,
            status: status,
            insuranceType: record.insuranceType,
            monthlySalary: record.monthlySalary
        });
    };

    /**
     * 關閉員工詳細資訊彈窗
     */
    const handleCloseEmployeeDetail = (): void => {
        setEmployeeDetailModal({
            visible: false,
            gradeUid: '',
            status: 'current',
            insuranceType: 1,
            monthlySalary: 0
        });
    };

    // ========== 配置對象 ==========

    /**
     * 標籤頁配置
     */
    const tabItems: TabsProps['items'] = [
        {
            key: INSURANCE_TYPES.LABOR.toString(),
            label: '勞保級距',
            children: null,
        },
        {
            key: INSURANCE_TYPES.HEALTH.toString(),
            label: '健保級距',
            children: null,
        },
        {
            key: INSURANCE_TYPES.ACCIDENT.toString(),
            label: '職災級距',
            children: null,
        },
    ];

    /**
     * 表格分頁配置
     */
    const paginationConfig = {
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total: number) => `共 ${total} 筆`,
        pageSize: 10,
    };

    // ========== 渲染組件 ==========

    return (
        <Card
            title={
                <Space>
                    <SettingOutlined style={{ color: '#1890ff' }} />
                    <Title level={5} style={{ margin: 0 }}>
                        保險級距設定
                    </Title>
                </Space>
            }
            extra={
                <Space>
                    <Button
                        type="default"
                        onClick={handleGenerateTestData}
                        loading={loading}
                        style={{
                            borderColor: '#ff7875',
                            color: '#ff7875',
                            backgroundColor: '#fff2f0'
                        }}
                    >
                        生成測試數據
                    </Button>
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAdd}
                    >
                        新增級距
                    </Button>
                </Space>
            }
            style={{
                borderRadius: '12px',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.08)'
            }}
        >
            <Typography.Text type="secondary" style={{ display: 'block', marginBottom: '16px' }}>
                管理勞保、健保、職災三種保險類型的投保級距設定
            </Typography.Text>

            {/* 保險類型標籤頁 */}
            <Tabs
                activeKey={activeTab}
                onChange={setActiveTab}
                items={tabItems}
            />

            {/* 顯示過期資料控制項 */}
            <div style={{ marginBottom: '16px', padding: '12px', backgroundColor: '#fafafa', borderRadius: '6px' }}>
                <Row justify="space-between" align="middle">
                    <Col>
                        <Checkbox
                            checked={showExpired}
                            onChange={(e) => setShowExpired(e.target.checked)}
                        >
                            顯示已過期級距
                        </Checkbox>
                    </Col>
                    <Col>
                        <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                            共 {originalDataSource.length} 筆級距，
                            生效中 {originalDataSource.filter(record => !isExpired(record)).length} 筆，
                            已過期 {originalDataSource.filter(record => isExpired(record)).length} 筆
                            {originalDataSource.length > 0 && (
                                <>
                                    {' | '}
                                    <UserOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
                                    生效員工 {originalDataSource.reduce((total, grade) => total + (grade.currentEmployeeCount || 0), 0)} 人，
                                    <ClockCircleOutlined style={{ color: '#1890ff', marginLeft: '8px', marginRight: '4px' }} />
                                    待生效 {originalDataSource.reduce((total, grade) => total + (grade.pendingEmployeeCount || 0), 0)} 人
                                </>
                            )}
                        </Typography.Text>
                    </Col>
                </Row>
            </div>

            {/* 級距資料表格 */}
            <Table
                columns={columns}
                dataSource={Array.isArray(dataSource) ? dataSource : []}
                rowKey={(record) => record.uid}
                loading={loading}
                scroll={{ x: 800 }}
                size="small"
                rowClassName={(record) => {
                    let className = '';
                    if (record.uid === deleteUid) {
                        className += 'row-deleting-pulse';
                    }
                    if (isExpired(record)) {
                        className += ' row-expired';
                    }
                    return className.trim();
                }}
                pagination={paginationConfig}
            />

            {/* 新增/編輯彈窗 */}
            <Modal
                title={editingRecord ? '編輯保險級距' : '新增保險級距'}
                open={modalVisible}
                onOk={handleSubmit}
                onCancel={handleModalCancel}
                destroyOnClose={false}
                width={600}
            /*forceRender*/
            >
                <Form
                    form={form}
                    layout="vertical"
                    preserve={false}
                >
                    {/* 顯示當前操作的保險類型 */}
                    <div style={{
                        marginBottom: '16px',
                        padding: '12px',
                        backgroundColor: '#f6f8fa',
                        borderRadius: '6px',
                        border: '1px solid #e1e4e8'
                    }}>
                        <Typography.Text strong style={{ color: '#1890ff' }}>
                            當前操作：{getInsuranceTypeName(parseInt(activeTab))}級距
                        </Typography.Text>
                    </div>

                    {/* 第一行：月投保薪資 */}
                    <Row gutter={16}>
                        <Col span={24}>
                            <Form.Item
                                name="monthlySalary"
                                label="月投保薪資"
                                rules={[
                                    { required: true, message: '請輸入月投保薪資' },
                                    { type: 'number', min: 1, message: '月投保薪資必須大於0' }
                                ]}
                            >
                                <InputNumber
                                    style={{ width: '100%' }}
                                    placeholder="請輸入月投保薪資"
                                    step={1}
                                    precision={0}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    {/* 第二行：生效日期、結束日期 */}
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="startDate"
                                label="生效日期"
                                rules={[{ required: true, message: '請選擇生效日期' }]}
                            >
                                <DatePicker
                                    style={{ width: '100%' }}
                                    placeholder="請選擇生效日期"
                                    format={DATE_FORMAT}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="endDate"
                                label="結束日期"
                                dependencies={['startDate']}
                                rules={[
                                    ({ getFieldValue }) => ({
                                        validator(_, value) {
                                            if (!value || !getFieldValue('startDate')) {
                                                return Promise.resolve();
                                            }
                                            if (value.isAfter(getFieldValue('startDate'))) {
                                                return Promise.resolve();
                                            }
                                            return Promise.reject(new Error('結束日期必須大於生效日期'));
                                        },
                                    }),
                                ]}
                            >
                                <DatePicker
                                    style={{ width: '100%' }}
                                    placeholder="請選擇結束日期"
                                    format={DATE_FORMAT}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    {/* 第三行：備註 */}
                    <Form.Item
                        name="remark"
                        label="備註"
                    >
                        <Input.TextArea
                            rows={3}
                            placeholder="請輸入備註"
                            maxLength={200}
                            showCount
                        />
                    </Form.Item>
                </Form>
            </Modal>

            {/* 刪除倒計時組件 */}
            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={handleDeleteComplete}
                    onCancel={handleDeleteCancel}
                />
            )}

            {/* 員工詳細資訊彈窗 */}
            <InsuranceGradeEmployeeDetailModal
                visible={employeeDetailModal.visible}
                onClose={handleCloseEmployeeDetail}
                gradeUid={employeeDetailModal.gradeUid}
                status={employeeDetailModal.status}
                insuranceType={employeeDetailModal.insuranceType}
                monthlySalary={employeeDetailModal.monthlySalary}
            />
        </Card>
    );
} 