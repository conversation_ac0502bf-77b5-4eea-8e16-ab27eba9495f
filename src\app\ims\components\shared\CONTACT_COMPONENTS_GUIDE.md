# Contact 組件使用指南

## 📋 組件概覽

Contact 組件庫提供統一的聯絡人管理功能，包含表單、表格、篩選器等完整功能。所有組件都遵循 IMS 標準設計模式，支援響應式設計和 TypeScript 類型安全。

### 核心組件

- **ContactFormModal** - 聯絡人表單模態框（支援 create/quick/edit 三種模式）
- **ContactTable** - 聯絡人表格組件（基於 ResponsiveTable，支援智能篩選）
- **UnifiedContactFilters** - 統一篩選器組件（使用 FilterSearchContainer）
- **ContactSelector** - 聯絡人選擇器（自動完成搜尋 + 模態框選擇）
- **PartnerContactManager** - Partner 聯絡人關聯管理組件
- **contactUtils** - 聯絡人工具函數庫

## 🚀 快速開始

### 1. ContactFormModal - 表單模態框

支援三種模式的聯絡人表單，適用於不同場景。

```tsx
import ContactFormModal from '@/app/ims/components/shared/ContactFormModal';
import { Contact } from '@/services/ims/ContactService';

const MyComponent = () => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [mode, setMode] = useState<'create' | 'quick' | 'edit'>('create');
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);

  const handleSubmit = async (contactData: Contact, partnerContactData?: Partial<PartnerContact>) => {
    setLoading(true);
    try {
      const response = selectedContact 
        ? await updateContact(contactData)
        : await addContact(contactData);
        
      if (response.success) {
        message.success(selectedContact ? '更新成功' : '新增成功');
        setVisible(false);
        setSelectedContact(null);
      }
    } catch (error) {
      message.error('操作失敗');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ContactFormModal
      visible={visible}
      onClose={() => {
        setVisible(false);
        setSelectedContact(null);
      }}
      onSubmit={handleSubmit}
      loading={loading}
      initialData={selectedContact}
      mode={mode}
      showPartnerContactFields={mode === 'quick'}
      partnerID="partner-123" // 快速新增時必需
    />
  );
};
```

#### 表單模式說明

- **create**: 完整新增模式，包含所有聯絡人欄位
- **quick**: 快速新增模式，用於 Partner 頁面，包含 PartnerContact 關聯欄位
- **edit**: 編輯模式，根據 initialData 預填表單

### 2. ContactTable - 表格組件

基於 ResponsiveTable 構建，支援智能篩選和多種操作模式。

```tsx
import ContactTable from '@/app/ims/components/shared/ContactTable';

const ContactList = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  return (
    <ContactTable
      contacts={contacts}
      loading={loading}
      onEdit={(contact) => {
        setSelectedContact(contact);
        setFormMode('edit');
        setModalVisible(true);
      }}
      onDelete={(contactId) => {
        Modal.confirm({
          title: '確定要刪除此聯絡人嗎？',
          onOk: () => handleDeleteContact(contactId)
        });
      }}
      rowSelection={{
        selectedRowKeys,
        onChange: setSelectedRowKeys,
        type: 'checkbox'
      }}
      showPagination={true}
      pageSize={10}
      size="middle"
    />
  );
};
```

#### 表格特性

- **智能篩選**: 每個欄位都支援 SmartColumnType 篩選
- **響應式設計**: 自動適配移動端、平板、桌面
- **操作模式**: 支援編輯、刪除、選擇等多種操作
- **批量操作**: 支援行選擇和批量操作

### 3. UnifiedContactFilters - 篩選器組件

統一的篩選和搜尋功能，取代傳統的 ContactFilters 和 ContactSearchForm。

```tsx
import UnifiedContactFilters from '@/app/ims/components/contact/UnifiedContactFilters';

const ContactManagement = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);

  const handleFilterResult = (filtered: Contact[]) => {
    setFilteredContacts(filtered);
  };

  return (
    <div>
      <Card>
        <UnifiedContactFilters
          contacts={contacts}
          onFilterResult={handleFilterResult}
          showStats={true}
          compact={false}
        />
      </Card>
      
      <Card style={{ marginTop: 16 }}>
        <ContactTable
          contacts={filteredContacts}
          // ... 其他屬性
        />
      </Card>
    </div>
  );
};
```

#### 篩選選項

支援以下篩選條件：
- **姓名**: 文字搜尋
- **電子郵件**: 文字搜尋  
- **公司**: 文字搜尋
- **部門**: 文字搜尋
- **聯絡人類型**: 下拉選擇
- **狀態**: 啟用/停用選擇

## 🛠 工具函數

### contactUtils 工具庫

提供統一的聯絡人處理工具函數。

```tsx
import {
  createEmptyContact,
  normalizeContact,
  filterContacts,
  applyContactFilters,
  getContactStats
} from '@/app/ims/components/shared/contactUtils';

// 創建空聯絡人
const emptyContact = createEmptyContact();

// 正規化聯絡人資料
const normalizedContact = normalizeContact(rawContactData);

// 篩選聯絡人
const filtered = filterContacts(contacts, searchText, {
  contactType: '客戶',
  isActive: true
});

// 應用篩選器（配合 FilterSearchContainer 使用）
const filteredByContainer = applyContactFilters(contacts, searchText, filterValues);

// 獲取統計資訊
const stats = getContactStats(contacts);
// { total: 100, active: 85, inactive: 15, typeStats: {...} }
```

## 📱 響應式設計

所有 Contact 組件都支援響應式設計：

- **移動端** (≤768px): 簡化佈局，垂直排列
- **平板** (769-1024px): 適中佈局，部分水平排列  
- **桌面** (>1024px): 完整佈局，水平排列

## 🎯 最佳實踐

### 1. 統一使用 shared 組件

```tsx
// ✅ 正確 - 使用 shared 目錄的組件
import ContactFormModal from '@/app/ims/components/shared/ContactFormModal';
import ContactTable from '@/app/ims/components/shared/ContactTable';
import { applyContactFilters } from '@/app/ims/components/shared/contactUtils';

// ❌ 錯誤 - 使用舊路徑
import ContactFormModal from '@/app/ims/components/ContactFormModal';
import ContactTable from '@/app/ims/components/contact/ContactTable';
```

### 2. 表單模式選擇

```tsx
// Partner 頁面快速新增
<ContactFormModal
  mode="quick"
  showPartnerContactFields={true}
  partnerID={currentPartner.partnerID}
/>

// 主頁面完整新增
<ContactFormModal
  mode="create"
  showPartnerContactFields={false}
/>

// 編輯現有聯絡人
<ContactFormModal
  mode="edit"
  initialData={selectedContact}
/>
```

### 3. 篩選器整合

```tsx
// 使用統一篩選器取代多個獨立組件
// ✅ 新方式
<UnifiedContactFilters
  contacts={contacts}
  onFilterResult={setFilteredContacts}
/>

// ❌ 舊方式
<ContactFilters onFilter={...} />
<ContactSearchForm onSearch={...} />
```

## 🔧 故障排除

### 常見問題

1. **導入路徑錯誤**
   - 確保使用 `@/app/ims/components/shared/` 路徑
   - 檢查 TypeScript 編譯錯誤

2. **表單驗證失敗**
   - 檢查 mode 屬性是否正確設置
   - 確認 showPartnerContactFields 配置

3. **篩選器不工作**
   - 確保 contacts 資料格式正確
   - 檢查 onFilterResult 回調函數

### 除錯技巧

```tsx
// 開發模式下顯示除錯資訊
{process.env.NODE_ENV === 'development' && (
  <div style={{ padding: 8, backgroundColor: '#f0f0f0' }}>
    聯絡人數: {contacts.length} | 篩選後: {filteredContacts.length}
  </div>
)}
```

## 🆕 新增組件

### ContactSelector - 聯絡人選擇器

提供自動完成搜尋和模態框選擇功能的聯絡人選擇器。

```tsx
import ContactSelector from '@/app/ims/components/shared/ContactSelector';

const MyComponent = () => {
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);

  return (
    <ContactSelector
      value={selectedContact}
      onChange={setSelectedContact}
      placeholder="搜尋或選擇聯絡人"
      showAddButton={true}
      onAddContact={() => setContactFormVisible(true)}
      allowClear={true}
    />
  );
};
```

### PartnerContactManager - Partner 聯絡人關聯管理

專門用於 Partner 頁面的聯絡人關聯管理組件。

```tsx
import PartnerContactManager from '@/app/ims/components/shared/PartnerContactManager';

const PartnerPage = () => {
  const [partnerContacts, setPartnerContacts] = useState<PartnerContact[]>([]);

  return (
    <PartnerContactManager
      partnerID={partnerId}
      partnerName={partnerName}
      partnerContacts={partnerContacts}
      onAddContact={handleAddContact}
      onLinkContact={handleLinkContact}
      onUpdatePartnerContact={handleUpdatePartnerContact}
      onRemovePartnerContact={handleRemovePartnerContact}
    />
  );
};
```

## 🔧 重要修復

### PartnerContact 驗證規則修復

- **Priority 欄位**: 移除必填驗證，設定預設值為 99
- **Role 欄位**: 移除必填驗證，允許用戶選填
- **快速新增模式**: 增加電話和職位欄位

### 組件路徑更新

所有 Contact 相關組件已移至 `src/app/ims/components/shared/` 目錄，確保跨模組重用。

## 📚 相關資源

- [USAGE_GUIDE.md](./USAGE_GUIDE.md) - 篩選組件使用指南
- [ResponsiveTable](./ResponsiveTable.tsx) - 響應式表格組件
- [FilterSearchContainer](./FilterSearchContainer.tsx) - 篩選搜尋容器
- [IMS-Contact-004 任務文檔](../../../DevelopeDoc/tasks/IMS-Contact-004/) - 重構任務詳情
