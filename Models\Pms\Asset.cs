﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
using FAST_ERP_Backend.Attributes;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 財產基本資料表
    /// </summary>
    public class Asset : ModelBaseEntity
    {
        [Key]
        [Comment("財產流水號")]
        public Guid AssetId { get; set; } // 財產流水號

        [Comment("財產編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string AssetNo { get; set; } // 財產編號

        [Comment("財產名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string AssetName { get; set; } // 財產名稱

        [Comment("財產簡稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string AssetShortName { get; set; } // 財產簡稱

        [Comment("取得日期")]
        [Column(TypeName = "bigint")]
        public long AcquisitionDate { get; set; } // 取得日期

        [Comment("數量")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Quantity { get; set; } = 1; // 數量

        [Comment("單位編號")]
        public Guid UnitId { get; set; } // 單位編號

        [Comment("單位")]
        [NotMapped]
        public string UnitName { get; set; } // 單位名稱

        [Comment("購入金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PurchaseAmount { get; set; } = 0; // 購入金額

        [Comment("折舊金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal DepreciationAmount { get; set; } = 0; // 折舊金額

        [Comment("累計折舊金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal AccumulatedDepreciationAmount { get; set; } = 0; // 累計折舊金額

        [Comment("輔助金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubsidyAmount { get; set; } = 0; // 輔助金額

        [Comment("預估殘值")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal EstimatedResidualValue { get; set; } = 0; // 預估殘值

        [Comment("部門")]
        [Column(TypeName = "nvarchar(100)")]
        public string DepartmentId { get; set; } // 部門

        [Comment("部門名稱")]
        [NotMapped]
        public string DepartmentName { get; set; } // 部門名稱

        [Comment("股別")]
        [Column(TypeName = "nvarchar(100)")]
        public string DivisionId { get; set; } // 股別

        [Comment("組別名稱")]
        [NotMapped]
        public string DivisionName { get; set; } // 組別名稱

        [Comment("保管人")]
        [Column(TypeName = "nvarchar(100)")]
        public string CustodianId { get; set; } // 保管人

        [Comment("使用人")]
        [Column(TypeName = "nvarchar(100)")]
        public string UserId { get; set; } // 使用人

        [Comment("財產狀態")]
        public Guid AssetStatusId { get; set; } // 財產狀態

        [Comment("狀態異動日期")]
        [Column(TypeName = "bigint")]
        public long StatusChangeDate { get; set; } // 狀態異動日期

        [Comment("使用狀態")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Usage { get; set; } // 使用狀態

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Notes { get; set; } // 備註

        [Comment("存放地點")]
        [Column(TypeName = "nvarchar(100)")]
        public string StorageLocationId { get; set; } // 存放地點

        [Comment("耐用年限")]
        [Column(TypeName = "int")]
        public int ServiceLife { get; set; } = 0; // 耐用年限

        [Comment("保固年限")]
        [Column(TypeName = "int")]
        public int InsurancePeriod { get; set; } = 0; // 保固年限

        [Comment("廠牌型號")]
        public Guid ManufacturerId { get; set; } // 廠牌型號

        [Comment("規格")]
        [Column(TypeName = "nvarchar(500)")]
        public string Specification { get; set; } // 規格

        [Comment("建物地址")]
        [Column(TypeName = "nvarchar(200)")]
        public string BuildingAddress { get; set; } // 建物地址

        [Comment("建物構造")]
        [Column(TypeName = "nvarchar(100)")]
        public string BuildingStructure { get; set; } // 建物構造

        [Comment("興建日期")]
        [Column(TypeName = "bigint")]
        public long? ConstructionDate { get; set; } // 興建日期

        [Comment("面積(m²)")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal FloorArea { get; set; } = 0; // 面積(m²)

        [Comment("公設(m²)")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PublicArea { get; set; } = 0; // 公設(m²)

        [Comment("使用執照日期")]
        [Column(TypeName = "bigint")]
        public long? UsageExpiryDate { get; set; } // 使用執照日期

        [Comment("使用執照號碼")]
        [Column(TypeName = "nvarchar(100)")]
        public string UsageLicenseNo { get; set; } // 使用執照號碼

        [Comment("適用房屋稅目")]
        [Column(TypeName = "nvarchar(50)")]
        public string BuildingTaxItem { get; set; } // 適用房屋稅目

        [Comment("公告現值")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PublicValue { get; set; } = 0; // 公告現值

        [Comment("地目")]
        [Column(TypeName = "nvarchar(50)")]
        public string LandSection { get; set; } // 地目

        [Comment("地段")]
        [Column(TypeName = "nvarchar(200)")]
        public string LandLocation { get; set; } // 地段

        [Comment("地號")]
        [Column(TypeName = "nvarchar(50)")]
        public string LandNumber { get; set; } // 地號

        [Comment("面積(m²)")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal LandArea { get; set; } = 0; // 面積(m²)

        [Comment("權狀號碼")]
        [Column(TypeName = "nvarchar(100)")]
        public string CertificateNo { get; set; } // 權狀號碼

        [Comment("財產科目")]
        public Guid AssetAccountId { get; set; } // 財產科目

        [Comment("財產子目")]
        public Guid AssetSubAccountId { get; set; }//財產子目

        [Comment("資產類別編號")]
        [Column(TypeName = "nvarchar(1)")]
        public string AssetCategoryId { get; set; } // 資產類別編號

        [Comment("自訂財產編號一")]
        [Column(TypeName = "nvarchar(100)")]
        public string CustomAssetNo1 { get; set; } // 自訂財產編號一

        [Comment("自訂財產編號二")]
        [Column(TypeName = "nvarchar(100)")]
        public string CustomAssetNo2 { get; set; } // 自訂財產編號二

        [Comment("報廢原因")]
        [Column(TypeName = "nvarchar(500)")]
        public string ScrapReason { get; set; } // 報廢原因

        [Comment("報廢日期")]
        [Column(TypeName = "bigint")]
        public long? ScrapDate { get; set; } // 報廢日期

        [Comment("預計報廢年度")]
        [Column(TypeName = "bigint")]
        public long? EstimatedScrapYear { get; set; } // 預計報廢年度

        [Comment("報廢後堪用")]
        [Column(TypeName = "nvarchar(1)")]
        public string UsableAfterScrap { get; set; } // 報廢後堪用 Y/N

        [Comment("出售日期")]
        [Column(TypeName = "bigint")]
        public long? SoldDate { get; set; } // 出售日期

        [Comment("出售原因")]
        [Column(TypeName = "nvarchar(500)")]
        public string SoldReason { get; set; } // 出售原因

        [Comment("出售金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal SoldAmount { get; set; } = 0; // 出售金額

        [Comment("捐贈日期")]
        [Column(TypeName = "bigint")]
        public long? DonatedDate { get; set; } // 捐贈日期

        [Comment("捐贈原因")]
        [Column(TypeName = "nvarchar(500)")]
        public string DonatedReason { get; set; } // 捐贈原因

        [Comment("捐贈對象")]
        [Column(TypeName = "nvarchar(200)")]
        public string DonatedTo { get; set; } // 捐贈對象

        [Comment("設備類型")]
        public Guid EquipmentTypeId { get; set; } // 設備類型

        // 附屬設備
        public virtual ICollection<AccessoryEquipment> AccessoryEquipments { get; set; }

        // 承保單位
        public virtual ICollection<AssetInsuranceUnitMapping> AssetInsuranceUnits { get; set; }


        // 攤提來源
        public virtual ICollection<AssetAmortizationSourceMapping> AssetAmortizationSources { get; set; }

        // 資產來源
        public virtual ICollection<AssetAssetSourceMapping> AssetAssetSources { get; set; }

        public Asset()
        {
            AssetId = Guid.Empty;
            AssetNo = "";
            AssetName = "";
            AssetShortName = "";
            AcquisitionDate = 0;
            Quantity = 1;
            UnitId = Guid.Empty;
            PurchaseAmount = 0;
            DepreciationAmount = 0;
            AccumulatedDepreciationAmount = 0;
            SubsidyAmount = 0;
            EstimatedResidualValue = 0;
            DepartmentId = "";
            DepartmentName = "";
            DivisionId = "";
            DivisionName = "";
            UnitName = "";
            CustodianId = "";
            UserId = "";
            AssetStatusId = Guid.Empty;
            StatusChangeDate = 0;
            Usage = "";
            Notes = "";
            StorageLocationId = "";
            ServiceLife = 0;
            InsurancePeriod = 0;
            ManufacturerId = Guid.Empty;
            Specification = "";
            BuildingAddress = "";
            BuildingStructure = "";
            ConstructionDate = 0;
            FloorArea = 0;
            PublicArea = 0;
            UsageExpiryDate = 0;
            UsageLicenseNo = "";
            BuildingTaxItem = "";
            PublicValue = 0;
            LandSection = "";
            LandLocation = "";
            LandNumber = "";
            LandArea = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
            CertificateNo = "";
            CustomAssetNo1 = "";
            CustomAssetNo2 = "";
            ScrapReason = "";
            ScrapDate = 0;
            EstimatedScrapYear = 0;
            UsableAfterScrap = "";
            SoldDate = 0;
            SoldReason = "";
            SoldAmount = 0;
            DonatedDate = 0;
            DonatedReason = "";
            DonatedTo = "";
            EquipmentTypeId = Guid.Empty;
            AccessoryEquipments = new List<AccessoryEquipment>();
            AssetInsuranceUnits = new List<AssetInsuranceUnitMapping>();
            AssetAmortizationSources = new List<AssetAmortizationSourceMapping>();
            AssetAssetSources = new List<AssetAssetSourceMapping>();
        }
    }

    /// <summary>
    /// 財產資料傳輸物件
    /// </summary>
    public class AssetDTO : ModelBaseEntityDTO
    {
        public Guid AssetId { get; set; } // 財產流水號
        public string AssetNo { get; set; } // 財產編號
        public string AssetName { get; set; } // 財產名稱
        public string AssetShortName { get; set; } // 財產簡稱
        public long AcquisitionDate { get; set; } // 取得日期
        public decimal Quantity { get; set; } // 數量
        public Guid UnitId { get; set; } // 單位編號
        public string UnitName { get; set; } // 單位名稱
        public decimal PurchaseAmount { get; set; } // 購入金額
        public decimal DepreciationAmount { get; set; } // 折舊金額
        public decimal AccumulatedDepreciationAmount { get; set; } // 累計折舊金額
        public decimal SubsidyAmount { get; set; } // 輔助金額
        public decimal EstimatedResidualValue { get; set; } // 預估殘值
        public string DepartmentId { get; set; } // 部門
        public string DepartmentName { get; set; } // 部門名稱
        public string DivisionId { get; set; } // 股別
        public string DivisionName { get; set; } // 組別名稱
        public string CustodianId { get; set; } // 保管人
        public string UserId { get; set; } // 使用人
        public Guid AssetStatusId { get; set; } // 財產狀態
        public long StatusChangeDate { get; set; } // 狀態異動日期
        public string Usage { get; set; } // 使用狀態
        public string Notes { get; set; } // 備註
        public string StorageLocationId { get; set; } // 存放地點
        public int ServiceLife { get; set; } // 耐用年限
        public int InsurancePeriod { get; set; } // 保固年限
        public Guid ManufacturerId { get; set; } // 廠牌型號
        public string Specification { get; set; } // 規格
        public string BuildingAddress { get; set; } // 建物地址
        public string BuildingStructure { get; set; } // 建物構造
        public long? ConstructionDate { get; set; } // 興建日期
        public decimal FloorArea { get; set; } // 面積(m²)
        public decimal PublicArea { get; set; } // 公設(m²)
        public long? UsageExpiryDate { get; set; } // 使用執照日期
        public string UsageLicenseNo { get; set; } // 使用執照號碼
        public string BuildingTaxItem { get; set; } // 適用房屋稅目
        public decimal PublicValue { get; set; } // 公告現值
        public string LandSection { get; set; } // 地目
        public string LandLocation { get; set; } // 地段
        public string LandNumber { get; set; } // 地號
        public decimal LandArea { get; set; } // 面積(m²)
        public string CertificateNo { get; set; } // 權狀號碼
        public Guid AssetAccountId { get; set; } // 財產科目
        public Guid AssetSubAccountId { get; set; } // 財產子目
        public string AssetCategoryId { get; set; } // 資產類別編號
        public decimal UnitPrice { get; set; } // 單價
        public string CustomAssetNo1 { get; set; } // 自訂財產編號一
        public string CustomAssetNo2 { get; set; } // 自訂財產編號二
        public string ScrapReason { get; set; } // 報廢原因
        public long? ScrapDate { get; set; } // 報廢日期
        public long? EstimatedScrapYear { get; set; } // 預計報廢年度
        public string UsableAfterScrap { get; set; } // 報廢後堪用 Y/N
        public Guid EquipmentTypeId { get; set; } // 設備類型

        public long? SoldDate { get; set; } // 出售日期
        public string SoldReason { get; set; } // 出售原因
        public decimal SoldAmount { get; set; } // 出售金額
        public long? DonatedDate { get; set; } // 捐贈日期
        public string DonatedReason { get; set; } // 捐贈原因
        public string DonatedTo { get; set; } // 捐贈對象

        public bool IsDeleted { get; set; }

        public AssetDTO()
        {
            AssetId = Guid.Empty;
            AssetNo = "";
            AssetName = "";
            AssetShortName = "";
            AcquisitionDate = 0;
            Quantity = 1;
            UnitId = Guid.Empty;
            UnitName = "";
            PurchaseAmount = 0;
            DepreciationAmount = 0;
            AccumulatedDepreciationAmount = 0;
            SubsidyAmount = 0;
            EstimatedResidualValue = 0;
            DepartmentId = "";
            DepartmentName = "";
            DivisionId = "";
            DivisionName = "";
            CustodianId = "";
            UserId = "";
            AssetStatusId = Guid.Empty;
            StatusChangeDate = 0;
            Usage = "";
            Notes = "";
            StorageLocationId = "";
            ServiceLife = 0;
            InsurancePeriod = 0;
            ManufacturerId = Guid.Empty;
            Specification = "";
            BuildingAddress = "";
            BuildingStructure = "";
            ConstructionDate = 0;
            FloorArea = 0;
            PublicArea = 0;
            UsageExpiryDate = 0;
            UsageLicenseNo = "";
            BuildingTaxItem = "";
            PublicValue = 0;
            LandSection = "";
            LandLocation = "";
            LandNumber = "";
            LandArea = 0;
            CertificateNo = "";
            AssetAccountId = Guid.Empty;
            AssetSubAccountId = Guid.Empty;
            AssetCategoryId = "";
            UnitPrice = 0;
            CustomAssetNo1 = "";
            CustomAssetNo2 = "";
            ScrapReason = "";
            ScrapDate = 0;
            EstimatedScrapYear = 0;
            UsableAfterScrap = "";
            EquipmentTypeId = Guid.Empty;
            SoldDate = 0;
            SoldReason = "";
            SoldAmount = 0;
            DonatedDate = 0;
            DonatedReason = "";
            DonatedTo = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class AssetWithAccessoriesDTO
    {
        public AssetDTO Asset { get; set; }
        public List<AccessoryEquipmentDTO> AccessoryEquipments { get; set; }
        public List<InsuranceUnitDTO> InsuranceUnits { get; set; }
        public List<AmortizationSourceDTO> AmortizationSources { get; set; }
        public List<AssetSourceDTO> AssetSources { get; set; }

        public AssetWithAccessoriesDTO()
        {
            Asset = new AssetDTO();
            AccessoryEquipments = new List<AccessoryEquipmentDTO>();
            InsuranceUnits = new List<InsuranceUnitDTO>();
            AmortizationSources = new List<AmortizationSourceDTO>();
            AssetSources = new List<AssetSourceDTO>();
        }
    }

    public class AssetNoRequestDTO
    {
        [Required(ErrorMessage = "科目不能為空")]
        [RegularExpression(@"^\d{1}$", ErrorMessage = "科目必須為1位數字")]
        public string Subject { get; set; }

        [Required(ErrorMessage = "子目不能為空")]
        [RegularExpression(@"^\d{2}$", ErrorMessage = "子目必須為2位數字")]
        public string SubSubject { get; set; }

        [Required(ErrorMessage = "類別不能為空")]
        [RegularExpression(@"^[A-Za-z]{1}$", ErrorMessage = "類別必須為1位英文字")]
        public string Category { get; set; }

        /* [Required(ErrorMessage = "手機號碼不能為空")]
        [MobileNumber]
        public string MobileNumber { get; set; }

        [Required(ErrorMessage = "統一編號不能為空")]
        [UniformNumber]
        public string UniformNumber { get; set; }

        [Required(ErrorMessage = "電話號碼不能為空")]
        [PhoneNumber]
        public string PhoneNumber { get; set; }

        [Required(ErrorMessage = "Email不能為空")]
        [Email]
        public string Email { get; set; } */
    }

    /// <summary>
    /// Excel資料行DTO
    /// </summary>
    public class ExcelRowDataDTO
    {
        public int RowIndex { get; set; }
        public Dictionary<string, string> ColumnValues { get; set; }

        public ExcelRowDataDTO()
        {
            ColumnValues = new Dictionary<string, string>();
        }
    }

    /// <summary>
    /// 批次轉檔驗證結果DTO
    /// </summary>
    public class BatchValidationResultDTO
    {
        public bool IsValid { get; set; }
        public List<BatchValidationErrorDTO> Errors { get; set; }
        public int TotalRows { get; set; }
        public int ValidRows { get; set; }
        public int ErrorRows { get; set; }
        public List<string> ColumnHeaders { get; set; }
        public List<ExcelRowDataDTO> ExcelData { get; set; }

        public BatchValidationResultDTO()
        {
            Errors = new List<BatchValidationErrorDTO>();
            ColumnHeaders = new List<string>();
            ExcelData = new List<ExcelRowDataDTO>();
        }
    }

    /// <summary>
    /// 批次驗證錯誤DTO
    /// </summary>
    public class BatchValidationErrorDTO
    {
        public int RowIndex { get; set; }
        public string ColumnName { get; set; }
        public string ErrorMessage { get; set; }
        public string CellValue { get; set; }
    }

    /// <summary>
    /// 批次轉檔結果DTO
    /// </summary>
    public class BatchImportResultDTO
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int TotalRows { get; set; }
        public int SuccessRows { get; set; }
        public int FailedRows { get; set; }
        public List<BatchImportErrorDTO> Errors { get; set; }
        public List<string> SuccessAssetNos { get; set; }
        public List<AssetImportSuccessDTO> SuccessAssets { get; set; }

        public BatchImportResultDTO()
        {
            Errors = new List<BatchImportErrorDTO>();
            SuccessAssetNos = new List<string>();
            SuccessAssets = new List<AssetImportSuccessDTO>();
        }
    }

    /// <summary>
    /// 批次轉檔成功資產DTO
    /// </summary>
    public class AssetImportSuccessDTO
    {
        public string AssetNo { get; set; }
        public string AssetName { get; set; }
    }

    /// <summary>
    /// 批次轉檔錯誤DTO
    /// </summary>
    public class BatchImportErrorDTO
    {
        public int RowIndex { get; set; }
        public string AssetNo { get; set; }
        public string AssetName { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// Excel欄位對應DTO
    /// </summary>
    public class ExcelColumnMappingDTO
    {
        public string PropertyName { get; set; }
        public string ColumnName { get; set; }
        public int ColumnIndex { get; set; }
        public bool IsRequired { get; set; }
        public string DataType { get; set; }
        public string ValidationPattern { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 財產基本資訊DTO（用於報廢單選擇財產）
    /// </summary>
    public class AssetBasicInfoDTO
    {
        public Guid AssetId { get; set; }
        public string AssetNo { get; set; }
        public string AssetName { get; set; }
        public string AssetShortName { get; set; }
        public string DepartmentId { get; set; }
        public string DepartmentName { get; set; }
        public long AcquisitionDate { get; set; }
        public decimal PurchaseAmount { get; set; }
        public string Specification { get; set; }

        public AssetBasicInfoDTO()
        {
            AssetId = Guid.Empty;
            AssetNo = "";
            AssetName = "";
            AssetShortName = "";
            DepartmentId = "";
            DepartmentName = "";
            AcquisitionDate = 0;
            PurchaseAmount = 0;
            Specification = "";
        }
    }

    /// <summary>
    /// 固定資產明細表 DTO
    /// </summary>
    public class FixedAssetDetailReportDTO
    {
        /// <summary>
        /// 財產流水號
        /// </summary>
        public Guid AssetId { get; set; }

        /// <summary>
        /// 財產編號
        /// </summary>
        public string AssetNo { get; set; }

        /// <summary>
        /// 財產名稱
        /// </summary>
        public string AssetName { get; set; }

        /// <summary>
        /// 取得日期
        /// </summary>
        public long AcquisitionDate { get; set; }

        /// <summary>
        /// 取得日期（格式化顯示）
        /// </summary>
        public string AcquisitionDateFormatted { get; set; }

        /// <summary>
        /// 規格
        /// </summary>
        public string Specification { get; set; }

        /// <summary>
        /// 數量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 單位名稱
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// 單價
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 取得價值
        /// </summary>
        public decimal AcquisitionValue { get; set; }

        /// <summary>
        /// 累計折舊
        /// </summary>
        public decimal AccumulatedDepreciation { get; set; }

        /// <summary>
        /// 本年度累計折舊
        /// </summary>
        public decimal CurrentYearAccumulatedDepreciation { get; set; }

        /// <summary>
        /// 淨值（帳面價值）
        /// </summary>
        public decimal NetBookValue { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// 財產科目名稱
        /// </summary>
        public string AssetAccountName { get; set; }

        /// <summary>
        /// 財產子目名稱
        /// </summary>
        public string AssetSubAccountName { get; set; }

        /// <summary>
        /// 部門名稱
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 保管人姓名
        /// </summary>
        public string CustodianName { get; set; }

        /// <summary>
        /// 使用人姓名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 存放地點名稱
        /// </summary>
        public string StorageLocationName { get; set; }

        /// <summary>
        /// 耐用年限
        /// </summary>
        public int ServiceLife { get; set; }

        /// <summary>
        /// 使用狀態
        /// </summary>
        public string Usage { get; set; }

        /// <summary>
        /// 財產狀態名稱
        /// </summary>
        public string AssetStatusName { get; set; }

        public FixedAssetDetailReportDTO()
        {
            AssetId = Guid.Empty;
            AssetNo = "";
            AssetName = "";
            AcquisitionDate = 0;
            AcquisitionDateFormatted = "";
            Specification = "";
            Quantity = 0;
            UnitName = "";
            UnitPrice = 0;
            AcquisitionValue = 0;
            AccumulatedDepreciation = 0;
            CurrentYearAccumulatedDepreciation = 0;
            NetBookValue = 0;
            Notes = "";
            AssetAccountName = "";
            AssetSubAccountName = "";
            DepartmentName = "";
            CustodianName = "";
            UserName = "";
            StorageLocationName = "";
            ServiceLife = 0;
            Usage = "";
            AssetStatusName = "";
        }
    }

    /// <summary>
    /// 固定資產明細表查詢請求 DTO
    /// </summary>
    public class FixedAssetDetailReportRequestDTO
    {
        /// <summary>
        /// 財產科目編號（可選）
        /// </summary>
        public Guid? AssetAccountId { get; set; }

        /// <summary>
        /// 財產子目編號（可選）
        /// </summary>
        public Guid? AssetSubAccountId { get; set; }

        /// <summary>
        /// 部門編號（可選）
        /// </summary>
        public string? DepartmentId { get; set; }

        /// <summary>
        /// 財產狀態編號（可選）
        /// </summary>
        public Guid? AssetStatusId { get; set; }

        /// <summary>
        /// 財產編號（模糊搜尋，可選）
        /// </summary>
        public string AssetNo { get; set; }

        /// <summary>
        /// 財產名稱（模糊搜尋，可選）
        /// </summary>
        public string? AssetName { get; set; }

        /// <summary>
        /// 取得日期起始（可選）
        /// </summary>
        public long? AcquisitionDateStart { get; set; }

        /// <summary>
        /// 取得日期結束（可選）
        /// </summary>
        public long? AcquisitionDateEnd { get; set; }

        /// <summary>
        /// 是否包含報廢財產（預設 false）
        /// </summary>
        public bool? IncludeScrapAssets { get; set; }

        /// <summary>
        /// 是否包含出售財產（預設 false）
        /// </summary>
        public bool? IncludeSoldAssets { get; set; }

        /// <summary>
        /// 是否包含捐贈財產（預設 false）
        /// </summary>
        public bool? IncludeDonatedAssets { get; set; }

        /// <summary>
        /// 指定年度（用於計算本年度累計折舊，預設為當前年度）
        /// </summary>
        public int? Year { get; set; }

        public FixedAssetDetailReportRequestDTO()
        {
            DepartmentId = "";
            AssetNo = "";
            AssetName = "";
            IncludeScrapAssets = false;
            IncludeSoldAssets = false;
            IncludeDonatedAssets = false;
        }
    }
}

