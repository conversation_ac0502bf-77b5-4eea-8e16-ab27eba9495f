import { GranularPermission, GranularPermissionAction, ActionDefinition } from '@/services/common/granularPermissionService';

// 遵循 FastERP 命名規範和 Ims 模組基準
export interface ActionManagerState {
  data: {
    actions: GranularPermissionAction[];
    validActions: ActionDefinition[];
    loading: boolean;
  };
  ui: {
    editingAction: string | null;
    editingValue: string;
    editingValues: Record<string, { code: string; name: string; sortCode: number }>;
    addingActions: Set<string>;
    selectedBatchCodes: Set<string>;
  };
}

export interface ActionManagerProps {
  visible: boolean;
  onCancel: () => void;
  permission: GranularPermission | null;
  onRefresh: () => void;
}

export interface ActionCardProps {
  action: GranularPermissionAction;
  isEditing: boolean;
  editValues: { code: string; name: string; sortCode: number };
  onStartEdit: () => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
  onUpdateEditValues: (values: { code: string; name: string; sortCode: number }) => void;
}

export interface AvailableActionCardProps {
  action: ActionDefinition;
  isSelected: boolean;
  onToggle: () => void;
}

export interface ActionEditFormProps {
  action: GranularPermissionAction;
  editValues: { code: string; name: string; sortCode: number };
  onUpdateValues: (values: { code: string; name: string; sortCode: number }) => void;
  onSave: () => void;
  onCancel: () => void;
}

export interface ActionManagementCardProps {
  state: ActionManagerState;
  availableActions: ActionDefinition[];
  permission: GranularPermission;
  onStartEdit: (actionId: string, action: GranularPermissionAction) => void;
  onCancelEdit: (actionId: string) => void;
  onSaveEdit: (actionId: string) => void;
  onUpdateEditValues: (actionId: string, values: { code: string; name: string; sortCode: number }) => void;
  onToggleBatchSelection: (actionCode: string) => void;
  onClearAllSelections: () => void;
  onBatchAdd: () => void;
}

// 初始狀態
export const initialState: ActionManagerState = {
  data: {
    actions: [],
    validActions: [],
    loading: false
  },
  ui: {
    editingAction: null,
    editingValue: '',
    editingValues: {},
    addingActions: new Set(),
    selectedBatchCodes: new Set()
  }
};
