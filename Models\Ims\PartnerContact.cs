using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 商業夥伴聯絡人關聯表 </summary>
public class PartnerContact : ModelBaseEntity
{
    /// <summary> 商業夥伴編號 </summary>
    [Key]
    [Comment("商業夥伴編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid PartnerID { get; set; }

    /// <summary> 聯絡人編號 </summary>
    [Key]
    [Comment("聯絡人編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid ContactID { get; set; }

    /// <summary> 角色（關係語意，如 DecisionMaker/Executor/Finance 等）</summary>
    [MaxLength(50)]
    [Comment("聯絡人角色（字串）")]
    [Column(TypeName = "nvarchar(50)")]
    public string? Role { get; set; }


    /// <summary> 聯絡人優先級 (數字越小優先級越高，0 = 主要聯絡人) </summary>
    [Comment("聯絡人優先級")]
    public int Priority { get; set; }

    /// <summary> 聯絡人備註 </summary>
    [MaxLength(500)]
    [Comment("聯絡人備註")]
    [Column(TypeName = "nvarchar(500)")]
    public string? Notes { get; set; }

    /// <summary> 聯絡人專長領域 </summary>
    [MaxLength(200)]
    [Comment("聯絡人專長領域")]
    [Column(TypeName = "nvarchar(200)")]
    public string? Expertise { get; set; }

    /// <summary> 聯絡人負責的業務範圍 </summary>
    [MaxLength(200)]
    [Comment("聯絡人負責的業務範圍")]
    [Column(TypeName = "nvarchar(200)")]
    public string? BusinessScope { get; set; }

    /// <summary> 聯絡人工作時間 </summary>
    [MaxLength(100)]
    [Comment("聯絡人工作時間")]
    [Column(TypeName = "nvarchar(100)")]
    public string? WorkingHours { get; set; }

    /// <summary> 聯絡人語言能力 </summary>
    [MaxLength(100)]
    [Comment("聯絡人語言能力")]
    [Column(TypeName = "nvarchar(100)")]
    public string? Languages { get; set; }

    /// <summary> 聯絡人緊急聯絡人 </summary>
    [MaxLength(100)]
    [Comment("聯絡人緊急聯絡人")]
    [Column(TypeName = "nvarchar(100)")]
    public string? EmergencyContact { get; set; }

    /// <summary> 聯絡人緊急聯絡電話 </summary>
    [MaxLength(20)]
    [Comment("聯絡人緊急聯絡電話")]
    [Column(TypeName = "nvarchar(20)")]
    public string? EmergencyPhone { get; set; }

    /// <summary> 導覽屬性 - 商業夥伴 </summary>
    public Partner Partner { get; set; } = null!;

    /// <summary> 導覽屬性 - 聯絡人 </summary>
    public Contact Contact { get; set; } = null!;

    // 不再關聯 ContactRole 實體，改為字串欄位 Role

    /// <summary> 建構式 </summary>
    public PartnerContact()
    {
        Priority = 99; // 預設優先序為 99
    }
}

/// <summary> 商業夥伴聯絡人關聯表 DTO </summary>
public class PartnerContactDTO : ModelBaseEntityDTO
{
    /// <summary> 商業夥伴編號 </summary>
    public Guid PartnerID { get; set; }

    /// <summary> 聯絡人編號 </summary>
    public Guid ContactID { get; set; }

    /// <summary> 聯絡人角色編號 </summary>
    public string? Role { get; set; }

    /// <summary> 是否為主要聯絡人 (針對此夥伴) </summary>
    public bool IsPrimary { get; set; }

    /// <summary> 聯絡人優先級 (數字越小優先級越高，0=主要聯絡人) </summary>
    public int Priority { get; set; }

    /// <summary> 聯絡人備註 </summary>
    [MaxLength(500, ErrorMessage = "備註長度不能超過500字元")]
    public string? Notes { get; set; }

    /// <summary> 聯絡人專長領域 </summary>
    [MaxLength(200, ErrorMessage = "專長領域長度不能超過200字元")]
    public string? Expertise { get; set; }

    /// <summary> 聯絡人負責的業務範圍 </summary>
    [MaxLength(200, ErrorMessage = "業務範圍長度不能超過200字元")]
    public string? BusinessScope { get; set; }

    /// <summary> 聯絡人工作時間 </summary>
    [MaxLength(100, ErrorMessage = "工作時間長度不能超過100字元")]
    public string? WorkingHours { get; set; }

    /// <summary> 聯絡人語言能力 </summary>
    [MaxLength(100, ErrorMessage = "語言能力長度不能超過100字元")]
    public string? Languages { get; set; }

    /// <summary> 聯絡人緊急聯絡人 </summary>
    [MaxLength(100, ErrorMessage = "緊急聯絡人長度不能超過100字元")]
    public string? EmergencyContact { get; set; }

    /// <summary> 聯絡人緊急聯絡電話 </summary>
    [MaxLength(20, ErrorMessage = "緊急聯絡電話長度不能超過20字元")]
    public string? EmergencyPhone { get; set; }

    /// <summary> 建構式 </summary>
    public PartnerContactDTO()
    {
        IsPrimary = false;
        Priority = 99; // 預設優先序為 99
    }
}