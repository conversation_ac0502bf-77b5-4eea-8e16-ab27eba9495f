"use client";

// ================================
// 1. 導入依賴模組
// ================================
import React, { useState, useEffect, useCallback } from 'react';
import { Card, Button, Typography, Tag, Descriptions, message, Spin, Space, Tooltip, Statistic, Row, Col, Progress } from 'antd';
import { 
  PlusOutlined,EditOutlined,DeleteOutlined,ContactsOutlined,UserOutlined,
  MailOutlined,PhoneOutlined,BankOutlined,UnorderedListOutlined,SyncOutlined,
  AppstoreOutlined,CheckCircleOutlined,StopOutlined
} from '@ant-design/icons';

// Services and Types
import { getContactList,addContact,updateContact,deleteContact,type Contact } from '@/services/ims/ContactService';

// Components
import ContactFormModal from '@/app/ims/components/shared/ContactFormModal';
import FilterSearchContainer from '@/app/ims/components/shared/FilterSearchContainer';
import ResponsiveStyles from '@/app/ims/components/shared/ResponsiveStyles';
import ResponsiveTable, { SmartColumnType } from '@/app/ims/components/shared/ResponsiveTable';
import { FilterOption } from '@/app/ims/types/filter';

// Hooks
import { useResponsive } from '@/hooks/useResponsive';
import { useGranularCan } from '@/hooks/useGranularCan';

// Utils
import { SYMBOLS, createContextLogger } from '@/utils/logger';

// ================================
// 2. 常數與工具函數定義
// ================================
// 創建上下文日誌器
const contactPageLogger = createContextLogger({ module: 'ContactManagementPage' });

// 篩選邏輯函數
const applyContactFilters = (
  contacts: Contact[],
  searchText: string,
  filterValues: Record<string, any>
): Contact[] => {
  let filtered = [...contacts];

  // 搜尋文字篩選
  if (searchText && searchText.trim()) {
    const searchLower = searchText.toLowerCase().trim();
    filtered = filtered.filter(contact =>
      contact.name.toLowerCase().includes(searchLower) ||
      (contact.email && contact.email.toLowerCase().includes(searchLower)) ||
      (contact.company && contact.company.toLowerCase().includes(searchLower)) ||
      (contact.phone && contact.phone.includes(searchText)) ||
      (contact.position && contact.position.toLowerCase().includes(searchLower))
    );
  }

  // 狀態篩選
  if (filterValues.isActive !== undefined) {
    filtered = filtered.filter(contact => contact.isActive === filterValues.isActive);
  }

  // 聯絡人類型篩選
  if (filterValues.contactType) {
    filtered = filtered.filter(contact => contact.contactType === filterValues.contactType);
  }

  return filtered;
};

const { Title } = Typography;

// ================================
// 3. 型別定義與常數配置
// ================================
// 統計資料介面
interface StatsData {
  totalContacts: number;
  activeContacts: number;
  internalContacts: number;
  externalContacts: number;
}

// 聯絡人類型選項
const CONTACT_TYPE_OPTIONS = [
  { label: '內部員工', value: 'internal' },
  { label: '外部聯絡人', value: 'external' },
  { label: '客戶', value: 'customer' },
  { label: '供應商', value: 'supplier' },
  { label: '合作夥伴', value: 'partner' }
];

// 篩選選項配置
const filterOptions: FilterOption[] = [
  {
    label: "姓名",
    value: "name",
    type: "input",
    placeholder: "輸入聯絡人姓名"
  },
  {
    label: "電子郵件",
    value: "email", 
    type: "input",
    placeholder: "輸入電子郵件"
  },
  {
    label: "公司",
    value: "company",
    type: "input", 
    placeholder: "輸入公司名稱"
  },
  {
    label: "部門",
    value: "department",
    type: "input",
    placeholder: "輸入部門名稱"
  },
  {
    label: "聯絡人類型",
    value: "contactType",
    type: "select",
    children: CONTACT_TYPE_OPTIONS,
    placeholder: "選擇聯絡人類型"
  },
  {
    label: "狀態",
    value: "isActive",
    type: "select",
    children: [
      { label: "啟用", value: "true" },
      { label: "停用", value: "false" }
    ],
    placeholder: "選擇狀態"
  }
];

// 工具函數
const safeString = (value: any): string => {
  return value != null ? String(value) : '';
};

const getContactTypeLabel = (type: string): string => {
  const option = CONTACT_TYPE_OPTIONS.find(opt => opt.value === type);
  return option ? option.label : type;
};

// ================================
// 4. 主要元件定義
// ================================
export default function ContactManagementPage() {
  // ================================
  // 4.1 狀態管理
  // ================================
  // 響應式 Hook
  const { isMobile } = useResponsive();
  
  // 權限檢查
  const can = useGranularCan();
  const canCreate = can('Ims/Contact', 'Create');
  const canUpdate = can('Ims/Contact', 'Update');
  const canDelete = can('Ims/Contact', 'Delete');
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [stats, setStats] = useState<StatsData>({
    totalContacts: 0,
    activeContacts: 0,
    internalContacts: 0,
    externalContacts: 0
  });

  // ================================
  // 4.2 業務邏輯函數
  // ================================
  // 計算統計資料
  const calculateStats = useCallback((contactsData: Contact[]) => {
    const newStats: StatsData = {
      totalContacts: contactsData.length,
      activeContacts: contactsData.filter(contact => contact.isActive).length,
      internalContacts: contactsData.filter(contact => contact.contactType === 'internal').length,
      externalContacts: contactsData.filter(contact => contact.contactType !== 'internal').length
    };
    setStats(newStats);
  }, []);

  // ================================
  // 4.3 資料載入與管理
  // ================================
  // 載入聯絡人列表
  const loadContacts = useCallback(async () => {
    try {
      setLoading(true);
      contactPageLogger.log(SYMBOLS.LOADING, '載入聯絡人列表');
      
      const response = await getContactList();
      if (response.success && response.data) {
        setContacts(response.data);
        setFilteredContacts(response.data);
        calculateStats(response.data);
        contactPageLogger.log(SYMBOLS.SUCCESS, `載入 ${response.data.length} 筆聯絡人`);
      } else {
        message.error(response.message || '載入聯絡人列表失敗');
        contactPageLogger.log(SYMBOLS.ERROR, '載入聯絡人列表失敗', response);
      }
    } catch (error) {
      message.error('載入聯絡人列表時發生錯誤');
      contactPageLogger.log(SYMBOLS.ERROR, '載入聯絡人列表異常', error);
    } finally {
      setLoading(false);
    }
  }, [calculateStats]);

  // ================================
  // 4.4 生命週期管理
  // ================================
  // 初始載入
  useEffect(() => {
    loadContacts();
  }, [loadContacts]);

  // ================================
  // 4.5 CRUD 操作處理函數
  // ================================
  // 處理表單提交
  const handleSubmit = async (contactData: Contact) => {
    try {
      setLoading(true);
      
      if (selectedContact) {
        // 更新聯絡人
        contactPageLogger.log(SYMBOLS.LOADING, '更新聯絡人', { contactID: selectedContact.contactID });
        const response = await updateContact({ ...contactData, contactID: selectedContact.contactID });
        
        if (response.success) {
          message.success('聯絡人更新成功');
          contactPageLogger.log(SYMBOLS.SUCCESS, '聯絡人更新成功');
          await loadContacts();
        } else {
          message.error(response.message || '更新聯絡人失敗');
          contactPageLogger.log(SYMBOLS.ERROR, '更新聯絡人失敗', response);
        }
      } else {
        // 新增聯絡人
        contactPageLogger.log(SYMBOLS.LOADING, '新增聯絡人');
        const response = await addContact(contactData);
        
        if (response.success) {
          message.success('聯絡人新增成功');
          contactPageLogger.log(SYMBOLS.SUCCESS, '聯絡人新增成功');
          await loadContacts();
        } else {
          message.error(response.message || '新增聯絡人失敗');
          contactPageLogger.log(SYMBOLS.ERROR, '新增聯絡人失敗', response);
        }
      }
      
      setIsModalVisible(false);
      setSelectedContact(null);
    } catch (error) {
      message.error('操作聯絡人時發生錯誤');
      contactPageLogger.log(SYMBOLS.ERROR, '操作聯絡人異常', error);
    } finally {
      setLoading(false);
    }
  };

  // 處理刪除
  const handleDelete = async (contact: Contact) => {
    try {
      setLoading(true);
      contactPageLogger.log(SYMBOLS.LOADING, '刪除聯絡人', { contactID: contact.contactID });
      
      const response = await deleteContact(contact.contactID);
      if (response.success) {
        message.success('聯絡人刪除成功');
        contactPageLogger.log(SYMBOLS.SUCCESS, '聯絡人刪除成功');
        await loadContacts();
      } else {
        message.error(response.message || '刪除聯絡人失敗');
        contactPageLogger.log(SYMBOLS.ERROR, '刪除聯絡人失敗', response);
      }
    } catch (error) {
      message.error('刪除聯絡人時發生錯誤');
      contactPageLogger.log(SYMBOLS.ERROR, '刪除聯絡人異常', error);
    } finally {
      setLoading(false);
    }
  };

  // 處理編輯
  const handleEdit = (contact: Contact) => {
    setSelectedContact(contact);
    setIsModalVisible(true);
  };

  // 處理新增
  const handleAdd = () => {
    setSelectedContact(null);
    setIsModalVisible(true);
  };

  // ================================
  // 4.6 表格配置與渲染函數
  // ================================
  // 表格欄位定義
  const columns: SmartColumnType<Contact>[] = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      responsive: ['xs', 'sm', 'md', 'lg', 'xl'] as const,
      render: (name: string) => (
        <Space>
          <UserOutlined style={{ color: '#1890ff' }} />
          <Typography.Text strong>{safeString(name)}</Typography.Text>
        </Space>
      )
    },
    {
      title: '職位',
      dataIndex: 'position',
      key: 'position',
      responsive: ['md', 'lg', 'xl'] as const,
      render: (position: string) => safeString(position) || '-'
    },
    {
      title: '電子郵件',
      dataIndex: 'email',
      key: 'email',
      responsive: ['lg', 'xl'] as const,
      render: (email: string) => email ? (
        <Space>
          <MailOutlined style={{ color: '#52c41a' }} />
          <Typography.Text copyable>{email}</Typography.Text>
        </Space>
      ) : '-'
    },
    {
      title: '電話',
      dataIndex: 'phone',
      key: 'phone',
      responsive: ['lg', 'xl'] as const,
      render: (phone: string) => phone ? (
        <Space>
          <PhoneOutlined style={{ color: '#fa8c16' }} />
          <Typography.Text copyable>{phone}</Typography.Text>
        </Space>
      ) : '-'
    },
    {
      title: '公司',
      dataIndex: 'company',
      key: 'company',
      responsive: ['xl'] as const,
      render: (company: string) => company ? (
        <Space>
          <BankOutlined style={{ color: '#722ed1' }} />
          <Typography.Text>{company}</Typography.Text>
        </Space>
      ) : '-'
    },
    {
      title: '類型',
      dataIndex: 'contactType',
      key: 'contactType',
      responsive: ['md', 'lg', 'xl'] as const,
      render: (type: string) => (
        <Tag color="blue">{getContactTypeLabel(type)}</Tag>
      )
    },
    {
      title: '狀態',
      dataIndex: 'isActive',
      key: 'isActive',
      responsive: ['sm', 'md', 'lg', 'xl'] as const,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '啟用' : '停用'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'actions',
      responsive: ['xs', 'sm', 'md', 'lg', 'xl'] as const,
      render: (_: any, record: Contact) => (
        <Space size="small">
          {canUpdate && (
            <Tooltip title="編輯">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
                size="small"
              />
            </Tooltip>
          )}
          {canDelete && (
            <Tooltip title="刪除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(record)}
                size="small"
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  // 移動端卡片渲染函數
  const mobileCardRender = ({ record: contact, actions }: { record: Contact; actions?: React.ReactNode }) => (
    <div>
      <div style={{ marginBottom: 12 }}>
        <Typography.Text strong style={{ fontSize: '16px', display: 'block' }}>
          {safeString(contact.name)}
        </Typography.Text>
        <div style={{ marginTop: 4 }}>
          <Tag color={contact.isActive ? 'green' : 'red'}>
            {contact.isActive ? '啟用' : '停用'}
          </Tag>
          <Tag color="blue">{getContactTypeLabel(contact.contactType)}</Tag>
        </div>
      </div>

      <Descriptions size="small" column={1} colon={false}>
        <Descriptions.Item label="職位">
          {safeString(contact.position) || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="電子郵件">
          {contact.email ? (
            <Typography.Text copyable>{contact.email}</Typography.Text>
          ) : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="電話">
          {contact.phone ? (
            <Typography.Text copyable>{contact.phone}</Typography.Text>
          ) : '-'}
        </Descriptions.Item>
        {contact.company && (
          <Descriptions.Item label="公司">
            {contact.company}
          </Descriptions.Item>
        )}
        {contact.department && (
          <Descriptions.Item label="部門">
            {contact.department}
          </Descriptions.Item>
        )}
      </Descriptions>

      {actions && (
        <div style={{ marginTop: 12, textAlign: 'right' }}>
          {actions}
        </div>
      )}
    </div>
  );

  // ================================
  // 4.7 主要渲染區塊
  // ================================
  return (
    <div className="p-6">
      <ResponsiveStyles />
      <Spin spinning={loading}>
        <div style={{ marginBottom: "24px" }}>
          <Title
            level={2}
            style={{
              margin: 0,
              display: "flex",
              alignItems: "center",
              gap: "12px",
            }}
          >
            <ContactsOutlined style={{ color: "#1890ff" }} />
            聯絡人管理
          </Title>
          <Typography.Text type="secondary">
            統一管理聯絡人資訊、分類與狀態設定
          </Typography.Text>
        </div>

        {/* ================================ */}
        {/* 4.7.1 統計卡片區塊 */}
        {/* ================================ */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={12} lg={8}>
            <Card
              size={isMobile ? 'small' : 'default'}
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <ContactsOutlined style={{ color: '#52c41a' }} />
                  <span>聯絡人狀態統計</span>
                </div>
              }
              className="unified-card"
              style={{ height: '100%' }}
              styles={{
                header: {
                  backgroundColor: '#fafafa',
                  borderBottom: '1px solid #f0f0f0',
                  padding: isMobile ? '12px 16px' : '16px 20px'
                },
                body: {
                  padding: isMobile ? '12px 16px' : '16px 20px'
                }
              }}
            >
              <div style={{ marginBottom: '16px' }}>
                <div style={{ fontSize: isMobile ? '20px' : '24px', fontWeight: 'bold', marginBottom: '12px' }}>
                  {stats.activeContacts} / {stats.totalContacts}
                </div>
                <Progress
                  percent={stats.totalContacts > 0 ? Math.round((stats.activeContacts / stats.totalContacts) * 100) : 0}
                  strokeColor="#52c41a"
                  showInfo={false}
                  size="small"
                />
                <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                  啟用率: {stats.totalContacts > 0 ? Math.round((stats.activeContacts / stats.totalContacts) * 100) : 0}%
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={24} lg={16}>
            <Card
              size={isMobile ? 'small' : 'default'}
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <AppstoreOutlined style={{ color: '#1890ff' }} />
                  <span>聯絡人分類管理</span>
                </div>
              }
              className="unified-card"
              style={{ height: '100%' }}
              styles={{
                header: {
                  backgroundColor: '#fafafa',
                  borderBottom: '1px solid #f0f0f0',
                  padding: isMobile ? '12px 16px' : '16px 20px'
                },
                body: {
                  padding: isMobile ? '12px 16px' : '16px 20px'
                }
              }}
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={8}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                    <Statistic
                      title="內部員工"
                      value={stats.internalContacts}
                      prefix={<UserOutlined />}
                      valueStyle={{ fontSize: isMobile ? '18px' : '20px' }}
                    />
                  </div>
                  <Button
                    type="primary"
                    size={isMobile ? 'small' : 'small'}
                    icon={<UserOutlined />}
                    className="unified-button"
                    style={{ width: '100%' }}
                    disabled
                  >
                    內部員工管理
                  </Button>
                </Col>
                <Col xs={24} sm={8}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                    <Statistic
                      title="外部聯絡人"
                      value={stats.externalContacts}
                      prefix={<ContactsOutlined />}
                      valueStyle={{ fontSize: isMobile ? '18px' : '20px' }}
                    />
                  </div>
                  <Button
                    type="primary"
                    size={isMobile ? 'small' : 'small'}
                    icon={<ContactsOutlined />}
                    className="unified-button"
                    style={{ width: '100%' }}
                    disabled
                  >
                    外部聯絡人管理
                  </Button>
                </Col>
                <Col xs={24} sm={8}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                    <Statistic
                      title="總聯絡人數"
                      value={stats.totalContacts}
                      prefix={<CheckCircleOutlined />}
                      valueStyle={{ fontSize: isMobile ? '18px' : '20px' }}
                    />
                  </div>
                  <Button
                    type="primary"
                    size={isMobile ? 'small' : 'small'}
                    icon={<PlusOutlined />}
                    onClick={handleAdd}
                    className="unified-button"
                    style={{ width: '100%' }}
                  >
                    新增聯絡人
                  </Button>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* ================================ */}
        {/* 4.7.2 篩選搜尋區塊 */}
        {/* ================================ */}
        <FilterSearchContainer
          title="篩選與搜尋"
          filterOptions={filterOptions}
          searchPlaceholder="搜尋聯絡人姓名、電子郵件、公司..."
          showStats={true}
          stats={{
            total: contacts.length,
            filtered: filteredContacts.length
          }}
          showClearMessage={true}
          clearMessage="已清除所有聯絡人篩選條件"
          onFilterResult={(state) => {
            // 應用篩選邏輯
            const filtered = applyContactFilters(
              contacts,
              state.searchText,
              state.filterValues
            );
            setFilteredContacts(filtered);
          }}
          className="mb-6"
        />

        {/* ================================ */}
        {/* 4.7.3 資料表格區塊 */}
        {/* ================================ */}
        <Card
            title={
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-start',
                width: '100%',
                flexWrap: isMobile ? 'wrap' : 'nowrap',
                gap: '12px'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <UnorderedListOutlined />
                  <span>聯絡人列表</span>
                  <Tag color="blue">{filteredContacts.length} 項</Tag>
                </div>

                {/* 主要操作按鈕 - 左對齊到標題列 */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  flexWrap: 'wrap',
                  marginTop: isMobile ? '8px' : '0',
                  marginLeft: isMobile ? '0' : '16px'
                }}>
                  <Button
                    icon={<SyncOutlined />}
                    onClick={loadContacts}
                    loading={loading}
                    size="small"
                    title="重新載入資料並清除所有篩選條件"
                    className="unified-button"
                  >
                    {!isMobile && '重新載入'}
                  </Button>
                  {canCreate && (
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={handleAdd}
                      size="small"
                      className="unified-button"
                    >
                      {isMobile ? '新增' : '新增聯絡人'}
                    </Button>
                  )}
                </div>
              </div>
            }
            className="enhanced-table unified-card"
            styles={{
              body: { padding: isMobile ? '8px' : '24px' },
              header: {
                padding: isMobile ? '12px 16px' : '16px 24px',
                borderBottom: '1px solid #f0f0f0',
                backgroundColor: '#fafafa'
              }
            }}
          >
            <ResponsiveTable
              columns={columns}
              dataSource={filteredContacts}
              rowKey="contactID"
              loading={loading}
              mobileCardRender={mobileCardRender}
              pagination={{
                pageSize: 20,
                total: filteredContacts.length,
                showSizeChanger: true,
                showQuickJumper: true
              }}
            />
          </Card>

        {/* ================================ */}
        {/* 4.7.4 模態視窗區塊 */}
        {/* ================================ */}
        <ContactFormModal
          visible={isModalVisible}
          mode={selectedContact ? 'edit' : 'create'}
          initialData={selectedContact}
          onClose={() => {
            setIsModalVisible(false);
            setSelectedContact(null);
          }}
          onSubmit={handleSubmit}
          loading={loading}
        />
      </Spin>
    </div>
  );
}
