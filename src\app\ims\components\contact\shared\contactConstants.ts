/**
 * 聯絡人管理系統常數定義
 * 
 * 統一管理所有聯絡人相關的常數，確保整個系統的一致性
 * 對應後端 Tools/ 中的常數定義
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// 聯絡人類型選項
export const CONTACT_TYPES = [
  { label: "客戶", value: "客戶" },
  { label: "供應商", value: "供應商" },
  { label: "合作夥伴", value: "合作夥伴" },
  { label: "其他", value: "其他" }
] as const;

// 聯絡人角色選項（用於 PartnerContact）
export const CONTACT_ROLES = [
  { label: "主辦", value: "主辦" },
  { label: "協辦", value: "協辦" },
  { label: "其他", value: "其他" }
] as const;

// 聯絡人狀態選項
export const CONTACT_STATUS_OPTIONS = [
  { label: "全部", value: undefined },
  { label: "啟用", value: true },
  { label: "停用", value: false }
] as const;

// 預設值（對應後端 ContactDTO 預設值）
export const CONTACT_DEFAULTS = {
  isActive: true,
  contactType: "客戶",
  role: "其他",
  priority: 99
} as const;

// 驗證規則常數（對應後端 ValidationHelper）
export const CONTACT_VALIDATION_RULES = {
  name: {
    required: true,
    maxLength: 100,
    message: {
      required: "請輸入姓名",
      maxLength: "姓名不能超過100個字元"
    }
  },
  email: {
    type: "email" as const,
    maxLength: 100,
    message: {
      type: "請輸入有效的電子郵件地址",
      maxLength: "電子郵件不能超過100個字元"
    }
  },
  phone: {
    maxLength: 20,
    message: {
      maxLength: "電話不能超過20個字元"
    }
  },
  position: {
    maxLength: 50,
    message: {
      maxLength: "職位不能超過50個字元"
    }
  },
  department: {
    maxLength: 50,
    message: {
      maxLength: "部門不能超過50個字元"
    }
  },
  company: {
    maxLength: 100,
    message: {
      maxLength: "公司不能超過100個字元"
    }
  }
} as const;

// 表格欄位配置
export const CONTACT_TABLE_COLUMNS = {
  name: {
    title: "姓名",
    dataIndex: "name",
    key: "name",
    smartFilter: true,
    filterType: "text" as const,
    filterPlaceholder: "搜尋姓名"
  },
  position: {
    title: "職位", 
    dataIndex: "position",
    key: "position",
    smartFilter: true,
    filterType: "text" as const,
    filterPlaceholder: "搜尋職位"
  },
  company: {
    title: "公司",
    dataIndex: "company", 
    key: "company",
    smartFilter: true,
    filterType: "text" as const,
    filterPlaceholder: "搜尋公司"
  },
  email: {
    title: "電子郵件",
    dataIndex: "email",
    key: "email",
    smartFilter: true,
    filterType: "text" as const,
    filterPlaceholder: "搜尋郵件"
  },
  phone: {
    title: "電話",
    dataIndex: "phone",
    key: "phone",
    smartFilter: true,
    filterType: "text" as const,
    filterPlaceholder: "搜尋電話"
  },
  contactType: {
    title: "類型",
    dataIndex: "contactType",
    key: "contactType",
    smartFilter: true,
    filterType: "select" as const,
    filterPlaceholder: "選擇類型"
  },
  isActive: {
    title: "狀態",
    dataIndex: "isActive",  // 統一使用 isActive
    key: "isActive",
    smartFilter: true,
    filterType: "select" as const,
    filterPlaceholder: "選擇狀態"
  }
} as const;

// 篩選器配置（配合 FilterSearchContainer）
export const CONTACT_FILTER_OPTIONS = [
  {
    label: "聯絡人姓名",
    value: "name",
    type: "input" as const,
    placeholder: "輸入姓名進行搜尋"
  },
  {
    label: "電子郵件",
    value: "email",
    type: "input" as const,
    placeholder: "輸入電子郵件進行搜尋"
  },
  {
    label: "公司",
    value: "company",
    type: "input" as const,
    placeholder: "輸入公司名稱進行搜尋"
  },
  {
    label: "部門",
    value: "department",
    type: "input" as const,
    placeholder: "輸入部門名稱進行搜尋"
  },
  {
    label: "聯絡人類型",
    value: "contactType",
    type: "select" as const,
    children: CONTACT_TYPES.map(type => ({
      label: type.label,
      value: type.value
    })),
    placeholder: "選擇聯絡人類型"
  },
  {
    label: "狀態",
    value: "isActive",  // 統一使用 isActive
    type: "select" as const,
    children: [
      { label: "啟用", value: "true" },
      { label: "停用", value: "false" }
    ],
    placeholder: "選擇狀態"
  }
] as const;

// 表單欄位配置
export const CONTACT_FORM_FIELDS = {
  basic: ["name", "position", "email", "phone"] as const,
  extended: ["department", "company", "contactType", "isActive"] as const,  // 統一使用 isActive
  all: ["name", "position", "email", "phone", "department", "company", "contactType", "isActive"] as const,
  quick: ["name", "email", "phone", "position", "contactType", "isActive"] as const  // 增加 phone 和 position 欄位
} as const;

// 響應式斷點
export const RESPONSIVE_BREAKPOINTS = {
  mobile: 768,
  tablet: 1024
} as const;
