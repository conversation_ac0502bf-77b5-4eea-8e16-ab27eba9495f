# SMS 模組資料模型

## 概述

本文檔詳細說明 SMS 模組中所有實體的資料模型，包括欄位定義、資料類型、關聯關係和約束條件。

## 通用欄位

所有 SMS 實體都包含以下通用欄位：

| 欄位名稱 | 類型 | 說明 | 範例 |
|---------|------|------|------|
| id | string(uuid) | 唯一識別碼 | "3fa85f64-5717-4562-b3fc-2c963f66afa6" |
| createTime | number | 建立時間（Unix 時間戳記） | 1640995200000 |
| createUserId | string | 建立使用者 ID | "user001" |
| updateTime | number | 更新時間（Unix 時間戳記） | 1640995200000 |
| updateUserId | string | 更新使用者 ID | "user002" |
| deleteTime | number | 刪除時間（Unix 時間戳記） | 0 |
| deleteUserId | string | 刪除使用者 ID | "" |
| isDeleted | boolean | 是否已刪除 | false |

## SmsDbServer 資料模型

### 主要欄位

| 欄位名稱 | 類型 | 必填 | 說明 | 範例 |
|---------|------|------|------|------|
| serverName | string | 是 | 伺服器名稱 | "MySQL-Prod-01" |
| location | string | 否 | 位置 | "Taipei" |
| ipAddress | string | 是 | IP 位址 | "*************" |
| dbEngine | string | 是 | 資料庫引擎 | "MySQL" |
| version | string | 否 | 版本 | "8.0" |
| environment | string | 是 | 環境 | "production" |
| remarks | string | 否 | 備註 | "主要生產環境資料庫" |
| vmPort | number | 否 | VM 連接埠 | 3306 |
| dockerPort | number | 否 | Docker 連接埠 | 3306 |
| enableDate | number | 否 | 啟用日期 | 1640995200000 |
| disableDate | number | 否 | 停用日期 | 0 |

### 資料約束

- `serverName`: 長度限制 1-100 字元，必須唯一
- `ipAddress`: 必須是有效的 IPv4 或 IPv6 格式
- `dbEngine`: 建議使用標準名稱，如 "MySQL", "PostgreSQL", "MongoDB", "SQL Server"
- `version`: 建議使用語意化版本號，如 "8.0", "14.0", "6.0"
- `environment`: 建議使用標準值："production", "staging", "development", "qa"
- `vmPort`, `dockerPort`: 範圍 1-65535

### 資料驗證規則

```typescript
interface SmsDbServerValidation {
  serverName: {
    required: true;
    minLength: 1;
    maxLength: 100;
    pattern: /^[a-zA-Z0-9\-_]+$/;
  };
  ipAddress: {
    required: true;
    pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  };
  dbEngine: {
    required: true;
    enum: ["MySQL", "PostgreSQL", "MongoDB", "SQL Server", "Oracle", "SQLite"];
  };
  environment: {
    required: true;
    enum: ["production", "staging", "development", "qa"];
  };
  vmPort: {
    min: 1;
    max: 65535;
  };
  dockerPort: {
    min: 1;
    max: 65535;
  };
}
```

## SmsWebServer 資料模型

### 主要欄位

| 欄位名稱 | 類型 | 必填 | 說明 | 範例 |
|---------|------|------|------|------|
| serverName | string | 是 | 伺服器名稱 | "WebServer-Prod-01" |
| location | string | 否 | 位置 | "Taipei" |
| ipAddress | string | 是 | IP 位址 | "*************" |
| operatingSystem | string | 否 | 作業系統 | "Ubuntu 20.04" |
| environment | string | 是 | 環境 | "production" |
| remarks | string | 否 | 備註 | "主要生產環境 Web 伺服器" |
| vmPort | number | 否 | VM 連接埠 | 80 |
| dockerPort | number | 否 | Docker 連接埠 | 8080 |
| enableDate | number | 否 | 啟用日期 | 1640995200000 |
| disableDate | number | 否 | 停用日期 | 0 |

### 資料約束

- `serverName`: 長度限制 1-100 字元，必須唯一
- `ipAddress`: 必須是有效的 IPv4 或 IPv6 格式
- `operatingSystem`: 建議使用標準名稱，如 "Ubuntu 20.04", "Windows Server 2019", "CentOS 8"
- `environment`: 建議使用標準值："production", "staging", "development", "qa"
- `vmPort`, `dockerPort`: 範圍 1-65535

### 資料驗證規則

```typescript
interface SmsWebServerValidation {
  serverName: {
    required: true;
    minLength: 1;
    maxLength: 100;
    pattern: /^[a-zA-Z0-9\-_]+$/;
  };
  ipAddress: {
    required: true;
    pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  };
  operatingSystem: {
    pattern: /^[a-zA-Z0-9\s\.\-_]+$/;
  };
  environment: {
    required: true;
    enum: ["production", "staging", "development", "qa"];
  };
  vmPort: {
    min: 1;
    max: 65535;
  };
  dockerPort: {
    min: 1;
    max: 65535;
  };
}
```

## SmsSite 資料模型

### 主要欄位

| 欄位名稱 | 類型 | 必填 | 說明 | 範例 |
|---------|------|------|------|------|
| siteName | string | 是 | 站台名稱 | "Production-Site-01" |
| webServerId | string(uuid) | 是 | Web 伺服器 ID | "3fa85f64-5717-4562-b3fc-2c963f66afa6" |
| dbServerId | string(uuid) | 是 | 資料庫伺服器 ID | "987fcdeb-51a2-43d1-b789-123456789abc" |
| purposeDescription | string | 否 | 用途描述 | "主要生產環境站台" |
| ownerUserId | string | 是 | 擁有者使用者 ID | "user001" |
| coOwnerUserId1 | string | 否 | 共同擁有者 1 使用者 ID | "user002" |
| coOwnerUserId2 | string | 否 | 共同擁有者 2 使用者 ID | "user003" |
| enableDate | number | 否 | 啟用日期 | 1640995200000 |
| disableDate | number | 否 | 停用日期 | 0 |

### 關聯物件

#### webServer

包含完整的 WebServer 物件資訊

#### dbServer

包含完整的 DbServer 物件資訊

#### owner

包含完整的 User 物件資訊

#### coOwner1, coOwner2

包含完整的 User 物件資訊

### 資料約束

- `siteName`: 長度限制 1-100 字元，必須唯一
- `webServerId`: 必須是有效的 UUID，且對應的 WebServer 必須存在
- `dbServerId`: 必須是有效的 UUID，且對應的 DbServer 必須存在
- `ownerUserId`: 必須是有效的使用者 ID
- `coOwnerUserId1`, `coOwnerUserId2`: 必須是有效的使用者 ID，且不能與 ownerUserId 重複

### 資料驗證規則

```typescript
interface SmsSiteValidation {
  siteName: {
    required: true;
    minLength: 1;
    maxLength: 100;
    pattern: /^[a-zA-Z0-9\-_\s]+$/;
  };
  webServerId: {
    required: true;
    pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  };
  dbServerId: {
    required: true;
    pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  };
  ownerUserId: {
    required: true;
    minLength: 1;
  };
  purposeDescription: {
    maxLength: 500;
  };
}
```

## User 資料模型

### 主要欄位

| 欄位名稱 | 類型 | 必填 | 說明 | 範例 |
|---------|------|------|------|------|
| userId | string | 是 | 使用者 ID | "user001" |
| account | string | 是 | 帳號 | "john.doe" |
| password | string | 是 | 密碼（加密） | "encrypted_password" |
| name | string | 是 | 姓名 | "John Doe" |
| enterpriseGroupId | string | 否 | 企業群組 ID | "group001" |
| rolesId | string | 否 | 角色 ID | "role001" |
| positionId | string | 否 | 職位 ID | "position001" |
| eMail | string | 否 | 電子郵件 | "<<EMAIL>>" |
| permanentAddress | string | 否 | 永久地址 | "台北市信義區..." |
| mailingAddress | string | 否 | 通訊地址 | "台北市信義區..." |
| telNo | string | 否 | 電話號碼 | "02-1234-5678" |
| phone | string | 否 | 手機號碼 | "0912-345-678" |
| altPhone | string | 否 | 備用電話 | "02-8765-4321" |
| sortCode | number | 否 | 排序碼 | 1 |
| unlockTime | number | 否 | 解鎖時間 | 0 |

### 資料約束

- `userId`: 長度限制 1-50 字元，必須唯一
- `account`: 長度限制 3-50 字元，必須唯一
- `password`: 長度限制 8-128 字元，必須加密
- `name`: 長度限制 1-100 字元
- `eMail`: 必須是有效的電子郵件格式
- `telNo`, `phone`, `altPhone`: 建議使用標準電話號碼格式

### 資料驗證規則

```typescript
interface UserValidation {
  userId: {
    required: true;
    minLength: 1;
    maxLength: 50;
    pattern: /^[a-zA-Z0-9\-_]+$/;
  };
  account: {
    required: true;
    minLength: 3;
    maxLength: 50;
    pattern: /^[a-zA-Z0-9\-_.]+$/;
  };
  password: {
    required: true;
    minLength: 8;
    maxLength: 128;
  };
  name: {
    required: true;
    minLength: 1;
    maxLength: 100;
  };
  eMail: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  };
}
```

## 資料關聯關係

### 一對多關係

- 一個 WebServer 可以有多個 Site
- 一個 DbServer 可以有多個 Site
- 一個 User 可以是多個 Site 的擁有者或共同擁有者

### 多對一關係

- 多個 Site 可以關聯到同一個 WebServer
- 多個 Site 可以關聯到同一個 DbServer

### 約束條件

- Site 必須同時關聯到 WebServer 和 DbServer
- 刪除 WebServer 或 DbServer 時，需要檢查是否有關聯的 Site
- 建議使用軟刪除（soft delete）來處理關聯資料

## 資料完整性

### 主鍵約束

- 所有實體都使用 UUID 作為主鍵
- UUID 必須是唯一的，不能重複

### 外鍵約束

- `SmsSite.webServerId` → `SmsWebServer.id`
- `SmsSite.dbServerId` → `SmsDbServer.id`
- `SmsSite.ownerUserId` → `User.userId`
- `SmsSite.coOwnerUserId1` → `User.userId`
- `SmsSite.coOwnerUserId2` → `User.userId`

### 唯一性約束

- `SmsDbServer.serverName`
- `SmsWebServer.serverName`
- `SmsSite.siteName`
- `User.userId`
- `User.account`

### 檢查約束

- `vmPort`, `dockerPort`: 範圍 1-65535
- `enableDate` < `disableDate`（如果兩者都有值）
- `createTime` ≤ `updateTime`
- `isDeleted`: 布林值

## 資料索引建議

### 主要索引

- 所有實體的 `id` 欄位（主鍵索引）
- `SmsDbServer.serverName`
- `SmsWebServer.serverName`
- `SmsSite.siteName`
- `User.userId`, `User.account`

### 查詢索引

- `SmsDbServer.environment`, `SmsDbServer.dbEngine`
- `SmsWebServer.environment`
- `SmsSite.webServerId`, `SmsSite.dbServerId`
- `User.enterpriseGroupId`, `User.rolesId`

### 複合索引

- `SmsDbServer(environment, dbEngine)`
- `SmsSite(webServerId, dbServerId)`
- `User(enterpriseGroupId, rolesId)`

## 資料備份與恢復

### 備份策略

- 定期完整備份所有 SMS 資料
- 增量備份變更的資料
- 備份相關的使用者資料

### 恢復策略

- 支援點對點恢復
- 支援選擇性恢復特定實體
- 恢復後驗證資料完整性

## 資料安全性

### 加密要求

- 密碼必須加密儲存
- 敏感資訊傳輸使用 HTTPS
- API 存取需要認證和授權

### 存取控制

- 基於角色的存取控制（RBAC）
- 最小權限原則
- 操作日誌記錄

### 資料保護

- 個人資料保護遵循相關法規
- 定期清理過期資料
- 資料脫敏處理
