"use client";
/* 資產攜出申請單
    /app/pms/document_maintenance/asset_carryout_form/page.tsx
  功能說明
    1. 選擇資產
    2. 選擇攜出日期
    3. 選擇攜出目的
    4. 選擇攜出人員
    5. 選擇攜出原因
    6. 選擇攜出地點
    7. 選擇攜出方式
    8. 選擇攜出人員
    9. 選擇攜出人員
    10. 選擇攜出人員
    11. 選擇攜出人員
*/
import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Form,
  Row,
  Col,
  message,
  Modal,
  DatePicker,
  Statistic,
  Steps,
  Tag,
  Spin,
  Typography,
  Checkbox,
  Popconfirm,
} from "antd";
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from "@ant-design/icons";
import { Grid } from "antd";
import type { TableRowSelection } from "antd/es/table/interface";
import {
  AssetCarryOut,
  AssetCarryOutQuery,
  AssetCarryOutStatistics,
  ApprovalRequest,
  CreateAssetCarryOutRequest,
  UpdateAssetCarryOutRequest,
} from "./interface";
import { createColumns, getMobileColumns, EXPORT_COLUMNS } from "./columns";
import {
  STATUS_OPTIONS,
  PURPOSE_OPTIONS,
  translateStatus,
  reverseTranslateStatus,
  createStepItemsWithCount,
  calculateOverallProgress,
  getOverallProgressStatus,
  sortCarryOutList,
} from "./config";
import AssetCarryOutForm from "./Form";
import {
  getAssetCarryOuts,
  getCarryOutStatistics,
  createAssetCarryOut,
  updateAssetCarryOut,
  approveAssetCarryOut,
  registerCarryOut,
  registerReturn,
} from "@/services/pms/assetCarryOutService";
import { getUsers } from "@/services/common/userService";
import { getAssets } from "@/services/pms/assetService";
import { getDepartments } from "@/services/common/departmentService";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { getCookie } from "@/utils/cookies";
import { getUserInfo } from "@/utils/userInfo";
import { siteConfig } from "@/config/site";
import dayjs from "dayjs";
import styles from "./page.module.css";

const { Option } = Select;
const { RangePicker } = DatePicker;
const { useBreakpoint } = Grid;
const { Title } = Typography;

// 主組件
const AssetCarryOutPage: React.FC = () => {
  // =========================== 狀態管理 ===========================
  const [data, setData] = useState<AssetCarryOut[]>([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<AssetCarryOutStatistics | null>(
    null
  );
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<AssetCarryOut[]>([]);

  // 表單相關狀態
  const [formVisible, setFormVisible] = useState(false);
  const [formMode, setFormMode] = useState<"add" | "edit" | "view">("add");
  const [editData, setEditData] = useState<AssetCarryOut | null>(null);

  // 搜尋表單
  const [searchForm] = Form.useForm();
  const [searchLoading, setSearchLoading] = useState(false);

  // 下拉選單選項
  const [userOptions, setUserOptions] = useState<any[]>([]);
  const [assetOptions, setAssetOptions] = useState<any[]>([]);

  // 響應式斷點
  const screens = useBreakpoint();
  const isMobile = !screens.md;

  // =========================== 數據載入 ===========================

  // 從實際資料計算統計
  const calculateStatisticsFromData = useCallback(
    (dataArray: AssetCarryOut[]) => {
      const stats: AssetCarryOutStatistics = {
        totalCount: dataArray.length,
        pendingCount: dataArray.filter(
          (item) => item.status === "PENDING" || item.status === "待審核"
        ).length,
        approvedCount: dataArray.filter(
          (item) => item.status === "APPROVED" || item.status === "已核准"
        ).length,
        rejectedCount: dataArray.filter(
          (item) => item.status === "REJECTED" || item.status === "已駁回"
        ).length,
        carriedOutCount: dataArray.filter(
          (item) => item.status === "CARRIED_OUT" || item.status === "已攜出"
        ).length,
        returnedCount: dataArray.filter(
          (item) => item.status === "RETURNED" || item.status === "已歸還"
        ).length,
        overdueCount: dataArray.filter(
          (item) => item.status === "OVERDUE" || item.status === "逾期"
        ).length,
      };

      setStatistics(stats);
    },
    []
  );

  // 載入資料
  const loadData = useCallback(
    async (query?: AssetCarryOutQuery) => {
      try {
        setLoading(true);
        const response = await getAssetCarryOuts(query);

        if (response.success && response.data) {
          // 處理可能的嵌套資料結構
          let dataArray: AssetCarryOut[] = [];
          if (
            (response.data as any).data &&
            Array.isArray((response.data as any).data)
          ) {
            dataArray = (response.data as any).data;
          } else if (Array.isArray(response.data)) {
            dataArray = response.data;
          }

          // 排序資料
          const sortedData = [...dataArray].sort(sortCarryOutList);
          setData(sortedData);

          // 從實際資料計算統計
          calculateStatisticsFromData(sortedData);
        } else {
          message.error(response.message || "載入攜出申請列表失敗");
          setData([]);
        }
      } catch (error) {
        console.error("載入資料錯誤:", error);
        message.error("載入攜出申請列表失敗");
        setData([]);
      } finally {
        setLoading(false);
      }
    },
    [calculateStatisticsFromData]
  );

  // 載入統計資料
  const loadStatistics = useCallback(async () => {
    try {
      const response = await getCarryOutStatistics();

      if (response.success && response.data) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error("載入統計資料錯誤:", error);
    }
  }, []);

  // 載入用戶選項
  const loadUserOptions = useCallback(async () => {
    try {
      const response = await getUsers();
      if (response.success && response.data) {
        const users = Array.isArray(response.data)
          ? response.data
          : (response.data as any)?.data || [];

        console.log("載入的用戶資料:", users.slice(0, 3)); // 顯示前3筆資料用於debug
        setUserOptions(users);
      }
    } catch (error) {
      console.error("載入用戶列表錯誤:", error);
    }
  }, []);

  // 載入資產選項
  const loadAssetOptions = useCallback(async () => {
    try {
      const response = await getAssets();
      if (response.success && response.data) {
        const assetDetails = Array.isArray(response.data)
          ? response.data
          : (response.data as any)?.data || [];

        // 從 AssetDetail[] 中提取 Asset 物件
        const assets = assetDetails
          .map((detail: any) => detail.asset || detail)
          .filter(Boolean);

        console.log("載入的資產資料:", assets.slice(0, 3)); // 顯示前3筆資料用於debug
        setAssetOptions(assets);
      }
    } catch (error) {
      console.error("載入資產列表錯誤:", error);
    }
  }, []);

  // 初始化載入
  useEffect(() => {
    loadData();
    loadUserOptions();
    loadAssetOptions();
    // 暫時不呼叫 loadStatistics，改為從實際資料計算
    // loadStatistics();
  }, [loadData, loadUserOptions, loadAssetOptions]);

  // =========================== 搜尋功能 ===========================

  // 執行搜尋
  const handleSearch = async () => {
    try {
      setSearchLoading(true);
      const values = searchForm.getFieldsValue();

      const query: AssetCarryOutQuery = {
        status: values.status
          ? reverseTranslateStatus(values.status)
          : undefined,
        applicantId: values.applicantName || undefined,
        assetId: values.assetNo || undefined,
      };

      // 移除空值
      Object.keys(query).forEach((key) => {
        if (query[key as keyof AssetCarryOutQuery] === undefined) {
          delete query[key as keyof AssetCarryOutQuery];
        }
      });

      const response = await getAssetCarryOuts(query);

      if (response.success && response.data) {
        // 處理可能的嵌套資料結構
        let dataArray: AssetCarryOut[] = [];
        if (
          (response.data as any).data &&
          Array.isArray((response.data as any).data)
        ) {
          dataArray = (response.data as any).data;
        } else if (Array.isArray(response.data)) {
          dataArray = response.data;
        }

        // 排序資料
        const sortedData = [...dataArray].sort(sortCarryOutList);
        setData(sortedData);

        // 從實際資料計算統計
        calculateStatisticsFromData(sortedData);
        message.success(`搜尋完成，共找到 ${sortedData.length} 筆資料`);
      } else {
        message.error(response.message || "搜尋失敗");
        setData([]);
      }
    } catch (error) {
      console.error("搜尋錯誤:", error);
      message.error("搜尋失敗");
    } finally {
      setSearchLoading(false);
    }
  };

  // 重置搜尋
  const handleResetSearch = () => {
    searchForm.resetFields();
    loadData(); // 重新載入所有資料
  };

  // =========================== 表單操作 ===========================

  // 新增攜出申請
  const handleAdd = () => {
    setFormMode("add");
    setEditData(null);
    setFormVisible(true);
  };

  // 編輯攜出申請
  const handleEdit = (record: AssetCarryOut) => {
    setFormMode("edit");
    setEditData(record);
    setFormVisible(true);
  };

  // 查看攜出申請
  const handleView = (record: AssetCarryOut) => {
    setFormMode("view");
    setEditData(record);
    setFormVisible(true);
  };

  // 表單提交
  const handleFormSubmit = async (formData: any) => {
    try {
      let response;

      if (formMode === "add") {
        response = await createAssetCarryOut(formData);
      } else if (formMode === "edit") {
        response = await updateAssetCarryOut(editData!.carryOutNo, formData);
      }

      if (response?.success) {
        message.success(
          formMode === "add" ? "攜出申請建立成功" : "攜出申請更新成功"
        );
        loadData();
        // loadStatistics(); // 統計會從資料自動計算
        setFormVisible(false);
      } else {
        message.error(response?.message || "操作失敗");
      }
    } catch (error) {
      console.error("表單提交錯誤:", error);
      message.error("操作失敗");
    }
  };

  // =========================== 資料操作 ===========================

  // 審核攜出申請
  const handleApprove = (record: AssetCarryOut) => {
    Modal.confirm({
      title: "確認核准",
      content: "確定要核准此攜出申請嗎？",
      okText: "核准",
      okType: "danger",
      cancelText: "取消",
      onOk: async () => {
        try {
          const userInfo = getUserInfo();
          const userId = userInfo?.userId || "";
          const userName = userInfo?.name || "";

          const request: ApprovalRequest = {
            isApproved: true,
            approverId: userId,
            comment: "核准通過",
          };

          const response = await approveAssetCarryOut(
            record.carryOutNo,
            request
          );

          if (response.success) {
            message.success("攜出申請核准成功");
            loadData();
            // loadStatistics(); // 統計會從資料自動計算
          } else {
            message.error(response.message || "核准失敗");
          }
        } catch (error) {
          console.error("核准錯誤:", error);
          message.error("核准失敗");
        }
      },
    });
  };

  // 駁回攜出申請
  const handleReject = (record: AssetCarryOut) => {
    Modal.confirm({
      title: "確認駁回",
      content: "確定要駁回此攜出申請嗎？",
      onOk: async () => {
        try {
          const userInfo = getUserInfo();
          const userId = userInfo?.userId || "";
          const userName = userInfo?.name || "";

          const request: ApprovalRequest = {
            isApproved: false,
            approverId: userId,
            comment: "駁回申請",
          };

          const response = await approveAssetCarryOut(
            record.carryOutNo,
            request
          );

          if (response.success) {
            message.success("攜出申請駁回成功");
            loadData();
            // loadStatistics(); // 統計會從資料自動計算
          } else {
            message.error(response.message || "駁回失敗");
          }
        } catch (error) {
          console.error("駁回錯誤:", error);
          message.error("駁回失敗");
        }
      },
    });
  };

  // 登記攜出
  const handleCarryOut = (record: AssetCarryOut) => {
    let selectedDate = DateTimeExtensions.getCurrentTimestamp();

    Modal.confirm({
      title: "登記攜出",
      okText: "確認攜出",
      cancelText: "取消",
      okType: "primary",
      width: 400,
      content: (
        <div style={{ marginTop: 16 }}>
          <p>請選擇實際攜出日期：</p>
          <DatePicker
            placeholder="請選擇攜出日期"
            defaultValue={dayjs()}
            format="YYYY/MM/DD"
            style={{ width: "100%" }}
            onChange={(date) => {
              selectedDate = date
                ? Math.floor(date.valueOf() / 1000)
                : DateTimeExtensions.getCurrentTimestamp();
            }}
          />
        </div>
      ),
      onOk: async () => {
        try {
          const userInfo = getUserInfo();
          const userId = userInfo?.userId || "";

          const request = {
            actualCarryOutDate: selectedDate,
            operatorId: userId,
          };

          const response = await registerCarryOut(record.carryOutNo, request);

          if (response.success) {
            message.success("攜出登記成功");
            loadData();
            // loadStatistics(); // 統計會從資料自動計算
          } else {
            message.error(response.message || "攜出登記失敗");
          }
        } catch (error) {
          console.error("攜出登記錯誤:", error);
          message.error("攜出登記失敗");
        }
      },
    });
  };

  // 登記歸還
  const handleReturn = (record: AssetCarryOut) => {
    let selectedDate = DateTimeExtensions.getCurrentTimestamp();

    Modal.confirm({
      title: "登記歸還",
      okText: "確認歸還",
      cancelText: "取消",
      okType: "primary",
      width: 400,
      content: (
        <div style={{ marginTop: 16 }}>
          <p>請選擇實際歸還日期：</p>
          <DatePicker
            placeholder="請選擇歸還日期"
            defaultValue={dayjs()}
            format="YYYY/MM/DD"
            style={{ width: "100%" }}
            onChange={(date) => {
              selectedDate = date
                ? Math.floor(date.valueOf() / 1000)
                : DateTimeExtensions.getCurrentTimestamp();
            }}
          />
        </div>
      ),
      onOk: async () => {
        try {
          const userInfo = getUserInfo();
          const userId = userInfo?.userId || "";

          const request = {
            actualReturnDate: selectedDate,
            operatorId: userId,
          };

          const response = await registerReturn(record.carryOutNo, request);

          if (response.success) {
            message.success("歸還登記成功");
            loadData();
            // loadStatistics(); // 統計會從資料自動計算
          } else {
            message.error(response.message || "歸還登記失敗");
          }
        } catch (error) {
          console.error("歸還登記錯誤:", error);
          message.error("歸還登記失敗");
        }
      },
    });
  };

  // =========================== 匯出功能 ===========================

  // 轉換為 CSV 格式
  const convertToCSV = (data: any[], includeCompanyHeader: boolean = false) => {
    if (data.length === 0) return "";

    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(",");

    const csvRows = data.map((row) =>
      headers
        .map((header) => {
          const value = row[header];
          // 處理包含逗號、換行符或雙引號的值
          if (
            typeof value === "string" &&
            (value.includes(",") || value.includes("\n") || value.includes('"'))
          ) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        })
        .join(",")
    );

    // 如果需要包含公司表頭
    if (includeCompanyHeader) {
      const companyName = siteConfig.copyright; // 從系統設定取得公司名稱
      const companyHeader = `${companyName} - 資產攜出申請單`;
      const emptyRow = ""; // 空行分隔
      const exportDate = `匯出日期：${DateTimeExtensions.formatDateFromTimestamp(
        DateTimeExtensions.getCurrentTimestamp()
      )}`;

      return [companyHeader, exportDate, emptyRow, csvHeaders, ...csvRows].join(
        "\n"
      );
    }

    return [csvHeaders, ...csvRows].join("\n");
  };

  // 下載 CSV 檔案
  const downloadCSV = (csvContent: string, filename: string) => {
    const BOM = "\uFEFF"; // 加入 BOM 確保中文正確顯示
    const blob = new Blob([BOM + csvContent], {
      type: "text/csv;charset=utf-8;",
    });
    const link = document.createElement("a");

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", filename);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // 匯出選中項目
  const handleExport = () => {
    if (selectedRows.length === 0) {
      message.warning("請選擇要匯出的項目");
      return;
    }

    try {
      // 準備匯出資料
      const exportData = selectedRows.map((item) => {
        const exportItem: any = {};

        Object.keys(EXPORT_COLUMNS).forEach((key) => {
          const columnName = EXPORT_COLUMNS[key as keyof typeof EXPORT_COLUMNS];
          let value = item[key as keyof AssetCarryOut];

          // 特殊處理
          if (key.includes("Date") && typeof value === "number" && value > 0) {
            value = DateTimeExtensions.formatDateFromTimestamp(value);
          } else if (key === "statusName" || key === "status") {
            value = translateStatus(String(value || ""));
          }

          exportItem[columnName] = value || "";
        });

        return exportItem;
      });

      // 轉換為 CSV 格式，加入公司名稱表頭
      const csvContent = convertToCSV(exportData, true);

      // 下載檔案
      downloadCSV(
        csvContent,
        `資產攜出申請單_${DateTimeExtensions.formatDateFromTimestamp(
          DateTimeExtensions.getCurrentTimestamp()
        ).replace(/\//g, "")}.csv`
      );

      message.success(`成功匯出 ${selectedRows.length} 筆攜出申請單`);
    } catch (error) {
      console.error("匯出失敗:", error);
      message.error("匯出失敗");
    }
  };

  // =========================== 表格設定 ===========================

  // 表格行選擇
  const rowSelection: TableRowSelection<AssetCarryOut> = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys, newSelectedRows) => {
      setSelectedRowKeys(newSelectedRowKeys);
      setSelectedRows(newSelectedRows);
    },
    // 在手機版時調整選擇欄位的寬度
    columnWidth: isMobile ? 40 : undefined,
  };

  // 表格欄位
  const actions = {
    onEdit: handleEdit,
    onView: handleView,
    onApprove: handleApprove,
    onReject: handleReject,
    onCarryOut: handleCarryOut,
    onReturn: handleReturn,
  };

  const columns = isMobile ? getMobileColumns(actions) : createColumns(actions);

  // =========================== 渲染組件 ===========================

  return (
    <div style={{ padding: "20px" }}>
      <Card title="財產攜出單">
        {/* 統計卡片區域 */}
        <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="總申請數"
                value={statistics?.totalCount || 0}
                valueStyle={{ color: "#1890ff" }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="待審核"
                value={statistics?.pendingCount || 0}
                valueStyle={{ color: "#faad14" }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="已核准"
                value={statistics?.approvedCount || 0}
                valueStyle={{ color: "#52c41a" }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="已攜出"
                value={statistics?.carriedOutCount || 0}
                valueStyle={{ color: "#1890ff" }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="已歸還"
                value={statistics?.returnedCount || 0}
                valueStyle={{ color: "#52c41a" }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="逾期未還"
                value={statistics?.overdueCount || 0}
                valueStyle={{ color: "#ff4d4f" }}
              />
            </Card>
          </Col>
        </Row>

        {/* 案件處理流程 */}
        <Card
          title={
            <div className={styles.processHeader}>
              <span className={styles.processTitle}>
                案件處理流程 - 總計{" "}
                <span
                  style={{
                    fontWeight: "bold",
                    textDecoration: "underline",
                    color: "#1890ff",
                  }}
                >
                  {statistics?.totalCount || 0}
                </span>{" "}
                件案件
              </span>
            </div>
          }
          className={styles.processSection}
          size="small"
          style={{ marginBottom: "24px" }}
        >
          <Steps
            current={calculateOverallProgress(statistics)}
            status={getOverallProgressStatus(statistics)}
            items={createStepItemsWithCount(statistics).map((item) => ({
              ...item,
              description: (
                <span>
                  {typeof item.description === "string"
                    ? item.description.split("共 ").length > 1
                      ? (() => {
                          const parts = item.description.split("共 ");
                          const afterCount = parts[1].split(" 件");
                          const count = afterCount[0];
                          return (
                            <>
                              {parts[0]}共{" "}
                              <span
                                style={{
                                  fontWeight: "bold",
                                  textDecoration: "underline",
                                }}
                              >
                                {count}
                              </span>{" "}
                              件{afterCount[1] || ""}
                            </>
                          );
                        })()
                      : item.description
                    : item.description}
                </span>
              ),
            }))}
            direction={isMobile ? "vertical" : "horizontal"}
            size="small"
            style={{ marginBottom: "16px" }}
          />
        </Card>

        {/* 工具列 */}
        <div className={styles.toolbar}>
          <div className={styles.toolbarLeft}>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              新增攜出申請
            </Button>
            {!isMobile && (
              <Button
                icon={<ExportOutlined />}
                onClick={handleExport}
                disabled={selectedRows.length === 0}
              >
                匯出選中項目 ({selectedRows.length})
              </Button>
            )}
          </div>
          <div className={styles.toolbarRight}>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                loadData();
                // 暫時不重新載入統計 API，統計會從資料自動計算
                // loadStatistics();
              }}
              loading={loading}
            >
              重新整理
            </Button>
          </div>
        </div>

        {/* 搜尋區域 */}
        <div className={styles.searchSection}>
          <Form
            form={searchForm}
            layout="horizontal"
            className={styles.searchForm}
          >
            <Row gutter={[16, 8]} className={styles.searchRow}>
              <Col xs={24} sm={12} md={6}>
                <Form.Item
                  label="狀態"
                  name="status"
                  style={{ marginBottom: 8 }}
                >
                  <Select
                    placeholder="請選擇狀態"
                    allowClear
                    optionRender={(option) => {
                      const status = STATUS_OPTIONS.find(
                        (s) => s.label === option.value
                      );
                      return status ? (
                        <Tag color={status.color} style={{ margin: 0 }}>
                          {status.label}
                        </Tag>
                      ) : (
                        option.label
                      );
                    }}
                    labelRender={(props) => {
                      const status = STATUS_OPTIONS.find(
                        (s) => s.label === props.value
                      );
                      return status ? (
                        <Tag
                          color={status.color}
                          style={{ margin: 0, fontSize: "14px" }}
                        >
                          {status.label}
                        </Tag>
                      ) : (
                        props.label
                      );
                    }}
                  >
                    {STATUS_OPTIONS.map((status) => (
                      <Select.Option
                        key={status.value}
                        value={status.label}
                        label={status.label}
                      >
                        <Tag color={status.color} style={{ margin: 0 }}>
                          {status.label}
                        </Tag>
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item
                  label="申請人"
                  name="applicantName"
                  style={{ marginBottom: 8 }}
                >
                  <Select
                    placeholder={`請選擇申請人 (共${userOptions.length}項)`}
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? "")
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    options={userOptions.map((user) => ({
                      value: user.userId || user.id,
                      label: user.userName || user.name || user.displayName,
                    }))}
                    notFoundContent={
                      userOptions.length === 0 ? "載入中..." : "無匹配項目"
                    }
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item
                  label="財產編號"
                  name="assetNo"
                  style={{ marginBottom: 8 }}
                >
                  <Select
                    placeholder={`請選擇財產編號 (共${assetOptions.length}項)`}
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? "")
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    options={assetOptions.map((asset) => ({
                      value: asset.assetId || asset.id,
                      label:
                        asset.assetNo && asset.assetName
                          ? `${asset.assetNo} - ${asset.assetName}`
                          : asset.assetNo || asset.assetName || "未知財產",
                    }))}
                    notFoundContent={
                      assetOptions.length === 0 ? "載入中..." : "無匹配項目"
                    }
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Space>
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    onClick={handleSearch}
                    loading={searchLoading}
                    className={styles.searchButton}
                  >
                    搜尋
                  </Button>
                  <Button onClick={handleResetSearch}>重置</Button>
                </Space>
              </Col>
            </Row>
          </Form>
        </div>

        {/* 批次操作區域 */}
        {!isMobile && selectedRows.length > 0 && (
          <div className={styles.batchSection}>
            <span className={styles.batchInfo}>
              已選擇 {selectedRows.length} 項
            </span>
            <Button size="small" onClick={() => setSelectedRowKeys([])}>
              清除選擇
            </Button>
          </div>
        )}

        {/* 表格區域 */}
        <Card
          title={
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <div style={{ display: "flex", alignItems: "center" }}>
                <span style={{ fontWeight: "600", fontSize: "16px" }}>
                  資產攜出列表
                </span>
                <span
                  style={{
                    marginLeft: "12px",
                    fontSize: "14px",
                    color: "#8c8c8c",
                  }}
                >
                  共 {data.length} 筆資料
                </span>
              </div>
            </div>
          }
          styles={{ body: { padding: "0" } }}
        >
          <Table
            rowSelection={isMobile ? undefined : rowSelection}
            columns={columns}
            dataSource={data}
            rowKey="carryOutId"
            loading={loading}
            size="small"
            className={styles.customTable}
            pagination={{
              total: data.length,
              pageSize: isMobile ? 20 : 50,
              showSizeChanger: !isMobile,
              showQuickJumper: !isMobile,
              showTotal: (total, range) =>
                `顯示第 ${range[0]}-${range[1]} 筆，共 ${total} 筆資料`,
              pageSizeOptions: ["20", "50", "100"],
              simple: isMobile,
            }}
          />
        </Card>
      </Card>

      {/* 表單對話框 */}
      <AssetCarryOutForm
        visible={formVisible}
        onClose={() => setFormVisible(false)}
        onSubmit={handleFormSubmit}
        editData={editData}
        mode={formMode}
      />
    </div>
  );
};

export default AssetCarryOutPage;
