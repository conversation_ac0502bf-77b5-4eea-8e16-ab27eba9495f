/**
 * 批量 Contact 操作組件
 * 
 * 提供批量操作界面，包括：
 * - 批量刪除確認
 * - 批量狀態變更
 * - 批量導出
 * - 操作進度顯示
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

"use client";

import React, { useState } from 'react';
import { 
  Modal, 
  Button, 
  Space, 
  Progress, 
  Alert, 
  List, 
  Typography, 
  Divider,
  Row,
  Col,
  Switch,
  Popconfirm,
  message
} from 'antd';
import { 
  DeleteOutlined, 
  DownloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { Contact } from '@/services/ims/ContactService';
import { useBatchContactOperations } from '@/app/ims/hooks/useBatchContactOperations';
import { useScreenSize } from './ResponsiveModalConfig';

const { Text, Title } = Typography;

export interface BatchContactOperationsProps {
  // 顯示控制
  visible: boolean;
  onClose: () => void;
  
  // 選中的聯絡人
  selectedContacts: Contact[];
  
  // 回調函數
  onOperationComplete?: () => void;
}

const BatchContactOperations: React.FC<BatchContactOperationsProps> = ({
  visible,
  onClose,
  selectedContacts,
  onOperationComplete
}) => {
  const { isMobile } = useScreenSize();
  const [operationType, setOperationType] = useState<'delete' | 'export' | 'status' | null>(null);
  const [newStatus, setNewStatus] = useState(true);
  
  const batchOperations = useBatchContactOperations();

  // 處理批量刪除
  const handleBatchDelete = async () => {
    const contactIds = selectedContacts.map(contact => contact.contactID);
    const result = await batchOperations.batchDelete(contactIds);
    
    if (result.success > 0) {
      onOperationComplete?.();
    }
    
    setOperationType(null);
  };

  // 處理批量導出
  const handleBatchExport = () => {
    batchOperations.exportToCSV(selectedContacts);
    setOperationType(null);
  };

  // 處理批量狀態變更
  const handleBatchStatusChange = async () => {
    const contactIds = selectedContacts.map(contact => contact.contactID);
    const result = await batchOperations.batchToggleStatus(contactIds, newStatus);
    
    if (result.success > 0) {
      onOperationComplete?.();
    }
    
    setOperationType(null);
  };

  // 處理關閉
  const handleClose = () => {
    if (!batchOperations.state.isProcessing) {
      batchOperations.resetState();
      setOperationType(null);
      onClose();
    }
  };

  // 渲染操作確認內容
  const renderOperationContent = () => {
    switch (operationType) {
      case 'delete':
        return (
          <div>
            <Alert
              message="批量刪除確認"
              description={`您即將刪除 ${selectedContacts.length} 個聯絡人，此操作無法撤銷。`}
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Title level={5}>將要刪除的聯絡人：</Title>
            <List
              size="small"
              dataSource={selectedContacts.slice(0, 10)} // 只顯示前10個
              renderItem={(contact) => (
                <List.Item>
                  <Space>
                    <Text strong>{contact.name}</Text>
                    <Text type="secondary">{contact.email}</Text>
                    <Text type="secondary">{contact.company}</Text>
                  </Space>
                </List.Item>
              )}
            />
            
            {selectedContacts.length > 10 && (
              <Text type="secondary">
                ...還有 {selectedContacts.length - 10} 個聯絡人
              </Text>
            )}
          </div>
        );

      case 'export':
        return (
          <div>
            <Alert
              message="批量導出"
              description={`將導出 ${selectedContacts.length} 個聯絡人的資料為 CSV 檔案。`}
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Text>導出內容包括：姓名、職位、電子郵件、電話、公司、部門、類型、狀態</Text>
          </div>
        );

      case 'status':
        return (
          <div>
            <Alert
              message="批量狀態變更"
              description={`將變更 ${selectedContacts.length} 個聯絡人的狀態。`}
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Row align="middle" style={{ marginBottom: 16 }}>
              <Col span={8}>
                <Text strong>新狀態：</Text>
              </Col>
              <Col span={16}>
                <Switch
                  checked={newStatus}
                  onChange={setNewStatus}
                  checkedChildren="啟用"
                  unCheckedChildren="停用"
                />
              </Col>
            </Row>
            
            <Text type="secondary">
              當前選中的聯絡人將被設置為 {newStatus ? '啟用' : '停用'} 狀態。
            </Text>
          </div>
        );

      default:
        return null;
    }
  };

  // 渲染操作按鈕
  const renderOperationButtons = () => {
    if (batchOperations.state.isProcessing) {
      return (
        <Button onClick={handleClose} disabled>
          處理中...
        </Button>
      );
    }

    switch (operationType) {
      case 'delete':
        return (
          <Space>
            <Button onClick={() => setOperationType(null)}>
              取消
            </Button>
            <Button 
              type="primary" 
              danger 
              onClick={handleBatchDelete}
              icon={<DeleteOutlined />}
            >
              確認刪除
            </Button>
          </Space>
        );

      case 'export':
        return (
          <Space>
            <Button onClick={() => setOperationType(null)}>
              取消
            </Button>
            <Button 
              type="primary" 
              onClick={handleBatchExport}
              icon={<DownloadOutlined />}
            >
              開始導出
            </Button>
          </Space>
        );

      case 'status':
        return (
          <Space>
            <Button onClick={() => setOperationType(null)}>
              取消
            </Button>
            <Button 
              type="primary" 
              onClick={handleBatchStatusChange}
            >
              確認變更
            </Button>
          </Space>
        );

      default:
        return (
          <Space wrap>
            <Popconfirm
              title="確定要刪除選中的聯絡人嗎？"
              description="此操作無法撤銷"
              onConfirm={() => setOperationType('delete')}
              okText="確定"
              cancelText="取消"
            >
              <Button 
                danger 
                icon={<DeleteOutlined />}
                disabled={selectedContacts.length === 0}
              >
                批量刪除
              </Button>
            </Popconfirm>
            
            <Button 
              icon={<DownloadOutlined />}
              onClick={() => setOperationType('export')}
              disabled={selectedContacts.length === 0}
            >
              導出資料
            </Button>
            
            <Button 
              onClick={() => setOperationType('status')}
              disabled={selectedContacts.length === 0}
            >
              變更狀態
            </Button>
          </Space>
        );
    }
  };

  // 渲染進度顯示
  const renderProgress = () => {
    if (!batchOperations.state.isProcessing && !batchOperations.state.results) {
      return null;
    }

    return (
      <div style={{ marginTop: 16 }}>
        <Divider />
        
        {batchOperations.state.isProcessing && (
          <div>
            <Text strong>{batchOperations.state.currentOperation}</Text>
            <Progress 
              percent={batchOperations.state.progress} 
              status="active"
              style={{ marginTop: 8 }}
            />
          </div>
        )}
        
        {batchOperations.state.results && (
          <div>
            <Title level={5}>操作結果</Title>
            <Row gutter={[16, 8]}>
              <Col span={8}>
                <Space>
                  <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  <Text>成功：{batchOperations.state.results.success}</Text>
                </Space>
              </Col>
              <Col span={8}>
                <Space>
                  <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
                  <Text>失敗：{batchOperations.state.results.failed}</Text>
                </Space>
              </Col>
            </Row>
            
            {batchOperations.state.results.errors.length > 0 && (
              <div style={{ marginTop: 8 }}>
                <Text type="danger">錯誤詳情：</Text>
                <List
                  size="small"
                  dataSource={batchOperations.state.results.errors.slice(0, 5)}
                  renderItem={(error) => (
                    <List.Item>
                      <Text type="danger" style={{ fontSize: '12px' }}>
                        {error}
                      </Text>
                    </List.Item>
                  )}
                />
                {batchOperations.state.results.errors.length > 5 && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    ...還有 {batchOperations.state.results.errors.length - 5} 個錯誤
                  </Text>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <ExclamationCircleOutlined />
          批量操作 ({selectedContacts.length} 個聯絡人)
        </Space>
      }
      open={visible}
      onCancel={handleClose}
      footer={renderOperationButtons()}
      width={isMobile ? '95%' : 600}
      style={{ top: isMobile ? 20 : undefined }}
      maskClosable={!batchOperations.state.isProcessing}
      closable={!batchOperations.state.isProcessing}
    >
      {renderOperationContent()}
      {renderProgress()}
    </Modal>
  );
};

export default BatchContactOperations;
