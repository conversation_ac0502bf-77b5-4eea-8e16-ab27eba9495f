# 職務管理系統

## 概述

職務管理系統用於管理企業內部的職稱（業務）及其負責人員的分配。系統支援為每個職稱指派主辦和協辦人員，並提供完整的 CRUD 操作。

## 功能特色

### 1. 職稱管理

- 新增職稱：建立新的業務職稱
- 編輯職稱：修改現有職稱名稱和排序
- 刪除職稱：軟刪除不需要的職稱
- 排序設定：支援自訂排序編號

### 2. 負責人管理

- **主辦人員**：可為每個職稱指派一位或多位主辦人員
- **協辦人員**：可為每個職稱指派多位協辦人員，支援排序
- **角色區分**：清楚區分主辦與協辦角色
- **動態管理**：支援即時新增、刪除負責人

### 3. 資料展示

- **業務負責人總表**：以表格形式展示所有職稱及其負責人
- **序號顯示**：自動產生序號方便識別
- **標籤展示**：使用不同顏色標籤區分主辦（藍色）和協辦（綠色）
- **搜尋功能**：支援按業務名稱搜尋

## 資料結構

### 職稱 (Position)

```typescript
interface Position {
    positionId: string;      // 職稱ID
    name: string;            // 職稱名稱
    sortCode: number;        // 排序編號
    createTime: number;      // 建立時間
    createUserId: string;    // 建立者ID
    updateTime?: number;     // 更新時間
    updateUserId?: string;   // 更新者ID
    deleteTime?: number;     // 刪除時間
    deleteUserId?: string;   // 刪除者ID
    isDeleted: boolean;      // 是否已刪除
}
```

### 職稱負責人 (PositionOwner)

```typescript
interface PositionOwner {
    positionOwnerId: string; // 負責人關聯ID
    positionId: string;      // 職稱ID
    positionName: string;    // 職稱名稱
    userId: string;          // 使用者ID
    userName: string;        // 使用者姓名
    roleType: string;        // 角色類型（主辦/協辦）
    orderNo: number;         // 排序編號
}
```

### 業務負責人總表 (PositionOwnerSummary)

```typescript
interface PositionOwnerSummary {
    positionId: string;    // 職稱ID
    positionName: string;  // 職稱名稱
    owners: OwnerInfo[];   // 所有負責人員（包含主辦和協辦）
}

interface OwnerInfo {
    userId: string;     // 使用者ID
    userName: string;   // 使用者姓名
    roleType: string;   // 角色類型（主辦/協辦）
    orderNo: number;    // 排序編號
}
```

## API 端點

### 職稱管理

- `GET /api/common/Position/GetPosition` - 取得職稱列表
- `GET /api/common/Position/GetPosition/{positionId}` - 取得職稱詳細資料
- `POST /api/common/Position/AddPosition` - 新增職稱
- `POST /api/common/Position/EditPosition` - 編輯職稱
- `POST /api/common/Position/DeletePosition` - 刪除職稱

### 負責人管理

- `GET /api/common/Position/Owners/Summary` - 取得業務負責人總表
- `GET /api/common/Position/Owners` - 取得負責人列表
- `POST /api/common/Position/Owners/Add` - 新增負責人
- `POST /api/common/Position/Owners/Edit` - 編輯負責人
- `POST /api/common/Position/Owners/Delete` - 刪除負責人

## 資料庫設計

### 表關係

1. **Common_Positions（職稱主表）**
   - 主鍵：PositionId
   - 與 Common_PositionOwners 為一對多關係

2. **Common_Users（使用者主表）**
   - 主鍵：UserId
   - 與 Common_PositionOwners 為一對多關係

3. **Common_PositionOwners（職稱負責人聯結表）**
   - 主鍵：PositionOwnerId
   - 外鍵：PositionId、UserId
   - 包含角色類型（RoleType）和排序（OrderNo）

### 索引設計

- 一般索引：(PositionId, RoleType, OrderNo) - 用於排序查詢
- 唯一索引：(PositionId, UserId, RoleType) - 防止重複指派

## 使用方式

### 1. 新增職稱

1. 點擊「新增職稱」按鈕
2. 輸入職稱名稱和排序編號
3. 點擊確定完成新增

### 2. 指派負責人

1. 在職稱列表中找到目標職稱
2. 點擊「新增主辦」或「新增協辦」按鈕
3. 選擇負責人員和設定排序
4. 點擊確定完成指派

### 3. 管理負責人

- **刪除負責人**：點擊標籤上的 ✕ 按鈕
- **編輯負責人**：點擊標籤進入編輯模式
- **調整排序**：編輯協辦人員的排序編號

## 權限控制

- 需要適當的系統權限才能存取職務管理功能
- 選單項目需要在後端系統設定中配置
- 支援細粒度權限控制（新增、編輯、刪除）

## 注意事項

1. 刪除職稱會使用軟刪除，不會實際刪除資料
2. 同一人可同時擔任同一職稱的主辦和協辦（不同角色不衝突）
3. 協辦人員支援排序，可設定協辦一、協辦二等順序
4. 系統會自動驗證資料完整性和唯一性約束

## 技術實現

- **前端框架**：React + TypeScript
- **UI 組件**：Ant Design
- **狀態管理**：React Hooks
- **資料請求**：自訂 httpClient
- **路由管理**：Next.js App Router
