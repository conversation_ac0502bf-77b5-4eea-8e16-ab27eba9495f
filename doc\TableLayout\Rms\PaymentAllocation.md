# PaymentAllocation 繳費分配關聯表

建立者: 系統自動產生
建立時間: 2025-12-20
DB相關: DB相關-Rms

## 資料表名稱

PaymentAllocation

## 資料表說明

用於描述一筆繳費（Payment）如何分配（核銷）到多筆費用（Fee）。

## 欄位說明

| 欄位名稱 | 資料型態 | 長度 | 允許空值 | 說明 |
| --- | --- | --- | --- | --- |
| PaymentAllocationId | nvarchar | 100 | 否 | 主鍵，分配編號 |
| PaymentId | nvarchar | 100 | 否 | 繳費編號 (FK) |
| FeeId | nvarchar | 100 | 否 | 費用編號 (FK) |
| Amount | decimal | 18,2 | 否 | 分配金額 |
| CreateTime | bigint | - | 否 | 新增時間 |
| CreateUserId | nvarchar | 100 | 是 | 新增者編號 |

## 約束與索引

- 金額約束：
  - 對同一筆 Payment，其所有 Allocation 的金額總和 ≤ Payment.Amount
  - 對同一筆 Fee，其所有 Allocation 的金額總和 ≤ Fee.Amount
- 建議索引：
  - `IX_PaymentAllocation_PaymentId`
  - `IX_PaymentAllocation_FeeId`

## 備註

- 建議在建立 Payment 與 Allocation 時，與 `Fee.PaidAmount/Status`、`Payment.UnappliedAmount/Status` 更新於同一交易中完成，確保資料一致性。
- 若發生作廢/更正，應建立反向分配或沖銷邏輯，以維持核銷紀錄的可追溯性。


