# SMS 模組實作指南

## 概述

本文檔提供 SMS 模組的實作指南，包括前端實作、後端 API 實作、資料庫設計和部署建議。

## 技術架構

### 前端技術棧

- **框架**: React 18 + TypeScript
- **UI 庫**: Ant Design (antd)
- **狀態管理**: Zustand 或 Redux Toolkit
- **路由**: React Router v6
- **HTTP 客戶端**: Axios 或 Fetch API
- **表單處理**: React Hook Form + Zod

### 後端技術棧

- **框架**: .NET 8 Web API 或 Node.js + Express
- **資料庫**: SQL Server 或 PostgreSQL
- **ORM**: Entity Framework Core 或 Prisma
- **認證**: JWT + Identity
- **日誌**: Serilog 或 Winston

### 資料庫設計

- **主資料庫**: 關聯式資料庫
- **快取**: Redis
- **搜尋**: Elasticsearch（可選）

## 前端實作

### 1. 專案結構

```
src/
├── app/
│   └── sms/
│       ├── components/
│       │   ├── SmsDbServers/
│       │   │   ├── SmsDbServerList.tsx
│       │   │   ├── SmsDbServerForm.tsx
│       │   │   ├── SmsDbServerDetail.tsx
│       │   │   └── index.ts
│       │   ├── SmsSites/
│       │   │   ├── SmsSiteList.tsx
│       │   │   ├── SmsSiteForm.tsx
│       │   │   ├── SmsSiteDetail.tsx
│       │   │   └── index.ts
│       │   └── SmsWebServers/
│       │       ├── SmsWebServerList.tsx
│       │       ├── SmsWebServerForm.tsx
│       │       ├── SmsWebServerDetail.tsx
│       │       └── index.ts
│       ├── hooks/
│       │   ├── useSmsDbServers.ts
│       │   ├── useSmsSites.ts
│       │   └── useSmsWebServers.ts
│       ├── services/
│       │   ├── smsDbServerService.ts
│       │   ├── smsSiteService.ts
│       │   └── smsWebServerService.ts
│       ├── types/
│       │   └── sms.ts
│       └── page.tsx
```

### 2. 類型定義

```typescript
// src/app/sms/types/sms.ts

export interface SmsDbServer {
  id: string;
  serverName: string;
  location?: string;
  ipAddress: string;
  dbEngine: string;
  version?: string;
  environment: string;
  remarks?: string;
  vmPort?: number;
  dockerPort?: number;
  enableDate?: number;
  disableDate?: number;
  createTime: number;
  createUserId: string;
  updateTime: number;
  updateUserId: string;
  deleteTime: number;
  deleteUserId: string;
  isDeleted: boolean;
}

export interface SmsWebServer {
  id: string;
  serverName: string;
  location?: string;
  ipAddress: string;
  operatingSystem?: string;
  environment: string;
  remarks?: string;
  vmPort?: number;
  dockerPort?: number;
  enableDate?: number;
  disableDate?: number;
  createTime: number;
  createUserId: string;
  updateTime: number;
  updateUserId: string;
  deleteTime: number;
  deleteUserId: string;
  isDeleted: boolean;
}

export interface SmsSite {
  id: string;
  siteName: string;
  webServerId: string;
  dbServerId: string;
  purposeDescription?: string;
  ownerUserId: string;
  coOwnerUserId1?: string;
  coOwnerUserId2?: string;
  enableDate?: number;
  disableDate?: number;
  createTime: number;
  createUserId: string;
  updateTime: number;
  updateUserId: string;
  deleteTime: number;
  deleteUserId: string;
  isDeleted: boolean;
  webServer?: SmsWebServer;
  dbServer?: SmsDbServer;
  owner?: User;
  coOwner1?: User;
  coOwner2?: User;
}

export interface User {
  userId: string;
  account: string;
  name: string;
  eMail?: string;
  phone?: string;
  positionId?: string;
}

export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
  totalCount?: number;
}

export interface QueryParams {
  keyword?: string;
  environment?: string;
  dbEngine?: string;
  webServerId?: string;
  dbServerId?: string;
  ownerUserId?: string;
  page?: number;
  pageSize?: number;
}
```

### 3. API 服務

```typescript
// src/app/sms/services/smsDbServerService.ts

import { httpClient } from '@/lib/http';
import { SmsDbServer, ApiResponse, QueryParams } from '../types/sms';

export class SmsDbServerService {
  private baseUrl = '/api/sms/SmsDbServers';

  async getList(params?: QueryParams): Promise<ApiResponse<SmsDbServer[]>> {
    const response = await httpClient.get(this.baseUrl, { params });
    return response.data;
  }

  async getById(id: string): Promise<ApiResponse<SmsDbServer>> {
    const response = await httpClient.get(`${this.baseUrl}/${id}`);
    return response.data;
  }

  async create(data: Partial<SmsDbServer>): Promise<ApiResponse<SmsDbServer>> {
    const response = await httpClient.post(this.baseUrl, data);
    return response.data;
  }

  async update(id: string, data: Partial<SmsDbServer>): Promise<ApiResponse<SmsDbServer>> {
    const response = await httpClient.put(`${this.baseUrl}/${id}`, data);
    return response.data;
  }

  async delete(id: string): Promise<ApiResponse<void>> {
    const response = await httpClient.delete(`${this.baseUrl}/${id}`);
    return response.data;
  }

  async batchCreate(data: Partial<SmsDbServer>[]): Promise<ApiResponse<SmsDbServer[]>> {
    const response = await httpClient.post(`${this.baseUrl}/batch`, data);
    return response.data;
  }
}

export const smsDbServerService = new SmsDbServerService();
```

### 4. 自定義 Hook

```typescript
// src/app/sms/hooks/useSmsDbServers.ts

import { useState, useEffect, useCallback } from 'react';
import { SmsDbServer, QueryParams } from '../types/sms';
import { smsDbServerService } from '../services/smsDbServerService';
import { message } from 'antd';

export function useSmsDbServers() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<SmsDbServer[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const fetchData = useCallback(async (params?: QueryParams) => {
    try {
      setLoading(true);
      const response = await smsDbServerService.getList({
        ...params,
        page: currentPage,
        pageSize,
      });
      
      if (response.success) {
        setData(response.data);
        setTotalCount(response.totalCount || 0);
      } else {
        message.error(response.message || '查詢失敗');
      }
    } catch (error) {
      console.error('查詢資料庫伺服器失敗:', error);
      message.error('查詢失敗，請稍後再試');
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize]);

  const create = useCallback(async (data: Partial<SmsDbServer>) => {
    try {
      const response = await smsDbServerService.create(data);
      if (response.success) {
        message.success('新增成功');
        fetchData();
        return response.data;
      } else {
        message.error(response.message || '新增失敗');
        return null;
      }
    } catch (error) {
      console.error('新增資料庫伺服器失敗:', error);
      message.error('新增失敗，請稍後再試');
      return null;
    }
  }, [fetchData]);

  const update = useCallback(async (id: string, data: Partial<SmsDbServer>) => {
    try {
      const response = await smsDbServerService.update(id, data);
      if (response.success) {
        message.success('更新成功');
        fetchData();
        return response.data;
      } else {
        message.error(response.message || '更新失敗');
        return null;
      }
    } catch (error) {
      console.error('更新資料庫伺服器失敗:', error);
      message.error('更新失敗，請稍後再試');
      return null;
    }
  }, [fetchData]);

  const remove = useCallback(async (id: string) => {
    try {
      const response = await smsDbServerService.delete(id);
      if (response.success) {
        message.success('刪除成功');
        fetchData();
        return true;
      } else {
        message.error(response.message || '刪除失敗');
        return false;
      }
    } catch (error) {
      console.error('刪除資料庫伺服器失敗:', error);
      message.error('刪除失敗，請稍後再試');
      return false;
    }
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    loading,
    data,
    totalCount,
    currentPage,
    pageSize,
    setCurrentPage,
    setPageSize,
    fetchData,
    create,
    update,
    remove,
  };
}
```

### 5. 元件實作

```typescript
// src/app/sms/components/SmsDbServers/SmsDbServerList.tsx

import React, { useState } from 'react';
import { Table, Button, Space, Modal, message, Input, Select } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { SmsDbServer } from '../../types/sms';
import { useSmsDbServers } from '../../hooks/useSmsDbServers';
import { SmsDbServerForm } from './SmsDbServerForm';

const { Search } = Input;
const { Option } = Select;

export function SmsDbServerList() {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchEnvironment, setSearchEnvironment] = useState('');
  const [searchDbEngine, setSearchDbEngine] = useState('');
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<SmsDbServer | null>(null);

  const {
    loading,
    data,
    totalCount,
    currentPage,
    pageSize,
    setCurrentPage,
    setPageSize,
    remove,
  } = useSmsDbServers();

  const handleSearch = () => {
    // 實作搜尋邏輯
  };

  const handleAdd = () => {
    setEditingRecord(null);
    setIsFormVisible(true);
  };

  const handleEdit = (record: SmsDbServer) => {
    setEditingRecord(record);
    setIsFormVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '確認刪除',
      content: '確定要刪除此資料庫伺服器嗎？',
      onOk: async () => {
        await remove(id);
      },
    });
  };

  const columns = [
    {
      title: '伺服器名稱',
      dataIndex: 'serverName',
      key: 'serverName',
    },
    {
      title: 'IP 位址',
      dataIndex: 'ipAddress',
      key: 'ipAddress',
    },
    {
      title: '資料庫引擎',
      dataIndex: 'dbEngine',
      key: 'dbEngine',
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '環境',
      dataIndex: 'environment',
      key: 'environment',
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: SmsDbServer) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            刪除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Search
            placeholder="搜尋伺服器名稱"
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            style={{ width: 200 }}
          />
          <Select
            placeholder="選擇環境"
            value={searchEnvironment}
            onChange={setSearchEnvironment}
            style={{ width: 120 }}
            allowClear
          >
            <Option value="production">生產環境</Option>
            <Option value="staging">測試環境</Option>
            <Option value="development">開發環境</Option>
          </Select>
          <Select
            placeholder="選擇資料庫引擎"
            value={searchDbEngine}
            onChange={setSearchDbEngine}
            style={{ width: 150 }}
            allowClear
          >
            <Option value="MySQL">MySQL</Option>
            <Option value="PostgreSQL">PostgreSQL</Option>
            <Option value="MongoDB">MongoDB</Option>
            <Option value="SQL Server">SQL Server</Option>
          </Select>
          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜尋
          </Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            新增
          </Button>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: totalCount,
          onChange: setCurrentPage,
          onShowSizeChange: (_, size) => setPageSize(size),
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
        }}
      />

      <SmsDbServerForm
        visible={isFormVisible}
        record={editingRecord}
        onCancel={() => setIsFormVisible(false)}
        onSuccess={() => {
          setIsFormVisible(false);
          setEditingRecord(null);
        }}
      />
    </div>
  );
}
```

## 後端實作

### 1. 控制器

```csharp
// Controllers/SmsDbServersController.cs

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SmsDbServersController : ControllerBase
{
    private readonly ISmsDbServerService _smsDbServerService;
    private readonly ILogger<SmsDbServersController> _logger;

    public SmsDbServersController(
        ISmsDbServerService smsDbServerService,
        ILogger<SmsDbServersController> logger)
    {
        _smsDbServerService = smsDbServerService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<ApiResponse<List<SmsDbServer>>>> GetList(
        [FromQuery] string? keyword,
        [FromQuery] string? dbEngine,
        [FromQuery] string? environment)
    {
        try
        {
            var query = new SmsDbServerQuery
            {
                Keyword = keyword,
                DbEngine = dbEngine,
                Environment = environment
            };

            var result = await _smsDbServerService.GetListAsync(query);
            return Ok(new ApiResponse<List<SmsDbServer>>
            {
                Success = true,
                Data = result,
                Message = "查詢成功"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查詢資料庫伺服器失敗");
            return StatusCode(500, new ApiResponse<List<SmsDbServer>>
            {
                Success = false,
                Message = "查詢失敗"
            });
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<SmsDbServer>>> GetById(Guid id)
    {
        try
        {
            var result = await _smsDbServerService.GetByIdAsync(id);
            if (result == null)
            {
                return NotFound(new ApiResponse<SmsDbServer>
                {
                    Success = false,
                    Message = "資料庫伺服器不存在"
                });
            }

            return Ok(new ApiResponse<SmsDbServer>
            {
                Success = true,
                Data = result,
                Message = "查詢成功"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查詢資料庫伺服器失敗");
            return StatusCode(500, new ApiResponse<SmsDbServer>
            {
                Success = false,
                Message = "查詢失敗"
            });
        }
    }

    [HttpPost]
    public async Task<ActionResult<ApiResponse<SmsDbServer>>> Create(
        [FromBody] CreateSmsDbServerRequest request)
    {
        try
        {
            var result = await _smsDbServerService.CreateAsync(request);
            return Ok(new ApiResponse<SmsDbServer>
            {
                Success = true,
                Data = result,
                Message = "新增成功"
            });
        }
        catch (ValidationException ex)
        {
            return BadRequest(new ApiResponse<SmsDbServer>
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "新增資料庫伺服器失敗");
            return StatusCode(500, new ApiResponse<SmsDbServer>
            {
                Success = false,
                Message = "新增失敗"
            });
        }
    }

    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<SmsDbServer>>> Update(
        Guid id,
        [FromBody] UpdateSmsDbServerRequest request)
    {
        try
        {
            var result = await _smsDbServerService.UpdateAsync(id, request);
            return Ok(new ApiResponse<SmsDbServer>
            {
                Success = true,
                Data = result,
                Message = "更新成功"
            });
        }
        catch (NotFoundException ex)
        {
            return NotFound(new ApiResponse<SmsDbServer>
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (ValidationException ex)
        {
            return BadRequest(new ApiResponse<SmsDbServer>
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新資料庫伺服器失敗");
            return StatusCode(500, new ApiResponse<SmsDbServer>
            {
                Success = false,
                Message = "更新失敗"
            });
        }
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse>> Delete(Guid id)
    {
        try
        {
            await _smsDbServerService.DeleteAsync(id);
            return Ok(new ApiResponse
            {
                Success = true,
                Message = "刪除成功"
            });
        }
        catch (NotFoundException ex)
        {
            return NotFound(new ApiResponse
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刪除資料庫伺服器失敗");
            return StatusCode(500, new ApiResponse
            {
                Success = false,
                Message = "刪除失敗"
            });
        }
    }

    [HttpPost("batch")]
    public async Task<ActionResult<ApiResponse<List<SmsDbServer>>>> BatchCreate(
        [FromBody] List<CreateSmsDbServerRequest> requests)
    {
        try
        {
            var result = await _smsDbServerService.BatchCreateAsync(requests);
            return Ok(new ApiResponse<List<SmsDbServer>>
            {
                Success = true,
                Data = result,
                Message = "批次新增成功"
            });
        }
        catch (ValidationException ex)
        {
            return BadRequest(new ApiResponse<List<SmsDbServer>>
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批次新增資料庫伺服器失敗");
            return StatusCode(500, new ApiResponse<List<SmsDbServer>>
            {
                Success = false,
                Message = "批次新增失敗"
            });
        }
    }
}
```

### 2. 服務層

```csharp
// Services/ISmsDbServerService.cs

public interface ISmsDbServerService
{
    Task<List<SmsDbServer>> GetListAsync(SmsDbServerQuery query);
    Task<SmsDbServer?> GetByIdAsync(Guid id);
    Task<SmsDbServer> CreateAsync(CreateSmsDbServerRequest request);
    Task<SmsDbServer> UpdateAsync(Guid id, UpdateSmsDbServerRequest request);
    Task DeleteAsync(Guid id);
    Task<List<SmsDbServer>> BatchCreateAsync(List<CreateSmsDbServerRequest> requests);
}

// Services/SmsDbServerService.cs

public class SmsDbServerService : ISmsDbServerService
{
    private readonly ISmsDbServerRepository _repository;
    private readonly IValidator<CreateSmsDbServerRequest> _createValidator;
    private readonly IValidator<UpdateSmsDbServerRequest> _updateValidator;
    private readonly ILogger<SmsDbServerService> _logger;

    public SmsDbServerService(
        ISmsDbServerRepository repository,
        IValidator<CreateSmsDbServerRequest> createValidator,
        IValidator<UpdateSmsDbServerRequest> updateValidator,
        ILogger<SmsDbServerService> logger)
    {
        _repository = repository;
        _createValidator = createValidator;
        _updateValidator = updateValidator;
        _logger = logger;
    }

    public async Task<List<SmsDbServer>> GetListAsync(SmsDbServerQuery query)
    {
        return await _repository.GetListAsync(query);
    }

    public async Task<SmsDbServer?> GetByIdAsync(Guid id)
    {
        return await _repository.GetByIdAsync(id);
    }

    public async Task<SmsDbServer> CreateAsync(CreateSmsDbServerRequest request)
    {
        var validationResult = await _createValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            throw new ValidationException(validationResult.Errors);
        }

        var entity = new SmsDbServer
        {
            Id = Guid.NewGuid(),
            ServerName = request.ServerName,
            Location = request.Location,
            IpAddress = request.IpAddress,
            DbEngine = request.DbEngine,
            Version = request.Version,
            Environment = request.Environment,
            Remarks = request.Remarks,
            VmPort = request.VmPort,
            DockerPort = request.DockerPort,
            EnableDate = request.EnableDate,
            DisableDate = request.DisableDate,
            CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            CreateUserId = request.CreateUserId,
            UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            UpdateUserId = request.CreateUserId,
            IsDeleted = false
        };

        return await _repository.CreateAsync(entity);
    }

    public async Task<SmsDbServer> UpdateAsync(Guid id, UpdateSmsDbServerRequest request)
    {
        var validationResult = await _updateValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            throw new ValidationException(validationResult.Errors);
        }

        var existingEntity = await _repository.GetByIdAsync(id);
        if (existingEntity == null)
        {
            throw new NotFoundException($"ID 為 {id} 的資料庫伺服器不存在");
        }

        existingEntity.ServerName = request.ServerName ?? existingEntity.ServerName;
        existingEntity.Location = request.Location ?? existingEntity.Location;
        existingEntity.IpAddress = request.IpAddress ?? existingEntity.IpAddress;
        existingEntity.DbEngine = request.DbEngine ?? existingEntity.DbEngine;
        existingEntity.Version = request.Version ?? existingEntity.Version;
        existingEntity.Environment = request.Environment ?? existingEntity.Environment;
        existingEntity.Remarks = request.Remarks ?? existingEntity.Remarks;
        existingEntity.VmPort = request.VmPort ?? existingEntity.VmPort;
        existingEntity.DockerPort = request.DockerPort ?? existingEntity.DockerPort;
        existingEntity.EnableDate = request.EnableDate ?? existingEntity.EnableDate;
        existingEntity.DisableDate = request.DisableDate ?? existingEntity.DisableDate;
        existingEntity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        existingEntity.UpdateUserId = request.UpdateUserId;

        return await _repository.UpdateAsync(existingEntity);
    }

    public async Task DeleteAsync(Guid id)
    {
        var existingEntity = await _repository.GetByIdAsync(id);
        if (existingEntity == null)
        {
            throw new NotFoundException($"ID 為 {id} 的資料庫伺服器不存在");
        }

        await _repository.DeleteAsync(id);
    }

    public async Task<List<SmsDbServer>> BatchCreateAsync(List<CreateSmsDbServerRequest> requests)
    {
        var entities = new List<SmsDbServer>();
        var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

        foreach (var request in requests)
        {
            var validationResult = await _createValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                throw new ValidationException(validationResult.Errors);
            }

            var entity = new SmsDbServer
            {
                Id = Guid.NewGuid(),
                ServerName = request.ServerName,
                Location = request.Location,
                IpAddress = request.IpAddress,
                DbEngine = request.DbEngine,
                Version = request.Version,
                Environment = request.Environment,
                Remarks = request.Remarks,
                VmPort = request.VmPort,
                DockerPort = request.DockerPort,
                EnableDate = request.EnableDate,
                DisableDate = request.DisableDate,
                CreateTime = currentTime,
                CreateUserId = request.CreateUserId,
                UpdateTime = currentTime,
                UpdateUserId = request.CreateUserId,
                IsDeleted = false
            };

            entities.Add(entity);
        }

        return await _repository.BatchCreateAsync(entities);
    }
}
```

## 資料庫設計

### 1. 資料表結構

```sql
-- 資料庫伺服器表
CREATE TABLE SmsDbServers (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ServerName NVARCHAR(100) NOT NULL UNIQUE,
    Location NVARCHAR(200),
    IpAddress NVARCHAR(45) NOT NULL,
    DbEngine NVARCHAR(50) NOT NULL,
    Version NVARCHAR(20),
    Environment NVARCHAR(20) NOT NULL,
    Remarks NVARCHAR(500),
    VmPort INT CHECK (VmPort BETWEEN 1 AND 65535),
    DockerPort INT CHECK (DockerPort BETWEEN 1 AND 65535),
    EnableDate BIGINT,
    DisableDate BIGINT,
    CreateTime BIGINT NOT NULL,
    CreateUserId NVARCHAR(50) NOT NULL,
    UpdateTime BIGINT NOT NULL,
    UpdateUserId NVARCHAR(50) NOT NULL,
    DeleteTime BIGINT DEFAULT 0,
    DeleteUserId NVARCHAR(50),
    IsDeleted BIT DEFAULT 0
);

-- Web 伺服器表
CREATE TABLE SmsWebServers (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ServerName NVARCHAR(100) NOT NULL UNIQUE,
    Location NVARCHAR(200),
    IpAddress NVARCHAR(45) NOT NULL,
    OperatingSystem NVARCHAR(100),
    Environment NVARCHAR(20) NOT NULL,
    Remarks NVARCHAR(500),
    VmPort INT CHECK (VmPort BETWEEN 1 AND 65535),
    DockerPort INT CHECK (DockerPort BETWEEN 1 AND 65535),
    EnableDate BIGINT,
    DisableDate BIGINT,
    CreateTime BIGINT NOT NULL,
    CreateUserId NVARCHAR(50) NOT NULL,
    UpdateTime BIGINT NOT NULL,
    UpdateUserId NVARCHAR(50) NOT NULL,
    DeleteTime BIGINT DEFAULT 0,
    DeleteUserId NVARCHAR(50),
    IsDeleted BIT DEFAULT 0
);

-- 站台表
CREATE TABLE SmsSites (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    SiteName NVARCHAR(100) NOT NULL UNIQUE,
    WebServerId UNIQUEIDENTIFIER NOT NULL,
    DbServerId UNIQUEIDENTIFIER NOT NULL,
    PurposeDescription NVARCHAR(500),
    OwnerUserId NVARCHAR(50) NOT NULL,
    CoOwnerUserId1 NVARCHAR(50),
    CoOwnerUserId2 NVARCHAR(50),
    EnableDate BIGINT,
    DisableDate BIGINT,
    CreateTime BIGINT NOT NULL,
    CreateUserId NVARCHAR(50) NOT NULL,
    UpdateTime BIGINT NOT NULL,
    UpdateUserId NVARCHAR(50) NOT NULL,
    DeleteTime BIGINT DEFAULT 0,
    DeleteUserId NVARCHAR(50),
    IsDeleted BIT DEFAULT 0,
    FOREIGN KEY (WebServerId) REFERENCES SmsWebServers(Id),
    FOREIGN KEY (DbServerId) REFERENCES SmsDbServers(Id)
);

-- 索引
CREATE INDEX IX_SmsDbServers_Environment ON SmsDbServers(Environment);
CREATE INDEX IX_SmsDbServers_DbEngine ON SmsDbServers(DbEngine);
CREATE INDEX IX_SmsDbServers_ServerName ON SmsDbServers(ServerName);
CREATE INDEX IX_SmsWebServers_Environment ON SmsWebServers(Environment);
CREATE INDEX IX_SmsWebServers_ServerName ON SmsWebServers(ServerName);
CREATE INDEX IX_SmsSites_WebServerId ON SmsSites(WebServerId);
CREATE INDEX IX_SmsSites_DbServerId ON SmsSites(DbServerId);
CREATE INDEX IX_SmsSites_OwnerUserId ON SmsSites(OwnerUserId);
```

## 部署建議

### 1. 環境配置

```json
// appsettings.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=SmsDb;Trusted_Connection=true;"
  },
  "JwtSettings": {
    "SecretKey": "your-secret-key-here",
    "Issuer": "your-issuer",
    "Audience": "your-audience",
    "ExpirationMinutes": 60
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}
```

### 2. Docker 部署

```dockerfile
# Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["SmsApi/SmsApi.csproj", "SmsApi/"]
RUN dotnet restore "SmsApi/SmsApi.csproj"
COPY . .
WORKDIR "/src/SmsApi"
RUN dotnet build "SmsApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "SmsApi.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "SmsApi.dll"]
```

### 3. 效能優化

- 使用 Redis 快取常用資料
- 實作資料庫連線池
- 使用非同步處理
- 實作分頁查詢
- 使用資料庫索引優化查詢

### 4. 安全性建議

- 實作 JWT 認證
- 使用 HTTPS
- 實作 API 限流
- 記錄操作日誌
- 資料驗證和清理

## 測試策略

### 1. 單元測試

- 服務層邏輯測試
- 驗證器測試
- 工具函數測試

### 2. 整合測試

- API 端點測試
- 資料庫操作測試
- 認證授權測試

### 3. 端到端測試

- 完整流程測試
- 使用者介面測試
- 效能測試

## 監控與維護

### 1. 日誌記錄

- 使用結構化日誌
- 記錄 API 呼叫
- 記錄錯誤和異常

### 2. 效能監控

- API 回應時間
- 資料庫查詢效能
- 系統資源使用率

### 3. 健康檢查

- 資料庫連線檢查
- 外部服務檢查
- 系統狀態檢查

## 總結

本文檔提供了 SMS 模組的完整實作指南，包括：

1. **前端實作**: React + TypeScript + Ant Design
2. **後端實作**: .NET Web API + Entity Framework
3. **資料庫設計**: SQL Server 資料表結構
4. **部署建議**: Docker + 環境配置
5. **測試策略**: 單元測試 + 整合測試
6. **監控維護**: 日誌記錄 + 效能監控

建議開發團隊根據實際需求和技術棧調整實作方案，並建立完整的測試和部署流程。
