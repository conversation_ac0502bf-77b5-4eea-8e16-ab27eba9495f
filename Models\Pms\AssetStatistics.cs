using System.ComponentModel.DataAnnotations;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 財產統計總覽 DTO
    /// </summary>
    public class AssetStatisticsOverviewDTO
    {
        /// <summary>
        /// 財產總數量
        /// </summary>
        public int TotalAssets { get; set; }

        /// <summary>
        /// 財產總價值
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// 累計折舊總金額
        /// </summary>
        public decimal TotalDepreciation { get; set; }

        /// <summary>
        /// 淨值總金額
        /// </summary>
        public decimal TotalNetValue { get; set; }

        /// <summary>
        /// 本年度新增財產數量
        /// </summary>
        public int ThisYearNewAssets { get; set; }

        /// <summary>
        /// 本月新增財產數量
        /// </summary>
        public int ThisMonthNewAssets { get; set; }

        /// <summary>
        /// 待報廢財產數量
        /// </summary>
        public int PendingScrapAssets { get; set; }

        /// <summary>
        /// 維修中財產數量
        /// </summary>
        public int UnderMaintenanceAssets { get; set; }

        /// <summary>
        /// 閒置財產數量
        /// </summary>
        public int IdleAssets { get; set; }

        /// <summary>
        /// 報廢後堪用財產數量
        /// </summary>
        public int ReusableAfterScrapAssets { get; set; }

        /// <summary>
        /// 統計生成時間
        /// </summary>
        public string GeneratedTime { get; set; } = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    }

    /// <summary>
    /// 按部門統計 DTO
    /// </summary>
    public class AssetStatisticsByDepartmentDTO
    {
        /// <summary>
        /// 部門ID
        /// </summary>
        public string DepartmentId { get; set; } = "";

        /// <summary>
        /// 部門名稱
        /// </summary>
        public string DepartmentName { get; set; } = "";

        /// <summary>
        /// 財產數量
        /// </summary>
        public int AssetCount { get; set; }

        /// <summary>
        /// 財產總價值
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// 累計折舊金額
        /// </summary>
        public decimal TotalDepreciation { get; set; }

        /// <summary>
        /// 淨值
        /// </summary>
        public decimal NetValue { get; set; }

        /// <summary>
        /// 佔總資產比例 (%)
        /// </summary>
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// 按狀態統計 DTO
    /// </summary>
    public class AssetStatisticsByStatusDTO
    {
        /// <summary>
        /// 狀態ID
        /// </summary>
        public Guid StatusId { get; set; }

        /// <summary>
        /// 狀態名稱
        /// </summary>
        public string StatusName { get; set; } = "";

        /// <summary>
        /// 財產數量
        /// </summary>
        public int AssetCount { get; set; }

        /// <summary>
        /// 財產總價值
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// 佔總資產比例 (%)
        /// </summary>
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// 按科目統計 DTO
    /// </summary>
    public class AssetStatisticsByAccountDTO
    {
        /// <summary>
        /// 科目ID
        /// </summary>
        public Guid AccountId { get; set; }

        /// <summary>
        /// 科目編號
        /// </summary>
        public string AccountNo { get; set; } = "";

        /// <summary>
        /// 科目名稱
        /// </summary>
        public string AccountName { get; set; } = "";

        /// <summary>
        /// 財產數量
        /// </summary>
        public int AssetCount { get; set; }

        /// <summary>
        /// 財產總價值
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// 累計折舊金額
        /// </summary>
        public decimal TotalDepreciation { get; set; }

        /// <summary>
        /// 淨值
        /// </summary>
        public decimal NetValue { get; set; }

        /// <summary>
        /// 佔總資產比例 (%)
        /// </summary>
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// 按年度統計 DTO
    /// </summary>
    public class AssetStatisticsByYearDTO
    {
        /// <summary>
        /// 年度
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 新增財產數量
        /// </summary>
        public int NewAssets { get; set; }

        /// <summary>
        /// 新增財產價值
        /// </summary>
        public decimal NewAssetsValue { get; set; }

        /// <summary>
        /// 報廢財產數量
        /// </summary>
        public int ScrappedAssets { get; set; }

        /// <summary>
        /// 報廢財產價值
        /// </summary>
        public decimal ScrappedAssetsValue { get; set; }

        /// <summary>
        /// 年度折舊金額
        /// </summary>
        public decimal YearDepreciation { get; set; }

        /// <summary>
        /// 年底資產總數
        /// </summary>
        public int YearEndTotalAssets { get; set; }

        /// <summary>
        /// 年底資產總價值
        /// </summary>
        public decimal YearEndTotalValue { get; set; }
    }

    /// <summary>
    /// 按月度統計 DTO
    /// </summary>
    public class AssetStatisticsByMonthDTO
    {
        /// <summary>
        /// 年度
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 月份
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// 年月字串 (YYYY-MM)
        /// </summary>
        public string YearMonth { get; set; } = "";

        /// <summary>
        /// 新增財產數量
        /// </summary>
        public int NewAssets { get; set; }

        /// <summary>
        /// 新增財產價值
        /// </summary>
        public decimal NewAssetsValue { get; set; }

        /// <summary>
        /// 報廢財產數量
        /// </summary>
        public int ScrappedAssets { get; set; }

        /// <summary>
        /// 報廢財產價值
        /// </summary>
        public decimal ScrappedAssetsValue { get; set; }

        /// <summary>
        /// 月度折舊金額
        /// </summary>
        public decimal MonthDepreciation { get; set; }
    }

    /// <summary>
    /// 按廠牌統計 DTO
    /// </summary>
    public class AssetStatisticsByManufacturerDTO
    {
        /// <summary>
        /// 廠牌ID
        /// </summary>
        public Guid ManufacturerId { get; set; }

        /// <summary>
        /// 廠牌名稱
        /// </summary>
        public string ManufacturerName { get; set; } = "";

        /// <summary>
        /// 財產數量
        /// </summary>
        public int AssetCount { get; set; }

        /// <summary>
        /// 財產總價值
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// 佔總資產比例 (%)
        /// </summary>
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// 按設備類型統計 DTO
    /// </summary>
    public class AssetStatisticsByEquipmentTypeDTO
    {
        /// <summary>
        /// 設備類型ID
        /// </summary>
        public Guid EquipmentTypeId { get; set; }

        /// <summary>
        /// 設備類型名稱
        /// </summary>
        public string EquipmentTypeName { get; set; } = "";

        /// <summary>
        /// 財產數量
        /// </summary>
        public int AssetCount { get; set; }

        /// <summary>
        /// 財產總價值
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// 佔總資產比例 (%)
        /// </summary>
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// 按使用年限統計 DTO
    /// </summary>
    public class AssetStatisticsByAgeDTO
    {
        /// <summary>
        /// 年限範圍
        /// </summary>
        public string AgeRange { get; set; } = "";

        /// <summary>
        /// 財產數量
        /// </summary>
        public int AssetCount { get; set; }

        /// <summary>
        /// 財產總價值
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// 佔總資產比例 (%)
        /// </summary>
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// 折舊統計 DTO
    /// </summary>
    public class AssetDepreciationStatisticsDTO
    {
        /// <summary>
        /// 年度
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 月份
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// 年月字串 (YYYY-MM)
        /// </summary>
        public string YearMonth { get; set; } = "";

        /// <summary>
        /// 當期折舊金額
        /// </summary>
        public decimal CurrentDepreciation { get; set; }

        /// <summary>
        /// 累計折舊金額
        /// </summary>
        public decimal AccumulatedDepreciation { get; set; }

        /// <summary>
        /// 折舊資產數量
        /// </summary>
        public int DepreciatedAssetsCount { get; set; }
    }

    /// <summary>
    /// 財產價值區間統計 DTO
    /// </summary>
    public class AssetStatisticsByValueRangeDTO
    {
        /// <summary>
        /// 價值區間
        /// </summary>
        public string ValueRange { get; set; } = "";

        /// <summary>
        /// 最小值
        /// </summary>
        public decimal MinValue { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public decimal MaxValue { get; set; }

        /// <summary>
        /// 財產數量
        /// </summary>
        public int AssetCount { get; set; }

        /// <summary>
        /// 財產總價值
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// 佔總資產比例 (%)
        /// </summary>
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// 完整財產統計結果 DTO
    /// </summary>
    public class AssetStatisticsResultDTO
    {
        /// <summary>
        /// 統計總覽
        /// </summary>
        public AssetStatisticsOverviewDTO Overview { get; set; } = new();

        /// <summary>
        /// 按部門統計
        /// </summary>
        public List<AssetStatisticsByDepartmentDTO> ByDepartment { get; set; } = new();

        /// <summary>
        /// 按狀態統計
        /// </summary>
        public List<AssetStatisticsByStatusDTO> ByStatus { get; set; } = new();

        /// <summary>
        /// 按科目統計
        /// </summary>
        public List<AssetStatisticsByAccountDTO> ByAccount { get; set; } = new();

        /// <summary>
        /// 按年度統計
        /// </summary>
        public List<AssetStatisticsByYearDTO> ByYear { get; set; } = new();

        /// <summary>
        /// 按月度統計
        /// </summary>
        public List<AssetStatisticsByMonthDTO> ByMonth { get; set; } = new();

        /// <summary>
        /// 按廠牌統計
        /// </summary>
        public List<AssetStatisticsByManufacturerDTO> ByManufacturer { get; set; } = new();

        /// <summary>
        /// 按設備類型統計
        /// </summary>
        public List<AssetStatisticsByEquipmentTypeDTO> ByEquipmentType { get; set; } = new();

        /// <summary>
        /// 按使用年限統計
        /// </summary>
        public List<AssetStatisticsByAgeDTO> ByAge { get; set; } = new();

        /// <summary>
        /// 折舊統計
        /// </summary>
        public List<AssetDepreciationStatisticsDTO> DepreciationStatistics { get; set; } = new();

        /// <summary>
        /// 按價值區間統計
        /// </summary>
        public List<AssetStatisticsByValueRangeDTO> ByValueRange { get; set; } = new();

        /// <summary>
        /// 統計生成時間
        /// </summary>
        public string GeneratedTime { get; set; } = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    }

    /// <summary>
    /// 財產統計查詢參數 DTO
    /// </summary>
    public class AssetStatisticsRequestDTO
    {
        /// <summary>
        /// 開始日期 (YYYY-MM-DD)
        /// </summary>
        public string? StartDate { get; set; }

        /// <summary>
        /// 結束日期 (YYYY-MM-DD)
        /// </summary>
        public string? EndDate { get; set; }

        /// <summary>
        /// 部門ID篩選
        /// </summary>
        public List<string>? DepartmentIds { get; set; }

        /// <summary>
        /// 狀態ID篩選
        /// </summary>
        public List<Guid>? StatusIds { get; set; }

        /// <summary>
        /// 科目ID篩選
        /// </summary>
        public List<Guid>? AccountIds { get; set; }

        /// <summary>
        /// 是否包含已刪除資產
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;

        /// <summary>
        /// 統計類型
        /// </summary>
        public List<string>? StatisticTypes { get; set; }
    }
}

