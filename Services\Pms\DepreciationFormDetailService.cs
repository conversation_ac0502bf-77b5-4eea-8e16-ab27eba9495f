﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models.Pms.Enums;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using FAST_ERP_Backend.Interfaces.Common;
using AutoMapper;

namespace FAST_ERP_Backend.Services.Pms
{
    public class DepreciationFormDetailService : IDepreciationFormDetailService
    {
        private readonly ERPDbContext _context;
        private readonly IPmsSystemParameterService _systemParameterService;
        private readonly IParameterValueService _parameterValueService;
        private readonly IMapper _mapper;

        public DepreciationFormDetailService(ERPDbContext context, IPmsSystemParameterService systemParameterService,
            IParameterValueService parameterValueService, IMapper mapper)
        {
            _context = context;
            _systemParameterService = systemParameterService;
            _parameterValueService = parameterValueService;
            _mapper = mapper;
        }

        /// <summary>
        /// 取得折舊紀錄資料
        /// </summary>
        /// <returns></returns>
        public async Task<List<DepreciationFormDetailDTO>> GetDepreciationAsync()
        {
            var query = from d in _context.Set<DepreciationFormDetail>().Where(d => !d.IsDeleted)
                        join a in _context.Pms_Assets on d.AssetId equals a.AssetId into assetGroup
                        from asset in assetGroup.DefaultIfEmpty()

                        join aa in _context.Pms_AssetAccounts on asset.AssetAccountId equals aa.AssetAccountId into accountGroup
                        from account in accountGroup.DefaultIfEmpty()
                        select new
                        {
                            Depreciation = d,
                            AssetNo = asset != null ? asset.AssetNo : "",
                            AssetName = asset != null ? asset.AssetName : "",
                            AssetAccountNo = account != null ? account.AssetAccountNo : ""
                        };

            var result = await query
                .OrderBy(item => item.Depreciation.DepreciationYear)
                .ThenBy(item => item.Depreciation.DepreciationMonth)
                .ThenBy(item => item.AssetAccountNo)
                .ToListAsync();

            // 批次查詢建立者名稱，避免 N+1 查詢
            var createUserIds = result
                .Where(item => !string.IsNullOrEmpty(item.Depreciation.CreateUserId))
                .Select(item => item.Depreciation.CreateUserId)
                .Distinct()
                .ToList();

            var createUserDict = await _context.Set<Users>()
                .AsNoTracking()
                .Where(u => createUserIds.Contains(u.UserId))
                .Select(u => new { u.UserId, u.Name })
                .ToDictionaryAsync(x => x.UserId, x => x.Name);

            return result.Select(item =>
            {
                var dto = _mapper.Map<DepreciationFormDetailDTO>(item.Depreciation);
                dto.AssetNo = item.AssetNo;
                dto.AssetName = item.AssetName;

                // 使用批次查詢結果設定建立者名稱
                if (!string.IsNullOrEmpty(item.Depreciation.CreateUserId) &&
                    createUserDict.TryGetValue(item.Depreciation.CreateUserId, out var createUserName))
                {
                    dto.CreateUserName = createUserName ?? "";
                }
                else
                {
                    dto.CreateUserName = "";
                }

                return dto;
            }).ToList();
        }

        /// <summary>
        /// 依年月取得折舊紀錄資料
        /// </summary>
        /// <param name="year">西元年(可選)</param>
        /// <param name="month">月份(可選)</param>
        /// <param name="hasCreateDepreciationFormDate">是否有新增折舊單日期(可選): true=有日期, false=無日期, null=全部</param>
        /// <returns></returns>
        public async Task<List<DepreciationFormDetailDTO>> GetDepreciationByYearMonthAsync(int? year = null, int? month = null, bool? hasCreateDepreciationFormDate = null)
        {
            var query = from d in _context.Set<DepreciationFormDetail>().Where(d => !d.IsDeleted)
                        join a in _context.Pms_Assets on d.AssetId equals a.AssetId into assetGroup
                        from asset in assetGroup.DefaultIfEmpty()
                        join aa in _context.Pms_AssetAccounts on asset.AssetAccountId equals aa.AssetAccountId into accountGroup
                        from account in accountGroup.DefaultIfEmpty()
                        select new
                        {
                            Depreciation = d,
                            AssetNo = asset != null ? asset.AssetNo : "",
                            AssetName = asset != null ? asset.AssetName : "",
                            AssetAccountNo = account != null ? account.AssetAccountNo : ""
                        };

            // 加入年份篩選條件
            if (year.HasValue)
            {
                query = query.Where(item => item.Depreciation.DepreciationYear == year.Value);
            }

            // 加入月份篩選條件
            if (month.HasValue)
            {
                query = query.Where(item => item.Depreciation.DepreciationMonth == month.Value);
            }

            // 加入CreateDepreciationFormDate篩選條件
            if (hasCreateDepreciationFormDate.HasValue)
            {
                if (hasCreateDepreciationFormDate.Value)
                {
                    // 篩選有CreateDepreciationFormDate的記錄 (大於0)
                    query = query.Where(item => item.Depreciation.CreateDepreciationFormDate > 0);
                }
                else
                {
                    // 篩選沒有CreateDepreciationFormDate的記錄 (等於0)
                    query = query.Where(item => item.Depreciation.CreateDepreciationFormDate == 0);
                }
            }

            var result = await query
                .OrderBy(item => item.Depreciation.DepreciationYear)
                .ThenBy(item => item.Depreciation.DepreciationMonth)
                .ThenBy(item => item.AssetAccountNo)
                .ToListAsync();

            // 批次查詢建立者名稱，避免 N+1 查詢
            var createUserIds = result
                .Where(item => !string.IsNullOrEmpty(item.Depreciation.CreateUserId))
                .Select(item => item.Depreciation.CreateUserId)
                .Distinct()
                .ToList();

            var createUserDict = await _context.Set<Users>()
                .AsNoTracking()
                .Where(u => createUserIds.Contains(u.UserId))
                .Select(u => new { u.UserId, u.Name })
                .ToDictionaryAsync(x => x.UserId, x => x.Name);

            return result.Select(item =>
            {
                var dto = _mapper.Map<DepreciationFormDetailDTO>(item.Depreciation);
                dto.AssetNo = item.AssetNo;
                dto.AssetName = item.AssetName;

                // 使用批次查詢結果設定建立者名稱
                if (!string.IsNullOrEmpty(item.Depreciation.CreateUserId) &&
                    createUserDict.TryGetValue(item.Depreciation.CreateUserId, out var createUserName))
                {
                    dto.CreateUserName = createUserName ?? "";
                }
                else
                {
                    dto.CreateUserName = "";
                }

                return dto;
            }).ToList();
        }

        /// <summary>
        /// 新增折舊紀錄
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddDepreciationAsync(DepreciationFormDetailDTO _data)
        {
            try
            {
                // 檢查累計折舊金額是否不為0
                if (_data.AccumulatedDepreciation > 0)
                {
                    // 取得財產資訊以計算正確的期初帳面價值
                    var asset = await _context.Pms_Assets
                        .FirstOrDefaultAsync(a => a.AssetId == _data.AssetId && !a.IsDeleted);

                    if (asset == null)
                    {
                        return (false, "找不到對應的財產資料");
                    }

                    // 計算正確的期初帳面價值
                    // 期初帳面價值 = 原始金額 - 累計折舊 + 本期折舊
                    _data.BeginningBookValue = _data.OriginalAmount - (_data.AccumulatedDepreciation - _data.CurrentDepreciation);

                    // 期末帳面價值 = 原始金額 - 累計折舊
                    _data.EndingBookValue = _data.OriginalAmount - _data.AccumulatedDepreciation;

                    // 檢查期末帳面價值是否已低於或等於殘值
                    if (_data.EndingBookValue <= asset.EstimatedResidualValue)
                    {
                        return (false, $"期末帳面價值 {_data.EndingBookValue:C} 已低於或等於殘值 {asset.EstimatedResidualValue:C}，無法新增折舊紀錄");
                    }

                    // 檢查是否為調整紀錄
                    if (!_data.IsAdjustment)
                    {
                        _data.IsAdjustment = true;
                        _data.AdjustmentReason = "累計折舊金額不為0，設為本期期初";
                    }

                    // 更新備註說明
                    var adjustmentNote = $"[累計折舊調整] 累計折舊金額: {_data.AccumulatedDepreciation:C}, 設為本期期初，折舊紀錄由下期開始攤提";
                    _data.Notes = string.IsNullOrEmpty(_data.Notes) ? adjustmentNote : $"{_data.Notes}; {adjustmentNote}";

                    Console.WriteLine($"累計折舊金額不為0，調整期初帳面價值: {_data.BeginningBookValue:C}, 期末帳面價值: {_data.EndingBookValue:C}");
                }
                else
                {
                    // 累計折舊金額為0時，正常計算期初和期末帳面價值
                    _data.BeginningBookValue = _data.OriginalAmount;
                    _data.EndingBookValue = _data.OriginalAmount - _data.CurrentDepreciation;

                    // 取得財產資訊以檢查殘值
                    var asset = await _context.Pms_Assets
                        .FirstOrDefaultAsync(a => a.AssetId == _data.AssetId && !a.IsDeleted);

                    if (asset != null)
                    {
                        // 檢查期末帳面價值是否已低於或等於殘值
                        if (_data.EndingBookValue <= asset.EstimatedResidualValue)
                        {
                            return (false, $"期末帳面價值 {_data.EndingBookValue:C} 已低於或等於殘值 {asset.EstimatedResidualValue:C}，無法新增折舊紀錄");
                        }
                    }
                }

                var entity = _mapper.Map<DepreciationFormDetail>(_data);
                entity.DepreciationId = string.IsNullOrEmpty(_data.DepreciationId) ? Guid.NewGuid() : Guid.Parse(_data.DepreciationId);
                entity.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.IsDeleted = false;

                // 如果沒有設定 CreateDepreciationFormDate，則設為0
                if (_data.CreateDepreciationFormDate == 0)
                {
                    entity.CreateDepreciationFormDate = 0;
                }

                // 確保期初和期末帳面價值的準確性
                entity.BeginningBookValue = _data.BeginningBookValue;
                entity.EndingBookValue = _data.EndingBookValue;

                _context.Add(entity);
                await _context.SaveChangesAsync();

                Console.WriteLine($"新增折舊紀錄成功: {entity.DepreciationId}, 期初帳面價值: {entity.BeginningBookValue:C}, 期末帳面價值: {entity.EndingBookValue:C}");
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"新增折舊紀錄失敗: {ex.Message}");
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯折舊紀錄
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditDepreciationAsync(DepreciationFormDetailDTO _data)
        {
            try
            {
                var entity = await _context.Set<DepreciationFormDetail>().FindAsync(Guid.Parse(_data.DepreciationId));
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                // 檢查累計折舊金額是否不為0
                if (_data.AccumulatedDepreciation > 0)
                {
                    // 取得財產資訊以計算正確的期初帳面價值
                    var asset = await _context.Pms_Assets
                        .FirstOrDefaultAsync(a => a.AssetId == _data.AssetId && !a.IsDeleted);

                    if (asset == null)
                    {
                        return (false, "找不到對應的財產資料");
                    }

                    // 計算正確的期初帳面價值
                    // 期初帳面價值 = 原始金額 - 累計折舊 + 本期折舊
                    _data.BeginningBookValue = _data.OriginalAmount - (_data.AccumulatedDepreciation - _data.CurrentDepreciation);

                    // 期末帳面價值 = 原始金額 - 累計折舊
                    _data.EndingBookValue = _data.OriginalAmount - _data.AccumulatedDepreciation;

                    // 檢查期末帳面價值是否已低於或等於殘值
                    if (_data.EndingBookValue <= asset.EstimatedResidualValue)
                    {
                        return (false, $"期末帳面價值 {_data.EndingBookValue:C} 已低於或等於殘值 {asset.EstimatedResidualValue:C}，無法編輯折舊紀錄");
                    }

                    // 檢查是否為調整紀錄
                    if (!_data.IsAdjustment)
                    {
                        _data.IsAdjustment = true;
                        _data.AdjustmentReason = "累計折舊金額不為0，設為本期期初";
                    }

                    // 更新備註說明
                    var adjustmentNote = $"[累計折舊調整] 累計折舊金額: {_data.AccumulatedDepreciation:C}, 設為本期期初，折舊紀錄由下期開始攤提";
                    _data.Notes = string.IsNullOrEmpty(_data.Notes) ? adjustmentNote : $"{_data.Notes}; {adjustmentNote}";

                    Console.WriteLine($"編輯時累計折舊金額不為0，調整期初帳面價值: {_data.BeginningBookValue:C}, 期末帳面價值: {_data.EndingBookValue:C}");
                }
                else
                {
                    // 累計折舊金額為0時，正常計算期初和期末帳面價值
                    _data.BeginningBookValue = _data.OriginalAmount;
                    _data.EndingBookValue = _data.OriginalAmount - _data.CurrentDepreciation;
                }

                _mapper.Map(_data, entity);
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                // 確保期初和期末帳面價值的準確性
                entity.BeginningBookValue = _data.BeginningBookValue;
                entity.EndingBookValue = _data.EndingBookValue;

                _context.Update(entity);
                await _context.SaveChangesAsync();

                Console.WriteLine($"編輯折舊紀錄成功: {entity.DepreciationId}, 期初帳面價值: {entity.BeginningBookValue:C}, 期末帳面價值: {entity.EndingBookValue:C}");
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除折舊紀錄
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteDepreciationAsync(DepreciationFormDetailDTO _data)
        {
            try
            {
                var entity = await _context.Set<DepreciationFormDetail>().FindAsync(Guid.Parse(_data.DepreciationId));
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得折舊紀錄資料
        /// </summary>
        /// <param name="_assetId"></param>
        /// <returns></returns>
        public async Task<List<DepreciationFormDetailDTO>> GetDepreciationByAssetAsync(string _assetId)
        {
            if (!Guid.TryParse(_assetId, out Guid assetId))
            {
                return new List<DepreciationFormDetailDTO>();
            }

            var query = from d in _context.Set<DepreciationFormDetail>().Where(d => d.AssetId == assetId && !d.IsDeleted)
                        join a in _context.Pms_Assets on d.AssetId equals a.AssetId into assetGroup
                        from asset in assetGroup.DefaultIfEmpty()
                        join aa in _context.Pms_AssetAccounts on asset.AssetAccountId equals aa.AssetAccountId into accountGroup
                        from account in accountGroup.DefaultIfEmpty()
                        select new
                        {
                            Depreciation = d,
                            AssetNo = asset != null ? asset.AssetNo : "",
                            AssetName = asset != null ? asset.AssetName : "",
                            AssetAccountNo = account != null ? account.AssetAccountNo : ""
                        };

            var result = await query
                .OrderBy(item => item.Depreciation.DepreciationYear)
                .ThenBy(item => item.Depreciation.DepreciationMonth)
                .ThenBy(item => item.AssetAccountNo)
                .ToListAsync();

            // 批次查詢建立者名稱，避免 N+1 查詢
            var createUserIds = result
                .Where(item => !string.IsNullOrEmpty(item.Depreciation.CreateUserId))
                .Select(item => item.Depreciation.CreateUserId)
                .Distinct()
                .ToList();

            var createUserDict = await _context.Set<Users>()
                .AsNoTracking()
                .Where(u => createUserIds.Contains(u.UserId))
                .Select(u => new { u.UserId, u.Name })
                .ToDictionaryAsync(x => x.UserId, x => x.Name);

            return result.Select(item =>
            {
                var dto = _mapper.Map<DepreciationFormDetailDTO>(item.Depreciation);
                dto.AssetNo = item.AssetNo;
                dto.AssetName = item.AssetName;

                // 使用批次查詢結果設定建立者名稱
                if (!string.IsNullOrEmpty(item.Depreciation.CreateUserId) &&
                    createUserDict.TryGetValue(item.Depreciation.CreateUserId, out var createUserName))
                {
                    dto.CreateUserName = createUserName ?? "";
                }
                else
                {
                    dto.CreateUserName = "";
                }

                return dto;
            }).ToList();
        }

        /// <summary>
        /// 取得可用折舊方法
        /// </summary>
        public async Task<(bool success, List<object> methods, string message)> GetAvailableDepreciationMethodsAsync()
        {
            try
            {
                var methods = await _systemParameterService.GetDepreciationMethodsAsync();
                if (methods == null || !methods.Any())
                {
                    return (false, null, "找不到可用的折舊方法");
                }

                var result = new List<object>();
                foreach (var method in methods)
                {
                    try
                    {
                        var jObject = JObject.Parse(method.ParameterValue);
                        var methodInfo = new
                        {
                            MethodId = method.ParameterId.ToString(),
                            MethodName = method.ParameterName,
                            MethodKey = jObject["key"]?.ToString(),
                            IsDefault = jObject["isDefault"]?.ToObject<bool>() ?? false,
                            Description = method.ParameterDescription
                        };
                        result.Add(methodInfo);
                    }
                    catch
                    {
                        // 忽略無法解析的方法
                    }
                }

                return (true, result, "成功取得折舊方法");
            }
            catch (Exception ex)
            {
                return (false, null, $"取得折舊方法失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得指定財產科目餘額遞減法折舊率
        /// </summary>
        public async Task<(bool success, decimal rate, string message)> GetDecliningBalanceRateForAssetAccountAsync(string assetAccountId)
        {
            if (string.IsNullOrEmpty(assetAccountId))
            {
                return (false, 0, "財產科目ID不能為空");
            }

            return await _systemParameterService.GetDecliningBalanceRateForAssetAccountAsync(assetAccountId);
        }

        /// <summary>
        /// 取得預設折舊方法
        /// </summary>
        /// <param name="depreciationMethods">折舊方法列表</param>
        /// <param name="assetStatus">財產狀態</param>
        /// <param name="assetAccountId">財產科目ID</param>
        /// <returns>(折舊方法代碼, 折舊方法名稱)</returns>
        private async Task<(string methodKey, string methodName)> GetDefaultDepreciationMethod(List<PmsSystemParameterDTO> depreciationMethods, string assetStatus = null, string assetAccountId = null)
        {
            // 預設直線法
            string depreciationMethodKey = "straight_line";
            string depreciationMethodName = "直線法";

            // 取得預設折舊方法
            var defaultMethod = depreciationMethods.FirstOrDefault(m =>
                _parameterValueService.ContainsProperty(m.ParameterValue, "isDefault") &&
                _parameterValueService.GetValueFromJson<bool>(m.ParameterValue, "isDefault", false));

            if (defaultMethod != null)
            {
                try
                {
                    depreciationMethodKey = _parameterValueService.GetValueFromJson<string>(defaultMethod.ParameterValue, "key", "straight_line");
                    depreciationMethodName = defaultMethod.ParameterName ?? "直線法";
                }
                catch
                {
                    // 使用預設方法
                    Console.WriteLine("解析預設折舊方法失敗，使用直線法");
                }
            }

            Console.WriteLine($"預設折舊方法: {depreciationMethodName}-{depreciationMethodKey}");

            // 檢查是否使用"依財產科目設定折舊法"
            var hybridMethod = depreciationMethods.FirstOrDefault(m =>
                _parameterValueService.ContainsProperty(m.ParameterValue, "key") &&
                _parameterValueService.GetValueFromJson<string>(m.ParameterValue, "key", "") == "hybrid");

            if (hybridMethod != null && !string.IsNullOrEmpty(assetAccountId))
            {
                try
                {
                    // 取得財產科目的折舊方法設定
                    var (success, methodId, methodName, message) = await _systemParameterService.GetAssetAccountDepreciationMethodAsync(assetAccountId);

                    if (success && !string.IsNullOrEmpty(methodId))
                    {
                        // 根據方法ID找到對應的折舊方法
                        var accountMethod = depreciationMethods.FirstOrDefault(m => m.ParameterId.ToString() == methodId);
                        if (accountMethod != null)
                        {
                            depreciationMethodKey = _parameterValueService.GetValueFromJson<string>(accountMethod.ParameterValue, "key", depreciationMethodKey);
                            depreciationMethodName = accountMethod.ParameterName ?? depreciationMethodName;
                            Console.WriteLine($"財產科目折舊方法: {depreciationMethodName}-{depreciationMethodKey}");
                            return (depreciationMethodKey, depreciationMethodName);
                        }
                    }
                    else
                    {
                        Console.WriteLine($"財產科目折舊方法設定失敗: {message}，使用預設方法");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"解析財產科目折舊方法失敗: {ex.Message}，使用預設方法");
                }
            }

            // 如果財產已指定折舊方法，則使用該方法
            if (!string.IsNullOrEmpty(assetStatus))
            {
                var assetMethod = depreciationMethods.FirstOrDefault(m =>
                    _parameterValueService.ContainsProperty(m.ParameterValue, "key") &&
                    _parameterValueService.GetValueFromJson<string>(m.ParameterValue, "key", "") == assetStatus);

                if (assetMethod != null)
                {
                    try
                    {
                        depreciationMethodKey = _parameterValueService.GetValueFromJson<string>(assetMethod.ParameterValue, "key", depreciationMethodKey);
                        depreciationMethodName = assetMethod.ParameterName ?? depreciationMethodName;
                    }
                    catch
                    {
                        // 保持原有方法
                        Console.WriteLine($"解析財產指定折舊方法失敗，使用{depreciationMethodName}");
                    }
                }
            }

            return (depreciationMethodKey, depreciationMethodName);
        }

        /// <summary>
        /// 產生整期折舊紀錄
        /// </summary>
        /// <param name="assetId">財產編號</param>
        /// <param name="userId">使用者編號</param>
        /// <returns>成功與否、訊息</returns>
        public async Task<(bool success, string message)> GenerateDepreciationScheduleAsync(string assetId, string userId)
        {
            try
            {
                if (!Guid.TryParse(assetId, out Guid parsedAssetId))
                {
                    return (false, "財產編號無效");
                }

                // 取得財產資訊
                var asset = await _context.Pms_Assets
                    .FirstOrDefaultAsync(a => a.AssetId == parsedAssetId && !a.IsDeleted);

                if (asset == null)
                {
                    return (false, "找不到指定的財產");
                }

                // 檢查是否為不計算折舊的科目 (1: 土地, 9: 未完工程)
                if (asset.AssetAccountId != null)
                {
                    var assetAccount = await _context.Pms_AssetAccounts
                        .FirstOrDefaultAsync(a => a.AssetAccountId == asset.AssetAccountId);

                    if (assetAccount != null && (assetAccount.AssetAccountNo == "1" || assetAccount.AssetAccountNo == "9"))
                    {
                        return (true, $"科目為「{assetAccount.AssetAccountName}」的財產不需計算折舊");
                    }
                }

                // 檢查財產是否已報廢
                if (asset.ScrapDate > 0)
                {
                    var message = asset.ScrapDate > 0 ? "該財產已報廢，無法計算折舊" : "該財產已預備報廢，無法計算折舊";
                    return (false, message);
                }

                // 檢查財產是否已捐贈
                if (asset.DonatedDate > 0)
                {
                    var message = asset.DonatedDate > 0 ? "該財產已捐贈，無法計算折舊" : "該財產已預備捐贈，無法計算折舊";
                    return (false, message);
                }

                // 檢查財產是否已出售
                if (asset.SoldDate > 0)
                {
                    var message = asset.SoldDate > 0 ? "該財產已出售，無法計算折舊" : "該財產已預備出售，無法計算折舊";
                    return (false, message);
                }

                // 檢查使用年限
                if (asset.ServiceLife <= 0)
                {
                    return (false, "財產使用年限必須大於0");
                }

                // 取得起始計算日期 (從取得日期開始)
                var startDate = asset.AcquisitionDate > 0 ?
                    DateTimeOffset.FromUnixTimeSeconds(asset.AcquisitionDate) :
                    DateTimeOffset.UtcNow;

                // 計算折舊結束期別：取得日期 + 耐用年限
                var acquisitionDate = DateTimeOffset.FromUnixTimeSeconds(asset.AcquisitionDate);
                var endDate = acquisitionDate.AddYears(asset.ServiceLife);
                Console.WriteLine($"折舊結束期別: {acquisitionDate:yyyy/MM} + {asset.ServiceLife}年 = {endDate:yyyy/MM}");

                // 取得預設折舊方法
                var depreciationMethods = await _systemParameterService.GetDepreciationMethodsAsync();
                (string depreciationMethodKey, string depreciationMethodName) = await GetDefaultDepreciationMethod(
                    depreciationMethods,
                    asset.AssetStatusId.ToString(),
                    asset.AssetAccountId.ToString());

                // 計算每期折舊記錄
                var deprecationRecords = new List<DepreciationFormDetailDTO>();//折舊紀錄清單
                decimal originalAmount = asset.DepreciationAmount; // 折舊金額
                decimal salvageValue = asset.EstimatedResidualValue; // 殘值
                decimal accumulatedDepreciation = asset.AccumulatedDepreciationAmount; // 累計折舊（從財產主檔取得）
                decimal remainingValue = originalAmount - accumulatedDepreciation; // 剩餘價值
                int remainingLifeYears = asset.ServiceLife; // 剩餘使用年限
                decimal depreciationRate = 0; // 折舊率
                decimal beginningBookValue = originalAmount - accumulatedDepreciation; // 期初帳面價值
                decimal endingBookValue = originalAmount - accumulatedDepreciation; // 期末帳面價值

                // 檢查是否已有累計折舊金額
                if (accumulatedDepreciation > 0)
                {
                    Console.WriteLine($"財產 {asset.AssetNo} 已有累計折舊金額: {accumulatedDepreciation:C}，從該金額開始計算");

                    // 以建檔月份的次月為起算月（CreateTime->UpdateTime->AcquisitionDate）
                    long baseTimestamp = (asset.CreateTime ?? 0) > 0 ? (asset.CreateTime ?? 0)
                                       : (asset.UpdateTime ?? 0) > 0 ? (asset.UpdateTime ?? 0)
                                       : asset.AcquisitionDate;

                    var baseDate = DateTimeOffset.FromUnixTimeSeconds(baseTimestamp);
                    // 對齊到該月1號後再加一個月
                    var firstDayOfMonth = new DateTimeOffset(baseDate.Year, baseDate.Month, 1, 0, 0, 0, TimeSpan.Zero);
                    startDate = firstDayOfMonth.AddMonths(0);

                    string timeSourceDesc = (asset.UpdateTime ?? 0) > 0 ? "更新時間"
                                          : (asset.CreateTime ?? 0) > 0 ? "建立時間"
                                          : "取得日期";

                    Console.WriteLine($"情境二：有累計折舊金額，使用{timeSourceDesc}({baseDate:yyyy/MM})的次月開始產生折舊紀錄: {startDate:yyyy/MM}，計算到結束期別: {endDate:yyyy/MM}");
                }
                else
                {
                    // 情境一：從取得日期開始
                    startDate = acquisitionDate;
                    Console.WriteLine($"情境一：新建財產，從取得日期開始產生折舊紀錄: {startDate:yyyy/MM}，計算到結束期別: {endDate:yyyy/MM}");
                }

                // 依折舊方法計算
                if (depreciationMethodKey == "straight_line") // 直線法
                {
                    // 年折舊 = (原始成本 - 殘值) / 使用年限
                    decimal yearlyDepreciation = (originalAmount - salvageValue) / asset.ServiceLife;

                    // 月折舊 = 年折舊 / 12
                    decimal monthlyDepreciation = Math.Round(yearlyDepreciation / 12, 2);

                    // 生成每期折舊記錄 - 從 startDate 到 endDate
                    var currentDate = startDate;
                    int year = 0;

                    while (currentDate < endDate)
                    {
                        // 當年度累計（用於最後一月差額調整，確保年度合計＝年額）
                        decimal currentYearAccumulated = 0m;

                        for (int month = 1; month <= 12; month++)
                        {
                            // 計算日期 - 簡化邏輯
                            var depreciationDate = startDate.AddYears(year).AddMonths(month - 1);

                            // 檢查是否超過結束日期
                            if (depreciationDate >= endDate)
                            {
                                Console.WriteLine($"已達到折舊結束期別 {endDate:yyyy/MM}，停止產生後續折舊紀錄");
                                break;
                            }

                            // 本期折舊（預設為每月均攤）
                            decimal currentDepreciation = monthlyDepreciation;

                            // 若為每年度最後一月，改採年度差額（確保年度合計＝年額）
                            if (month == 12)
                            {
                                var yearRemainder = yearlyDepreciation - currentYearAccumulated;
                                currentDepreciation = Math.Round(yearRemainder, 2);
                                if (currentDepreciation < 0) currentDepreciation = 0;
                            }

                            // 防止剩餘價值低於殘值
                            if (remainingValue - currentDepreciation < salvageValue)
                            {
                                // 本期折舊=剩餘價值-殘值
                                decimal adjustedDepreciation = remainingValue - salvageValue;
                                // 防止本期折舊小於0
                                if (adjustedDepreciation < 0) adjustedDepreciation = 0;

                                // 如果是12月且有調整，需要同步調整年度累計
                                if (month == 12)
                                {
                                    // 重新計算當年度累計，確保不會產生負數
                                    var originalCurrentDepreciation = currentDepreciation;
                                    currentDepreciation = adjustedDepreciation;
                                    // 調整年度累計
                                    currentYearAccumulated += (currentDepreciation - originalCurrentDepreciation);
                                }
                                else
                                {
                                    currentDepreciation = adjustedDepreciation;
                                }
                            }

                            // 檢查是否已達到殘值，如果是則停止產生折舊紀錄
                            if (remainingValue <= salvageValue)
                            {
                                Console.WriteLine($"財產 {asset.AssetNo} 已達到殘值 {salvageValue:C}，停止產生後續折舊紀錄");
                                break; // 跳出月份迴圈
                            }

                            // 更新計算值
                            accumulatedDepreciation += currentDepreciation;
                            remainingValue -= currentDepreciation;
                            currentYearAccumulated += currentDepreciation;

                            // 期初帳面價值=原始金額-累計折舊+本期折舊
                            beginningBookValue = originalAmount - (accumulatedDepreciation - currentDepreciation);
                            // 期末帳面價值=原始金額-累計折舊
                            endingBookValue = originalAmount - accumulatedDepreciation;

                            // 更新剩餘年限
                            if (month == 12)
                            {
                                remainingLifeYears--;
                            }

                            // 前期累計折舊（折舊前）
                            var priorAccumulatedDepreciation = accumulatedDepreciation - currentDepreciation;

                            // 建立折舊記錄
                            var depreciation = new DepreciationFormDetailDTO
                            {
                                DepreciationId = Guid.NewGuid().ToString(),//折舊紀錄編號
                                AssetId = parsedAssetId,//財產編號
                                AssetNo = asset.AssetNo,//財產編號
                                AssetName = asset.AssetName,//財產名稱
                                DepreciationYear = depreciationDate.Year,//折舊年度
                                DepreciationMonth = depreciationDate.Month,//折舊月份
                                OriginalAmount = originalAmount,//原始金額
                                PriorAccumulatedDepreciation = priorAccumulatedDepreciation,//前期累計折舊
                                AccumulatedDepreciation = accumulatedDepreciation,//累計折舊
                                CurrentDepreciation = currentDepreciation,//本期折舊
                                DepreciationRate = 0, // 直線法無折舊率
                                DepreciationMethod = depreciationMethodName,//折舊方法
                                ServiceLifeRemaining = remainingLifeYears,//剩餘年限
                                BeginningBookValue = beginningBookValue,//期初帳面價值
                                EndingBookValue = endingBookValue,//期末帳面價值
                                DepreciationDate = 0,//折舊日期
                                CreateUserId = userId,//建立者
                                CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds()//建立時間
                            };

                            deprecationRecords.Add(depreciation);
                        }

                        // 檢查是否已達到殘值，如果是則停止產生後續年度折舊紀錄
                        if (remainingValue <= salvageValue)
                        {
                            Console.WriteLine($"財產 {asset.AssetNo} 已達到殘值 {salvageValue:C}，停止產生後續折舊紀錄");
                            break; // 跳出年度迴圈
                        }

                        // 更新到下一年度
                        year++;
                        currentDate = currentDate.AddYears(1);
                    }
                }
                else if (depreciationMethodKey == "declining_balance") // 餘額遞減法
                {
                    // 取得財產科目的遞減法折舊率
                    bool rateSuccess;
                    decimal rateValue;
                    string rateMessage;
                    (rateSuccess, rateValue, rateMessage) = await _systemParameterService.GetDecliningBalanceRateForAssetAccountAsync(
                        asset.AssetAccountId.ToString());

                    if (!rateSuccess)
                    {
                        // 使用預設折舊率
                        rateValue = 10; // 預設10%
                    }

                    // 折舊率轉為小數 (10% -> 0.1)
                    depreciationRate = rateValue / 100;

                    Console.WriteLine($"折舊率: {depreciationRate}");

                    // 生成每期折舊記錄 - 從 startDate 到 endDate
                    var currentDate = startDate;
                    int year = 0;

                    while (currentDate < endDate)
                    {
                        // 餘額遞減法：每年重新計算當年度的期初帳面價值
                        decimal currentYearBeginningBookValue = originalAmount - accumulatedDepreciation;

                        // 年折舊 = 期初帳面價值 × 折舊率
                        decimal yearlyDepreciation = currentYearBeginningBookValue * depreciationRate;

                        // 月折舊 = 年折舊 / 12
                        decimal monthlyDepreciation = Math.Round(yearlyDepreciation / 12, 2);

                        // 當年度累計（用於最後一月差額調整，確保年度合計＝年額）
                        decimal currentYearAccumulated = 0m;

                        for (int month = 1; month <= 12; month++)
                        {
                            // 計算日期 - 簡化邏輯
                            var depreciationDate = startDate.AddYears(year).AddMonths(month - 1);

                            // 檢查是否超過結束日期
                            if (depreciationDate >= endDate)
                            {
                                Console.WriteLine($"已達到折舊結束期別 {endDate:yyyy/MM}，停止產生後續折舊紀錄");
                                break;
                            }

                            // 本期折舊（預設為每月均攤）
                            decimal currentDepreciation = monthlyDepreciation;

                            // 若為每年度最後一月，改採年度差額（確保年度合計＝年額）
                            if (month == 12)
                            {
                                var yearRemainder = yearlyDepreciation - currentYearAccumulated;
                                currentDepreciation = Math.Round(yearRemainder, 2);
                                if (currentDepreciation < 0) currentDepreciation = 0;
                            }

                            // 防止剩餘價值低於殘值
                            if (remainingValue - currentDepreciation < salvageValue)
                            {
                                decimal adjustedDepreciation = remainingValue - salvageValue;
                                if (adjustedDepreciation < 0) adjustedDepreciation = 0;

                                // 如果是12月且有調整，需要同步調整年度累計
                                if (month == 12)
                                {
                                    var originalCurrentDepreciation = currentDepreciation;
                                    currentDepreciation = adjustedDepreciation;
                                    currentYearAccumulated += (currentDepreciation - originalCurrentDepreciation);
                                }
                                else
                                {
                                    currentDepreciation = adjustedDepreciation;
                                }
                            }

                            // 檢查是否已達到殘值，如果是則停止產生折舊紀錄
                            if (remainingValue <= salvageValue)
                            {
                                Console.WriteLine($"財產 {asset.AssetNo} 已達到殘值 {salvageValue:C}，停止產生後續折舊紀錄");
                                break; // 跳出月份迴圈
                            }

                            // 更新計算值
                            accumulatedDepreciation += currentDepreciation;
                            remainingValue -= currentDepreciation;
                            currentYearAccumulated += currentDepreciation;

                            // 期初帳面價值=原始金額-累計折舊+本期折舊
                            beginningBookValue = originalAmount - (accumulatedDepreciation - currentDepreciation);
                            // 期末帳面價值=原始金額-累計折舊
                            endingBookValue = originalAmount - accumulatedDepreciation;

                            // 更新剩餘年限
                            if (month == 12)
                            {
                                remainingLifeYears--;
                            }

                            // 前期累計折舊（折舊前）
                            var priorAccumulatedDepreciation = accumulatedDepreciation - currentDepreciation;

                            // 建立折舊記錄
                            var depreciation = new DepreciationFormDetailDTO
                            {
                                DepreciationId = Guid.NewGuid().ToString(),//折舊紀錄編號
                                AssetId = parsedAssetId,//財產編號
                                AssetNo = asset.AssetNo,//財產編號
                                AssetName = asset.AssetName,//財產名稱
                                DepreciationYear = depreciationDate.Year,//折舊年度
                                DepreciationMonth = depreciationDate.Month,//折舊月份
                                OriginalAmount = originalAmount,//原始金額
                                PriorAccumulatedDepreciation = priorAccumulatedDepreciation,//前期累計折舊
                                AccumulatedDepreciation = accumulatedDepreciation,//累計折舊
                                CurrentDepreciation = currentDepreciation,//本期折舊
                                DepreciationRate = depreciationRate,//折舊率
                                DepreciationMethod = depreciationMethodName,//折舊方法
                                ServiceLifeRemaining = remainingLifeYears,//剩餘年限
                                BeginningBookValue = beginningBookValue,//期初帳面價值
                                EndingBookValue = endingBookValue,//期末帳面價值
                                DepreciationDate = 0,//折舊日期
                                CreateUserId = userId,//建立者
                                CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds()//建立時間
                            };

                            deprecationRecords.Add(depreciation);
                        }

                        // 檢查是否已達到殘值，如果是則停止產生後續年度折舊紀錄
                        if (remainingValue <= salvageValue)
                        {
                            Console.WriteLine($"財產 {asset.AssetNo} 已達到殘值 {salvageValue:C}，停止產生後續折舊紀錄");
                            break; // 跳出年度迴圈
                        }

                        // 更新到下一年度
                        year++;
                        currentDate = currentDate.AddYears(1);
                    }
                }

                // 儲存折舊記錄
                foreach (var depreciation in deprecationRecords)
                {
                    var entity = _mapper.Map<DepreciationFormDetail>(depreciation);
                    entity.DepreciationId = Guid.Parse(depreciation.DepreciationId);
                    entity.IsAdjustment = false;
                    entity.AdjustmentReason = "";
                    entity.Notes = "系統自動產生";
                    entity.BeginningBookValue = depreciation.BeginningBookValue;
                    entity.EndingBookValue = depreciation.EndingBookValue;
                    entity.IsDeleted = false;

                    _context.Add(entity);
                }

                await _context.SaveChangesAsync();

                return (true, $"已成功產生 {deprecationRecords.Count} 筆折舊紀錄");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"產生折舊紀錄失敗: {ex.Message}");
                return (false, $"產生折舊紀錄失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 計算折舊
        /// </summary>
        /// <param name="depreciationIds">折舊紀錄編號清單</param>
        /// <param name="userId">使用者編號</param>
        /// <returns>成功與否、訊息</returns>
        public async Task<(bool success, List<string> messages)> CalculateDepreciationAsync(List<string> depreciationIds, string userId)
        {
            var messages = new List<string>();
            var successCount = 0;
            using (var transaction = await _context.Database.BeginTransactionAsync())
            {
                try
                {
                    foreach (var depId in depreciationIds)
                    {
                        if (!Guid.TryParse(depId, out Guid depreciationGuid))
                        {
                            messages.Add($"折舊紀錄ID格式錯誤: {depId}");
                            continue;
                        }
                        var depreciation = await _context.Set<DepreciationFormDetail>().FirstOrDefaultAsync(d => d.DepreciationId == depreciationGuid && !d.IsDeleted);
                        if (depreciation == null)
                        {
                            messages.Add($"找不到折舊紀錄: {depId}");
                            continue;
                        }
                        var asset = await _context.Pms_Assets.FirstOrDefaultAsync(a => a.AssetId == depreciation.AssetId && !a.IsDeleted);
                        if (asset == null)
                        {
                            messages.Add($"找不到對應財產主檔: {depreciation.AssetId}");
                            continue;
                        }

                        // 期初帳面價值
                        depreciation.BeginningBookValue = depreciation.OriginalAmount - (depreciation.AccumulatedDepreciation - depreciation.CurrentDepreciation);
                        // 期末帳面價值
                        depreciation.EndingBookValue = depreciation.OriginalAmount - depreciation.AccumulatedDepreciation;

                        // 檢查累計折舊金額是否合理
                        if (depreciation.AccumulatedDepreciation > 0)
                        {
                            // 驗證累計折舊金額不應超過原始金額減去殘值
                            var assetWithResidual = await _context.Pms_Assets
                                .FirstOrDefaultAsync(a => a.AssetId == depreciation.AssetId && !a.IsDeleted);

                            if (assetWithResidual != null)
                            {
                                decimal maxDepreciation = depreciation.OriginalAmount - assetWithResidual.EstimatedResidualValue;
                                if (depreciation.AccumulatedDepreciation > maxDepreciation)
                                {
                                    // 調整累計折舊金額為最大值
                                    depreciation.AccumulatedDepreciation = maxDepreciation;
                                    depreciation.CurrentDepreciation = Math.Max(0, depreciation.CurrentDepreciation);

                                    // 重新計算期初和期末帳面價值
                                    depreciation.BeginningBookValue = depreciation.OriginalAmount - (depreciation.AccumulatedDepreciation - depreciation.CurrentDepreciation);
                                    depreciation.EndingBookValue = depreciation.OriginalAmount - depreciation.AccumulatedDepreciation;

                                    // 標記為調整紀錄
                                    depreciation.IsAdjustment = true;
                                    depreciation.AdjustmentReason = "累計折舊金額超過合理範圍，已調整為最大值";

                                    Console.WriteLine($"折舊紀錄 {depId} 累計折舊金額已調整: {depreciation.AccumulatedDepreciation:C}");
                                }
                            }
                        }

                        // 執行折舊
                        depreciation.Notes = (depreciation.Notes ?? "") + "[已執行本期折舊]";//備註
                        depreciation.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();//更新時間
                        depreciation.UpdateUserId = userId;//更新者
                        _context.Update(depreciation);//更新折舊紀錄

                        // 更新財產主檔
                        asset.AccumulatedDepreciationAmount = depreciation.AccumulatedDepreciation;//累計折舊
                        asset.DepreciationAmount = depreciation.OriginalAmount;//原始金額
                        asset.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();//更新時間
                        asset.UpdateUserId = userId;//更新者
                        _context.Update(asset);//更新財產主檔
                        successCount++;//成功筆數
                        messages.Add($"折舊紀錄 {depId} 執行成功，財產 {asset.AssetNo} 已更新，期初帳面價值: {depreciation.BeginningBookValue:C}, 期末帳面價值: {depreciation.EndingBookValue:C}");
                    }
                    // 儲存變更
                    await _context.SaveChangesAsync();
                    // 提交交易
                    await transaction.CommitAsync();
                    // 回傳結果
                    if (successCount == 0)
                        return (false, messages);
                    return (true, messages);
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    messages.Add($"執行過程發生錯誤: {ex.Message}");
                    return (false, messages);
                }
            }
        }

        /// <summary>
        /// 產生固定資產明細表
        /// </summary>
        /// <param name="request">查詢條件</param>
        /// <returns>固定資產明細表資料</returns>
        public async Task<List<FixedAssetDetailReportDTO>> GenerateFixedAssetDetailReportAsync(FixedAssetDetailReportRequestDTO request)
        {
            try
            {
                // 設定預設年度為當前年度
                int targetYear = request.Year ?? DateTime.Now.Year;

                // 建立基礎查詢
                var query = from asset in _context.Pms_Assets.Where(a => !a.IsDeleted)

                                // 聯結財產科目
                            join assetAccount in _context.Pms_AssetAccounts
                            on asset.AssetAccountId equals assetAccount.AssetAccountId into assetAccountGroup
                            from assetAccount in assetAccountGroup.DefaultIfEmpty()

                                // 聯結財產子目
                            join assetSubAccount in _context.Pms_AssetSubAccounts
                            on asset.AssetSubAccountId equals assetSubAccount.AssetSubAccountId into assetSubAccountGroup
                            from assetSubAccount in assetSubAccountGroup.DefaultIfEmpty()

                                // 聯結單位
                            join unit in _context.Common_Units
                            on asset.UnitId equals unit.UnitId into unitGroup
                            from unit in unitGroup.DefaultIfEmpty()

                                // 聯結部門
                            join department in _context.Common_Departments
                            on asset.DepartmentId equals department.DepartmentId into departmentGroup
                            from department in departmentGroup.DefaultIfEmpty()

                                // 聯結財產狀態
                            join assetStatus in _context.Pms_AssetStatus
                            on asset.AssetStatusId equals assetStatus.AssetStatusId into assetStatusGroup
                            from assetStatus in assetStatusGroup.DefaultIfEmpty()

                                // 聯結存放地點
                            join storageLocation in _context.Pms_StorageLocations
                            on asset.StorageLocationId equals storageLocation.StorageLocationId.ToString() into storageLocationGroup
                            from storageLocation in storageLocationGroup.DefaultIfEmpty()

                                // 聯結保管人
                            join custodian in _context.Set<Users>()
                            on asset.CustodianId equals custodian.UserId into custodianGroup
                            from custodian in custodianGroup.DefaultIfEmpty()

                                // 聯結使用人
                            join user in _context.Set<Users>()
                            on asset.UserId equals user.UserId into userGroup
                            from user in userGroup.DefaultIfEmpty()

                            select new
                            {
                                Asset = asset,
                                AssetAccountName = assetAccount != null ? assetAccount.AssetAccountName : "",
                                AssetSubAccountName = assetSubAccount != null ? assetSubAccount.AssetSubAccountName : "",
                                UnitName = unit != null ? unit.Name : "",
                                DepartmentName = department != null ? department.Name : "",
                                AssetStatusName = assetStatus != null ? assetStatus.Name : "",
                                StorageLocationName = storageLocation != null ? storageLocation.Name : "",
                                CustodianName = custodian != null ? custodian.Name : "",
                                UserName = user != null ? user.Name : ""
                            };

                // 應用篩選條件
                if (request.AssetAccountId.HasValue)
                {
                    query = query.Where(x => x.Asset.AssetAccountId == request.AssetAccountId.Value);
                }

                if (request.AssetSubAccountId.HasValue)
                {
                    query = query.Where(x => x.Asset.AssetSubAccountId == request.AssetSubAccountId.Value);
                }

                if (!string.IsNullOrEmpty(request.DepartmentId))
                {
                    query = query.Where(x => x.Asset.DepartmentId == request.DepartmentId);
                }

                if (request.AssetStatusId.HasValue)
                {
                    query = query.Where(x => x.Asset.AssetStatusId == request.AssetStatusId.Value);
                }

                if (!string.IsNullOrEmpty(request.AssetNo))
                {
                    query = query.Where(x => x.Asset.AssetNo.Contains(request.AssetNo));
                }

                if (!string.IsNullOrEmpty(request.AssetName))
                {
                    query = query.Where(x => x.Asset.AssetName.Contains(request.AssetName));
                }

                if (request.AcquisitionDateStart.HasValue)
                {
                    query = query.Where(x => x.Asset.AcquisitionDate >= request.AcquisitionDateStart.Value);
                }

                if (request.AcquisitionDateEnd.HasValue)
                {
                    query = query.Where(x => x.Asset.AcquisitionDate <= request.AcquisitionDateEnd.Value);
                }

                // 處理是否包含報廢/出售/捐贈財產的條件
                if (!request.IncludeScrapAssets.HasValue || !request.IncludeScrapAssets.Value)
                {
                    query = query.Where(x => x.Asset.ScrapDate == null || x.Asset.ScrapDate == 0);
                }

                if (!request.IncludeSoldAssets.HasValue || !request.IncludeSoldAssets.Value)
                {
                    query = query.Where(x => x.Asset.SoldDate == null || x.Asset.SoldDate == 0);
                }

                if (!request.IncludeDonatedAssets.HasValue || !request.IncludeDonatedAssets.Value)
                {
                    query = query.Where(x => x.Asset.DonatedDate == null || x.Asset.DonatedDate == 0);
                }

                // 執行查詢並排序
                var results = await query
                    .OrderBy(x => x.Asset.DepartmentId)
                    .ThenBy(x => x.Asset.AssetNo)
                    .ThenBy(x => x.Asset.AcquisitionDate)
                    .ToListAsync();

                // 取得所有資產ID以查詢本年度累計折舊
                var assetIds = results.Select(r => r.Asset.AssetId).ToList();

                // 查詢指定年度的累計折舊
                var currentYearDepreciations = await _context.Set<DepreciationFormDetail>()
                    .Where(d => !d.IsDeleted &&
                               assetIds.Contains(d.AssetId) &&
                               d.DepreciationYear == targetYear)
                    .GroupBy(d => d.AssetId)
                    .Select(g => new
                    {
                        AssetId = g.Key,
                        CurrentYearDepreciation = g.Sum(d => d.CurrentDepreciation)
                    })
                    .ToDictionaryAsync(x => x.AssetId, x => x.CurrentYearDepreciation);

                // 轉換為DTO
                var reportData = results.Select(item =>
                {
                    var asset = item.Asset;

                    // 計算單價 = 取得價值 / 數量
                    decimal unitPrice = asset.Quantity > 0 ? asset.PurchaseAmount / asset.Quantity : 0;

                    // 計算淨值 = 取得價值 - 累計折舊
                    decimal netBookValue = asset.PurchaseAmount - asset.AccumulatedDepreciationAmount;

                    // 取得本年度累計折舊
                    decimal currentYearDepreciation = currentYearDepreciations.TryGetValue(asset.AssetId, out var yearDepreciation)
                        ? yearDepreciation : 0;

                    // 格式化取得日期為 yyyy/MM/dd
                    string acquisitionDateFormatted = "";
                    if (asset.AcquisitionDate > 0)
                    {
                        try
                        {
                            var acquisitionDateTime = DateTimeOffset.FromUnixTimeSeconds(asset.AcquisitionDate);
                            acquisitionDateFormatted = acquisitionDateTime.ToString("yyyy/MM/dd");
                        }
                        catch
                        {
                            acquisitionDateFormatted = "";
                        }
                    }

                    return new FixedAssetDetailReportDTO
                    {
                        AssetId = asset.AssetId,
                        AssetNo = asset.AssetNo ?? "",
                        AssetName = asset.AssetName ?? "",
                        AcquisitionDate = asset.AcquisitionDate,
                        AcquisitionDateFormatted = acquisitionDateFormatted,
                        Specification = asset.Specification ?? "",
                        Quantity = asset.Quantity,
                        UnitName = item.UnitName,
                        UnitPrice = unitPrice,
                        AcquisitionValue = asset.PurchaseAmount,
                        AccumulatedDepreciation = asset.AccumulatedDepreciationAmount,
                        CurrentYearAccumulatedDepreciation = currentYearDepreciation,
                        NetBookValue = netBookValue,
                        Notes = asset.Notes ?? "",
                        AssetAccountName = item.AssetAccountName,
                        AssetSubAccountName = item.AssetSubAccountName,
                        DepartmentName = item.DepartmentName,
                        CustodianName = item.CustodianName,
                        UserName = item.UserName,
                        StorageLocationName = item.StorageLocationName,
                        ServiceLife = asset.ServiceLife,
                        Usage = asset.Usage ?? "",
                        AssetStatusName = item.AssetStatusName
                    };
                }).ToList();

                Console.WriteLine($"固定資產明細表產生完成，共 {reportData.Count} 筆記錄（{targetYear}年度本年度累計折舊已計算）");
                return reportData;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"產生固定資產明細表失敗: {ex.Message}");
                throw new Exception($"產生固定資產明細表失敗: {ex.Message}", ex);
            }
        }

    }
}

