'use client';

import React, { useState, useEffect } from 'react';
import { Card, Typography, Tag, Row, Col, Divider, Space, Spin, message, Collapse, Modal, Button } from 'antd';
import { DownOutlined, RightOutlined, EyeOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { getEmployeeDetail, Employee } from '@/services/pas/EmployeeService';
import ServiceDepartmentChangeInfo from '@/app/pas/employee_main/employee_modules/serviceDepartment/ServiceDepartmentChangeInfo';
import ExpenseDepartmentChangeInfo from '@/app/pas/employee_main/employee_modules/expenseDepartment/ExpenseDepartmentChangeInfo';
import PromotionInfo from '@/app/pas/employee_main/employee_modules/promotion/PromotionInfo';
import CurrentInsuranceOverview from '@/app/pas/employee_main/employee_modules/insuranceHistory/CurrentInsuranceOverview';
import InsuranceHistoryInfo from '@/app/pas/employee_main/employee_modules/insuranceHistory/InsuranceHistoryInfo';

interface EmployeeCurrentDataCardProps {
    userId: string;
    title?: string;
    size?: 'default' | 'small';
    style?: React.CSSProperties;
    loading?: boolean;
}

const EmployeeCurrentDataCard: React.FC<EmployeeCurrentDataCardProps> = ({
    userId,
    title = "當前員工資料",
    size = "small",
    style = { marginBottom: 16, backgroundColor: '#fafafa' },
    loading: externalLoading = false,
}) => {
    const [employee, setEmployee] = useState<Employee | null>(null);
    const [internalLoading, setInternalLoading] = useState(false);
    const [modalOpen, setModalOpen] = useState(false);
    const [modalType, setModalType] = useState<'service' | 'expense' | 'promotion' | 'insurance' | null>(null);

    // 載入員工資料
    const loadEmployeeData = async (empUserId: string) => {
        if (!empUserId) return;

        setInternalLoading(true);
        try {
            const response = await getEmployeeDetail(empUserId);
            if (response.success && response.data) {
                setEmployee(response.data);
            } else {
                console.warn(`無法載入員工資料：${empUserId}`, response.message);
                setEmployee(null);
            }
        } catch (error) {
            console.error('載入員工資料失敗:', error);
            setEmployee(null);
        } finally {
            setInternalLoading(false);
        }
    };

    // 當 userId 改變時重新載入資料
    useEffect(() => {
        if (userId) {
            loadEmployeeData(userId);
        } else {
            setEmployee(null);
        }
    }, [userId]);

    // 監聽 Modal 關閉，自動重新載入資料
    useEffect(() => {
        if (!modalOpen && userId) {
            // 當升遷異動 Modal 關閉時，重新載入員工資料
            loadEmployeeData(userId);
        }
    }, [modalOpen, modalType, userId]);

    // 渲染內容
    const renderContent = () => {
        if (!employee) {
            return (
                <Col span={24}>
                    <Typography.Text type="secondary">
                        {userId ? '無法載入員工資料' : '未指定員工'}
                    </Typography.Text>
                </Col>
            );
        }

        const currentPromotion = employee.currentPromotion;
        const currentSalary = employee.currentSalary;
        const currentServiceDept = employee.currentServiceDepartment;
        const currentExpenseDept = employee.currentExpenseDepartment;

        // 準備摺疊面板的項目
        const collapseItems = [];

        // 部門資訊
        if (currentServiceDept || currentExpenseDept) {
            collapseItems.push({
                key: 'department',
                label: (
                    <Typography.Text strong style={{ color: '#1890ff' }}>
                        部門資訊
                    </Typography.Text>
                ),
                children: (
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                        <div>
                            <Typography.Text strong>服務部門：</Typography.Text>
                            {currentServiceDept?.serviceDepartmentName ? (
                                <Typography.Link
                                    style={{ marginLeft: 8 }}
                                    onClick={() => { setModalType('service'); setModalOpen(true); }}
                                >
                                    {currentServiceDept.serviceDepartmentName}
                                </Typography.Link>
                            ) : (
                                <Typography.Text style={{ marginLeft: 8 }}>未設定</Typography.Text>
                            )}
                        </div>
                        <div>
                            <Typography.Text strong>開支部門：</Typography.Text>
                            {currentExpenseDept?.expenseDepartmentName ? (
                                <Typography.Link
                                    style={{ marginLeft: 8 }}
                                    onClick={() => { setModalType('expense'); setModalOpen(true); }}
                                >
                                    {currentExpenseDept.expenseDepartmentName}
                                </Typography.Link>
                            ) : (
                                <Typography.Text style={{ marginLeft: 8 }}>未設定</Typography.Text>
                            )}
                        </div>
                    </Space>
                ),
            });
        }

        // 薪資主檔資料
        if (currentSalary) {
            collapseItems.push({
                key: 'salary',
                label: (
                    <Typography.Text strong style={{ color: '#52c41a' }}>
                        薪資主檔資料
                    </Typography.Text>
                ),
                children: (
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                        <div>
                            <Typography.Text strong>發薪狀態：</Typography.Text>
                            <Tag color="orange" style={{ marginLeft: 8 }}>
                                {currentSalary.salaryStatusName || '未知'}
                            </Tag>
                        </div>
                        <div>
                            <Typography.Text strong>員工自提類型：</Typography.Text>
                            <Typography.Text style={{ marginLeft: 8 }}>
                                {currentSalary.employeeContributionTypeName || currentSalary.employeeContributionType || '未設定'}
                            </Typography.Text>
                        </div>
                        <div>
                            <Typography.Text strong>員工自提金額：</Typography.Text>
                            <Typography.Text style={{ marginLeft: 8, color: '#52c41a' }}>
                                {currentSalary.employeeContributionAmount ? `NT$ ${currentSalary.employeeContributionAmount.toLocaleString()}` : 'NT$ 0'}
                            </Typography.Text>
                        </div>
                        <div>
                            <Typography.Text strong>稅務類型：</Typography.Text>
                            <Typography.Text style={{ marginLeft: 8 }}>
                                {currentSalary.taxTypeName || currentSalary.taxType || '未設定'}
                            </Typography.Text>
                        </div>
                        <div>
                            <Typography.Text strong>轉帳帳戶：</Typography.Text>
                            <Typography.Text style={{ marginLeft: 8 }}>
                                {currentSalary.transferAccount || '未設定'}
                            </Typography.Text>
                        </div>
                    </Space>
                ),
            });
        }

        // 當前生效薪資
        if (currentPromotion) {
            collapseItems.push({
                key: 'promotion',
                label: (
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography.Text strong style={{ color: '#1890ff' }}>
                            當前生效薪資
                        </Typography.Text>
                        <Button
                            type="link"
                            size="small"
                            icon={<EyeOutlined />}
                            onClick={(e) => {
                                e.stopPropagation();
                                setModalType('promotion');
                                setModalOpen(true);
                            }}
                            style={{ padding: 0, height: 'auto' }}
                        >
                            檢視
                        </Button>
                    </div>
                ),
                children: (
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                        <div>
                            <Typography.Text strong>薪資類型：</Typography.Text>
                            <Tag color="blue" style={{ marginLeft: 8 }}>
                                {currentPromotion.salaryTypeName || '未設定'}
                            </Tag>
                        </div>
                        <div>
                            <Typography.Text strong>薪資金額：</Typography.Text>
                            <Typography.Text style={{ marginLeft: 8, color: '#52c41a' }}>
                                {currentPromotion.salaryAmount ? `NT$ ${parseFloat(currentPromotion.salaryAmount).toLocaleString()}` : '未設定'}
                            </Typography.Text>
                        </div>
                        <div>
                            <Typography.Text strong>加給類型：</Typography.Text>
                            <Typography.Text style={{ marginLeft: 8 }}>
                                {currentPromotion.allowanceTypeName || '未設定'}
                            </Typography.Text>
                        </div>
                        <div>
                            <Typography.Text strong>職位：</Typography.Text>
                            <Typography.Text style={{ marginLeft: 8 }}>
                                {currentPromotion.jobTitleName || currentPromotion.jobTitle || '未設定'}
                            </Typography.Text>
                        </div>
                        <div>
                            <Typography.Text strong>職等：</Typography.Text>
                            <Typography.Text style={{ marginLeft: 8 }}>
                                {currentPromotion.jobLevelName || currentPromotion.jobLevel || '未設定'}
                            </Typography.Text>
                        </div>
                        <div>
                            <Typography.Text strong>生效日期：</Typography.Text>
                            <Typography.Text style={{ marginLeft: 8 }}>
                                {currentPromotion.effectiveDate ? dayjs(currentPromotion.effectiveDate).format('YYYY-MM-DD') : '未設定'}
                            </Typography.Text>
                        </div>
                    </Space>
                ),
            });
        }

        // 當前投保級距
        collapseItems.push({
            key: 'insurance',
            label: (
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Typography.Text strong style={{ color: '#fa8c16' }}>
                        當前投保級距
                    </Typography.Text>
                    <Button
                        type="link"
                        size="small"
                        icon={<EyeOutlined />}
                        onClick={(e) => {
                            e.stopPropagation();
                            setModalType('insurance');
                            setModalOpen(true);
                        }}
                        style={{ padding: 0, height: 'auto' }}
                    >
                        檢視
                    </Button>
                </div>
            ),
            children: (
                <CurrentInsuranceOverview
                    userId={userId}
                    showTitle={false}
                    size="small"
                    style={{ margin: 0 }}
                    useContainerWidth={true}
                />
            ),
        });

        return (
            <Row gutter={[16, 8]}>
                {/* 員工基本資訊 - 固定顯示 */}
                <Col span={24}>
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                        <div>
                            <Typography.Text strong style={{ color: '#1677ff', fontSize: 16 }}>員工姓名：</Typography.Text>
                            <Typography.Text style={{ marginLeft: 8, fontSize: 16, fontWeight: 'bold' }}>
                                {employee.usersDTO?.name || '未設定'}
                            </Typography.Text>
                        </div>
                        <div>
                            <Typography.Text strong style={{ color: '#1677ff' }}>員工編號：</Typography.Text>
                            <Typography.Text style={{ marginLeft: 8, fontWeight: 'bold' }}>
                                {employee.empNo || '未設定'}
                            </Typography.Text>
                        </div>
                    </Space>
                </Col>

                {/* 可摺疊的詳細資料 */}
                {collapseItems.length > 0 ? (
                    <Col span={24}>
                        <Collapse
                            size="small"
                            items={collapseItems}
                            defaultActiveKey={['department']} // 預設展開部門資訊
                            style={{ marginTop: 8 }}
                        />
                    </Col>
                ) : (
                    <Col span={24}>
                        <Typography.Text type="secondary" style={{ marginTop: 8, display: 'block' }}>
                            無其他薪資資料
                        </Typography.Text>
                    </Col>
                )}
            </Row>
        );
    };

    const isLoading = externalLoading || internalLoading;

    return (
        <Card
            title={title}
            size={size}
            style={style}
        >
            <Spin spinning={isLoading} tip="載入員工資料中...">
                {renderContent()}
            </Spin>
            {modalOpen && (
                <Modal
                    open={modalOpen}
                    title={
                        modalType === 'service' ? '服務部門異動資料' :
                            modalType === 'expense' ? '開支部門異動資料' :
                                modalType === 'promotion' ? '升遷異動資料' :
                                    modalType === 'insurance' ? '保險級距歷程' : ''
                    }
                    width={modalType === 'promotion' ? 1200 : modalType === 'insurance' ? 1400 : 980}
                    footer={null}
                    onCancel={() => {
                        setModalOpen(false);
                    }}
                >
                    {modalType === 'service' && (
                        <ServiceDepartmentChangeInfo userId={userId} active={true} />
                    )}
                    {modalType === 'expense' && (
                        <ExpenseDepartmentChangeInfo userId={userId} active={true} />
                    )}
                    {modalType === 'promotion' && (
                        <PromotionInfo userId={userId} active={true} />
                    )}
                    {modalType === 'insurance' && (
                        <InsuranceHistoryInfo userId={userId} active={true} />
                    )}
                </Modal>
            )}
        </Card>
    );
};

export default EmployeeCurrentDataCard;
