# 構建階段
FROM node:18-alpine AS builder
WORKDIR /app

# 安裝 Python 和構建工具 (某些 npm 包需要)
RUN apk add --no-cache python3 make g++

# 複製 package 文件並安裝依賴
COPY package*.json yarn.lock ./
RUN yarn install --frozen-lockfile --network-timeout 100000

# 複製源代碼並構建
COPY . .
RUN yarn build

# 生產階段
FROM node:18-alpine AS runner
WORKDIR /app

# 安裝必要的工具
RUN apk add --no-cache curl wget

# 創建非 root 用戶
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 複製構建產物和依賴
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/yarn.lock ./yarn.lock

# 複製 Next.js 構建輸出
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# 切換到非 root 用戶
USER nextjs

# 設定環境變數
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME=0.0.0.0

# 暴露端口
EXPOSE 3000

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD wget --quiet --tries=1 --spider http://localhost:3000 || exit 1

# 啟動應用
CMD ["node", "server.js"]