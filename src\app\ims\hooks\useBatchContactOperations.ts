/**
 * 批量 Contact 操作 Hook
 * 
 * 提供批量操作功能，包括：
 * - 批量創建聯絡人
 * - 批量更新聯絡人
 * - 批量刪除聯絡人
 * - 批量狀態變更
 * - 批量導入/導出
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useState, useCallback } from 'react';
import { message } from 'antd';
import { 
  Contact, 
  addContact, 
  updateContact, 
  deleteContact 
} from '@/services/ims/ContactService';
import { 
  validateContactBasicData,
  sanitizeContactData,
  contactsToCSV,
  logHelperUsage 
} from '@/app/ims/utils/contactHelpers';
import { logger, createContextLogger } from '@/utils/logger';

// 創建上下文日誌器
const batchOperationsLogger = createContextLogger({ module: 'useBatchContactOperations' });

export interface BatchOperationResult {
  success: number;
  failed: number;
  errors: string[];
  results: any[];
}

export interface BatchOperationState {
  isProcessing: boolean;
  progress: number;
  currentOperation: string;
  results: BatchOperationResult | null;
}

export interface BatchContactOperations {
  // 狀態
  state: BatchOperationState;
  
  // 批量操作
  batchCreate: (contacts: Partial<Contact>[]) => Promise<BatchOperationResult>;
  batchUpdate: (contacts: Contact[]) => Promise<BatchOperationResult>;
  batchDelete: (contactIds: string[]) => Promise<BatchOperationResult>;
  batchToggleStatus: (contactIds: string[], isActive: boolean) => Promise<BatchOperationResult>;
  
  // 導入/導出
  exportToCSV: (contacts: Contact[], filename?: string) => void;
  importFromCSV: (csvContent: string) => Promise<BatchOperationResult>;
  
  // 重置狀態
  resetState: () => void;
}

export const useBatchContactOperations = (): BatchContactOperations => {
  const [state, setState] = useState<BatchOperationState>({
    isProcessing: false,
    progress: 0,
    currentOperation: '',
    results: null
  });

  // 更新狀態的輔助函數
  const updateState = useCallback((updates: Partial<BatchOperationState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // 重置狀態
  const resetState = useCallback(() => {
    setState({
      isProcessing: false,
      progress: 0,
      currentOperation: '',
      results: null
    });
  }, []);

  // 批量創建聯絡人
  const batchCreate = useCallback(async (contacts: Partial<Contact>[]): Promise<BatchOperationResult> => {
    const result: BatchOperationResult = {
      success: 0,
      failed: 0,
      errors: [],
      results: []
    };

    try {
      updateState({ 
        isProcessing: true, 
        progress: 0, 
        currentOperation: '批量創建聯絡人',
        results: null 
      });

      batchOperationsLogger.info(`開始批量創建 ${contacts.length} 個聯絡人`);
      logHelperUsage('batchCreate', { count: contacts.length });

      for (let i = 0; i < contacts.length; i++) {
        const contact = contacts[i];
        
        try {
          // 數據清理和驗證
          const sanitizedData = sanitizeContactData(contact);
          const validation = validateContactBasicData(sanitizedData);
          
          if (!validation.isValid) {
            result.failed++;
            result.errors.push(`第 ${i + 1} 個聯絡人驗證失敗: ${validation.errors.join(', ')}`);
            continue;
          }

          // 創建聯絡人
          const response = await addContact(sanitizedData);
          
          if (response.success) {
            result.success++;
            result.results.push(response.data);
          } else {
            result.failed++;
            result.errors.push(`第 ${i + 1} 個聯絡人創建失敗: ${response.message}`);
          }
        } catch (error) {
          result.failed++;
          result.errors.push(`第 ${i + 1} 個聯絡人創建異常: ${error}`);
        }

        // 更新進度
        const progress = Math.round(((i + 1) / contacts.length) * 100);
        updateState({ progress });
      }

      batchOperationsLogger.info('批量創建完成', result);
      message.success(`批量創建完成：成功 ${result.success} 個，失敗 ${result.failed} 個`);

    } catch (error) {
      batchOperationsLogger.error('批量創建失敗', error);
      message.error('批量創建失敗');
    } finally {
      updateState({ 
        isProcessing: false, 
        progress: 100,
        results: result 
      });
    }

    return result;
  }, [updateState]);

  // 批量更新聯絡人
  const batchUpdate = useCallback(async (contacts: Contact[]): Promise<BatchOperationResult> => {
    const result: BatchOperationResult = {
      success: 0,
      failed: 0,
      errors: [],
      results: []
    };

    try {
      updateState({ 
        isProcessing: true, 
        progress: 0, 
        currentOperation: '批量更新聯絡人',
        results: null 
      });

      batchOperationsLogger.info(`開始批量更新 ${contacts.length} 個聯絡人`);
      logHelperUsage('batchUpdate', { count: contacts.length });

      for (let i = 0; i < contacts.length; i++) {
        const contact = contacts[i];
        
        try {
          // 數據清理和驗證
          const sanitizedData = sanitizeContactData(contact) as Contact;
          sanitizedData.contactID = contact.contactID; // 保持原始 ID
          
          const validation = validateContactBasicData(sanitizedData);
          
          if (!validation.isValid) {
            result.failed++;
            result.errors.push(`第 ${i + 1} 個聯絡人驗證失敗: ${validation.errors.join(', ')}`);
            continue;
          }

          // 更新聯絡人
          const response = await updateContact(sanitizedData);
          
          if (response.success) {
            result.success++;
            result.results.push(response.data);
          } else {
            result.failed++;
            result.errors.push(`第 ${i + 1} 個聯絡人更新失敗: ${response.message}`);
          }
        } catch (error) {
          result.failed++;
          result.errors.push(`第 ${i + 1} 個聯絡人更新異常: ${error}`);
        }

        // 更新進度
        const progress = Math.round(((i + 1) / contacts.length) * 100);
        updateState({ progress });
      }

      batchOperationsLogger.info('批量更新完成', result);
      message.success(`批量更新完成：成功 ${result.success} 個，失敗 ${result.failed} 個`);

    } catch (error) {
      batchOperationsLogger.error('批量更新失敗', error);
      message.error('批量更新失敗');
    } finally {
      updateState({ 
        isProcessing: false, 
        progress: 100,
        results: result 
      });
    }

    return result;
  }, [updateState]);

  // 批量刪除聯絡人
  const batchDelete = useCallback(async (contactIds: string[]): Promise<BatchOperationResult> => {
    const result: BatchOperationResult = {
      success: 0,
      failed: 0,
      errors: [],
      results: []
    };

    try {
      updateState({ 
        isProcessing: true, 
        progress: 0, 
        currentOperation: '批量刪除聯絡人',
        results: null 
      });

      batchOperationsLogger.info(`開始批量刪除 ${contactIds.length} 個聯絡人`);
      logHelperUsage('batchDelete', { count: contactIds.length });

      for (let i = 0; i < contactIds.length; i++) {
        const contactId = contactIds[i];
        
        try {
          const response = await deleteContact(contactId);
          
          if (response.success) {
            result.success++;
            result.results.push({ contactId, deleted: true });
          } else {
            result.failed++;
            result.errors.push(`聯絡人 ${contactId} 刪除失敗: ${response.message}`);
          }
        } catch (error) {
          result.failed++;
          result.errors.push(`聯絡人 ${contactId} 刪除異常: ${error}`);
        }

        // 更新進度
        const progress = Math.round(((i + 1) / contactIds.length) * 100);
        updateState({ progress });
      }

      batchOperationsLogger.info('批量刪除完成', result);
      message.success(`批量刪除完成：成功 ${result.success} 個，失敗 ${result.failed} 個`);

    } catch (error) {
      batchOperationsLogger.error('批量刪除失敗', error);
      message.error('批量刪除失敗');
    } finally {
      updateState({ 
        isProcessing: false, 
        progress: 100,
        results: result 
      });
    }

    return result;
  }, [updateState]);

  // 批量狀態變更
  const batchToggleStatus = useCallback(async (contactIds: string[], isActive: boolean): Promise<BatchOperationResult> => {
    // TODO: 實現批量狀態變更
    // 需要後端提供批量更新 API 或者逐個更新
    const result: BatchOperationResult = {
      success: 0,
      failed: contactIds.length,
      errors: ['批量狀態變更功能開發中...'],
      results: []
    };

    message.info('批量狀態變更功能開發中...');
    return result;
  }, []);

  // 導出為 CSV
  const exportToCSV = useCallback((contacts: Contact[], filename?: string) => {
    try {
      const csvContent = contactsToCSV(contacts);
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      
      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename || `contacts_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      batchOperationsLogger.info(`導出 ${contacts.length} 個聯絡人到 CSV`);
      logHelperUsage('exportToCSV', { count: contacts.length, filename });
      message.success(`成功導出 ${contacts.length} 個聯絡人`);
    } catch (error) {
      batchOperationsLogger.error('CSV 導出失敗', error);
      message.error('CSV 導出失敗');
    }
  }, []);

  // 從 CSV 導入
  const importFromCSV = useCallback(async (csvContent: string): Promise<BatchOperationResult> => {
    // TODO: 實現 CSV 導入功能
    const result: BatchOperationResult = {
      success: 0,
      failed: 1,
      errors: ['CSV 導入功能開發中...'],
      results: []
    };

    message.info('CSV 導入功能開發中...');
    return result;
  }, []);

  return {
    state,
    batchCreate,
    batchUpdate,
    batchDelete,
    batchToggleStatus,
    exportToCSV,
    importFromCSV,
    resetState
  };
};

export default useBatchContactOperations;
