using FAST_ERP_Backend.Interfaces.Rms;
using FAST_ERP_Backend.Models.Rms;
using FAST_ERP_Backend.Models;
using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;

namespace FAST_ERP_Backend.Services.Rms
{
    /// <summary>
    /// 房源管理服務實作
    /// </summary>
    public class PropertyService : IPropertyService
    {
        private readonly ERPDbContext _context;
        private readonly ILogger<PropertyService> _logger;

        /// <summary>
        /// 初始化房源服務
        /// </summary>
        /// <param name="context">資料庫上下文</param>
        /// <param name="logger">日誌服務</param>
        public PropertyService(ERPDbContext context, ILogger<PropertyService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 取得房源列表
        /// </summary>
        public async Task<ApiResponse<List<PropertyDTO>>> GetPropertyListAsync(PropertyQueryRequestDTO request)
        {
            try
            {
                var query = _context.Rms_Properties.AsQueryable();

                // 套用查詢條件
                if (!string.IsNullOrEmpty(request.PropertyId))
                    query = query.Where(p => p.PropertyId == request.PropertyId);

                if (!string.IsNullOrEmpty(request.PropertyName))
                    query = query.Where(p => p.PropertyName.Contains(request.PropertyName));

                if (!string.IsNullOrEmpty(request.Address))
                    query = query.Where(p => p.Address.Contains(request.Address));

                if (!string.IsNullOrEmpty(request.Status))
                    query = query.Where(p => p.Status == request.Status);

                if (!string.IsNullOrEmpty(request.Type))
                    query = query.Where(p => p.Type == request.Type);

                if (!string.IsNullOrEmpty(request.Owner))
                    query = query.Where(p => p.Owner.Contains(request.Owner));

                // 取得總筆數
                var totalCount = await query.CountAsync();

                // 分頁查詢
                var properties = await query
                    .OrderByDescending(p => p.CreateTime) // 改為按建立時間倒序排列
                    .Select(p => new PropertyDTO
                    {
                        PropertyId = p.PropertyId,
                        PropertyName = p.PropertyName,
                        Address = p.Address,
                        Status = p.Status,
                        Area = p.Area,
                        Floor = p.Floor,
                        Type = p.Type,
                        Owner = p.Owner,
                        Note = p.Note,
                        CreateTime = p.CreateTime ?? 0,
                        CreateUserId = p.CreateUserId,
                        UpdateTime = p.UpdateTime ?? 0,
                        UpdateUserId = p.UpdateUserId
                    })
                    .ToListAsync();

                return new ApiResponse<List<PropertyDTO>>
                {
                    Success = true,
                    Data = properties,
                    Message = "取得房源列表成功",
                    HttpCode = 200
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得房源列表時發生錯誤");
                return ApiResponse<List<PropertyDTO>>.ErrorResult("取得房源列表時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 取得房源詳細資料
        /// </summary>
        public async Task<ApiResponse<PropertyDTO>> GetPropertyByIdAsync(string propertyId)
        {
            try
            {
                var property = await _context.Rms_Properties
                    .Where(p => p.PropertyId == propertyId)
                    .Select(p => new PropertyDTO
                    {
                        PropertyId = p.PropertyId,
                        PropertyName = p.PropertyName,
                        Address = p.Address,
                        Status = p.Status,
                        Area = p.Area,
                        Floor = p.Floor,
                        Type = p.Type,
                        Owner = p.Owner,
                        Note = p.Note,
                        CreateTime = p.CreateTime ?? 0,
                        CreateUserId = p.CreateUserId,
                        UpdateTime = p.UpdateTime ?? 0,
                        UpdateUserId = p.UpdateUserId
                    })
                    .FirstOrDefaultAsync();

                if (property == null)
                {
                    return ApiResponse<PropertyDTO>.ErrorResult("房源不存在", 404);
                }

                return ApiResponse<PropertyDTO>.SuccessResult(property, "取得房源詳細資料成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得房源詳細資料時發生錯誤，PropertyId: {PropertyId}", propertyId);
                return ApiResponse<PropertyDTO>.ErrorResult("取得房源詳細資料時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 新增房源
        /// </summary>
        public async Task<ApiResponse<PropertyDTO>> CreatePropertyAsync(PropertyCreateRequestDTO request, string tokenUid)
        {
            try
            {
                var property = new Property
                {
                    PropertyId = Guid.NewGuid().ToString(),
                    PropertyName = request.PropertyName,
                    Address = request.Address,
                    Status = "1", // 新增房源預設為空置狀態
                    Area = request.Area,
                    Floor = request.Floor,
                    Type = request.Type,
                    Owner = request.Owner,
                    Note = request.Note,
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    CreateUserId = tokenUid
                };

                _context.Rms_Properties.Add(property);
                await _context.SaveChangesAsync();

                var propertyDto = new PropertyDTO
                {
                    PropertyId = property.PropertyId,
                    PropertyName = property.PropertyName,
                    Address = property.Address,
                    Status = property.Status,
                    Area = property.Area,
                    Floor = property.Floor,
                    Type = property.Type,
                    Owner = property.Owner,
                    Note = property.Note,
                    CreateTime = property.CreateTime ?? 0,
                    CreateUserId = property.CreateUserId
                };

                return ApiResponse<PropertyDTO>.SuccessResult(propertyDto, "新增房源成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "新增房源時發生錯誤");
                return ApiResponse<PropertyDTO>.ErrorResult("新增房源時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 更新房源
        /// </summary>
        public async Task<ApiResponse<string>> UpdatePropertyAsync(PropertyUpdateRequestDTO request, string tokenUid)
        {
            try
            {
                // 由於 PropertyExistsAttribute 已經驗證房源存在，這裡可以直接查詢
                var property = await _context.Rms_Properties
                    .FirstOrDefaultAsync(p => p.PropertyId == request.PropertyId);

                // 更新資料
                property.PropertyName = request.PropertyName;
                property.Address = request.Address;
                property.Status = request.Status;
                property.Area = request.Area;
                property.Floor = request.Floor;
                property.Type = request.Type;
                property.Owner = request.Owner;
                property.Note = request.Note;
                property.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                property.UpdateUserId = tokenUid;

                await _context.SaveChangesAsync();

                return ApiResponse<string>.SuccessResult("更新房源成功", "更新房源成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新房源時發生錯誤");
                return ApiResponse<string>.ErrorResult("更新房源時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 刪除房源
        /// </summary>
        public async Task<ApiResponse<string>> DeletePropertyAsync(PropertyDeleteRequestDTO request, string tokenUid)
        {
            try
            {
                // 由於 PropertyExistsAttribute 和 PropertyCanDeleteAttribute 已經驗證房源存在且可刪除，這裡可以直接查詢
                var property = await _context.Rms_Properties
                    .FirstOrDefaultAsync(p => p.PropertyId == request.PropertyId);

                // 軟刪除
                property.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                property.DeleteUserId = tokenUid;
                property.IsDeleted = true;

                await _context.SaveChangesAsync();

                return ApiResponse<string>.SuccessResult("刪除房源成功", "刪除房源成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刪除房源時發生錯誤");
                return ApiResponse<string>.ErrorResult("刪除房源時發生錯誤", 500);
            }
        }
    }
} 