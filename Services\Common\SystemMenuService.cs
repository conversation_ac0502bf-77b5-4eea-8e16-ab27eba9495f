using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Common
{
    public class SystemMenuService : ISystemMenuService
    {
        private readonly ERPDbContext _context;

        public SystemMenuService(ERPDbContext context)
        {
            _context = context;
        }

        public async Task<List<SystemMenuDTO>> GetSystemMenuAsync(string systemMenuId)
        {
            var query = _context.Common_SystemMenu.AsQueryable();

            if (!string.IsNullOrEmpty(systemMenuId))
            {
                query = query.Where(e => e.SystemMenuId == systemMenuId);
            }

            return await query.Select(menu => new SystemMenuDTO
            {
                SystemMenuId = menu.SystemMenuId,
                SystemGroupId = menu.SystemGroupId,
                Label = menu.Label,
                Key = menu.Key,
                Icon = menu.Icon,
                ParentId = menu.ParentId,
                IsMenu = menu.IsMenu,
            }).ToListAsync();
        }

        public async Task<(bool, string)> AddSystemMenuAsync(SystemMenuDTO systemMenu, String tokenUid = "")
        {
            try
            {
                var newMenu = new SystemMenu
                {
                    SystemMenuId = Guid.NewGuid().ToString(),
                    SystemGroupId = systemMenu.SystemGroupId,
                    Label = systemMenu.Label,
                    Key = systemMenu.Key,
                    Icon = systemMenu.Icon,
                    ParentId = systemMenu.ParentId,
                    IsMenu = systemMenu.IsMenu,
                };

                await _context.Common_SystemMenu.AddAsync(newMenu);
                await _context.SaveChangesAsync();

                return (true, "新增系統選單成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增系統選單失敗: {ex.Message}");
            }
        }

        public async Task<(bool, string)> EditSystemMenuAsync(SystemMenuDTO systemMenu, String tokenUid = "")
        {
            var existingMenu = await _context.Common_SystemMenu
                .FirstOrDefaultAsync(e => e.SystemMenuId == systemMenu.SystemMenuId);

            if (existingMenu != null)
            {
                try
                {
                    existingMenu.SystemGroupId = systemMenu.SystemGroupId;
                    existingMenu.Label = systemMenu.Label;
                    existingMenu.Key = systemMenu.Key;
                    existingMenu.Icon = systemMenu.Icon;
                    existingMenu.ParentId = systemMenu.ParentId;
                    existingMenu.IsMenu = systemMenu.IsMenu;

                    await _context.SaveChangesAsync();
                    return (true, "更新系統選單成功");
                }
                catch (Exception ex)
                {
                    return (false, $"更新系統選單失敗: {ex.Message}");
                }
            }
            return (false, "系統選單不存在");
        }

        public async Task<(bool, string)> DeleteSystemMenuAsync(SystemMenuDTO systemMenu, String tokenUid = "")
        {
            var existingMenu = await _context.Common_SystemMenu
                .FirstOrDefaultAsync(e => e.SystemMenuId == systemMenu.SystemMenuId);

            if (existingMenu != null)
            {
                try
                {
                    existingMenu.IsDeleted = true;

                    await _context.SaveChangesAsync();
                    return (true, "刪除系統選單成功");
                }
                catch (Exception ex)
                {
                    return (false, $"刪除系統選單失敗: {ex.Message}");
                }
            }
            return (false, "系統選單不存在");
        }

        /// <summary>
        /// 取得系統群組的選單
        /// </summary>
        /// <param name="systemGroupId"></param>
        /// <param name="RolesId"></param>
        /// <returns></returns>
        public async Task<List<SystemGroupsNode>> GetGroupMenuAsync(string systemGroupId, string RolesId)
        {

            var menusQuery = _context.Common_SystemMenu.AsQueryable();
            var groupsQuery = _context.Common_SystemGroups.AsQueryable();

            if (!string.IsNullOrEmpty(systemGroupId))
            {
                menusQuery = menusQuery.Where(e => e.SystemGroupId == systemGroupId);
                groupsQuery = groupsQuery.Where(e => e.SystemGroupId == systemGroupId);
            }

            if (!string.IsNullOrEmpty(RolesId))
            {
                // 取得所有有權限的選單ID和父節點ID
                var allPermittedMenuIds = await GetAllPermittedMenuIdsAsync(RolesId);

                if (allPermittedMenuIds.Count == 0)
                {
                    // 如果沒有權限，直接返回空列表
                    return new List<SystemGroupsNode>();
                }

                menusQuery = menusQuery.Where(m => allPermittedMenuIds.Contains(m.SystemMenuId));
            }
            var menuNodes = await menusQuery
                .OrderBy(e => e.ParentId)
                .ThenBy(e => e.CreateTime)
                .Select(m => new SystemMenuNode
                {
                    SystemMenuId = m.SystemMenuId,
                    SystemGroupId = m.SystemGroupId,
                    Label = m.Label,
                    Key = m.Key,
                    Icon = m.Icon,
                    ParentId = m.ParentId,
                    IsMenu = m.IsMenu
                }).ToListAsync();

            // 取得選單樹
            var rootNodes = BuildMenuTree(menuNodes);

            // 將選單按系統群組分組
            var rootGroup = rootNodes
                .GroupBy(m => m.SystemGroupId)
                .ToDictionary(g => g.Key, g => g.ToList());

            // 取得系統群組資訊
            var groupsNodes = await groupsQuery
               .Select(m => new SystemGroupsNode
               {
                   SystemGroupId = m.SystemGroupId,
                   SystemCode = m.SystemCode,
                   Name = m.Name,
                   Menus = rootGroup.ContainsKey(m.SystemGroupId) ? rootGroup[m.SystemGroupId] : new List<SystemMenuNode>()
               }).ToListAsync();

            return groupsNodes;
        }

        /// <summary>
        /// 構建選單樹結構
        /// </summary>
        /// <param name="menuNodes"></param>
        /// <returns></returns>
        private List<SystemMenuNode> BuildMenuTree(List<SystemMenuNode> menuNodes)
        {
            // 創建一個以SystemMenuId為鍵的字典，便於快速查找菜單
            var menuDict = menuNodes.ToDictionary(m => m.SystemMenuId);

            var rootNodes = new List<SystemMenuNode>();

            // 構建選單樹結構
            foreach (var node in menuNodes)
            {
                if (string.IsNullOrEmpty(node.ParentId))
                {
                    // 如果沒有父節點ID，則是根節點
                    rootNodes.Add(node);
                }
                // 如果有父節點ID，將當前節點加入到父節點的子節點列表中
                else if (menuDict.TryGetValue(node.ParentId, out var parentNode))
                {
                    parentNode.Children.Add(node);
                }
            }

            return rootNodes;
        }

        /// <summary>
        /// 檢查用戶是否擁有特定選單的權限
        /// </summary>
        /// <param name="verifyMenuName">要檢查的選單鍵值</param>
        /// <param name="userId">用戶ID</param>
        /// <returns>用戶是否擁有權限</returns>
        public async Task<bool> VerifyMenuAsync(string verifyMenuName, string userId)
        {
            // 使用三表聯接的LINQ查詢檢查用戶權限
            var query = _context.Common_Users
                .Where(user => user.UserId == userId)
                .Join(_context.Common_RolesPermissions,
                    user => user.RolesId,
                    permission => permission.RolesId,
                    (user, permission) => permission)
                .Join(_context.Common_SystemMenu,
                    permission => permission.SystemMenuId,
                    menu => menu.SystemMenuId,
                    (permission, menu) => menu)
                .Where(menu => menu.Key == verifyMenuName);

            // 顯示生成的 SQL 語句
            string sqlQuery = query.ToQueryString();
            Console.WriteLine(sqlQuery);
            Console.WriteLine("sqlQuery");
            // 然後再執行實際查詢
            var hasPermission = await query.AnyAsync();
            return hasPermission;
        }

        /// <summary>
        /// 取得所有有權限的選單ID（包含自動補齊的父節點）
        /// 優化：支援末節點權限儲存策略，自動補齊父節點以完整顯示選單樹
        /// </summary>
        /// <param name="RolesId">角色ID</param>
        /// <returns>完整的權限選單ID列表（包含父節點）</returns>
        private async Task<List<string>> GetAllPermittedMenuIdsAsync(string RolesId)
        {
            // 1. 取得角色的直接權限選單ID（可能只包含末節點）
            var rolePermissions = await _context.Common_RolesPermissions
                .Where(rp => rp.RolesId == RolesId && !rp.IsDeleted)
                .Select(rp => rp.SystemMenuId)
                .ToListAsync();

            if (rolePermissions.Count == 0)
            {
                return new List<string>();
            }

            // 2. 查詢所有選單的父子關係
            var allMenus = await _context.Common_SystemMenu
                .Where(m => !m.IsDeleted)
                .Select(m => new { m.SystemMenuId, m.ParentId, m.Label })
                .ToListAsync();

            // 3. 建立選單ID到父節點的映射
            var menuToParentMap = allMenus.ToDictionary(m => m.SystemMenuId, m => m.ParentId);

            // 4. 使用廣度優先搜尋，自動補齊所有父節點
            var allParentIds = new HashSet<string>();
            var processedIds = new HashSet<string>();
            var queue = new Queue<string>(rolePermissions);

            Console.WriteLine($"[SystemMenuService] 開始處理角色 {RolesId} 的權限補齊");
            Console.WriteLine($"[SystemMenuService] 原始權限數量: {rolePermissions.Count}");

            while (queue.Count > 0)
            {
                var currentId = queue.Dequeue();

                if (processedIds.Contains(currentId))
                    continue;

                processedIds.Add(currentId);

                // 從映射中取得父節點ID
                var parentId = menuToParentMap.GetValueOrDefault(currentId);

                if (!string.IsNullOrEmpty(parentId) && !allParentIds.Contains(parentId))
                {
                    allParentIds.Add(parentId);
                    queue.Enqueue(parentId);

                    // 記錄補齊的父節點（用於調試）
                    var parentMenu = allMenus.FirstOrDefault(m => m.SystemMenuId == parentId);
                    Console.WriteLine($"[SystemMenuService] 自動補齊父節點: {parentId} ({parentMenu?.Label})");
                }
            }

            // 5. 合併直接權限和自動補齊的父節點
            var allPermittedMenuIds = rolePermissions
                .Concat(allParentIds)
                .Distinct()
                .ToList();

            Console.WriteLine($"[SystemMenuService] 補齊後權限數量: {allPermittedMenuIds.Count}");
            Console.WriteLine($"[SystemMenuService] 自動補齊父節點數量: {allParentIds.Count}");

            return allPermittedMenuIds;
        }

        /// <summary>
        /// 識別選單樹中的末節點（葉子節點）
        /// 用於支援末節點權限儲存策略
        /// </summary>
        /// <returns>所有末節點的ID列表</returns>
        public async Task<List<string>> GetLeafMenuIdsAsync()
        {
            // 查詢所有選單
            var allMenus = await _context.Common_SystemMenu
                .Where(m => !m.IsDeleted)
                .Select(m => new { m.SystemMenuId, m.ParentId, m.Label, m.IsMenu })
                .ToListAsync();

            // 找出所有有子節點的選單ID
            var parentMenuIds = allMenus
                .Where(m => !string.IsNullOrEmpty(m.ParentId))
                .Select(m => m.ParentId)
                .Distinct()
                .ToHashSet();

            // 末節點 = 所有選單 - 有子節點的選單
            var leafMenuIds = allMenus
                .Where(m => !parentMenuIds.Contains(m.SystemMenuId))
                .Select(m => m.SystemMenuId)
                .ToList();

            Console.WriteLine($"[SystemMenuService] 識別末節點數量: {leafMenuIds.Count}");
            Console.WriteLine($"[SystemMenuService] 總選單數量: {allMenus.Count}");

            return leafMenuIds;
        }
    }
}
