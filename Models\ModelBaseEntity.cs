using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
namespace FAST_ERP_Backend.Models;

/// <summary> Model基底類別 </summary>
/// <remarks> 用於資料庫實體類別的基底類別，包含記錄增刪修的欄位屬性 </remarks>
public abstract class ModelBaseEntity
{
    /// <summary> 新增時間 </summary>
    [Comment("新增時間")]
    [Column(TypeName = "bigint")]
    public long? CreateTime { get; set; }

    /// <summary> 新增者編號 </summary>
    [Comment("新增者編號")]
    [Column(TypeName = "nvarchar(100)")]
    public string? CreateUserId { get; set; }

    /// <summary> 更新時間 </summary>
    [Comment("更新時間")]
    [Column(TypeName = "bigint")]
    public long? UpdateTime { get; set; }

    /// <summary> 更新者編號 </summary>
    [Comment("更新者編號")]
    [Column(TypeName = "nvarchar(100)")]
    public string? UpdateUserId { get; set; }

    /// <summary> 刪除時間 </summary>
    [Comment("刪除時間")]
    [Column(TypeName = "bigint")]
    public long? DeleteTime { get; set; }

    /// <summary> 刪除者編號 </summary>
    [Comment("刪除者編號")]
    [Column(TypeName = "nvarchar(100)")]
    public string? DeleteUserId { get; set; }

    /// <summary> 刪除狀態 </summary>
    [Comment("刪除狀態")]
    [Column(TypeName = "bit")]
    [DefaultValue(false)]
    public bool IsDeleted { get; set; }
}

/// <summary> Model基底類別 DTO </summary>
public abstract class ModelBaseEntityDTO
{
    /// <summary> 新增時間 </summary>
    public long? CreateTime { get; set; }

    /// <summary> 新增者編號 </summary>
    public string? CreateUserId { get; set; }

    /// <summary> 更新時間 </summary>
    public long? UpdateTime { get; set; }

    /// <summary> 更新者編號 </summary>
    public string? UpdateUserId { get; set; }
    [JsonIgnore]
    /// <summary> 刪除時間 </summary>
    public long? DeleteTime { get; set; }
    [JsonIgnore]
    /// <summary> 刪除者編號 </summary>
    public string? DeleteUserId { get; set; }
    [JsonIgnore]
    /// <summary> 刪除狀態 </summary>
    public bool IsDeleted { get; set; }
}

/// <summary>
/// API 回應模型
/// </summary>
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public T Data { get; set; }
    public PaginateInfo Paginate { get; set; }
    
    [JsonIgnore]
    public int HttpCode { get; set; } = 200;

    public static ApiResponse<T> SuccessResult(T data, string message = "操作成功")
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Message = message,
            HttpCode = 200
        };
    }

    public static ApiResponse<T> ErrorResult(string message, int httpCode = 500)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Data = default(T),
            Message = message,
            HttpCode = httpCode
        };
    }
}

/// <summary>
/// 分頁資訊
/// </summary>
public class PaginateInfo
{
    public int TotalCount { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
}