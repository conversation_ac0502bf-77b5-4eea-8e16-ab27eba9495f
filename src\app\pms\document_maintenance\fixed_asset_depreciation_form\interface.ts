import { Depreciation } from "@/services/pms/depreciationFormDetailService";
import { Asset } from "@/services/pms/assetService";
import { Department } from "@/services/common/departmentService";
import { AssetAccount } from "@/services/pms/assetAccountService";
import { AssetSubAccount } from "@/services/pms/assetSubAccountService";
import { AssetStatus } from "@/services/pms/assetStatusService";
import { DepreciationFormStatistics } from "@/services/pms/depreciationFormService";
import { FormInstance } from "antd";

// 定義財產折舊單的查詢參數介面
export interface AssetDepreciationFormQuery {
    keyword?: string;
    assetAccountId?: string;
    assetSubAccountId?: string;
    departmentId?: string;
    assetStatusId?: string;
    year?: number;
    month?: number;
    assetId?: string;
}

// 定義折舊方法
export enum DepreciationMethod {
    StraightLine = "直線法",
    DecliningBalance = "餘額遞減法",
}

// 狀態顏色映射
export const STATUS_COLORS: Record<string, string> = {
    "已計算": "green",
    "未計算": "orange",
    "調整": "blue",
    "錯誤": "red",
    "草稿": "gray",
    "待審核": "orange",
    "已核准": "green",
    "已駁回": "red",
    "已執行": "blue"
};

// 折舊單狀態選項
export const DEPRECIATION_STATUS_OPTIONS = [
    { label: "草稿", value: "DRAFT", color: "gray" },
    { label: "待審核", value: "PENDING", color: "orange" },
    { label: "已核准", value: "APPROVED", color: "green" },
    { label: "已駁回", value: "REJECTED", color: "red" },
    { label: "已執行", value: "EXECUTED", color: "blue" },
];

// 表單初始值
export const formInitialValues = {
    depreciationYear: new Date().getFullYear(),
    depreciationMonth: new Date().getMonth() + 1,
    depreciationMethod: DepreciationMethod.StraightLine,
    isAdjustment: false,
    adjustmentReason: "",
    notes: ""
};

// 折舊模擬結果
export interface DepreciationSimulationResult {
    assetId: string;
    assetNo: string;
    assetName: string;
    originalAmount: number;
    accumulatedDepreciation: number;
    currentDepreciation: number;
    remainingValue: number;
    serviceLifeRemaining: number;
    depreciationRate: number;
    departmentId: string;
    departmentName: string;
    assetAccountId: string;
    assetAccountName: string;
    assetSubAccountId: string;
    assetSubAccountName: string;
}

// 表單Props
export interface FormProps {
    editingDepreciation: Depreciation | null;
    isViewMode: boolean;
    selectedAsset: Asset | null;
    onCancel: () => void;
    onSuccess: (data: Depreciation) => void;
    departments: Department[];
    assetAccounts: AssetAccount[];
    assetSubAccounts: AssetSubAccount[];
    assetStatusOptions: AssetStatus[];
    depreciationMethods: string[];
    isMobile?: boolean;
    form: FormInstance;
} 