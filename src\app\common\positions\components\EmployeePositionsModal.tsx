"use client";

import React, { useState, useEffect } from "react";
import {
    Modal,
    Table,
    Button,
    Space,
    message,
    Badge,
    Tag,
    Empty,
    Tooltip,
    Typography,
    Card,
} from "antd";
import {
    UserOutlined,
    EditOutlined,
    EyeOutlined,
    TeamOutlined,
    CalendarOutlined,
    DeleteOutlined,
} from "@ant-design/icons";
import {
    PositionOwner,
    getPositionOwners,
    deletePositionOwner,
} from "@/services/common/positionService";
import { useOptions } from "@/contexts/OptionsContext";
import { useAuth } from "@/contexts/AuthContext";
import PositionOwnerEditModal from "./PositionOwnerEditModal";
import dayjs from "dayjs";

const { Text } = Typography;
const { confirm } = Modal;

interface EmployeePositionsModalProps {
    visible: boolean;
    onClose: () => void;
    userId?: string;
    onDataChange?: () => void;
}

const EmployeePositionsModal: React.FC<EmployeePositionsModalProps> = ({
    visible,
    onClose,
    userId,
    onDataChange,
}) => {
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<PositionOwner[]>([]);
    const [editModalVisible, setEditModalVisible] = useState(false);
    const [selectedPositionOwnerId, setSelectedPositionOwnerId] = useState<string>("");
    const [editMode, setEditMode] = useState<"add" | "edit" | "view">("edit");

    const { users } = useOptions() as any;
    const { user } = useAuth();

    // 載入員工職務資料
    const loadEmployeePositions = async () => {
        if (!userId) return;

        try {
            setLoading(true);
            const response = await getPositionOwners();

            if (response.success && response.data) {
                // 篩選該員工的所有職務
                const employeePositions = response.data.filter(
                    (owner: PositionOwner) => owner.userId === userId
                );

                // 按職務名稱和角色類型排序
                employeePositions.sort((a: PositionOwner, b: PositionOwner) => {
                    if (a.positionName !== b.positionName) {
                        return a.positionName.localeCompare(b.positionName);
                    }
                    // 主辦排在協辦前面
                    if (a.roleType === "主辦" && b.roleType === "協辦") return -1;
                    if (a.roleType === "協辦" && b.roleType === "主辦") return 1;
                    return a.orderNo - b.orderNo;
                });

                setData(employeePositions);
            } else {
                message.error("載入員工職務資料失敗");
                setData([]);
            }
        } catch (error) {
            console.error("載入員工職務資料錯誤:", error);
            message.error("載入員工職務資料失敗");
            setData([]);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (visible && userId) {
            loadEmployeePositions();
        }
    }, [visible, userId]);

    const handleEdit = (record: PositionOwner) => {
        setSelectedPositionOwnerId(record.positionOwnerId);
        setEditMode("edit");
        setEditModalVisible(true);
    };

    const handleView = (record: PositionOwner) => {
        setSelectedPositionOwnerId(record.positionOwnerId);
        setEditMode("view");
        setEditModalVisible(true);
    };

    const handleDelete = (record: PositionOwner) => {
        confirm({
            title: "確認刪除",
            content: (
                <span>
                    確定要刪除{record.roleType}
                    <span style={{ fontWeight: "bold", color: "#ff4d4f" }}>
                        {record.departmentName
                            ? `${record.departmentName} - ${record.userName}`
                            : record.userName}
                    </span>
                    的
                    <span style={{ fontWeight: "bold", color: "#ff4d4f" }}>
                        {record.positionName}
                    </span>
                    職務資料嗎？
                </span>
            ),
            okText: "確定",
            cancelText: "取消",
            onOk: async () => {
                try {
                    // 建立要刪除的資料，設定 isDeleted = true
                    const deleteData: PositionOwner = {
                        ...record,
                        updateTime: Date.now(),
                        updateUserId: user?.userId || "",
                    };

                    const response = await deletePositionOwner(deleteData);
                    if (response.success) {
                        message.success("刪除職務資料成功");
                        loadEmployeePositions();
                        if (onDataChange) {
                            onDataChange();
                        }
                    } else {
                        message.error(response.message || "刪除職務資料失敗");
                    }
                } catch (error) {
                    console.error("刪除職務資料錯誤:", error);
                    message.error("刪除職務資料失敗");
                }
            },
        });
    };


    const handleEditSuccess = () => {
        loadEmployeePositions();
        if (onDataChange) {
            onDataChange();
        }
    };

    const handleClose = () => {
        setData([]);
        onClose();
    };

    // 計算任職期間
    const calculateTenure = (startDate?: string | number, endDate?: string | number): string => {
        if (!startDate) return "";

        // 處理 timestamp（數字）或字串格式
        const start = typeof startDate === 'number'
            ? dayjs.unix(startDate)
            : dayjs(startDate, "YYYY-MM-DD");
        const end = endDate
            ? (typeof endDate === 'number' ? dayjs.unix(endDate) : dayjs(endDate, "YYYY-MM-DD"))
            : dayjs();

        if (!start.isValid() || !end.isValid() || end.isBefore(start)) return "";

        const years = end.diff(start, "year");
        const afterYears = start.add(years, "year");
        const days = end.diff(afterYears, "day");

        const parts: string[] = [];
        if (years > 0) parts.push(`${years}年`);
        if (days > 0) parts.push(`${days}天`);

        return parts.length > 0 ? `(${parts.join("")})` : "";
    };

    // 判斷是否為現任
    const isCurrentPosition = (endDate?: string | number): boolean => {
        if (!endDate) return true;
        const end = typeof endDate === 'number'
            ? dayjs.unix(endDate)
            : dayjs(endDate, "YYYY-MM-DD");
        return end.isAfter(dayjs());
    };

    const selectedUser = users?.find((u: any) => u.userId === userId);

    const columns = [
        {
            title: "職務名稱",
            dataIndex: "positionName",
            key: "positionName",
            render: (text: string) => <Text strong>{text}</Text>,
        },
        {
            title: "角色類型",
            dataIndex: "roleType",
            key: "roleType",
            width: 100,
            align: "center" as const,
            render: (roleType: string) => (
                <Badge
                    status={roleType === "主辦" ? "processing" : "default"}
                    text={roleType}
                />
            ),
        },
        {
            title: "排序",
            dataIndex: "orderNo",
            key: "orderNo",
            width: 80,
            align: "center" as const,
        },
        {
            title: "任職期間",
            key: "tenure",
            width: 200,
            render: (_: any, record: PositionOwner) => {
                const isCurrent = isCurrentPosition(record.endDate);
                const tenure = calculateTenure(record.startDate, record.endDate);

                return (
                    <Space direction="vertical" size="small">
                        <div>
                            <CalendarOutlined style={{ marginRight: 4 }} />
                            {(typeof record.startDate === 'number'
                                ? dayjs.unix(record.startDate).format("YYYY-MM-DD")
                                : record.startDate) || "未設定"} ~ {
                                record.endDate
                                    ? (typeof record.endDate === 'number'
                                        ? dayjs.unix(record.endDate).format("YYYY-MM-DD")
                                        : record.endDate)
                                    : "現任"
                            }
                        </div>
                        {tenure && (
                            <Text type="secondary" style={{ fontSize: "12px" }}>
                                {tenure}
                            </Text>
                        )}
                    </Space>
                );
            },
        },
        {
            title: "狀態",
            key: "status",
            width: 100,
            align: "center" as const,
            render: (_: any, record: PositionOwner) => {
                const isCurrent = isCurrentPosition(record.endDate);
                return (
                    <Tag color={isCurrent ? "green" : "red"}>
                        {isCurrent ? "現任" : "已卸任"}
                    </Tag>
                );
            },
        },
        {
            title: "操作",
            key: "actions",
            width: 160,
            align: "center" as const,
            render: (_: any, record: PositionOwner) => (
                <Space>
                    <Tooltip title="檢視詳情">
                        <Button
                            type="link"
                            size="small"
                            icon={<EyeOutlined />}
                            onClick={() => handleView(record)}
                        />
                    </Tooltip>
                    <Tooltip title="編輯">
                        <Button
                            type="link"
                            size="small"
                            icon={<EditOutlined />}
                            onClick={() => handleEdit(record)}
                        />
                    </Tooltip>
                    <Tooltip title="刪除職務資料">
                        <Button
                            type="link"
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => handleDelete(record)}
                        />
                    </Tooltip>
                </Space>
            ),
        },
    ];

    return (
        <>
            <Modal
                title={
                    <Space>
                        <UserOutlined />
                        {selectedUser ? (
                            <>
                                {selectedUser.serviceDepartmentName && (
                                    <Text type="secondary">{selectedUser.serviceDepartmentName} - </Text>
                                )}
                                <Text strong>{selectedUser.name}</Text>
                                <Text type="secondary"> - 職務清單</Text>
                            </>
                        ) : (
                            "員工職務清單"
                        )}
                    </Space>
                }
                open={visible}
                onCancel={handleClose}
                footer={[
                    <Button key="close" onClick={handleClose}>
                        關閉
                    </Button>,
                ]}
                width={900}
                destroyOnClose
            >
                <div style={{ marginBottom: 16 }}>
                    {selectedUser && (
                        <Card size="small">
                            <Space>
                                <UserOutlined />
                                <Text>員工：{selectedUser.name}</Text>
                                {selectedUser.serviceDepartmentName && (
                                    <>
                                        <TeamOutlined />
                                        <Text>部門：{selectedUser.serviceDepartmentName}</Text>
                                    </>
                                )}
                                {selectedUser.altPhone && (
                                    <Text type="secondary">分機：{selectedUser.altPhone}</Text>
                                )}
                            </Space>
                        </Card>
                    )}
                </div>

                <Table
                    columns={columns}
                    dataSource={data}
                    rowKey="positionOwnerId"
                    loading={loading}
                    scroll={{ x: 800 }}
                    size="small"
                    locale={{
                        emptyText: (
                            <Empty
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description={
                                    <span style={{ color: "#999" }}>
                                        該員工暫無職務資料
                                    </span>
                                }
                            />
                        ),
                    }}
                    pagination={{
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) =>
                            `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
                        pageSizeOptions: ["10", "20", "50"],
                        size: "small",
                    }}
                />
            </Modal>

            {/* 職務編輯 Modal */}
            <PositionOwnerEditModal
                visible={editModalVisible}
                onClose={() => setEditModalVisible(false)}
                onSuccess={handleEditSuccess}
                mode={editMode}
                positionOwnerId={selectedPositionOwnerId}
            />
        </>
    );
};

export default EmployeePositionsModal;
