/**
 * 聯絡人選擇器組件
 * 
 * 提供快速搜尋和選擇聯絡人的功能
 * 支援自動完成、模態框選擇等多種模式
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

"use client";

import React, { useState, useEffect } from 'react';
import { 
  AutoComplete, 
  Button, 
  Space, 
  Avatar, 
  Typography, 
  Tag,
  Tooltip,
  Input
} from 'antd';
import { 
  UserOutlined, 
  SearchOutlined, 
  PlusOutlined,
  MailOutlined,
  PhoneOutlined
} from '@ant-design/icons';

// Services
import { Contact, searchContacts } from '@/services/ims/ContactService';

// 共享組件
import ContactListModal from '../ContactListModal';

// 響應式工具
import { useScreenSize } from './ResponsiveModalConfig';

const { Text } = Typography;

export interface ContactSelectorProps {
  /** 當前選中的聯絡人 */
  value?: Contact | null;
  /** 選擇變更回調 */
  onChange?: (contact: Contact | null) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 佔位符文字 */
  placeholder?: string;
  /** 是否顯示新增按鈕 */
  showAddButton?: boolean;
  /** 新增聯絡人回調 */
  onAddContact?: () => void;
  /** 自定義樣式類名 */
  className?: string;
  /** 是否允許清除 */
  allowClear?: boolean;
}

/**
 * 聯絡人選擇器組件
 * 
 * 特性：
 * - 自動完成搜尋功能
 * - 模態框選擇模式
 * - 響應式設計
 * - 聯絡人資訊預覽
 */
const ContactSelector: React.FC<ContactSelectorProps> = ({
  value,
  onChange,
  disabled = false,
  placeholder = "搜尋或選擇聯絡人",
  showAddButton = true,
  onAddContact,
  className = '',
  allowClear = true
}) => {
  const { isMobile } = useScreenSize();
  const [searchText, setSearchText] = useState('');
  const [searchOptions, setSearchOptions] = useState<any[]>([]);
  const [searching, setSearching] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  // 搜尋聯絡人
  const handleSearch = async (searchValue: string) => {
    if (!searchValue.trim()) {
      setSearchOptions([]);
      return;
    }

    setSearching(true);
    try {
      const response = await searchContacts(searchValue);
      if (response.success && response.data) {
        const options = response.data.map((contact: Contact) => ({
          value: contact.contactID,
          label: (
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Avatar size="small" icon={<UserOutlined />} />
              <div style={{ flex: 1 }}>
                <div style={{ fontWeight: 500 }}>{contact.name}</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  <Space size={4}>
                    {contact.position && <Text type="secondary">{contact.position}</Text>}
                    {contact.company && <Text type="secondary">@ {contact.company}</Text>}
                  </Space>
                </div>
                <div style={{ fontSize: '11px', color: '#999' }}>
                  <Space size={8}>
                    {contact.email && (
                      <span><MailOutlined /> {contact.email}</span>
                    )}
                    {contact.phone && (
                      <span><PhoneOutlined /> {contact.phone}</span>
                    )}
                  </Space>
                </div>
              </div>
              <Tag color={contact.isActive ? 'success' : 'default'}>
                {contact.isActive ? '啟用' : '停用'}
              </Tag>
            </div>
          ),
          contact: contact
        }));
        setSearchOptions(options);
      }
    } catch (error) {
      console.error('搜尋聯絡人失敗:', error);
    } finally {
      setSearching(false);
    }
  };

  // 選擇聯絡人
  const handleSelect = (contactId: string, option: any) => {
    const selectedContact = option.contact;
    onChange?.(selectedContact);
    setSearchText(selectedContact.name);
  };

  // 清除選擇
  const handleClear = () => {
    onChange?.(null);
    setSearchText('');
    setSearchOptions([]);
  };

  // 從模態框選擇
  const handleModalSelect = (contact: Contact) => {
    onChange?.(contact);
    setSearchText(contact.name);
    setModalVisible(false);
  };

  // 同步顯示值
  useEffect(() => {
    if (value) {
      setSearchText(value.name);
    } else {
      setSearchText('');
    }
  }, [value]);

  // 渲染當前選中的聯絡人資訊
  const renderSelectedContact = () => {
    if (!value) return null;

    return (
      <div style={{ 
        padding: '8px 12px', 
        border: '1px solid #d9d9d9', 
        borderRadius: '6px',
        backgroundColor: '#fafafa',
        marginTop: 8
      }}>
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <div>
            <div style={{ fontWeight: 500 }}>{value.name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              <Space size={4}>
                {value.position && <Text type="secondary">{value.position}</Text>}
                {value.company && <Text type="secondary">@ {value.company}</Text>}
              </Space>
            </div>
          </div>
          <Tag color={value.isActive ? 'success' : 'default'}>
            {value.isActive ? '啟用' : '停用'}
          </Tag>
        </Space>
      </div>
    );
  };

  return (
    <div className={`contact-selector ${className}`}>
      <Space.Compact style={{ width: '100%' }}>
        <AutoComplete
          style={{ flex: 1 }}
          value={searchText}
          options={searchOptions}
          onSearch={handleSearch}
          onSelect={handleSelect}
          placeholder={placeholder}
          disabled={disabled}
          allowClear={allowClear}
          onChange={(value) => {
            setSearchText(value);
            if (!value && allowClear) {
              handleClear();
            }
          }}
          dropdownStyle={{ maxHeight: 300 }}
          notFoundContent={searching ? "搜尋中..." : "無搜尋結果"}
        />
        
        <Tooltip title="瀏覽所有聯絡人">
          <Button
            icon={<SearchOutlined />}
            onClick={() => setModalVisible(true)}
            disabled={disabled}
          />
        </Tooltip>
        
        {showAddButton && onAddContact && (
          <Tooltip title="新增聯絡人">
            <Button
              icon={<PlusOutlined />}
              onClick={onAddContact}
              disabled={disabled}
              type="primary"
              ghost
            />
          </Tooltip>
        )}
      </Space.Compact>

      {/* 顯示選中的聯絡人資訊 */}
      {value && renderSelectedContact()}

      {/* 聯絡人選擇模態框 */}
      <ContactListModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onEdit={() => {}} // 在選擇模式下不需要編輯功能
        onSelect={handleModalSelect}
        selectMode={true}
      />
    </div>
  );
};

export default ContactSelector;
