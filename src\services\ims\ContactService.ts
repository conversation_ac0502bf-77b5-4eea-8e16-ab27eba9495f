// =========================================================================================
// Service for Contact Management - 聯絡人管理服務
// =========================================================================================

import { ModelBaseEntityDTO } from '@/services/ims/partner';
import { apiEndpoints, ApiResponse } from "@/config/api";
import { httpClient } from "../http";
import { logger, SYMBOLS, createContextLogger } from '@/utils/logger';
import {
  validateContactBasicData,
  sanitizeContactData,
  logHelperUsage
} from '@/app/ims/utils/contactHelpers';

// 創建上下文日誌器
const contactServiceLogger = createContextLogger({ module: 'ContactService' });

// =========================================================================================
// 型別定義
// =========================================================================================

/**
 * 聯絡人資料介面
 * 對應後端 ContactDTO 結構
 */
export interface Contact extends ModelBaseEntityDTO {
  contactID: string;
  name: string;
  position?: string;
  email?: string;
  phone?: string;
  isActive: boolean;
  contactType: string;
  department?: string;
  company?: string;
}

// =========================================================================================
// 工具函數
// =========================================================================================

/**
 * 建立空的聯絡人物件
 * 對應後端 ContactDTO 預設值
 */
export const createEmptyContact = (): Partial<Contact> => ({
  contactID: '',
  name: '',
  position: '',
  email: '',
  phone: '',
  isActive: true,
  contactType: '客戶',
  department: '',
  company: '',
});

/**
 * 驗證聯絡人資料（使用共享 Helper）
 */
const validateContactData = (data: any): { isValid: boolean; message?: string } => {
  logHelperUsage('validateContactData', { hasData: !!data });

  const validation = validateContactBasicData(data);

  if (!validation.isValid) {
    return {
      isValid: false,
      message: validation.errors.join(', ')
    };
  }

  // 如果有警告，記錄但不阻止操作
  if (validation.warnings.length > 0) {
    contactServiceLogger.log(SYMBOLS.WARNING, '聯絡人資料驗證警告', {
      warnings: validation.warnings
    });
  }

  return { isValid: true };
};

// =========================================================================================
// 回應正規化
// =========================================================================================

/**
 * 將後端回傳的聯絡人資料正規化為前端 Contact 介面（處理大小寫差異）
 */
const normalizeContact = (input: any): Contact => {
  if (!input || typeof input !== 'object') {
    return {
      contactID: '',
      name: '',
      position: '',
      email: '',
      phone: '',
      isActive: true,
      contactType: '客戶',
      department: '',
      company: '',
      createTime: null,
      createUserId: null,
      updateTime: null,
      updateUserId: null,
      deleteTime: null,
      deleteUserId: null,
      isDeleted: false,
    };
  }

  const anyObj = input as any;
  const contactID = anyObj.contactID ?? anyObj.ContactID ?? anyObj.id ?? '';

  return {
    contactID: contactID,
    name: anyObj.name ?? anyObj.Name ?? '',
    position: anyObj.position ?? anyObj.Position ?? undefined,
    email: anyObj.email ?? anyObj.Email ?? undefined,
    phone: anyObj.phone ?? anyObj.Phone ?? undefined,
    isActive: (anyObj.isActive ?? anyObj.IsActive ?? anyObj.status ?? anyObj.Status ?? true) as boolean,
    contactType: anyObj.contactType ?? anyObj.ContactType ?? '客戶',
    department: anyObj.department ?? anyObj.Department ?? undefined,
    company: anyObj.company ?? anyObj.Company ?? undefined,
    createTime: anyObj.createTime ?? anyObj.CreateTime ?? null,
    createUserId: anyObj.createUserId ?? anyObj.CreateUserId ?? null,
    updateTime: anyObj.updateTime ?? anyObj.UpdateTime ?? null,
    updateUserId: anyObj.updateUserId ?? anyObj.UpdateUserId ?? null,
    deleteTime: anyObj.deleteTime ?? anyObj.DeleteTime ?? null,
    deleteUserId: anyObj.deleteUserId ?? anyObj.DeleteUserId ?? null,
    isDeleted: anyObj.isDeleted ?? anyObj.IsDeleted ?? false,
  } as Contact;
};

// =========================================================================================
// API 服務函數
// =========================================================================================

/**
 * 取得聯絡人列表
 */
export const getContactList = async (): Promise<ApiResponse<Contact[]>> => {
  try {
    contactServiceLogger.log(SYMBOLS.LOADING, '取得聯絡人列表');
    
    const response = await httpClient<Contact[]>(apiEndpoints.getContactList);
    
    if (response.success) {
      contactServiceLogger.log(SYMBOLS.SUCCESS, '取得聯絡人列表成功', { 
        count: response.data?.length || 0 
      });
    } else {
      contactServiceLogger.log(SYMBOLS.WARNING, '取得聯絡人列表失敗', { 
        message: response.message 
      });
    }
    
    return response;
  } catch (error) {
    contactServiceLogger.log(SYMBOLS.ERROR, '取得聯絡人列表失敗', error);
    throw error;
  }
};

/**
 * 根據ID取得聯絡人
 */
export const getContactById = async (contactId: string): Promise<ApiResponse<Contact>> => {
  try {
    contactServiceLogger.log(SYMBOLS.LOADING, '取得聯絡人詳細資料', { contactId });
    
    const response = await httpClient<Contact>(`${apiEndpoints.getContact}/${contactId}`);
    
    if (response.success) {
      contactServiceLogger.log(SYMBOLS.SUCCESS, '取得聯絡人詳細資料成功', { contactId });
    } else {
      contactServiceLogger.log(SYMBOLS.WARNING, '取得聯絡人詳細資料失敗', { 
        contactId, 
        message: response.message 
      });
    }
    
    return response;
  } catch (error) {
    contactServiceLogger.log(SYMBOLS.ERROR, '取得聯絡人詳細資料失敗', { contactId, error });
    throw error;
  }
};

/**
 * 新增聯絡人
 */
export const addContact = async (contactData: Partial<Contact>): Promise<ApiResponse<Contact>> => {
  try {
    contactServiceLogger.log(SYMBOLS.LOADING, '新增聯絡人', { name: contactData.name });

    // 資料清理和正規化
    const sanitizedData = sanitizeContactData(contactData);
    logHelperUsage('sanitizeContactData', { originalData: contactData, sanitizedData });

    // 資料驗證
    const validation = validateContactData(sanitizedData);
    if (!validation.isValid) {
      contactServiceLogger.log(SYMBOLS.WARNING, '新增聯絡人資料驗證失敗', {
        message: validation.message
      });
      throw new Error(validation.message);
    }

    const response = await httpClient<Contact>(
      apiEndpoints.addContact,
      {
        method: 'POST',
        body: JSON.stringify(sanitizedData)
      }
    );
    
    if (response.success) {
      const normalized = response.data ? normalizeContact(response.data) : undefined;
      contactServiceLogger.log(SYMBOLS.SUCCESS, '新增聯絡人成功', { 
        contactId: normalized?.contactID,
        name: contactData.name
      });
      // 回傳正規化後的資料，避免大小寫差異造成前端取不到 contactID
      return { ...response, data: normalized as any };
    } else {
      contactServiceLogger.log(SYMBOLS.WARNING, '新增聯絡人失敗', { 
        message: response.message 
      });
      return response;
    }
  } catch (error) {
    contactServiceLogger.log(SYMBOLS.ERROR, '新增聯絡人失敗', error);
    throw error;
  }
};

/**
 * 更新聯絡人
 */
export const updateContact = async (contactData: Contact): Promise<ApiResponse<Contact>> => {
  try {
    contactServiceLogger.log(SYMBOLS.LOADING, '更新聯絡人', {
      contactId: contactData.contactID,
      name: contactData.name
    });

    // 資料清理和正規化
    const sanitizedData = sanitizeContactData(contactData) as Contact;
    sanitizedData.contactID = contactData.contactID; // 保持原始 ID
    logHelperUsage('sanitizeContactData', { originalData: contactData, sanitizedData });

    // 資料驗證
    const validation = validateContactData(sanitizedData);
    if (!validation.isValid) {
      contactServiceLogger.log(SYMBOLS.WARNING, '更新聯絡人資料驗證失敗', {
        message: validation.message
      });
      throw new Error(validation.message);
    }
    
    const response = await httpClient<Contact>(
      `${apiEndpoints.editContact}`,
      {
        method: 'PUT',
        body: JSON.stringify(sanitizedData)
      }
    );
    
    if (response.success) {
      const normalized = response.data ? normalizeContact(response.data) : undefined;
      contactServiceLogger.log(SYMBOLS.SUCCESS, '更新聯絡人成功', { 
        contactId: normalized?.contactID ?? contactData.contactID
      });
      return { ...response, data: normalized as any };
    } else {
      contactServiceLogger.log(SYMBOLS.WARNING, '更新聯絡人失敗', { 
        contactId: contactData.contactID,
        message: response.message 
      });
      return response;
    }
  } catch (error) {
    contactServiceLogger.log(SYMBOLS.ERROR, '更新聯絡人失敗', { 
      contactId: contactData.contactID, 
      error 
    });
    throw error;
  }
};

/**
 * 刪除聯絡人
 */
export const deleteContact = async (contactId: string): Promise<ApiResponse<void>> => {
  try {
    contactServiceLogger.log(SYMBOLS.LOADING, '刪除聯絡人', { contactId });
    
    const response = await httpClient<void>(`${apiEndpoints.deleteContact}/${contactId}`, {
      method: 'DELETE'
    });
    
    if (response.success) {
      contactServiceLogger.log(SYMBOLS.SUCCESS, '刪除聯絡人成功', { contactId });
    } else {
      contactServiceLogger.log(SYMBOLS.WARNING, '刪除聯絡人失敗', { 
        contactId,
        message: response.message 
      });
    }
    
    return response;
  } catch (error) {
    contactServiceLogger.log(SYMBOLS.ERROR, '刪除聯絡人失敗', { contactId, error });
    throw error;
  }
};

/**
 * 搜尋聯絡人
 */
export const searchContacts = async (keyword: string): Promise<ApiResponse<Contact[]>> => {
  try {
    contactServiceLogger.log(SYMBOLS.LOADING, '搜尋聯絡人', { keyword });
    
    const response = await httpClient<Contact[]>(
      `${apiEndpoints.searchContacts}?search=${encodeURIComponent(keyword)}`
    );
    
    if (response.success) {
      contactServiceLogger.log(SYMBOLS.SUCCESS, '搜尋聯絡人成功', { 
        keyword,
        count: response.data?.length || 0 
      });
    } else {
      contactServiceLogger.log(SYMBOLS.WARNING, '搜尋聯絡人失敗', { 
        keyword,
        message: response.message 
      });
    }
    
    return response;
  } catch (error) {
    contactServiceLogger.log(SYMBOLS.ERROR, '搜尋聯絡人失敗', { keyword, error });
    throw error;
  }
};
