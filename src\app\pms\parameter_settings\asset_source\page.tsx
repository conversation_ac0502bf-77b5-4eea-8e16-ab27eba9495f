"use client";
/* 財產來源
    /app/pms/parameter_settings/asset_source/page.tsx
  功能說明
    1. 選擇財產來源
    2. 新增財產來源
    3. 編輯財產來源
    4. 刪除財產來源
*/
import React, { useEffect, useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Tag,
  List,
  Typography,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import {
  AssetSource,
  getAssetSources,
  createAssetSource,
  updateAssetSource,
  deleteAssetSource,
} from "@/services/pms/assetSourceService";
import { notifySuccess, notifyError } from "@/utils/notification";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import {
  getDepartments,
  Department,
} from "@/services/common/departmentService";
import { useAuth } from "@/contexts/AuthContext";

const { Text } = Typography;

const AssetSourcePage: React.FC = () => {
  const [assetSources, setAssetSources] = useState<AssetSource[]>([]);
  const [filteredSources, setFilteredSources] = useState<AssetSource[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingSource, setEditingSource] = useState<AssetSource | null>(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState("");
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const { user } = useAuth();
  const [isMobile, setIsMobile] = useState(false);

  // 加載資產來源列表
  const loadAssetSources = async () => {
    setLoading(true);
    try {
      const response = await getAssetSources();
      if (response.success && response.data) {
        setAssetSources(response.data);
        setFilteredSources(response.data);
      } else {
        notifyError("獲取資產來源列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取資產來源列表失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  // 加載部門列表
  const loadDepartments = async () => {
    try {
      const response = await getDepartments();
      if (response.success && response.data) {
        setDepartments(response.data);
      } else {
        notifyError("獲取部門列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取部門列表失敗", "請稍後再試");
    }
  };

  // 加載資產來源列表和部門列表
  useEffect(() => {
    loadAssetSources();
    loadDepartments();
  }, []);

  // 處理搜尋
  useEffect(() => {
    // 過濾資產來源
    const filtered = assetSources.filter((source) =>
      source.assetSourceName
        ? source.assetSourceName
            .toLowerCase()
            .includes(searchText.toLowerCase())
        : false
    );
    setFilteredSources(filtered);
  }, [searchText, assetSources]);

  // 檢查手機版
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // 表格列定義
  const columns: ColumnsType<AssetSource> = [
    {
      key: "index",
      width: 80,
      render: (_, __, index) => <span>{index + 1}</span>,
    },
    {
      title: "來源名稱",
      dataIndex: "assetSourceName",
      key: "assetSourceName",
    },
    {
      title: "建立時間",
      dataIndex: "createTime",
      key: "createTime",
      render: (text) => (
        <span>{DateTimeExtensions.formatFromTimestamp(text)}</span>
      ),
    },
    {
      title: "建立者",
      dataIndex: "createUserName",
      key: "createUserName",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
        </Space>
      ),
    },
  ];

  // 處理新增/編輯表單提交
  const handleSubmit = async (values: any) => {
    try {
      if (editingSource) {
        // 更新資產來源
        const response = await updateAssetSource({
          ...values,
          updateUserId: user?.userId,
        });
        if (response.success) {
          notifySuccess("更新成功", "資產來源已更新");
          loadAssetSources();
        } else {
          notifyError("更新失敗", response.message);
        }
      } else {
        // 新增財產來源
        const response = await createAssetSource({
          ...values,
          createUserId: user?.userId,
        });

        if (response.success) {
          notifySuccess("新增成功", "財產來源已新增");
          loadAssetSources();
        } else {
          notifyError("新增失敗", response.message);
        }
      }
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      notifyError("操作失敗", "請稍後再試");
    }
  };

  // 處理編輯
  const handleEdit = (source: AssetSource) => {
    setEditingSource(source);
    form.setFieldsValue(source);
    setIsModalVisible(true);
  };

  // 打開新增財產來源的表單
  const showAddModal = () => {
    setIsAddModalVisible(true);
  };

  // 新增財產來源
  const handleAddSubmit = async (values: any) => {
    try {
      const response = await createAssetSource({
        ...values,
        createUserId: user?.userId,
      });
      if (response.success) {
        notifySuccess("新增成功", "資產來源已新增");
        loadAssetSources();
        setIsAddModalVisible(false);
        form.resetFields();
      } else {
        notifyError("新增失敗", response.message);
      }
    } catch (error) {
      notifyError("操作失敗", "請稍後再試");
    }
  };

  // 手機版列表
  const renderMobileList = () => {
    return (
      <List
        loading={loading}
        dataSource={filteredSources}
        renderItem={(source) => (
          <List.Item
            key={source.assetSourceId}
            style={{
              padding: "12px",
              borderBottom: "1px solid #f0f0f0",
            }}
          >
            <div style={{ width: "100%" }}>
              {/* 標題列 */}
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "8px",
                  gap: "8px",
                }}
              >
                <div
                  style={{
                    width: 24,
                    height: 24,
                    background: "#f0f0f0",
                    borderRadius: "50%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    fontSize: "14px",
                    flexShrink: 0,
                  }}
                >
                  {filteredSources.indexOf(source) + 1}
                </div>
                <Text strong style={{ fontSize: "16px" }}>
                  {source.assetSourceName}
                </Text>
              </div>

              {/* 時間資訊 */}
              <div style={{ marginBottom: "8px" }}>
                <Text type="secondary" style={{ fontSize: "13px" }}>
                  建立時間：
                  {DateTimeExtensions.formatFromTimestamp(source.createTime)}
                </Text>
              </div>

              {/* 建立者 */}
              <div style={{ marginBottom: "8px" }}>
                <Text style={{ fontSize: "14px", color: "#666" }}>
                  建立者：{source.createUserName}
                </Text>
              </div>

              {/* 操作按鈕列 */}
              <div
                style={{
                  display: "flex",
                  gap: "16px",
                  marginTop: "12px",
                }}
              >
                <Button
                  type="link"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(source)}
                  style={{ padding: 0 }}
                >
                  編輯
                </Button>
              </div>
            </div>
          </List.Item>
        )}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showTotal: (total) => `共 ${total} 筆資料`,
          size: "small",
          style: { marginTop: "16px" },
        }}
      />
    );
  };

  return (
    <Card
      title="財產來源"
      styles={{
        body: { padding: isMobile ? "12px" : "24px" },
      }}
    >
      <Space
        style={{
          marginBottom: 16,
          width: "100%",
          flexDirection: isMobile ? "column" : "row",
        }}
      >
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={showAddModal}
          style={{ width: isMobile ? "100%" : "auto" }}
        >
          新增財產來源
        </Button>
        <Input
          placeholder="搜尋資產來源名稱"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: "100%" }}
        />
      </Space>

      {isMobile ? (
        renderMobileList()
      ) : (
        <Table
          columns={columns}
          dataSource={filteredSources}
          rowKey="assetSourceId"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
            showTotal: (total) => `共 ${total} 筆資料`,
          }}
        />
      )}

      <Modal
        title={editingSource ? "編輯財產來源" : "新增財產來源"}
        open={isModalVisible}
        onOk={form.submit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        okText="確認"
        cancelText="取消"
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item name="assetSourceId" label="來源編號：">
            {editingSource ? (
              <span>{editingSource.assetSourceId}</span>
            ) : (
              <Input placeholder="請輸入來源編號" />
            )}
          </Form.Item>

          <Form.Item
            name="assetSourceName"
            label="來源名稱："
            rules={[{ required: true, message: "請輸入來源名稱" }]}
          >
            <Input placeholder="請輸入來源名稱" />
          </Form.Item>

          <Form.Item name="createTime" label="建立時間：">
            <span>
              {DateTimeExtensions.formatFromTimestamp(
                editingSource?.createTime
              )}
            </span>
          </Form.Item>

          <Form.Item name="createUserName" label="建立者：">
            <span>{editingSource?.createUserName}</span>
          </Form.Item>

          <Form.Item
            name="createUserId"
            label="建立者編號："
            style={{ display: "none" }}
          >
            <span>{editingSource?.createUserId}</span>
          </Form.Item>

          <Form.Item name="updateTime" label="更新時間：">
            <span>
              {DateTimeExtensions.formatFromTimestamp(
                editingSource?.updateTime
              )}
            </span>
          </Form.Item>

          {editingSource?.updateUserName && (
            <Form.Item name="updateUserName" label="更新者：">
              <span>{editingSource?.updateUserName}</span>
            </Form.Item>
          )}

          {editingSource?.updateUserId && (
            <Form.Item
              name="updateUserId"
              label="更新者編號："
              style={{ display: "none" }}
            >
              <span>{editingSource?.updateUserId}</span>
            </Form.Item>
          )}
        </Form>
      </Modal>

      <Modal
        title="新增財產來源"
        open={isAddModalVisible}
        onOk={form.submit}
        onCancel={() => {
          setIsAddModalVisible(false);
          form.resetFields();
        }}
        okText="確認"
        cancelText="取消"
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleAddSubmit}>
          <Form.Item
            name="assetSourceName"
            label="來源名稱："
            rules={[{ required: true, message: "請輸入來源名稱" }]}
          >
            <Input placeholder="請輸入來源名稱" />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default AssetSourcePage;
