using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Pms
{
    public partial class AssetService
    {
        /// <summary>
        /// 按部門取得財產統計
        /// </summary>
        /// <param name="departmentIds">部門ID篩選</param>
        /// <returns>部門統計資料</returns>
        public async Task<List<AssetStatisticsByDepartmentDTO>> GetAssetStatisticsByDepartmentAsync(List<string>? departmentIds = null)
        {
            try
            {
                var query = from asset in _context.Set<Asset>()
                            .Where(a => !a.IsDeleted && (a.DeleteTime == null || a.DeleteTime == 0))
                            join dept in _context.Set<Department>()
                                .Where(d => !d.IsDeleted && (d.DeleteTime == null || d.DeleteTime == 0))
                                on asset.DepartmentId equals dept.DepartmentId into deptGroup
                            from dept in deptGroup.DefaultIfEmpty()
                            select new { Asset = asset, Department = dept };

                if (departmentIds != null && departmentIds.Any())
                {
                    query = query.Where(x => departmentIds.Contains(x.Asset.DepartmentId));
                }

                var data = await query.AsNoTracking().ToListAsync();
                var totalValue = data.Sum(x => x.Asset.PurchaseAmount);

                var result = data
                    .GroupBy(x => new { Id = x.Asset.DepartmentId, Name = x.Department?.Name ?? "未分配部門" })
                    .Select(g =>
                    {
                        var assetValue = g.Sum(x => x.Asset.PurchaseAmount);
                        var depreciation = g.Sum(x => x.Asset.AccumulatedDepreciationAmount);
                        return new AssetStatisticsByDepartmentDTO
                        {
                            DepartmentId = g.Key.Id ?? "",
                            DepartmentName = g.Key.Name,
                            AssetCount = g.Count(),
                            TotalValue = assetValue,
                            TotalDepreciation = depreciation,
                            NetValue = assetValue - depreciation,
                            Percentage = totalValue > 0 ? Math.Round((assetValue / totalValue) * 100, 2) : 0
                        };
                    })
                    .OrderByDescending(x => x.TotalValue)
                    .ToList();

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"按部門取得財產統計時發生錯誤: {ex.Message}");
                return new List<AssetStatisticsByDepartmentDTO>();
            }
        }

        /// <summary>
        /// 按狀態取得財產統計
        /// </summary>
        /// <param name="statusIds">狀態ID篩選</param>
        /// <returns>狀態統計資料</returns>
        public async Task<List<AssetStatisticsByStatusDTO>> GetAssetStatisticsByStatusAsync(List<Guid>? statusIds = null)
        {
            try
            {
                var query = from asset in _context.Set<Asset>()
                            .Where(a => !a.IsDeleted && (a.DeleteTime == null || a.DeleteTime == 0))
                            join status in _context.Set<AssetStatus>()
                                .Where(s => !s.IsDeleted)
                                on asset.AssetStatusId equals status.AssetStatusId into statusGroup
                            from status in statusGroup.DefaultIfEmpty()
                            select new { Asset = asset, Status = status };

                if (statusIds != null && statusIds.Any())
                {
                    query = query.Where(x => statusIds.Contains(x.Asset.AssetStatusId));
                }

                var data = await query.AsNoTracking().ToListAsync();
                var totalValue = data.Sum(x => x.Asset.PurchaseAmount);

                var result = data
                    .GroupBy(x => new { Id = x.Asset.AssetStatusId, Name = x.Status?.Name ?? "未知狀態" })
                    .Select(g =>
                    {
                        var assetValue = g.Sum(x => x.Asset.PurchaseAmount);
                        return new AssetStatisticsByStatusDTO
                        {
                            StatusId = g.Key.Id,
                            StatusName = g.Key.Name,
                            AssetCount = g.Count(),
                            TotalValue = assetValue,
                            Percentage = totalValue > 0 ? Math.Round((assetValue / totalValue) * 100, 2) : 0
                        };
                    })
                    .OrderByDescending(x => x.AssetCount)
                    .ToList();

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"按狀態取得財產統計時發生錯誤: {ex.Message}");
                return new List<AssetStatisticsByStatusDTO>();
            }
        }

        /// <summary>
        /// 按科目取得財產統計
        /// </summary>
        /// <param name="accountIds">科目ID篩選</param>
        /// <returns>科目統計資料</returns>
        public async Task<List<AssetStatisticsByAccountDTO>> GetAssetStatisticsByAccountAsync(List<Guid>? accountIds = null)
        {
            try
            {
                var query = from asset in _context.Set<Asset>()
                            .Where(a => !a.IsDeleted && (a.DeleteTime == null || a.DeleteTime == 0))
                            join account in _context.Set<AssetAccount>()
                                .Where(ac => !ac.IsDeleted && (ac.DeleteTime == null || ac.DeleteTime == 0))
                                on asset.AssetAccountId equals account.AssetAccountId into accountGroup
                            from account in accountGroup.DefaultIfEmpty()
                            orderby account.AssetAccountNo
                            select new { Asset = asset, Account = account };

                if (accountIds != null && accountIds.Any())
                {
                    query = query.Where(x => accountIds.Contains(x.Asset.AssetAccountId));
                }

                var data = await query.AsNoTracking().ToListAsync();
                var totalValue = data.Sum(x => x.Asset.PurchaseAmount);

                var result = data
                    .GroupBy(x => new
                    {
                        Id = x.Asset.AssetAccountId,
                        No = x.Account?.AssetAccountNo ?? "未知",
                        Name = x.Account?.AssetAccountName ?? "未分配科目"
                    })
                    .Select(g =>
                    {
                        var assetValue = g.Sum(x => x.Asset.PurchaseAmount);
                        var depreciation = g.Sum(x => x.Asset.AccumulatedDepreciationAmount);
                        return new AssetStatisticsByAccountDTO
                        {
                            AccountId = g.Key.Id,
                            AccountNo = g.Key.No,
                            AccountName = g.Key.Name,
                            AssetCount = g.Count(),
                            TotalValue = assetValue,
                            TotalDepreciation = depreciation,
                            NetValue = assetValue - depreciation,
                            Percentage = totalValue > 0 ? Math.Round((assetValue / totalValue) * 100, 2) : 0
                        };
                    })
                    .OrderByDescending(x => x.TotalValue)
                    .ToList();

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"按科目取得財產統計時發生錯誤: {ex.Message}");
                return new List<AssetStatisticsByAccountDTO>();
            }
        }

        /// <summary>
        /// 按年度取得財產統計
        /// </summary>
        /// <param name="startYear">起始年度</param>
        /// <param name="endYear">結束年度</param>
        /// <returns>年度統計資料</returns>
        public async Task<List<AssetStatisticsByYearDTO>> GetAssetStatisticsByYearAsync(int? startYear = null, int? endYear = null)
        {
            try
            {
                var currentYear = DateTime.Now.Year;
                var start = startYear ?? (currentYear - 4); // 預設顯示過去5年
                var end = endYear ?? currentYear;

                var result = new List<AssetStatisticsByYearDTO>();

                for (int year = start; year <= end; year++)
                {
                    var yearStart = new DateTime(year, 1, 1);
                    var yearEnd = new DateTime(year, 12, 31, 23, 59, 59);
                    var yearStartTs = ((DateTimeOffset)yearStart).ToUnixTimeSeconds();
                    var yearEndTs = ((DateTimeOffset)yearEnd).ToUnixTimeSeconds();

                    // 新增的資產
                    var newAssets = await _context.Set<Asset>()
                        .Where(a => a.CreateTime >= yearStartTs && a.CreateTime <= yearEndTs
                                    && !a.IsDeleted && (a.DeleteTime == null || a.DeleteTime == 0))
                        .AsNoTracking()
                        .ToListAsync();

                    // 報廢的資產
                    var scrappedAssets = await _context.Set<Asset>()
                        .Where(a => a.ScrapDate >= yearStartTs && a.ScrapDate <= yearEndTs
                                    && a.ScrapDate > 0)
                        .AsNoTracking()
                        .ToListAsync();

                    // 年底總資產
                    var yearEndAssets = await _context.Set<Asset>()
                        .Where(a => a.CreateTime <= yearEndTs
                                    && (!a.IsDeleted || (a.DeleteTime != null && a.DeleteTime > yearEndTs))
                                    && (a.ScrapDate == null || a.ScrapDate == 0 || a.ScrapDate > yearEndTs))
                        .AsNoTracking()
                        .ToListAsync();

                    // 年度折舊金額（從折舊明細表計算）
                    var yearDepreciation = await _context.Set<DepreciationFormDetail>()
                        .Where(d => d.DepreciationYear == year && !d.IsDeleted)
                        .AsNoTracking()
                        .SumAsync(d => d.CurrentDepreciation);

                    var yearStat = new AssetStatisticsByYearDTO
                    {
                        Year = year,
                        NewAssets = newAssets.Count,
                        NewAssetsValue = newAssets.Sum(a => a.PurchaseAmount),
                        ScrappedAssets = scrappedAssets.Count,
                        ScrappedAssetsValue = scrappedAssets.Sum(a => a.PurchaseAmount),
                        YearDepreciation = yearDepreciation,
                        YearEndTotalAssets = yearEndAssets.Count,
                        YearEndTotalValue = yearEndAssets.Sum(a => a.PurchaseAmount)
                    };

                    result.Add(yearStat);
                }

                return result.OrderBy(x => x.Year).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"按年度取得財產統計時發生錯誤: {ex.Message}");
                return new List<AssetStatisticsByYearDTO>();
            }
        }

        /// <summary>
        /// 按月度取得財產統計
        /// </summary>
        /// <param name="year">年度</param>
        /// <param name="months">月份數量（預設12個月）</param>
        /// <returns>月度統計資料</returns>
        public async Task<List<AssetStatisticsByMonthDTO>> GetAssetStatisticsByMonthAsync(int? year = null, int months = 12)
        {
            try
            {
                var targetYear = year ?? DateTime.Now.Year;
                var result = new List<AssetStatisticsByMonthDTO>();

                for (int month = 1; month <= Math.Min(months, 12); month++)
                {
                    var monthStart = new DateTime(targetYear, month, 1);
                    var monthEnd = new DateTime(targetYear, month, DateTime.DaysInMonth(targetYear, month), 23, 59, 59);
                    var monthStartTs = ((DateTimeOffset)monthStart).ToUnixTimeSeconds();
                    var monthEndTs = ((DateTimeOffset)monthEnd).ToUnixTimeSeconds();

                    // 當月新增資產
                    var newAssets = await _context.Set<Asset>()
                        .Where(a => a.CreateTime >= monthStartTs && a.CreateTime <= monthEndTs
                                    && !a.IsDeleted && (a.DeleteTime == null || a.DeleteTime == 0))
                        .AsNoTracking()
                        .ToListAsync();

                    // 當月報廢資產
                    var scrappedAssets = await _context.Set<Asset>()
                        .Where(a => a.ScrapDate >= monthStartTs && a.ScrapDate <= monthEndTs
                                    && a.ScrapDate > 0)
                        .AsNoTracking()
                        .ToListAsync();

                    // 當月折舊金額
                    var monthDepreciation = await _context.Set<DepreciationFormDetail>()
                        .Where(d => d.DepreciationYear == targetYear && d.DepreciationMonth == month && !d.IsDeleted)
                        .AsNoTracking()
                        .SumAsync(d => d.CurrentDepreciation);

                    var monthStat = new AssetStatisticsByMonthDTO
                    {
                        Year = targetYear,
                        Month = month,
                        YearMonth = $"{targetYear:D4}-{month:D2}",
                        NewAssets = newAssets.Count,
                        NewAssetsValue = newAssets.Sum(a => a.PurchaseAmount),
                        ScrappedAssets = scrappedAssets.Count,
                        ScrappedAssetsValue = scrappedAssets.Sum(a => a.PurchaseAmount),
                        MonthDepreciation = monthDepreciation
                    };

                    result.Add(monthStat);
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"按月度取得財產統計時發生錯誤: {ex.Message}");
                return new List<AssetStatisticsByMonthDTO>();
            }
        }

        /// <summary>
        /// 按廠牌取得財產統計
        /// </summary>
        /// <returns>廠牌統計資料</returns>
        public async Task<List<AssetStatisticsByManufacturerDTO>> GetAssetStatisticsByManufacturerAsync()
        {
            try
            {
                var query = from asset in _context.Set<Asset>()
                            .Where(a => !a.IsDeleted && (a.DeleteTime == null || a.DeleteTime == 0))
                            join manufacturer in _context.Set<Manufacturer>()
                                .Where(m => !m.IsDeleted && (m.DeleteTime == null || m.DeleteTime == 0))
                                on asset.ManufacturerId equals manufacturer.ManufacturerId into mfgGroup
                            from manufacturer in mfgGroup.DefaultIfEmpty()
                            select new { Asset = asset, Manufacturer = manufacturer };

                var data = await query.AsNoTracking().ToListAsync();
                var totalValue = data.Sum(x => x.Asset.PurchaseAmount);

                var result = data
                    .GroupBy(x => new
                    {
                        Id = x.Asset.ManufacturerId,
                        Name = x.Manufacturer?.Name ?? "未知廠牌"
                    })
                    .Select(g =>
                    {
                        var assetValue = g.Sum(x => x.Asset.PurchaseAmount);
                        return new AssetStatisticsByManufacturerDTO
                        {
                            ManufacturerId = g.Key.Id,
                            ManufacturerName = g.Key.Name,
                            AssetCount = g.Count(),
                            TotalValue = assetValue,
                            Percentage = totalValue > 0 ? Math.Round((assetValue / totalValue) * 100, 2) : 0
                        };
                    })
                    .OrderByDescending(x => x.AssetCount)
                    .ToList();

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"按廠牌取得財產統計時發生錯誤: {ex.Message}");
                return new List<AssetStatisticsByManufacturerDTO>();
            }
        }

        /// <summary>
        /// 按設備類型取得財產統計
        /// </summary>
        /// <returns>設備類型統計資料</returns>
        public async Task<List<AssetStatisticsByEquipmentTypeDTO>> GetAssetStatisticsByEquipmentTypeAsync()
        {
            try
            {
                var query = from asset in _context.Set<Asset>()
                            .Where(a => !a.IsDeleted && (a.DeleteTime == null || a.DeleteTime == 0))
                            join equipType in _context.Set<EquipmentType>()
                                .Where(et => !et.IsDeleted && (et.DeleteTime == null || et.DeleteTime == 0))
                                on asset.EquipmentTypeId equals equipType.EquipmentTypeId into etGroup
                            from equipType in etGroup.DefaultIfEmpty()
                            select new { Asset = asset, EquipmentType = equipType };

                var data = await query.AsNoTracking().ToListAsync();
                var totalValue = data.Sum(x => x.Asset.PurchaseAmount);

                var result = data
                    .GroupBy(x => new
                    {
                        Id = x.Asset.EquipmentTypeId,
                        Name = x.EquipmentType?.Name ?? "未分類設備"
                    })
                    .Select(g =>
                    {
                        var assetValue = g.Sum(x => x.Asset.PurchaseAmount);
                        return new AssetStatisticsByEquipmentTypeDTO
                        {
                            EquipmentTypeId = g.Key.Id,
                            EquipmentTypeName = g.Key.Name,
                            AssetCount = g.Count(),
                            TotalValue = assetValue,
                            Percentage = totalValue > 0 ? Math.Round((assetValue / totalValue) * 100, 2) : 0
                        };
                    })
                    .OrderByDescending(x => x.AssetCount)
                    .ToList();

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"按設備類型取得財產統計時發生錯誤: {ex.Message}");
                return new List<AssetStatisticsByEquipmentTypeDTO>();
            }
        }

        /// <summary>
        /// 按使用年限取得財產統計
        /// </summary>
        /// <returns>使用年限統計資料</returns>
        public async Task<List<AssetStatisticsByAgeDTO>> GetAssetStatisticsByAgeAsync()
        {
            try
            {
                var assets = await _context.Set<Asset>()
                    .Where(a => !a.IsDeleted && (a.DeleteTime == null || a.DeleteTime == 0)
                                && a.AcquisitionDate > 0)
                    .AsNoTracking()
                    .ToListAsync();

                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var totalValue = assets.Sum(a => a.PurchaseAmount);

                var ageGroups = assets
                    .Select(a =>
                    {
                        var purchaseDate = DateTimeOffset.FromUnixTimeSeconds(a.AcquisitionDate);
                        var ageYears = (DateTime.Now - purchaseDate.DateTime).Days / 365.0;

                        string ageRange;
                        if (ageYears < 1) ageRange = "未滿1年";
                        else if (ageYears < 3) ageRange = "1-3年";
                        else if (ageYears < 5) ageRange = "3-5年";
                        else if (ageYears < 10) ageRange = "5-10年";
                        else if (ageYears < 15) ageRange = "10-15年";
                        else ageRange = "15年以上";

                        return new { Asset = a, AgeRange = ageRange };
                    })
                    .GroupBy(x => x.AgeRange)
                    .Select(g =>
                    {
                        var assetValue = g.Sum(x => x.Asset.PurchaseAmount);
                        return new AssetStatisticsByAgeDTO
                        {
                            AgeRange = g.Key,
                            AssetCount = g.Count(),
                            TotalValue = assetValue,
                            Percentage = totalValue > 0 ? Math.Round((assetValue / totalValue) * 100, 2) : 0
                        };
                    })
                    .OrderBy(x => GetAgeRangeOrder(x.AgeRange))
                    .ToList();

                return ageGroups;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"按使用年限取得財產統計時發生錯誤: {ex.Message}");
                return new List<AssetStatisticsByAgeDTO>();
            }
        }

        /// <summary>
        /// 取得折舊統計資料
        /// </summary>
        /// <param name="year">年度</param>
        /// <param name="months">月份數量（預設12個月）</param>
        /// <returns>折舊統計資料</returns>
        public async Task<List<AssetDepreciationStatisticsDTO>> GetAssetDepreciationStatisticsAsync(int? year = null, int months = 12)
        {
            try
            {
                var targetYear = year ?? DateTime.Now.Year;
                var result = new List<AssetDepreciationStatisticsDTO>();

                for (int month = 1; month <= Math.Min(months, 12); month++)
                {
                    var depreciationDetails = await _context.Set<DepreciationFormDetail>()
                        .Where(d => d.DepreciationYear == targetYear && d.DepreciationMonth == month && !d.IsDeleted)
                        .AsNoTracking()
                        .ToListAsync();

                    var depStat = new AssetDepreciationStatisticsDTO
                    {
                        Year = targetYear,
                        Month = month,
                        YearMonth = $"{targetYear:D4}-{month:D2}",
                        CurrentDepreciation = depreciationDetails.Sum(d => d.CurrentDepreciation),
                        AccumulatedDepreciation = depreciationDetails.Sum(d => d.AccumulatedDepreciation),
                        DepreciatedAssetsCount = depreciationDetails.Count
                    };

                    result.Add(depStat);
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得折舊統計資料時發生錯誤: {ex.Message}");
                return new List<AssetDepreciationStatisticsDTO>();
            }
        }

        /// <summary>
        /// 按價值區間取得財產統計
        /// </summary>
        /// <returns>價值區間統計資料</returns>
        public async Task<List<AssetStatisticsByValueRangeDTO>> GetAssetStatisticsByValueRangeAsync()
        {
            try
            {
                var assets = await _context.Set<Asset>()
                    .Where(a => !a.IsDeleted && (a.DeleteTime == null || a.DeleteTime == 0))
                    .AsNoTracking()
                    .ToListAsync();

                var totalValue = assets.Sum(a => a.PurchaseAmount);

                var valueRanges = new List<(string Range, decimal Min, decimal Max)>
                {
                    ("0-1萬", 0, 10000),
                    ("1-5萬", 10000, 50000),
                    ("5-10萬", 50000, 100000),
                    ("10-50萬", 100000, 500000),
                    ("50-100萬", 500000, 1000000),
                    ("100萬以上", 1000000, decimal.MaxValue)
                };

                var result = valueRanges.Select(range =>
                {
                    var rangeAssets = assets.Where(a =>
                        a.PurchaseAmount >= range.Min &&
                        a.PurchaseAmount < range.Max).ToList();

                    var rangeValue = rangeAssets.Sum(a => a.PurchaseAmount);

                    return new AssetStatisticsByValueRangeDTO
                    {
                        ValueRange = range.Range,
                        MinValue = range.Min,
                        MaxValue = range.Max == decimal.MaxValue ? 0 : range.Max,
                        AssetCount = rangeAssets.Count(),
                        TotalValue = rangeValue,
                        Percentage = totalValue > 0 ? Math.Round((rangeValue / totalValue) * 100, 2) : 0
                    };
                }).Where(x => x.AssetCount > 0).ToList();

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"按價值區間取得財產統計時發生錯誤: {ex.Message}");
                return new List<AssetStatisticsByValueRangeDTO>();
            }
        }

        /// <summary>
        /// 取得年齡範圍排序順序
        /// </summary>
        /// <param name="ageRange">年齡範圍</param>
        /// <returns>排序順序</returns>
        private static int GetAgeRangeOrder(string ageRange)
        {
            return ageRange switch
            {
                "未滿1年" => 1,
                "1-3年" => 2,
                "3-5年" => 3,
                "5-10年" => 4,
                "10-15年" => 5,
                "15年以上" => 6,
                _ => 7
            };
        }
    }
}
