# Fee 費用資料表

建立者: 系統自動產生
建立時間: 2024-07-19
DB相關: DB相關-Rms

## 資料表名稱

Fee

## 資料表說明

用於儲存各類費用資料（如電費、水費、停車費、火險費等）

## 欄位說明

| 欄位名稱 | 資料型態 | 長度 | 允許空值 | 說明 |
| --- | --- | --- | --- | --- |
| FeeId | nvarchar | 100 | 否 | 主鍵，費用編號 |
| ContractId | nvarchar | 100 | 否 | 合約編號 (FK) |
| FeeType | nvarchar | 50 | 否 | 費用類型（電費/水費/停車費/火險費等）|
| Amount | decimal | 18,2 | 否 | 費用金額 |
| FeeDate | bigint | - | 否 | 費用應繳日期 |
| BillingPeriod | nvarchar | 6 | 否 | 計費期間（例如 202501）|
| PaidAmount | decimal | 18,2 | 否 | 已繳金額（核銷累計）|
| Status | nvarchar | 50 | 否 | 費用狀態（未繳(0)/部分已繳(1)/已繳(2)/作廢(9)）|
| Note | nvarchar | MAX | 是 | 備註 |
| CreateTime | bigint | - | 否 | 新增時間 |
| CreateUserId | nvarchar | 100 | 是 | 新增者編號 |
| UpdateTime | bigint | - | 是 | 更新時間 |
| UpdateUserId | nvarchar | 100 | 是 | 更新者編號 |
| DeleteTime | bigint | - | 是 | 刪除時間 |
| DeleteUserId | nvarchar | 100 | 是 | 刪除者編號 |

## 備註

- 費用逾期判斷：狀態為「未繳」且 FeeDate < 當前日期即視為逾期
- 防重規則：同 ContractId + FeeType + BillingPeriod 不得重複
- 建議索引：IX_Fee_ContractId_BillingPeriod、IX_Fee_Status、IX_Fee_FeeDate
- 狀態邏輯：Payment 核銷時，更新 PaidAmount 與 Status（未繳→部分已繳→已繳）；`Payment.Amount` 由本次 allocations 加總計算