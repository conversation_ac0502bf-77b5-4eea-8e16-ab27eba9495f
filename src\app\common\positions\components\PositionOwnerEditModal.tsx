"use client";

import React, { useState, useEffect } from "react";
import {
    Modal,
    Form,
    Select,
    Input,
    DatePicker,
    InputNumber,
    Button,
    message,
    Space,
    Descriptions,
    Badge,
    Divider,
} from "antd";
import {
    UserOutlined,
    CalendarOutlined,
    EditOutlined,
    PlusOutlined,
} from "@ant-design/icons";
import {
    PositionOwner,
    addPositionOwner,
    editPositionOwner,
    getPositionOwners,
} from "@/services/common/positionService";
import { useOptions } from "@/contexts/OptionsContext";
import { useAuth } from "@/contexts/AuthContext";
import dayjs, { Dayjs } from "dayjs";

const { Option } = Select;

interface PositionOwnerEditModalProps {
    visible: boolean;
    onClose: () => void;
    onSuccess: () => void;
    mode: "add" | "edit" | "view";
    positionOwnerId?: string;
    defaultPositionId?: string;
    defaultUserId?: string;
    defaultRoleType?: "主辦" | "協辦";
}

const PositionOwnerEditModal: React.FC<PositionOwnerEditModalProps> = ({
    visible,
    onClose,
    onSuccess,
    mode,
    positionOwnerId,
    defaultPositionId,
    defaultUserId,
    defaultRoleType,
}) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [loadingData, setLoadingData] = useState(false);
    const [currentData, setCurrentData] = useState<PositionOwner | null>(null);
    const [currentMode, setCurrentMode] = useState<"add" | "edit" | "view">(mode);

    const { users, positions } = useOptions() as any;
    const { user } = useAuth();

    // 同步外部傳入的 mode
    useEffect(() => {
        setCurrentMode(mode);
    }, [mode]);

    // 載入現有資料（編輯模式或檢視模式）
    useEffect(() => {
        if (visible && (mode === "edit" || mode === "view") && positionOwnerId) {
            loadCurrentData();
        } else if (visible && mode === "add") {
            // 新增模式，設定預設值
            form.resetFields();
            const defaultValues: any = {};

            if (defaultPositionId) defaultValues.positionId = defaultPositionId;
            if (defaultUserId) defaultValues.userId = defaultUserId;
            if (defaultRoleType) defaultValues.roleType = defaultRoleType;

            // 計算該職務該角色的下一個排序編號
            if (defaultPositionId && defaultRoleType) {
                calculateNextOrderNo(defaultPositionId, defaultRoleType);
            } else {
                defaultValues.orderNo = 1;
            }

            form.setFieldsValue(defaultValues);
        }
    }, [visible, mode, positionOwnerId, defaultPositionId, defaultUserId, defaultRoleType]);

    const loadCurrentData = async () => {
        if (!positionOwnerId) return;

        try {
            setLoadingData(true);
            const response = await getPositionOwners();

            if (response.success && response.data) {
                const ownerData = response.data.find(
                    (owner: PositionOwner) => owner.positionOwnerId === positionOwnerId
                );

                if (ownerData) {
                    setCurrentData(ownerData);
                    // 處理日期格式：後端回傳的是 timestamp (number) 或字串格式
                    const parseDate = (dateValue: string | number | null) => {
                        if (!dateValue) return null;

                        // 如果是數字，表示是 timestamp（秒）
                        if (typeof dateValue === 'number') {
                            return dayjs.unix(dateValue);
                        }

                        // 如果是字串，嘗試解析為 yyyy-MM-dd 格式
                        if (typeof dateValue === 'string') {
                            const parsed = dayjs(dateValue, "YYYY-MM-DD");
                            return parsed.isValid() ? parsed : null;
                        }

                        return null;
                    };

                    form.setFieldsValue({
                        positionId: ownerData.positionId,
                        userId: ownerData.userId,
                        roleType: ownerData.roleType,
                        orderNo: ownerData.orderNo,
                        startDate: parseDate(ownerData.startDate || ""),
                        endDate: parseDate(ownerData.endDate || ""),
                    });
                } else {
                    message.error("找不到該筆資料");
                    onClose();
                }
            }
        } catch (error) {
            console.error("載入資料錯誤:", error);
            message.error("載入資料失敗");
        } finally {
            setLoadingData(false);
        }
    };

    const calculateNextOrderNo = async (positionId: string, roleType: "主辦" | "協辦") => {
        try {
            const response = await getPositionOwners();
            if (response.success && response.data) {
                const sameRoleOwners = response.data.filter(
                    (owner: PositionOwner) =>
                        owner.positionId === positionId &&
                        owner.roleType === roleType &&
                        !owner.endDate // 只計算未結束的
                );
                const maxOrderNo = sameRoleOwners.length > 0
                    ? Math.max(...sameRoleOwners.map((owner: PositionOwner) => owner.orderNo))
                    : 0;
                form.setFieldsValue({ orderNo: maxOrderNo + 1 });
            }
        } catch (error) {
            console.error("計算排序編號錯誤:", error);
        }
    };

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            setLoading(true);

            // 找到選擇的使用者和職務資訊
            const selectedUser = users?.find((u: any) => u.userId === values.userId);
            const selectedPosition = positions?.find((p: any) => p.positionId === values.positionId);

            const ownerData: PositionOwner = {
                positionOwnerId: currentMode === "edit" ? positionOwnerId! : "",
                positionId: values.positionId,
                positionName: selectedPosition?.name || "",
                userId: values.userId,
                userName: selectedUser?.name || "",
                roleType: values.roleType,
                orderNo: values.orderNo || 1,
                departmentName: selectedUser?.serviceDepartmentName || null,
                startDate: values.startDate ? values.startDate.unix() : 0,
                endDate: values.endDate ? values.endDate.unix() : null,
            };

            const response = currentMode === "edit"
                ? await editPositionOwner(ownerData)
                : await addPositionOwner(ownerData);

            if (response.success) {
                message.success(currentMode === "edit" ? "編輯負責人成功" : "新增負責人成功");
                onSuccess();

                // 如果是從檢視模式切換到編輯模式，儲存後回到檢視模式
                if (mode === "view" && currentMode === "edit") {
                    setCurrentMode("view");
                    // 重新載入資料以顯示最新狀態
                    if (positionOwnerId) {
                        loadCurrentData();
                    }
                } else {
                    // 其他情況（新增模式或直接編輯模式）關閉 Modal
                    onClose();
                }
            } else {
                message.error(response.message || "操作失敗");
            }
        } catch (error: any) {
            if (error?.name !== "ValidationError") {
                console.error("提交錯誤:", error);
                message.error("操作失敗");
            }
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        form.resetFields();
        setCurrentData(null);
        setCurrentMode(mode); // 重置為原始模式
        onClose();
    };

    // 切換到編輯模式
    const handleSwitchToEdit = () => {
        setCurrentMode("edit");
    };

    return (
        <Modal
            title={
                <Space>
                    {currentMode === "edit" ? <EditOutlined /> : currentMode === "view" ? <UserOutlined /> : <PlusOutlined />}
                    {currentMode === "edit" ? "編輯負責人" : currentMode === "view" ? "檢視負責人" : "新增負責人"}
                    {currentMode === "view" && (
                        <Button
                            type="link"
                            size="small"
                            icon={<EditOutlined />}
                            onClick={handleSwitchToEdit}
                            style={{ marginLeft: 8 }}
                        >
                            編輯資料
                        </Button>
                    )}
                </Space>
            }
            open={visible}
            onOk={currentMode === "view" ? undefined : handleSubmit}
            onCancel={handleClose}
            confirmLoading={loading}
            okText={currentMode === "view" ? undefined : currentMode === "edit" ? "儲存" : "確定"}
            cancelText={currentMode === "view" ? "關閉" : "取消"}
            footer={currentMode === "view" ? [
                <Button key="close" onClick={handleClose}>
                    關閉
                </Button>
            ] : undefined}
            width={600}
            destroyOnClose
        >
            {loadingData ? (
                <div style={{ textAlign: "center", padding: "40px 0" }}>載入中...</div>
            ) : (
                <>
                    {/* 檢視模式：只顯示資料 */}
                    {currentMode === "view" && currentData && (
                        <>
                            <Descriptions
                                title="負責人資料"
                                size="small"
                                bordered
                                column={2}
                                style={{ marginBottom: 16 }}
                            >
                                <Descriptions.Item label="職務">
                                    {currentData.positionName}
                                </Descriptions.Item>
                                <Descriptions.Item label="負責人">
                                    <Space>
                                        <UserOutlined />
                                        {currentData.departmentName
                                            ? `${currentData.departmentName} - ${currentData.userName}`
                                            : currentData.userName}
                                    </Space>
                                </Descriptions.Item>
                                <Descriptions.Item label="角色類型">
                                    <Badge
                                        status={currentData.roleType === "主辦" ? "processing" : "default"}
                                        text={currentData.roleType}
                                    />
                                </Descriptions.Item>
                                <Descriptions.Item label="排序編號">
                                    {currentData.orderNo}
                                </Descriptions.Item>
                                <Descriptions.Item label="開始日期">
                                    <Space>
                                        <CalendarOutlined />
                                        {currentData.startDate
                                            ? (typeof currentData.startDate === 'number'
                                                ? dayjs.unix(currentData.startDate).format("YYYY-MM-DD")
                                                : currentData.startDate)
                                            : "未設定"}
                                    </Space>
                                </Descriptions.Item>
                                <Descriptions.Item label="結束日期">
                                    <Space>
                                        <CalendarOutlined />
                                        {currentData.endDate
                                            ? (typeof currentData.endDate === 'number'
                                                ? dayjs.unix(currentData.endDate).format("YYYY-MM-DD")
                                                : currentData.endDate)
                                            : "未設定"}
                                    </Space>
                                </Descriptions.Item>
                            </Descriptions>
                        </>
                    )}

                    {/* 編輯/新增模式：顯示表單 */}
                    {(currentMode === "edit" || currentMode === "add") && (
                        <>
                            {/* 當前資料顯示（編輯模式） */}
                            {currentMode === "edit" && currentData && (
                                <>
                                    <Descriptions
                                        title="當前資料"
                                        size="small"
                                        bordered
                                        column={2}
                                        style={{ marginBottom: 16 }}
                                    >
                                        <Descriptions.Item label="職務">
                                            {currentData.positionName}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="負責人">
                                            <Space>
                                                <UserOutlined />
                                                {currentData.departmentName
                                                    ? `${currentData.departmentName} - ${currentData.userName}`
                                                    : currentData.userName}
                                            </Space>
                                        </Descriptions.Item>
                                        <Descriptions.Item label="角色類型">
                                            <Badge
                                                status={currentData.roleType === "主辦" ? "processing" : "default"}
                                                text={currentData.roleType}
                                            />
                                        </Descriptions.Item>
                                        <Descriptions.Item label="排序編號">
                                            {currentData.orderNo}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="開始日期">
                                            <Space>
                                                <CalendarOutlined />
                                                {currentData.startDate
                                                    ? (typeof currentData.startDate === 'number'
                                                        ? dayjs.unix(currentData.startDate).format("YYYY-MM-DD")
                                                        : currentData.startDate)
                                                    : "未設定"}
                                            </Space>
                                        </Descriptions.Item>
                                        <Descriptions.Item label="結束日期">
                                            <Space>
                                                <CalendarOutlined />
                                                {currentData.endDate
                                                    ? (typeof currentData.endDate === 'number'
                                                        ? dayjs.unix(currentData.endDate).format("YYYY-MM-DD")
                                                        : currentData.endDate)
                                                    : "未設定"}
                                            </Space>
                                        </Descriptions.Item>
                                    </Descriptions>
                                    <Divider />
                                </>
                            )}

                            <Form form={form} layout="vertical">
                                <Form.Item
                                    name="positionId"
                                    label="職務"
                                    rules={[{ required: true, message: "請選擇職務" }]}
                                >
                                    <Select
                                        placeholder="請選擇職務"
                                        showSearch
                                        optionFilterProp="children"
                                        filterOption={(input, option) =>
                                            (option?.children as unknown as string)
                                                ?.toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        onChange={(value) => {
                                            const roleType = form.getFieldValue("roleType");
                                            if (roleType) {
                                                calculateNextOrderNo(value, roleType);
                                            }
                                        }}
                                    >
                                        {(positions || []).map((position: any) => (
                                            <Option key={position.positionId} value={position.positionId}>
                                                {position.name}
                                            </Option>
                                        ))}
                                    </Select>
                                </Form.Item>

                                {/* 負責人選擇（新增模式或新增模式且未選擇負責人時才顯示） */}
                                {(!defaultUserId || currentMode === "add") && (
                                    <Form.Item
                                        name="userId"
                                        label="負責人"
                                        rules={[{ required: true, message: "請選擇負責人" }]}
                                    >
                                        <Select
                                            placeholder="請選擇負責人"
                                            showSearch
                                            optionFilterProp="children"
                                            filterOption={(input, option) =>
                                                (option?.children as unknown as string)
                                                    ?.toLowerCase()
                                                    .includes(input.toLowerCase())
                                            }
                                        >
                                            {(users || []).map((user: any) => (
                                                <Option key={user.userId} value={user.userId}>
                                                    {user.serviceDepartmentName
                                                        ? `${user.serviceDepartmentName} - ${user.name}`
                                                        : user.name}
                                                </Option>
                                            ))}
                                        </Select>
                                    </Form.Item>
                                )}

                                <Form.Item
                                    name="roleType"
                                    label="角色類型"
                                    rules={[{ required: true, message: "請選擇角色類型" }]}
                                >
                                    <Select
                                        placeholder="請選擇角色類型"
                                        onChange={(value) => {
                                            const positionId = form.getFieldValue("positionId");
                                            if (positionId) {
                                                calculateNextOrderNo(positionId, value);
                                            }
                                        }}
                                    >
                                        <Option value="主辦">主辦</Option>
                                        <Option value="協辦">協辦</Option>
                                    </Select>
                                </Form.Item>

                                <Form.Item
                                    name="orderNo"
                                    label="排序編號"
                                    rules={[{ required: true, message: "請輸入排序編號" }]}
                                >
                                    <InputNumber
                                        placeholder="排序編號"
                                        style={{ width: "100%" }}
                                        min={1}
                                    />
                                </Form.Item>

                                <Form.Item
                                    name="startDate"
                                    label="開始日期"
                                    rules={[{ required: true, message: "請選擇開始日期" }]}
                                >
                                    <DatePicker
                                        placeholder="請選擇開始日期"
                                        format="YYYY-MM-DD"
                                        style={{ width: "100%" }}
                                    />
                                </Form.Item>

                                <Form.Item
                                    name="endDate"
                                    label="結束日期"
                                    dependencies={["startDate"]}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(_, value) {
                                                const start = getFieldValue("startDate");
                                                if (!value || !start) {
                                                    return Promise.resolve();
                                                }
                                                if (value.isAfter(start, "day")) {
                                                    return Promise.resolve();
                                                }
                                                return Promise.reject(new Error("結束日期需大於開始日期"));
                                            },
                                        }),
                                    ]}
                                >
                                    <DatePicker
                                        placeholder="請選擇結束日期"
                                        format="YYYY-MM-DD"
                                        style={{ width: "100%" }}
                                    />
                                </Form.Item>
                            </Form>
                        </>
                    )}
                </>
            )}
        </Modal>
    );
};

export default PositionOwnerEditModal;
