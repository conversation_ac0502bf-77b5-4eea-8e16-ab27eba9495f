using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;
using Swashbuckle.AspNetCore.Annotations;
using System.ComponentModel.DataAnnotations;

namespace FAST_ERP_Backend.Controllers.Common
{
    /// <summary>
    /// 選單權限優化控制器
    /// 提供權限分析、清理和驗證功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [SwaggerTag("選單權限優化管理")]
    public class MenuPermissionOptimizationController : ControllerBase
    {
        private readonly ERPDbContext _context;
        private readonly ISystemMenuService _systemMenuService;

        public MenuPermissionOptimizationController(
            ERPDbContext context,
            ISystemMenuService systemMenuService)
        {
            _context = context;
            _systemMenuService = systemMenuService;
        }

        /// <summary>
        /// 分析權限結構
        /// </summary>
        /// <returns>權限結構分析報告</returns>
        [HttpGet("analyze")]
        [SwaggerOperation(Summary = "分析權限結構", Description = "分析當前權限儲存結構，識別冗餘的父節點權限")]
        public async Task<IActionResult> AnalyzePermissionStructure()
        {
            try
            {
                // 統計總體資料
                var totalMenus = await _context.Common_SystemMenu
                    .Where(m => !m.IsDeleted)
                    .CountAsync();

                var totalPermissions = await _context.Common_RolesPermissions
                    .Where(rp => !rp.IsDeleted)
                    .CountAsync();

                var totalRoles = await _context.Common_RolesPermissions
                    .Where(rp => !rp.IsDeleted)
                    .Select(rp => rp.RolesId)
                    .Distinct()
                    .CountAsync();

                // 識別末節點
                var leafMenuIds = await _systemMenuService.GetLeafMenuIdsAsync();

                // 分析選單結構
                var menuStructure = await _context.Common_SystemMenu
                    .Where(m => !m.IsDeleted)
                    .Select(m => new
                    {
                        m.SystemMenuId,
                        m.Label,
                        m.ParentId,
                        ChildrenCount = _context.Common_SystemMenu
                            .Where(c => c.ParentId == m.SystemMenuId && !c.IsDeleted)
                            .Count(),
                        PermissionCount = _context.Common_RolesPermissions
                            .Where(rp => rp.SystemMenuId == m.SystemMenuId && !rp.IsDeleted)
                            .Count()
                    })
                    .ToListAsync();

                var leafNodes = menuStructure.Where(m => m.ChildrenCount == 0).ToList();
                var parentNodes = menuStructure.Where(m => m.ChildrenCount > 0).ToList();

                // 識別潛在的冗餘權限
                var redundantPermissions = await IdentifyRedundantPermissions();

                var analysis = new
                {
                    Summary = new
                    {
                        TotalMenus = totalMenus,
                        TotalPermissions = totalPermissions,
                        TotalRoles = totalRoles,
                        LeafNodeCount = leafNodes.Count,
                        ParentNodeCount = parentNodes.Count,
                        LeafNodesWithPermissions = leafNodes.Count(l => l.PermissionCount > 0),
                        ParentNodesWithPermissions = parentNodes.Count(p => p.PermissionCount > 0),
                        RedundantPermissionCount = redundantPermissions.Count
                    },
                    OptimizationPotential = new
                    {
                        CanRemovePermissions = redundantPermissions.Count,
                        StorageSavingPercentage = totalPermissions > 0 
                            ? Math.Round((redundantPermissions.Count * 100.0 / totalPermissions), 2) 
                            : 0,
                        EstimatedFinalPermissionCount = totalPermissions - redundantPermissions.Count
                    },
                    RedundantPermissionSamples = redundantPermissions.Take(10).ToList()
                };

                return Ok(new { success = true, data = analysis });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"分析失敗: {ex.Message}" });
            }
        }

        /// <summary>
        /// 識別冗餘的父節點權限
        /// </summary>
        /// <returns>冗餘權限列表</returns>
        private async Task<List<object>> IdentifyRedundantPermissions()
        {
            var redundantPermissions = new List<object>();

            // 查詢所有父節點權限
            var parentPermissions = await (
                from rp in _context.Common_RolesPermissions
                join menu in _context.Common_SystemMenu on rp.SystemMenuId equals menu.SystemMenuId
                where !rp.IsDeleted && !menu.IsDeleted
                where _context.Common_SystemMenu.Any(child => child.ParentId == menu.SystemMenuId && !child.IsDeleted)
                select new
                {
                    rp.RolesPermissionsId,
                    rp.RolesId,
                    rp.SystemMenuId,
                    menu.Label
                }
            ).ToListAsync();

            // 檢查每個父節點權限是否冗餘
            foreach (var parentPerm in parentPermissions)
            {
                // 檢查該父節點的所有子節點是否都有權限
                var childMenus = await _context.Common_SystemMenu
                    .Where(child => child.ParentId == parentPerm.SystemMenuId && !child.IsDeleted)
                    .ToListAsync();

                if (childMenus.Any())
                {
                    var allChildrenHavePermission = true;
                    foreach (var child in childMenus)
                    {
                        var hasPermission = await _context.Common_RolesPermissions
                            .AnyAsync(rp => rp.SystemMenuId == child.SystemMenuId 
                                         && rp.RolesId == parentPerm.RolesId 
                                         && !rp.IsDeleted);
                        
                        if (!hasPermission)
                        {
                            allChildrenHavePermission = false;
                            break;
                        }
                    }

                    if (allChildrenHavePermission)
                    {
                        redundantPermissions.Add(new
                        {
                            parentPerm.RolesPermissionsId,
                            parentPerm.RolesId,
                            parentPerm.SystemMenuId,
                            parentPerm.Label,
                            Reason = "所有子節點都有權限，父節點權限冗餘",
                            ChildrenCount = childMenus.Count
                        });
                    }
                }
            }

            return redundantPermissions;
        }

        /// <summary>
        /// 獲取末節點列表
        /// </summary>
        /// <returns>末節點選單列表</returns>
        [HttpGet("leaf-nodes")]
        [SwaggerOperation(Summary = "獲取末節點列表", Description = "獲取選單樹中所有末節點（葉子節點）的列表")]
        public async Task<IActionResult> GetLeafNodes()
        {
            try
            {
                var leafMenuIds = await _systemMenuService.GetLeafMenuIdsAsync();
                
                var leafMenus = await _context.Common_SystemMenu
                    .Where(m => leafMenuIds.Contains(m.SystemMenuId) && !m.IsDeleted)
                    .Select(m => new
                    {
                        m.SystemMenuId,
                        m.Label,
                        m.Key,
                        m.SystemGroupId,
                        m.IsMenu,
                        PermissionCount = _context.Common_RolesPermissions
                            .Where(rp => rp.SystemMenuId == m.SystemMenuId && !rp.IsDeleted)
                            .Count()
                    })
                    .OrderBy(m => m.Label)
                    .ToListAsync();

                return Ok(new { 
                    success = true, 
                    data = new
                    {
                        TotalLeafNodes = leafMenus.Count,
                        LeafNodesWithPermissions = leafMenus.Count(l => l.PermissionCount > 0),
                        LeafMenus = leafMenus
                    }
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"獲取末節點失敗: {ex.Message}" });
            }
        }

        /// <summary>
        /// 驗證權限完整性
        /// </summary>
        /// <param name="rolesId">角色ID</param>
        /// <returns>權限完整性檢查結果</returns>
        [HttpGet("verify/{rolesId}")]
        [SwaggerOperation(Summary = "驗證權限完整性", Description = "驗證指定角色的權限設定是否完整且無冗餘")]
        public async Task<IActionResult> VerifyPermissionIntegrity([Required] string rolesId)
        {
            try
            {
                // 獲取角色的直接權限
                var directPermissions = await _context.Common_RolesPermissions
                    .Where(rp => rp.RolesId == rolesId && !rp.IsDeleted)
                    .Select(rp => rp.SystemMenuId)
                    .ToListAsync();

                // 獲取末節點列表
                var leafMenuIds = await _systemMenuService.GetLeafMenuIdsAsync();

                // 分析權限結構
                var leafPermissions = directPermissions.Where(p => leafMenuIds.Contains(p)).ToList();
                var parentPermissions = directPermissions.Where(p => !leafMenuIds.Contains(p)).ToList();

                // 檢查冗餘的父節點權限
                var redundantParentPermissions = new List<string>();
                foreach (var parentId in parentPermissions)
                {
                    var children = await _context.Common_SystemMenu
                        .Where(m => m.ParentId == parentId && !m.IsDeleted)
                        .Select(m => m.SystemMenuId)
                        .ToListAsync();

                    if (children.All(child => directPermissions.Contains(child)))
                    {
                        redundantParentPermissions.Add(parentId);
                    }
                }

                var verification = new
                {
                    RolesId = rolesId,
                    TotalPermissions = directPermissions.Count,
                    LeafPermissions = leafPermissions.Count,
                    ParentPermissions = parentPermissions.Count,
                    RedundantParentPermissions = redundantParentPermissions.Count,
                    IsOptimized = redundantParentPermissions.Count == 0,
                    OptimizationSuggestion = redundantParentPermissions.Count > 0 
                        ? $"可以移除 {redundantParentPermissions.Count} 個冗餘的父節點權限"
                        : "權限設定已優化",
                    RedundantPermissionIds = redundantParentPermissions
                };

                return Ok(new { success = true, data = verification });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"驗證失敗: {ex.Message}" });
            }
        }
    }
}
