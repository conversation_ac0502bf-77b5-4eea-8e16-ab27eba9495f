"use client";
/* 固定資產明細表
    /app/pms/asset_report/asset_detail/page.tsx
  功能說明
    1. 選擇財產科目
    2. 選擇財產子目
    3. 選擇所屬部門
    4. 選擇財產狀態
    5. 輸入財產編號
    6. 輸入財產名稱
    7. 選擇取得日期範圍
    8. 選擇包含選項（報廢/出售/捐贈）
    9. 產生報表
    10. 預覽報表
    11. 列印報表
    12. 返回列表
    13. 重置
    14. 簽核章
*/
import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  useMemo,
} from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Form,
  Row,
  message,
  DatePicker,
  Statistic,
  Typography,
  Badge,
  Tag,
  Empty,
  Col,
  Grid,
  Tooltip,
  Switch,
  Checkbox,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  PrinterOutlined,
  FileExcelOutlined,
  EyeOutlined,
  DatabaseOutlined,
  DollarOutlined,
  CalendarOutlined,
  HomeOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import {
  generateFixedAssetDetailReport,
  FixedAssetDetailReportQuery,
  FixedAssetDetailReportItem,
} from "@/services/pms/assetService";
import { getAssetAccounts } from "@/services/pms/assetAccountService";
import { getAssetSubAccounts } from "@/services/pms/assetSubAccountService";
import { getAssetCategories } from "@/services/pms/assetCategoryService";
import { getDepartments } from "@/services/common/departmentService";
import { getAssetStatuses } from "@/services/pms/assetStatusService";
import { getStorageLocations } from "@/services/pms/storageLocationService";
import { getUnits } from "@/services/common/unitService";
import { getUsers } from "@/services/common/userService";
import {
  getReportSignatureTemplates,
  ReportSignatureTemplate,
} from "@/services/common/reportSignatureTemplateService";
import { formatTWCurrency } from "@/utils/formatUtils";
import { siteConfig } from "@/config/site";
import { STATUS_COLORS } from "@/constants/pms/statusColors";
import ReportHeader, {
  getReportPrintStyles,
} from "@/app/components/common/ReportHeader";
import ReportFooter from "@/app/components/common/ReportFooter";
import { useOptions } from "@/contexts/OptionsContext";

const { Option } = Select;
const { RangePicker } = DatePicker;
const { useBreakpoint } = Grid;
const { Title, Text } = Typography;

// 統計資料介面
interface AssetDetailStatistics {
  totalCount: number;
  totalValue: number;
  totalDepreciation: number;
  currentYearDepreciation: number;
  netValue: number;
}

// 主組件
const AssetDetailReportPage: React.FC = () => {
  // =========================== 狀態管理 ===========================
  const [data, setData] = useState<FixedAssetDetailReportItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<AssetDetailStatistics>({
    totalCount: 0,
    totalValue: 0,
    totalDepreciation: 0,
    currentYearDepreciation: 0,
    netValue: 0,
  });

  // 從 Context 取得選單資料
  const {
    assetAccounts,
    assetSubAccounts,
    assetCategories,
    assetStatuses,
    departments,
    storageLocations,
    units,
    users,
    signatureTemplates,
    refreshOptions,
  } = useOptions();

  // 表單
  const [searchForm] = Form.useForm();
  const [selectedSignatureTemplateId, setSelectedSignatureTemplateId] =
    useState<string>("");
  const [selectedAssetAccountName, setSelectedAssetAccountName] =
    useState<string>("");

  // 列印相關
  const printRef = useRef<HTMLDivElement>(null);
  const [isPrintMode, setIsPrintMode] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // 響應式斷點
  const screens = useBreakpoint();
  const isMobile = !screens.md;

  // =========================== 數據載入 ===========================

  // 計算統計數據
  const calculateStatistics = useCallback(
    (reportData: FixedAssetDetailReportItem[]) => {
      let totalValue = 0;
      let totalDepreciation = 0;
      let currentYearDepreciation = 0;

      reportData.forEach((item) => {
        totalValue += item.acquisitionValue || 0;
        totalDepreciation += item.accumulatedDepreciation || 0;
        currentYearDepreciation += item.currentYearAccumulatedDepreciation || 0;
      });

      const newStatistics: AssetDetailStatistics = {
        totalCount: reportData.length,
        totalValue: Math.round(totalValue),
        totalDepreciation: Math.round(totalDepreciation),
        currentYearDepreciation: Math.round(currentYearDepreciation),
        netValue: Math.round(totalValue - totalDepreciation),
      };

      setStatistics(newStatistics);
    },
    []
  );

  // 載入報表資料
  const loadData = useCallback(
    async (query: FixedAssetDetailReportQuery) => {
      try {
        setLoading(true);
        const response = await generateFixedAssetDetailReport(query);

        if (response.success && response.data) {
          setData(response.data);
          calculateStatistics(response.data);
          message.success(response.message || "成功產生固定資產明細表");
        } else {
          console.error("API 回應錯誤:", response);
          message.error(response.message || "產生報表失敗");
          setData([]);
          setStatistics({
            totalCount: 0,
            totalValue: 0,
            totalDepreciation: 0,
            currentYearDepreciation: 0,
            netValue: 0,
          });
        }
      } catch (error) {
        console.error("載入報表錯誤:", error);
        message.error("載入報表失敗");
        setData([]);
      } finally {
        setLoading(false);
      }
    },
    [calculateStatistics]
  );

  // =========================== 事件處理 ===========================

  // 搜尋處理
  const handleSearch = async () => {
    const formValues = await searchForm.getFieldsValue();
    const query: FixedAssetDetailReportQuery = {};

    if (formValues.assetAccountId) {
      query.assetAccountId = formValues.assetAccountId;
      // 更新選中的財產科目名稱
      const selectedAccount = assetAccounts.find(
        (account: any) => account.assetAccountId === formValues.assetAccountId
      );
      setSelectedAssetAccountName(selectedAccount?.assetAccountName || "");
    } else {
      setSelectedAssetAccountName("");
    }

    if (formValues.assetSubAccountId) {
      query.assetSubAccountId = formValues.assetSubAccountId;
    }

    if (formValues.departmentId) {
      query.departmentId = formValues.departmentId;
    }

    if (formValues.assetStatusId) {
      query.assetStatusId = formValues.assetStatusId;
    }

    if (formValues.assetNo) {
      query.assetNo = formValues.assetNo.trim();
    }

    if (formValues.assetName) {
      query.assetName = formValues.assetName.trim();
    }

    if (formValues.acquisitionDateRange) {
      query.acquisitionDateStart = formValues.acquisitionDateRange[0].valueOf();
      query.acquisitionDateEnd = formValues.acquisitionDateRange[1].valueOf();
    }

    // 包含選項
    query.includeScrapAssets = formValues.includeScrapAssets || false;
    query.includeSoldAssets = formValues.includeSoldAssets || false;
    query.includeDonatedAssets = formValues.includeDonatedAssets || false;

    await loadData(query);
  };

  // 重置搜尋
  const handleResetSearch = () => {
    searchForm.resetFields();
    setData([]);
    setSelectedAssetAccountName("");
    setStatistics({
      totalCount: 0,
      totalValue: 0,
      totalDepreciation: 0,
      currentYearDepreciation: 0,
      netValue: 0,
    });
  };

  // 列印處理
  const handlePrint = () => {
    if (printRef.current) {
      const printContent = printRef.current.innerHTML;
      const printWindow = window.open("", "_blank");

      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>${siteConfig.copyright}固定資產明細表</title>
              <style>
                body { 
                  font-family: Arial, sans-serif; 
                  margin: 0; 
                  padding: 2px; 
                  background: white;
                  font-size: 12px;
                }
                table { 
                  width: 100%; 
                  border-collapse: collapse; 
                  border: 1px solid #000;
                  font-size: 9px;
                  table-layout: auto;
                }
                th { 
                  border: 1px solid #000; 
                  padding: 2px; 
                  background-color: #f5f5f5; 
                  font-weight: bold; 
                  text-align: center;
                  white-space: nowrap;
                  font-size: 9px;
                  vertical-align: middle;
                }
                td { 
                  border: 1px solid #000; 
                  padding: 1px 2px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  font-size: 8px;
                  vertical-align: top;
                  line-height: 1.2;
                  max-width: 120px;
                }
                td.long-content {
                  white-space: normal;
                  word-wrap: break-word;
                  word-break: break-word;
                  max-width: none;
                }
                .total-row { 
                  background-color: #f0f0f0 !important; 
                  font-weight: bold; 
                }
                .text-center { 
                  text-align: center; 
                }
                .text-right { 
                  text-align: right; 
                }
                ${getReportPrintStyles("固定資產明細表")}
              </style>
            </head>
            <body>
              ${printContent}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  // 匯出 Excel
  const handleExportExcel = () => {
    try {
      const csvData = convertToCSV(data, true);
      const fileName = `${siteConfig.copyright}固定資產明細表_${dayjs().format(
        "YYYYMMDD"
      )}.csv`;
      downloadCSV(csvData, fileName);
      message.success("匯出成功");
    } catch (error) {
      console.error("匯出錯誤:", error);
      message.error("匯出失敗");
    }
  };

  // 轉換為 CSV 格式
  const convertToCSV = (
    reportData: FixedAssetDetailReportItem[],
    includeCompanyHeader: boolean = false
  ) => {
    const headers = [
      "所屬部門",
      "財產名稱",
      "取得日期",
      "規格",
      "數量",
      "財產編號",
      "單價",
      "取得價值",
      "本年度折舊",
      "累計折舊",
      "淨值",
      "備註",
    ];

    let csvContent = "";

    if (includeCompanyHeader) {
      csvContent += `${siteConfig.copyright || "公司名稱"}固定資產明細表\n`;
      csvContent += `列印日期：${dayjs().format("YYYY/MM/DD")}\n`;
      csvContent += `總計：${
        statistics.totalCount
      } 筆，總值：${formatTWCurrency(statistics.totalValue)}\n`;

      // 加入簽核模板資訊
      if (selectedSignatureTemplateId) {
        const selectedTemplate = signatureTemplates.find(
          (t) => t.id === selectedSignatureTemplateId
        );
        if (selectedTemplate) {
          csvContent += `簽核模板：${selectedTemplate.name}\n`;
        }
      }

      csvContent += `\n`;
    }

    csvContent += headers.join(",") + "\n";

    reportData.forEach((item) => {
      const row = [
        item.departmentName || "",
        item.assetName || "",
        item.acquisitionDateFormatted || "",
        item.specification || "",
        item.quantity + " " + item.unitName || 1 + " " + item.unitName,
        item.assetNo || "",
        item.unitPrice || 0,
        item.acquisitionValue || 0,
        item.currentYearAccumulatedDepreciation || 0,
        item.accumulatedDepreciation || 0,
        item.netBookValue || 0,
        item.notes || "",
      ];

      csvContent += row.join(",") + "\n";
    });

    // 合計行
    csvContent += `\n合計,,,,,,,${statistics.totalValue},${statistics.currentYearDepreciation},${statistics.totalDepreciation},${statistics.netValue},,\n`;

    return csvContent;
  };

  // 下載 CSV 檔案
  const downloadCSV = (csvContent: string, filename: string) => {
    const BOM = "\uFEFF";
    const blob = new Blob([BOM + csvContent], {
      type: "text/csv;charset=utf-8;",
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // =========================== 渲染組件 ===========================

  // 表格欄位定義
  const columns = [
    {
      title: "所屬部門",
      dataIndex: "departmentName",
      key: "departmentName",
      width: 120,
      fixed: "left" as const,
    },
    {
      title: "財產名稱",
      dataIndex: "assetName",
      key: "assetName",
      width: 200,
      fixed: "left" as const,
    },
    {
      title: "取得日期",
      dataIndex: "acquisitionDateFormatted",
      key: "acquisitionDateFormatted",
      width: 120,
    },
    {
      title: "規格",
      dataIndex: "specification",
      key: "specification",
      width: 150,
      ellipsis: true,
    },
    {
      title: "數量",
      dataIndex: "quantity",
      key: "quantity",
      width: 120,
      align: "center" as const,
      render: (_: any, record: any) => {
        return `${record.quantity} ${record.unitName ? record.unitName : ""}`;
      },
    },
    {
      title: "財產編號",
      dataIndex: "assetNo",
      key: "assetNo",
      width: 120,
    },
    {
      title: "單價",
      dataIndex: "unitPrice",
      key: "unitPrice",
      width: 120,
      align: "right" as const,
      render: (value: number) => formatTWCurrency(value || 0),
    },
    {
      title: "取得價值",
      dataIndex: "acquisitionValue",
      key: "acquisitionValue",
      width: 120,
      align: "right" as const,
      render: (value: number) => formatTWCurrency(value || 0),
    },
    {
      title: "累計折舊",
      children: [
        {
          title: "本年度",
          dataIndex: "currentYearAccumulatedDepreciation",
          key: "currentYearAccumulatedDepreciation",
          width: 120,
          align: "right" as const,
          render: (value: number) => formatTWCurrency(value || 0),
        },
        {
          title: "累計數",
          dataIndex: "accumulatedDepreciation",
          key: "accumulatedDepreciation",
          width: 120,
          align: "right" as const,
          render: (value: number) => formatTWCurrency(value || 0),
        },
      ],
    },
    {
      title: "淨值",
      dataIndex: "netBookValue",
      key: "netBookValue",
      width: 120,
      align: "right" as const,
      render: (value: number) => formatTWCurrency(value || 0),
    },
    {
      title: "備註",
      dataIndex: "notes",
      key: "notes",
      width: 250,
    },
  ];

  return (
    <div style={{ padding: "20px" }}>
      <Card title={`固定資產明細表`}>
        {/* 統計卡片區域 */}
        <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="財產總數"
                value={statistics.totalCount}
                valueStyle={{ color: "#1890ff" }}
                prefix={<DatabaseOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="取得總值"
                value={statistics.totalValue}
                valueStyle={{ color: "#1890ff" }}
                prefix={<DollarOutlined />}
                formatter={(value) => formatTWCurrency(Number(value))}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="本年度折舊"
                value={statistics.currentYearDepreciation}
                valueStyle={{ color: "#faad14" }}
                prefix={<CalendarOutlined />}
                formatter={(value) => formatTWCurrency(Number(value))}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="累計折舊"
                value={statistics.totalDepreciation}
                valueStyle={{ color: "#fa8c16" }}
                prefix={<CalendarOutlined />}
                formatter={(value) => formatTWCurrency(Number(value))}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="淨值"
                value={statistics.netValue}
                valueStyle={{ color: "#52c41a" }}
                prefix={<HomeOutlined />}
                formatter={(value) => formatTWCurrency(Number(value))}
              />
            </Card>
          </Col>
        </Row>

        {/* 搜尋區域 */}
        <Card title="查詢條件" style={{ marginBottom: "24px" }}>
          <Form form={searchForm} layout="vertical">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetAccountId" label="財產科目">
                  <Select placeholder="請選擇" allowClear>
                    {assetAccounts.map((item: any) => (
                      <Option
                        key={item.assetAccountId}
                        value={item.assetAccountId}
                      >
                        {item.assetAccountName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetSubAccountId" label="財產子目">
                  <Select placeholder="請選擇" allowClear>
                    {assetSubAccounts.map((item: any) => (
                      <Option
                        key={item.assetSubAccountId}
                        value={item.assetSubAccountId}
                      >
                        {item.assetSubAccountName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="departmentId" label="所屬部門">
                  <Select placeholder="請選擇" allowClear>
                    {departments.map((item) => (
                      <Option key={item.departmentId} value={item.departmentId}>
                        {item.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetStatusId" label="財產狀態">
                  <Select placeholder="請選擇" allowClear>
                    {assetStatuses.map((item) => (
                      <Option
                        key={item.assetStatusId}
                        value={item.assetStatusId}
                      >
                        <Tag
                          color={STATUS_COLORS[item.name] || "default"}
                          style={{ marginRight: 8 }}
                        >
                          {item.name}
                        </Tag>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetNo" label="財產編號">
                  <Input placeholder="請輸入財產編號" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetName" label="財產名稱">
                  <Input placeholder="請輸入財產名稱" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item name="acquisitionDateRange" label="取得日期範圍">
                  <RangePicker style={{ width: "100%" }} />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item label="報表簽核章">
                  <Select
                    value={selectedSignatureTemplateId}
                    onChange={setSelectedSignatureTemplateId}
                    placeholder="請選擇簽核模板"
                    allowClear
                    style={{ width: "100%" }}
                  >
                    {signatureTemplates.map((template) => (
                      <Option key={template.id} value={template.id}>
                        {template.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[16, 16]} style={{ marginTop: "16px" }}>
              <Col xs={24} sm={8}>
                <Form.Item name="includeScrapAssets" valuePropName="checked">
                  <Checkbox>包含報廢財產</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={24} sm={8}>
                <Form.Item name="includeSoldAssets" valuePropName="checked">
                  <Checkbox>包含出售財產</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={24} sm={8}>
                <Form.Item name="includeDonatedAssets" valuePropName="checked">
                  <Checkbox>包含捐贈財產</Checkbox>
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col>
                <Space>
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    onClick={handleSearch}
                    loading={loading}
                  >
                    產生報表
                  </Button>
                  <Button icon={<ReloadOutlined />} onClick={handleResetSearch}>
                    重置
                  </Button>
                  {data.length > 0 && (
                    <>
                      <Button
                        icon={<EyeOutlined />}
                        onClick={() => setIsPrintMode(!isPrintMode)}
                      >
                        {isPrintMode ? "返回列表" : "預覽報表"}
                      </Button>
                      {isPrintMode && (
                        <Button
                          type="primary"
                          icon={<PrinterOutlined />}
                          onClick={handlePrint}
                        >
                          列印
                        </Button>
                      )}
                      <Button
                        icon={<FileExcelOutlined />}
                        onClick={handleExportExcel}
                      >
                        匯出 Excel
                      </Button>
                    </>
                  )}
                </Space>
              </Col>
            </Row>
          </Form>
        </Card>

        {/* 列印/預覽區域 */}
        <div ref={printRef}>
          {isPrintMode ? (
            // 列印模式
            <div
              style={{
                background: "white",
                padding: "5px",
                fontSize: "14px",
                fontFamily: "Arial, sans-serif",
              }}
            >
              {/* 報表標題 */}
              <ReportHeader
                reportTitle="固定資產明細表"
                currentPage={currentPage}
                totalPages={totalPages}
                isPrintMode={isPrintMode}
              />

              {/* 報表資訊 */}
              <div style={{ marginBottom: "12px" }}>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    fontSize: "12px",
                    color: "#666",
                  }}
                >
                  <Text>財產科目：{selectedAssetAccountName || "全部"}</Text>
                  <Text>產生時間：{dayjs().format("YYYY-MM-DD HH:mm:ss")}</Text>
                </div>
              </div>

              {/* 固定資產明細表表格 */}
              <table
                style={{
                  width: "100%",
                  borderCollapse: "collapse",
                  border: "1px solid #000",
                  fontSize: "11px",
                }}
              >
                <thead>
                  <tr style={{ backgroundColor: "#f5f5f5" }}>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      所屬部門
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      財產名稱
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      取得日期
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      規格
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      數量
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      財產編號
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      單價
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      取得價值
                    </th>
                    <th
                      colSpan={2}
                      style={{
                        border: "1px solid #000",
                        padding: "0",
                        textAlign: "center",
                        verticalAlign: "top",
                        height: "40px",
                        position: "relative",
                      }}
                    >
                      <div
                        style={{
                          textAlign: "center",
                          fontSize: "12px",
                          fontWeight: "bold",
                          padding: "4px 2px 2px 2px",
                          height: "18px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        累計折舊
                      </div>
                      <div
                        style={{
                          position: "absolute",
                          top: "22px",
                          left: "0",
                          right: "0",
                          height: "1px",
                          backgroundColor: "#000",
                          borderTop: "1px #000",
                        }}
                      ></div>
                      <div
                        style={{
                          position: "absolute",
                          top: "22px",
                          left: "46%",
                          width: "1px",
                          height: "22px",
                          backgroundColor: "#000",
                          border: "1px #000",
                        }}
                      ></div>
                      <div
                        style={{
                          position: "absolute",
                          top: "22px",
                          left: "0",
                          width: "50%",
                          height: "18px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          fontSize: "10px",
                        }}
                      >
                        本年度
                      </div>
                      <div
                        style={{
                          position: "absolute",
                          top: "22px",
                          left: "50%",
                          width: "50%",
                          height: "18px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          fontSize: "10px",
                        }}
                      >
                        累計數
                      </div>
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      淨值
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      備註
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {data.length === 0 ? (
                    <tr>
                      <td
                        colSpan={13}
                        style={{
                          border: "1px solid #000",
                          padding: "40px",
                          textAlign: "center",
                          fontSize: "16px",
                          color: "#999",
                        }}
                      >
                        <Empty
                          image={Empty.PRESENTED_IMAGE_SIMPLE}
                          description="查無符合條件的資料"
                        />
                      </td>
                    </tr>
                  ) : (
                    (() => {
                      const grouped = data.reduce(
                        (acc: Record<string, typeof data>, cur) => {
                          const key = cur.departmentName || "";
                          if (!acc[key]) acc[key] = [] as typeof data;
                          acc[key].push(cur);
                          return acc;
                        },
                        {} as Record<string, typeof data>
                      );

                      const deptNames = Object.keys(grouped);

                      return (
                        <>
                          {deptNames.map((dept) => {
                            const rows = grouped[dept];
                            const sumUnitPrice = rows.reduce(
                              (s, r) => s + (r.unitPrice || 0),
                              0
                            );
                            const sumAcq = rows.reduce(
                              (s, r) => s + (r.acquisitionValue || 0),
                              0
                            );
                            const sumCY = rows.reduce(
                              (s, r) =>
                                s + (r.currentYearAccumulatedDepreciation || 0),
                              0
                            );
                            const sumAcc = rows.reduce(
                              (s, r) => s + (r.accumulatedDepreciation || 0),
                              0
                            );
                            const sumNet = rows.reduce(
                              (s, r) => s + (r.netBookValue || 0),
                              0
                            );

                            return (
                              <React.Fragment key={dept}>
                                {rows.map((item, index) => (
                                  <tr key={(item.assetId || "") + "-" + index}>
                                    <td
                                      style={{
                                        border: "1px solid #000",
                                        padding: "2px 4px",
                                        textAlign: "center",
                                      }}
                                    >
                                      {item.departmentName}
                                    </td>
                                    <td
                                      className={
                                        (item.assetName || "").length > 15
                                          ? "long-content"
                                          : ""
                                      }
                                      style={{
                                        border: "1px solid #000",
                                        padding: "2px 4px",
                                      }}
                                    >
                                      {item.assetName}
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid #000",
                                        padding: "2px 4px",
                                        textAlign: "center",
                                      }}
                                    >
                                      {item.acquisitionDateFormatted || ""}
                                    </td>
                                    <td
                                      className={
                                        (item.specification || "").length > 20
                                          ? "long-content"
                                          : ""
                                      }
                                      style={{
                                        border: "1px solid #000",
                                        padding: "2px 4px",
                                      }}
                                    >
                                      {item.specification || ""}
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid #000",
                                        padding: "2px 4px",
                                        textAlign: "center",
                                      }}
                                    >
                                      {(item.quantity || 1) +
                                        " " +
                                        item.unitName ||
                                        "" + " " + item.unitName}
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid #000",
                                        padding: "2px 4px",
                                        textAlign: "center",
                                      }}
                                    >
                                      {item.assetNo}
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid #000",
                                        padding: "2px 4px",
                                        textAlign: "right",
                                      }}
                                    >
                                      {(item.unitPrice || 0).toLocaleString(
                                        "zh-TW",
                                        {
                                          minimumFractionDigits: 2,
                                          maximumFractionDigits: 2,
                                        }
                                      )}
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid #000",
                                        padding: "2px 4px",
                                        textAlign: "right",
                                      }}
                                    >
                                      {(
                                        item.acquisitionValue || 0
                                      ).toLocaleString("zh-TW", {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2,
                                      })}
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid #000",
                                        padding: "2px 4px",
                                        textAlign: "right",
                                      }}
                                    >
                                      {(
                                        item.currentYearAccumulatedDepreciation ||
                                        0
                                      ).toLocaleString("zh-TW", {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2,
                                      })}
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid #000",
                                        padding: "2px 4px",
                                        textAlign: "right",
                                      }}
                                    >
                                      {(
                                        item.accumulatedDepreciation || 0
                                      ).toLocaleString("zh-TW", {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2,
                                      })}
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid #000",
                                        padding: "2px 4px",
                                        textAlign: "right",
                                      }}
                                    >
                                      {(item.netBookValue || 0).toLocaleString(
                                        "zh-TW",
                                        {
                                          minimumFractionDigits: 2,
                                          maximumFractionDigits: 2,
                                        }
                                      )}
                                    </td>
                                    <td
                                      className={
                                        (item.notes || "").length > 20
                                          ? "long-content"
                                          : ""
                                      }
                                      style={{
                                        border: "1px solid #000",
                                        padding: "2px 4px",
                                      }}
                                    >
                                      {item.notes || ""}
                                    </td>
                                  </tr>
                                ))}
                                {/* 部門合計列 */}
                                <tr
                                  className="total-row"
                                  style={{
                                    backgroundColor: "#f0f0f0",
                                    fontWeight: "bold",
                                  }}
                                >
                                  <td
                                    colSpan={6}
                                    style={{
                                      border: "1px solid #000",
                                      padding: "2px 4px",
                                      textAlign: "center",
                                    }}
                                  >
                                    {dept || "未設定部門"} 合計
                                  </td>
                                  <td
                                    style={{
                                      border: "1px solid #000",
                                      padding: "2px 4px",
                                      textAlign: "right",
                                    }}
                                  >
                                    {sumUnitPrice.toLocaleString("zh-TW", {
                                      minimumFractionDigits: 2,
                                      maximumFractionDigits: 2,
                                    })}
                                  </td>
                                  <td
                                    style={{
                                      border: "1px solid #000",
                                      padding: "2px 4px",
                                      textAlign: "right",
                                    }}
                                  >
                                    {sumAcq.toLocaleString("zh-TW", {
                                      minimumFractionDigits: 2,
                                      maximumFractionDigits: 2,
                                    })}
                                  </td>
                                  <td
                                    style={{
                                      border: "1px solid #000",
                                      padding: "2px 4px",
                                      textAlign: "right",
                                    }}
                                  >
                                    {sumCY.toLocaleString("zh-TW", {
                                      minimumFractionDigits: 2,
                                      maximumFractionDigits: 2,
                                    })}
                                  </td>
                                  <td
                                    style={{
                                      border: "1px solid #000",
                                      padding: "2px 4px",
                                      textAlign: "right",
                                    }}
                                  >
                                    {sumAcc.toLocaleString("zh-TW", {
                                      minimumFractionDigits: 2,
                                      maximumFractionDigits: 2,
                                    })}
                                  </td>
                                  <td
                                    style={{
                                      border: "1px solid #000",
                                      padding: "2px 4px",
                                      textAlign: "right",
                                    }}
                                  >
                                    {sumNet.toLocaleString("zh-TW", {
                                      minimumFractionDigits: 2,
                                      maximumFractionDigits: 2,
                                    })}
                                  </td>
                                  <td
                                    style={{
                                      border: "1px solid #000",
                                      padding: "2px 4px",
                                    }}
                                  ></td>
                                </tr>
                              </React.Fragment>
                            );
                          })}
                        </>
                      );
                    })()
                  )}

                  {/* 合計行 */}
                  {data.length > 0 &&
                    (() => {
                      const totalUnitPrice = data.reduce(
                        (s, r) => s + (r.unitPrice || 0),
                        0
                      );
                      return (
                        <tr
                          className="total-row"
                          style={{
                            backgroundColor: "#f0f0f0",
                            fontWeight: "bold",
                          }}
                        >
                          <td
                            colSpan={6}
                            style={{
                              border: "1px solid #000",
                              padding: "2px 4px",
                              textAlign: "center",
                            }}
                          >
                            總計
                          </td>
                          <td
                            style={{
                              border: "1px solid #000",
                              padding: "2px 4px",
                              textAlign: "right",
                            }}
                          >
                            {totalUnitPrice.toLocaleString("zh-TW", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}
                          </td>
                          <td
                            style={{
                              border: "1px solid #000",
                              padding: "2px 4px",
                              textAlign: "right",
                            }}
                          >
                            {statistics.totalValue.toLocaleString("zh-TW", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}
                          </td>
                          <td
                            style={{
                              border: "1px solid #000",
                              padding: "2px 4px",
                              textAlign: "right",
                            }}
                          >
                            {statistics.currentYearDepreciation.toLocaleString(
                              "zh-TW",
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}
                          </td>
                          <td
                            style={{
                              border: "1px solid #000",
                              padding: "2px 4px",
                              textAlign: "right",
                            }}
                          >
                            {statistics.totalDepreciation.toLocaleString(
                              "zh-TW",
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}
                          </td>
                          <td
                            style={{
                              border: "1px solid #000",
                              padding: "2px 4px",
                              textAlign: "right",
                            }}
                          >
                            {statistics.netValue.toLocaleString("zh-TW", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}
                          </td>
                          <td
                            style={{
                              border: "1px solid #000",
                              padding: "2px 4px",
                            }}
                          ></td>
                        </tr>
                      );
                    })()}
                </tbody>
              </table>

              {/* 簽核章 */}
              {selectedSignatureTemplateId && (
                <ReportFooter
                  selectedTemplate={signatureTemplates.find(
                    (t) => t.id === selectedSignatureTemplateId
                  )}
                />
              )}
            </div>
          ) : (
            // 列表模式
            data.length > 0 && (
              <Card
                title={`固定資產明細表 共 ${data.length} 筆資料`}
                extra={
                  <Space>
                    <Badge count={data.length} showZero>
                      <Button size="small">總計</Button>
                    </Badge>
                  </Space>
                }
              >
                <Table
                  columns={columns}
                  dataSource={data}
                  rowKey={(record) => record.assetId}
                  loading={loading}
                  scroll={{ x: 1800 }}
                  locale={{
                    emptyText: (
                      <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description={
                          <span style={{ color: "#999" }}>
                            查無符合條件的資料
                            <br />
                            請調整查詢條件後重新產生報表
                          </span>
                        }
                      />
                    ),
                  }}
                  pagination={{
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
                    pageSizeOptions: ["10", "20", "50", "100"],
                  }}
                  summary={(pageData) => {
                    const totalValue = pageData.reduce((sum, record) => {
                      return sum + (record.acquisitionValue || 0);
                    }, 0);
                    const currentYearDepreciation = pageData.reduce(
                      (sum, record) => {
                        return (
                          sum + (record.currentYearAccumulatedDepreciation || 0)
                        );
                      },
                      0
                    );
                    const totalDepreciation = pageData.reduce((sum, record) => {
                      return sum + (record.accumulatedDepreciation || 0);
                    }, 0);
                    const totalNetValue = pageData.reduce((sum, record) => {
                      return sum + (record.netBookValue || 0);
                    }, 0);

                    return (
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0} colSpan={7}>
                          <Text strong>本頁小計</Text>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={1}>
                          <Text strong>{formatTWCurrency(totalValue)}</Text>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={2}>
                          <Text strong>
                            {formatTWCurrency(currentYearDepreciation)}
                          </Text>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={3}>
                          <Text strong>
                            {formatTWCurrency(totalDepreciation)}
                          </Text>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={4}>
                          <Text strong>{formatTWCurrency(totalNetValue)}</Text>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell
                          index={5}
                          colSpan={2}
                        ></Table.Summary.Cell>
                      </Table.Summary.Row>
                    );
                  }}
                />
              </Card>
            )
          )}
        </div>
      </Card>
    </div>
  );
};

export default AssetDetailReportPage;
