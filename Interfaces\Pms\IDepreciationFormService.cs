using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IDepreciationFormService
    {
        /// <summary>
        /// 取得折舊單資料
        /// </summary>
        /// <returns>折舊單資料列表</returns>
        Task<List<DepreciationFormDTO>> GetDepreciationFormsAsync();

        /// <summary>
        /// 取得折舊單明細
        /// </summary>
        /// <param name="depreciationFormId">折舊單編號</param>
        /// <returns>折舊單明細資料</returns>
        Task<DepreciationFormDTO> GetDepreciationFormDetailsAsync(string depreciationFormId);

        /// <summary>
        /// 新增折舊單
        /// </summary>
        /// <param name="_data">折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddDepreciationFormAsync(DepreciationFormDTO _data);

        /// <summary>
        /// 編輯折舊單
        /// </summary>
        /// <param name="_data">折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditDepreciationFormAsync(DepreciationFormDTO _data);

        /// <summary>
        /// 刪除折舊單
        /// </summary>
        /// <param name="_data">折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteDepreciationFormAsync(DepreciationFormDTO _data);

        /// <summary>
        /// 檢查折舊紀錄是否已被使用
        /// </summary>
        /// <param name="_depreciationId">折舊紀錄編號</param>
        /// <returns>是否已被使用</returns>
        Task<bool> IsDepreciationUsedAsync(string _depreciationId);

        /// <summary>
        /// 產生財產折舊表
        /// </summary>
        /// <param name="request">查詢參數</param>
        /// <returns>財產折舊表報告</returns>
        Task<AssetDepreciationReportResponseDTO> GenerateAssetDepreciationReportAsync(AssetDepreciationReportRequestDTO request);

        /// <summary>
        /// 審核折舊單
        /// </summary>
        /// <param name="depreciationFormId">折舊單編號</param>
        /// <param name="isApproved">是否核准</param>
        /// <param name="approverId">審核人員</param>
        /// <param name="comment">審核意見</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        Task<(bool success, string message)> ApproveDepreciationFormAsync(string depreciationFormId, bool isApproved, string approverId, string? comment = null);

        /// <summary>
        /// 執行折舊單（審核通過後執行資產累計折舊金額更新）
        /// </summary>
        /// <param name="depreciationFormId">折舊單編號</param>
        /// <param name="operatorId">操作人員</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        Task<(bool success, string message)> ExecuteDepreciationFormAsync(string depreciationFormId, string operatorId);

        /// <summary>
        /// 批次處理折舊單
        /// </summary>
        /// <param name="batchDto">批次處理資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        Task<(bool success, string message)> BatchProcessDepreciationFormsAsync(DepreciationFormBatchDTO batchDto);

        /// <summary>
        /// 取得折舊單統計資料
        /// </summary>
        /// <param name="userId">使用者ID (可選，用於個人統計)</param>
        /// <returns>統計資料</returns>
        Task<object> GetDepreciationFormStatisticsAsync(string? userId = null);
    }
}