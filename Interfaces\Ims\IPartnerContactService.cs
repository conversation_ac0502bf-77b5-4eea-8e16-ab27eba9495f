using FAST_ERP_Backend.Models.Ims;

namespace FAST_ERP_Backend.Interfaces.Ims;

/// <summary> 商業夥伴聯絡人服務介面 </summary>
public interface IPartnerContactService
{
    /// <summary> 取得商業夥伴的所有聯絡人 </summary>
    Task<List<PartnerContactDTO>> GetAsync(Guid PartnerID);
    
    /// <summary> 取得特定商業夥伴聯絡人關聯 </summary>
    Task<PartnerContactDTO> GetAsync(Guid PartnerID, Guid ContactID);
    
    /// <summary> 新增商業夥伴聯絡人關聯 </summary>
    Task<(bool, string)> AddAsync(PartnerContactDTO dto);
    
    /// <summary> 更新商業夥伴聯絡人關聯 </summary>
    Task<(bool, string)> UpdateAsync(PartnerContactDTO dto);
    
    /// <summary> 刪除商業夥伴聯絡人關聯（硬刪除） </summary>
    Task<(bool, string)> DeleteAsync(Guid PartnerID, Guid ContactID);
} 