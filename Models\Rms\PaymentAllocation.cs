using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Rms
{
    /// <summary>
    /// 繳費分配關聯表
    /// </summary>
    public class PaymentAllocation : ModelBaseEntity
    {
        /// <summary>
        /// 分配編號
        /// </summary>
        [Key]
        [Comment("分配編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string PaymentAllocationId { get; set; }

        /// <summary>
        /// 繳費編號
        /// </summary>
        [Comment("繳費編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string PaymentId { get; set; }

        /// <summary>
        /// 費用編號
        /// </summary>
        [Comment("費用編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string FeeId { get; set; }

        /// <summary>
        /// 分配金額
        /// </summary>
        [Comment("分配金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        /// <summary>
        /// 繳費紀錄導航屬性
        /// </summary>
        public virtual Payment Payment { get; set; }

        /// <summary>
        /// 費用導航屬性
        /// </summary>
        public virtual Fee Fee { get; set; }
    }
}


