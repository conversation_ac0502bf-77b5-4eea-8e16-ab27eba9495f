using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Ims;
using Microsoft.EntityFrameworkCore;
using System.Transactions;

namespace FAST_ERP_Backend.Services.Ims;

/// <summary> 商業夥伴聯絡人服務 </summary>
public class PartnerContactService(ERPDbContext _context, ICurrentUserService _currentUserService, ILoggerService _logger) : IPartnerContactService
{
    /// <summary> 取得商業夥伴的所有聯絡人 </summary>
    public async Task<List<PartnerContactDTO>> GetAsync(Guid PartnerID)
    {
        var contacts = await _context.Ims_PartnerContact
            .Where(pc => pc.PartnerID == PartnerID) // 不過濾 IsActive，由前端判斷顯示
            .Include(pc => pc.Contact)
            .OrderBy(pc => pc.Priority)
            .ToListAsync();

        return contacts.Select(pc => EntityToDto(pc)).ToList();
    }

    /// <summary> 取得特定商業夥伴聯絡人關聯 </summary>
    public async Task<PartnerContactDTO> GetAsync(Guid PartnerID, Guid ContactID)
    {
        var entity = await _context.Ims_PartnerContact
            .Where(pc => pc.PartnerID == PartnerID && pc.ContactID == ContactID) // 不過濾 IsActive
            .Include(pc => pc.Contact)
            .FirstOrDefaultAsync();

        return EntityToDto(entity);
    }

    /// <summary> 新增商業夥伴聯絡人關聯 </summary>
    public async Task<(bool, string)> AddAsync(PartnerContactDTO dto)
    {
        using var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
        
        try
        {
            // 檢查是否已存在相同的關聯
            var existingContact = await _context.Ims_PartnerContact
                .FirstOrDefaultAsync(pc => pc.PartnerID == dto.PartnerID && pc.ContactID == dto.ContactID);

            if (existingContact != null)
            {
                throw new Exception($"商業夥伴聯絡人關聯已存在: {dto.PartnerID} - {dto.ContactID}");
            }

            var entity = new PartnerContact
            {
                PartnerID = dto.PartnerID,
                ContactID = dto.ContactID,
                Role = dto.Role,
                Priority = dto.Priority,
                Notes = dto.Notes,
                Expertise = dto.Expertise,
                BusinessScope = dto.BusinessScope,
                WorkingHours = dto.WorkingHours,
                Languages = dto.Languages,
                EmergencyContact = dto.EmergencyContact,
                EmergencyPhone = dto.EmergencyPhone,
                CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                CreateUserId = _currentUserService.UserId
            };

            _context.Ims_PartnerContact.Add(entity);
            await _context.SaveChangesAsync();

            transaction.Complete();
            
            await _logger.LogInfoAsync($"商業夥伴聯絡人關聯新增成功: {dto.PartnerID} - {dto.ContactID}", "PartnerContactService");
            
            return (true, $"商業夥伴聯絡人關聯新增成功!");
        }
        catch (Exception ex)
        {
            await _logger.LogErrorAsync($"商業夥伴聯絡人關聯新增失敗: {ex.Message}", ex, "PartnerContactService");
            throw;
        }
    }

    /// <summary> 更新商業夥伴聯絡人關聯 </summary>
    public async Task<(bool, string)> UpdateAsync(PartnerContactDTO dto)
    {
        using var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
        
        try
        {
            var entity = await _context.Ims_PartnerContact
                .FirstOrDefaultAsync(pc => pc.PartnerID == dto.PartnerID && pc.ContactID == dto.ContactID);

            if (entity == null)
            {
                throw new Exception($"商業夥伴聯絡人關聯不存在: {dto.PartnerID} - {dto.ContactID}");
            }

            // 更新屬性（不允許更改複合鍵）
            entity.Role = dto.Role;
            entity.Priority = dto.Priority;
            entity.Notes = dto.Notes;
            entity.Expertise = dto.Expertise;
            entity.BusinessScope = dto.BusinessScope;
            entity.WorkingHours = dto.WorkingHours;
            entity.Languages = dto.Languages;
            entity.EmergencyContact = dto.EmergencyContact;
            entity.EmergencyPhone = dto.EmergencyPhone;
            entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            entity.UpdateUserId = _currentUserService.UserId;

            await _context.SaveChangesAsync();
            transaction.Complete();

            await _logger.LogInfoAsync($"商業夥伴聯絡人關聯更新成功: {dto.PartnerID} - {dto.ContactID}", "PartnerContactService");
            
            return (true, $"商業夥伴聯絡人關聯更新成功!");
        }
        catch (Exception ex)
        {
            await _logger.LogErrorAsync($"商業夥伴聯絡人關聯更新失敗: {ex.Message}", ex, "PartnerContactService");
            throw;
        }
    }

    /// <summary> 刪除商業夥伴聯絡人關聯（硬刪除） </summary>
    public async Task<(bool, string)> DeleteAsync(Guid PartnerID, Guid ContactID)
    {
        try
        {
            var entity = await _context.Ims_PartnerContact
                .FirstOrDefaultAsync(pc => pc.PartnerID == PartnerID && pc.ContactID == ContactID);

            if (entity == null)
            {
                throw new Exception($"聯絡人關聯不存在: {PartnerID} - {ContactID}");
            }

            // 硬刪除，直接移除記錄
            _context.Ims_PartnerContact.Remove(entity);
            await _context.SaveChangesAsync();

            await _logger.LogInfoAsync($"商業夥伴聯絡人關聯硬刪除成功: {PartnerID} - {ContactID}", "PartnerContactService");
            
            return (true, $"商業夥伴聯絡人關聯已刪除!");
        }
        catch (Exception ex)
        {
            await _logger.LogErrorAsync($"商業夥伴聯絡人關聯硬刪除失敗: {ex.Message}", ex, "PartnerContactService");
            throw;
        }
    }




    /// <summary> 將 Entity 映射到 DTO </summary>
    private PartnerContactDTO EntityToDto(PartnerContact entity)
    {
        if (entity == null) return null;

        return new PartnerContactDTO
        {
            PartnerID = entity.PartnerID,
            ContactID = entity.ContactID,
            Role = entity.Role,
            IsPrimary = entity.Priority == 0, // 優先級為 0 表示主要聯絡人
            Priority = entity.Priority,
            Notes = entity.Notes,
            Expertise = entity.Expertise,
            BusinessScope = entity.BusinessScope,
            WorkingHours = entity.WorkingHours,
            Languages = entity.Languages,
            EmergencyContact = entity.EmergencyContact,
            EmergencyPhone = entity.EmergencyPhone,
            CreateTime = entity.CreateTime,
            CreateUserId = entity.CreateUserId,
            UpdateTime = entity.UpdateTime,
            UpdateUserId = entity.UpdateUserId,
            DeleteTime = entity.DeleteTime,
            DeleteUserId = entity.DeleteUserId,
            IsDeleted = entity.IsDeleted
        };
    }
} 