"use client";

import React, { useEffect } from "react";
import {
  Modal,
  Form,
  Card,
  Row,
  Col,
  Input,
  Select,
  InputNumber,
  message,
} from "antd";
import { UserOutlined, ContactsOutlined, EditOutlined } from "@ant-design/icons";

const { TextArea } = Input;
const { Option } = Select;

// Services
import { Contact } from "@/services/ims/ContactService";
import { PartnerContact } from "@/services/ims/partner";

// 共享組件
import ContactFormFields from "../contact/ContactFormFields";

// 響應式工具
import { useScreenSize, getResponsiveModalConfig } from "./ResponsiveModalConfig";

// 常數和工具
import { CONTACT_DEFAULTS } from "../contact/shared/contactConstants";
import { sanitizeContactData } from "../contact/shared/contactValidation";

// Types
interface ContactFormModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: Contact, partnerContactData?: Partial<PartnerContact>) => Promise<void>;
  loading: boolean;
  initialData?: Contact | null;
  mode?: 'create' | 'quick' | 'edit';
  /** Partner 頁面快速新增時的 Partner ID */
  partnerID?: string;
  /** 是否顯示 PartnerContact 相關欄位 */
  showPartnerContactFields?: boolean;
}

const ContactFormModal: React.FC<ContactFormModalProps> = ({
  visible,
  onClose,
  onSubmit,
  loading,
  initialData,
  mode = 'create',
  partnerID,
  showPartnerContactFields = false,
}) => {
  const [form] = Form.useForm();
  const [partnerContactForm] = Form.useForm();
  const { screenSize, isMobile } = useScreenSize();

  // 獲取響應式配置
  const responsiveConfig = getResponsiveModalConfig(screenSize);

  // 初始化表單數據
  useEffect(() => {
    if (visible) {
      if (initialData) {
        // 編輯模式：填入現有數據
        form.setFieldsValue({
          ...CONTACT_DEFAULTS,
          ...initialData
        });
      } else {
        // 新增模式：使用預設值
        form.setFieldsValue(CONTACT_DEFAULTS);
      }

      // 重置 PartnerContact 表單
      if (showPartnerContactFields) {
        partnerContactForm.setFieldsValue({
          partnerID: partnerID,
          priority: 99,
          role: ''  // 改為空字串，允許用戶選填
        });
      }
    }
  }, [visible, initialData, form, partnerContactForm, partnerID, showPartnerContactFields]);

  // 清理表單
  useEffect(() => {
    if (!visible) {
      form.resetFields();
      partnerContactForm.resetFields();
    }
  }, [visible, form, partnerContactForm]);

  // 將 ContactFormModal 的模式映射到 ContactFormFields 的模式
  const getContactFormFieldsMode = (): 'quick' | 'full' | 'readonly' => {
    switch (mode) {
      case 'quick':
        return 'quick';
      case 'edit':
        return 'full';
      case 'create':
      default:
        return 'full';
    }
  };

  // 處理表單提交
  const handleSubmit = async () => {
    try {
      // 驗證主表單
      const contactValues = await form.validateFields();
      
      // 清理和驗證聯絡人數據
      const contactData = sanitizeContactData(contactValues);

      // 如果顯示 PartnerContact 欄位，也要驗證這部分
      let partnerContactData: Partial<PartnerContact> | undefined;
      if (showPartnerContactFields) {
        const partnerContactValues = await partnerContactForm.validateFields();
        partnerContactData = {
          partnerID: partnerContactValues.partnerID,
          priority: partnerContactValues.priority || 99,
          role: partnerContactValues.role || 'Other'
        };
      }

      await onSubmit(contactData, partnerContactData);
    } catch (error) {
      console.error("表單驗證失敗:", error);
      message.error("請檢查表單內容");
    }
  };

  // 獲取模態框標題
  const getModalTitle = () => {
    const icons = {
      create: <UserOutlined style={{ color: "#1890ff" }} />,
      quick: <ContactsOutlined style={{ color: "#52c41a" }} />,
      edit: <EditOutlined style={{ color: "#faad14" }} />
    };

    const titles = {
      create: "新增聯絡人",
      quick: "快速新增聯絡人",
      edit: "編輯聯絡人"
    };

    return (
      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        {icons[mode]}
        <span>{initialData ? titles.edit : titles[mode]}</span>
      </div>
    );
  };

  return (
    <Modal
      title={getModalTitle()}
      open={visible}
      onCancel={onClose}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={responsiveConfig.modalWidth}
      style={responsiveConfig.modalStyle}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
      okText={initialData ? "更新" : "新增"}
      cancelText="取消"
      className="contact-form-modal"
    >
      <div style={{ 
        maxHeight: responsiveConfig.modalMaxHeight,
        overflowY: 'auto',
        padding: isMobile ? '8px' : '16px'
      }}>
        {/* 聯絡人基本資料 */}
        <Card
          title={
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <UserOutlined style={{ color: "#1890ff" }} />
              <span>聯絡人資料</span>
            </div>
          }
          size="small"
          style={{ marginBottom: showPartnerContactFields ? 16 : 0 }}
        >
          <Form
            form={form}
            layout="vertical"
            onValuesChange={() => {
              // 可以在這裡處理值變更，例如即時驗證
            }}
            requiredMark={false}
            scrollToFirstError
            preserve={false}
            initialValues={{
              ...CONTACT_DEFAULTS,
              ...initialData
            }}
          >
            <ContactFormFields
              mode={getContactFormFieldsMode()}
              initialValues={initialData || undefined}
              form={form}
            />
          </Form>
        </Card>

        {/* PartnerContact 關聯資料（可選） */}
        {showPartnerContactFields && (
          <Card
            title={
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <ContactsOutlined style={{ color: "#52c41a" }} />
                <span>關聯設定</span>
              </div>
            }
            size="small"
          >
            <Form
              form={partnerContactForm}
              layout="vertical"
              requiredMark={false}
              preserve={false}
            >
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="優先順序"
                    name="priority"
                    rules={[
                      { type: "number", min: 0, max: 999, message: "優先順序必須在 0-999 之間" }
                    ]}
                    tooltip="數字越小優先級越高，0 代表主要聯絡人，預設為 99"
                  >
                    <InputNumber
                      placeholder="預設 99"
                      style={{ width: "100%" }}
                      min={0}
                      max={999}
                      defaultValue={99}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="角色"
                    name="role"
                    tooltip="聯絡人在此夥伴關係中的角色，可選填"
                  >
                    <Input placeholder="例如：業務聯絡人、技術聯絡人（可選填）" />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default ContactFormModal;
