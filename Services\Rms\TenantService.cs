using FAST_ERP_Backend.Interfaces.Rms;
using FAST_ERP_Backend.Models.Rms;
using FAST_ERP_Backend.Models;
using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;

namespace FAST_ERP_Backend.Services.Rms
{
    /// <summary>
    /// 租戶管理服務實作
    /// </summary>
    public class TenantService : ITenantService
    {
        private readonly ERPDbContext _context;
        private readonly ILogger<TenantService> _logger;

        /// <summary>
        /// 初始化租戶服務
        /// </summary>
        /// <param name="context">資料庫上下文</param>
        /// <param name="logger">日誌服務</param>
        public TenantService(ERPDbContext context, ILogger<TenantService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 取得租戶列表
        /// </summary>
        public async Task<ApiResponse<List<TenantDTO>>> GetTenantListAsync(TenantQueryRequestDTO request)
        {
            try
            {
                var query = _context.Rms_Tenants.AsQueryable();

                // 套用查詢條件
                if (!string.IsNullOrEmpty(request.TenantId))
                    query = query.Where(t => t.TenantId == request.TenantId);

                if (!string.IsNullOrEmpty(request.TenantName))
                    query = query.Where(t => t.TenantName.Contains(request.TenantName));

                if (!string.IsNullOrEmpty(request.ContactPerson))
                    query = query.Where(t => t.ContactPerson.Contains(request.ContactPerson));

                if (!string.IsNullOrEmpty(request.ContactIdNumber))
                    query = query.Where(t => t.ContactIdNumber.Contains(request.ContactIdNumber));

                if (!string.IsNullOrEmpty(request.ContactPhone))
                    query = query.Where(t => t.ContactPhone.Contains(request.ContactPhone));

                if (!string.IsNullOrEmpty(request.Email))
                    query = query.Where(t => t.Email.Contains(request.Email));

                // 取得總筆數
                var totalCount = await query.CountAsync();

                // 分頁查詢
                var tenants = await query
                    .OrderByDescending(t => t.CreateTime) // 改為按建立時間倒序排列
                    .Select(t => new TenantDTO
                    {
                        TenantId = t.TenantId,
                        TenantName = t.TenantName,
                        ContactPerson = t.ContactPerson,
                        ContactIdNumber = t.ContactIdNumber,
                        ContactPhone = t.ContactPhone,
                        Email = t.Email,
                        Address = t.Address,
                        Note = t.Note,
                        CreateTime = t.CreateTime ?? 0,
                        CreateUserId = t.CreateUserId,
                        UpdateTime = t.UpdateTime ?? 0,
                        UpdateUserId = t.UpdateUserId
                    })
                    .ToListAsync();

                return new ApiResponse<List<TenantDTO>>
                {
                    Success = true,
                    Data = tenants,
                    Message = "取得租戶列表成功",
                    HttpCode = 200
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得租戶列表時發生錯誤");
                return ApiResponse<List<TenantDTO>>.ErrorResult("取得租戶列表時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 取得租戶詳細資料
        /// </summary>
        public async Task<ApiResponse<TenantDTO>> GetTenantByIdAsync(string tenantId)
        {
            try
            {
                var tenant = await _context.Rms_Tenants
                    .Where(t => t.TenantId == tenantId)
                    .Select(t => new TenantDTO
                    {
                        TenantId = t.TenantId,
                        TenantName = t.TenantName,
                        ContactPerson = t.ContactPerson,
                        ContactIdNumber = t.ContactIdNumber,
                        ContactPhone = t.ContactPhone,
                        Email = t.Email,
                        Address = t.Address,
                        Note = t.Note,
                        CreateTime = t.CreateTime ?? 0,
                        CreateUserId = t.CreateUserId,
                        UpdateTime = t.UpdateTime ?? 0,
                        UpdateUserId = t.UpdateUserId
                    })
                    .FirstOrDefaultAsync();

                if (tenant == null)
                {
                    return ApiResponse<TenantDTO>.ErrorResult("租戶不存在", 404);
                }

                return ApiResponse<TenantDTO>.SuccessResult(tenant, "取得租戶詳細資料成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得租戶詳細資料時發生錯誤，TenantId: {TenantId}", tenantId);
                return ApiResponse<TenantDTO>.ErrorResult("取得租戶詳細資料時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 新增租戶
        /// </summary>
        public async Task<ApiResponse<TenantDTO>> CreateTenantAsync(TenantCreateRequestDTO request, string tokenUid)
        {
            try
            {
                var tenant = new Tenant
                {
                    TenantId = Guid.NewGuid().ToString(),
                    TenantName = request.TenantName,
                    ContactPerson = request.ContactPerson,
                    ContactIdNumber = request.ContactIdNumber,
                    ContactPhone = request.ContactPhone,
                    Email = request.Email,
                    Address = request.Address,
                    Note = request.Note,
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    CreateUserId = tokenUid
                };

                _context.Rms_Tenants.Add(tenant);
                await _context.SaveChangesAsync();

                var tenantDto = new TenantDTO
                {
                    TenantId = tenant.TenantId,
                    TenantName = tenant.TenantName,
                    ContactPerson = tenant.ContactPerson,
                    ContactIdNumber = tenant.ContactIdNumber,
                    ContactPhone = tenant.ContactPhone,
                    Email = tenant.Email,
                    Address = tenant.Address,
                    Note = tenant.Note,
                    CreateTime = tenant.CreateTime ?? 0,
                    CreateUserId = tenant.CreateUserId
                };

                return ApiResponse<TenantDTO>.SuccessResult(tenantDto, "新增租戶成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "新增租戶時發生錯誤");
                return ApiResponse<TenantDTO>.ErrorResult("新增租戶時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 更新租戶
        /// </summary>
        public async Task<ApiResponse<string>> UpdateTenantAsync(TenantUpdateRequestDTO request, string tokenUid)
        {
            try
            {
                // 由於 TenantExistsAttribute 已經驗證租戶存在，這裡可以直接查詢
                var tenant = await _context.Rms_Tenants
                    .FirstOrDefaultAsync(t => t.TenantId == request.TenantId);

                // 更新資料
                tenant.TenantName = request.TenantName;
                tenant.ContactPerson = request.ContactPerson;
                tenant.ContactIdNumber = request.ContactIdNumber;
                tenant.ContactPhone = request.ContactPhone;
                tenant.Email = request.Email;
                tenant.Address = request.Address;
                tenant.Note = request.Note;
                tenant.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                tenant.UpdateUserId = tokenUid;

                await _context.SaveChangesAsync();

                return ApiResponse<string>.SuccessResult("更新租戶成功", "更新租戶成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新租戶時發生錯誤");
                return ApiResponse<string>.ErrorResult("更新租戶時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 刪除租戶
        /// </summary>
        public async Task<ApiResponse<string>> DeleteTenantAsync(TenantDeleteRequestDTO request, string tokenUid)
        {
            try
            {
                // 由於 TenantExistsAttribute 和 TenantCanDeleteAttribute 已經驗證租戶存在且可刪除，這裡可以直接查詢
                var tenant = await _context.Rms_Tenants
                    .FirstOrDefaultAsync(t => t.TenantId == request.TenantId);

                // 軟刪除
                tenant.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                tenant.DeleteUserId = tokenUid;
                tenant.IsDeleted = true;

                await _context.SaveChangesAsync();

                return ApiResponse<string>.SuccessResult("刪除租戶成功", "刪除租戶成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刪除租戶時發生錯誤");
                return ApiResponse<string>.ErrorResult("刪除租戶時發生錯誤", 500);
            }
        }
    }
}