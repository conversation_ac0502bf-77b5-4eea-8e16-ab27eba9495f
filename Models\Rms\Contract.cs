using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
using FAST_ERP_Backend.Attributes;
using FAST_ERP_Backend.Models.Rms.Enums;

namespace FAST_ERP_Backend.Models.Rms
{
    /// <summary>
    /// 合約資料表
    /// </summary>
    public class Contract : ModelBaseEntity
    {
        [Key]
        [Comment("合約編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string ContractId { get; set; } // 合約編號

        [Comment("房源編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string PropertyId { get; set; } // 房源編號 (FK)

        [Comment("租戶編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string TenantId { get; set; } // 租戶編號 (FK)

        [Comment("合約起始日")]
        [Column(TypeName = "bigint")]
        public long StartDate { get; set; } // 合約起始日

        [Comment("合約終止日")]
        [Column(TypeName = "bigint")]
        public long EndDate { get; set; } // 合約終止日

        [Comment("月租金金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal RentAmount { get; set; } // 月租金金額

        [Comment("押金金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? DepositAmount { get; set; } // 押金金額

        [Comment("合約狀態")]
        [Column(TypeName = "nvarchar(50)")]
        public string Status { get; set; } = ContractStatusInfo.Draft.Code; // 合約狀態（草稿/啟用/終止）

        [Comment("快照資料")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string? SnapshotData { get; set; } // JSON格式的快照資料

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string? Note { get; set; } // 備註

        [Comment("每月繳款日(1-28)，未填則預設採起始日的日並壓到28")]
        [Column(TypeName = "int")]
        public int PaymentDueDay { get; set; } // 每月繳款日

        // Navigation Properties
        public virtual Property Property { get; set; }
        public virtual Tenant Tenant { get; set; }
        public virtual ICollection<Fee> Fees { get; set; }
        public virtual ICollection<Payment> Payments { get; set; }

        public Contract()
        {
            Fees = new HashSet<Fee>();
            Payments = new HashSet<Payment>();
        }
    }

    /// <summary>
    /// 合約資料傳輸物件
    /// </summary>
    public class ContractDTO : ModelBaseEntityDTO
    {
        public string ContractId { get; set; } // 合約編號
        public string PropertyId { get; set; } // 房源編號
        public string TenantId { get; set; } // 租戶編號
        public long StartDate { get; set; } // 合約起始日
        public long EndDate { get; set; } // 合約終止日
        public decimal RentAmount { get; set; } // 月租金金額
        public decimal? DepositAmount { get; set; } // 押金金額
        public string Status { get; set; } // 合約狀態
        public string? SnapshotData { get; set; } // 快照資料
        public string? Note { get; set; } // 備註
        public int PaymentDueDay { get; set; } // 每月繳款日

        // 關聯資料
        public string? PropertyName { get; set; } // 房源名稱
        public string? TenantName { get; set; } // 租戶名稱

        public ContractDTO()
        {
        }
    }

    /// <summary>
    /// 合約查詢請求 DTO
    /// </summary>
    public class ContractQueryRequestDTO
    {
        public string? ContractId { get; set; }
        public string? PropertyId { get; set; }
        public string? TenantId { get; set; }
        public string? Status { get; set; }
        public string? PropertyName { get; set; }
        public string? TenantName { get; set; }
        
        // 分頁參數
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// 合約建立請求 DTO
    /// </summary>
    public class ContractCreateRequestDTO
    {
        [Required(ErrorMessage = "房源編號不能為空")]
        [ContractCreateValidation]
        public string PropertyId { get; set; }

        [Required(ErrorMessage = "租戶編號不能為空")]
        public string TenantId { get; set; }

        [Required(ErrorMessage = "合約起始日不能為空")]
        public long StartDate { get; set; }

        [Required(ErrorMessage = "合約終止日不能為空")]
        public long EndDate { get; set; }

        [Required(ErrorMessage = "月租金金額不能為空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "月租金金額必須大於0")]
        public decimal RentAmount { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "押金金額不能為負數")]
        public decimal? DepositAmount { get; set; }

        [StringLength(500, ErrorMessage = "備註長度不能超過500字元")]
        public string Note { get; set; }

        [Required(ErrorMessage = "每月繳款日不能為空")]
        [Range(1, 28, ErrorMessage = "每月繳款日需介於1到28")]
        public int PaymentDueDay { get; set; }
    }

    /// <summary>
    /// 合約更新請求 DTO
    /// </summary>
    public class ContractUpdateRequestDTO
    {
        [Required(ErrorMessage = "合約編號不能為空")]
        [ContractUpdateValidation]
        public string ContractId { get; set; }

        [Required(ErrorMessage = "房源編號不能為空")]
        public string PropertyId { get; set; }

        [Required(ErrorMessage = "租戶編號不能為空")]
        public string TenantId { get; set; }

        [Required(ErrorMessage = "合約起始日不能為空")]
        public long StartDate { get; set; }

        [Required(ErrorMessage = "合約終止日不能為空")]
        public long EndDate { get; set; }

        [Required(ErrorMessage = "月租金金額不能為空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "月租金金額必須大於0")]
        public decimal RentAmount { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "押金金額不能為負數")]
        public decimal? DepositAmount { get; set; }

        [StringLength(500, ErrorMessage = "備註長度不能超過500字元")]
        public string Note { get; set; }

        [Required(ErrorMessage = "每月繳款日不能為空")]
        [Range(1, 28, ErrorMessage = "每月繳款日需介於1到28")]
        public int PaymentDueDay { get; set; }
    }

    /// <summary>
    /// 合約啟用請求 DTO
    /// </summary>
    public class ContractActivateRequestDTO
    {
        [Required(ErrorMessage = "合約編號不能為空")]
        [ContractCanActivate]
        public string ContractId { get; set; }
    }

    /// <summary>
    /// 合約終止請求 DTO
    /// </summary>
    public class ContractTerminateRequestDTO
    {
        [Required(ErrorMessage = "合約編號不能為空")]
        [ContractCanTerminate]
        public string ContractId { get; set; }
    }

    /// <summary>
    /// 合約刪除請求 DTO
    /// </summary>
    public class ContractDeleteRequestDTO
    {
        [Required(ErrorMessage = "合約編號不能為空")]
        [ContractCanDelete]
        public string ContractId { get; set; }
    }

    /// <summary>
    /// 合約可啟用驗證屬性
    /// </summary>
    public class ContractCanActivateAttribute : ValidationAttribute
    {
        public ContractCanActivateAttribute()
        {
            ErrorMessage = "合約無法啟用";
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success; // 空值由其他驗證處理
            }

            // 嘗試從服務容器中取得 DbContext
            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            var contractId = value.ToString();
            
            // 檢查合約狀態是否為草稿
            var contract = context.Rms_Contracts
                .FirstOrDefault(c => c.ContractId == contractId);

            if (contract == null)
            {
                return new ValidationResult("合約不存在");
            }

            if (contract.Status != "0") // 不是草稿狀態
            {
                return new ValidationResult("只有草稿狀態的合約可以啟用");
            }

            // 檢查房源是否為空置狀態
            var property = context.Rms_Properties
                .FirstOrDefault(p => p.PropertyId == contract.PropertyId);

            if (property == null)
            {
                return new ValidationResult("房源不存在");
            }

            if (property.Status != "1") // 不是空置狀態
            {
                return new ValidationResult("房源必須為空置狀態才能啟用合約");
            }

            // 檢查是否有日期重疊的其他啟用合約
            var overlappingContracts = context.Rms_Contracts
                .Where(c => c.PropertyId == contract.PropertyId 
                           && c.Status == "1" // 啟用狀態
                           && c.ContractId != contractId
                           && ((c.StartDate <= contract.StartDate && c.EndDate >= contract.StartDate) ||
                                (c.StartDate <= contract.EndDate && c.EndDate >= contract.EndDate) ||
                                (c.StartDate >= contract.StartDate && c.EndDate <= contract.EndDate)))
                .Any();

            if (overlappingContracts)
            {
                return new ValidationResult("該房源在此期間已有其他啟用的合約");
            }

            // 檢查該合約是否已存在任何費用資料（避免啟用時重複產生費用）
            var hasAnyFee = context.Rms_Fees.Any(f => f.ContractId == contractId);
            if (hasAnyFee)
            {
                return new ValidationResult("該合約已存在費用資料，無法啟用");
            }

            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 合約可終止驗證屬性
    /// </summary>
    public class ContractCanTerminateAttribute : ValidationAttribute
    {
        public ContractCanTerminateAttribute()
        {
            ErrorMessage = "合約無法終止";
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success; // 空值由其他驗證處理
            }

            // 嘗試從服務容器中取得 DbContext
            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            var contractId = value.ToString();
            
            // 檢查合約狀態是否為啟用
            var contract = context.Rms_Contracts
                .FirstOrDefault(c => c.ContractId == contractId);

            if (contract == null)
            {
                return new ValidationResult("合約不存在");
            }

            if (contract.Status != "1") // 不是啟用狀態
            {
                return new ValidationResult("只有啟用狀態的合約可以終止");
            }
            
            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 合約可刪除驗證屬性
    /// </summary>
    public class ContractCanDeleteAttribute : ValidationAttribute
    {
        public ContractCanDeleteAttribute()
        {
            ErrorMessage = "合約無法刪除";
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success; // 空值由其他驗證處理
            }

            // 嘗試從服務容器中取得 DbContext
            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            var contractId = value.ToString();
            
            // 檢查合約狀態
            var contract = context.Rms_Contracts
                .FirstOrDefault(c => c.ContractId == contractId);

            if (contract == null)
            {
                return new ValidationResult("合約不存在");
            }

            // 只有草稿狀態的合約可以刪除
            if (contract.Status != "0")
            {
                return new ValidationResult("只有草稿狀態的合約可以刪除");
            }

            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 合約更新複合驗證屬性（優化 SQL 查詢）
    /// </summary>
    public class ContractUpdateValidationAttribute : ValidationAttribute
    {
        public ContractUpdateValidationAttribute()
        {
            ErrorMessage = "合約更新驗證失敗";
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success; // 空值由其他驗證處理
            }

            // 嘗試從服務容器中取得 DbContext
            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            var contractId = value.ToString();
            
            // 一次查詢取得合約、房源、租戶資料
            var contract = context.Rms_Contracts
                .Include(c => c.Property)
                .Include(c => c.Tenant)
                .FirstOrDefault(c => c.ContractId == contractId);

            if (contract == null)
            {
                return new ValidationResult("合約不存在");
            }

            // 檢查合約狀態
            if (contract.Status != "0")
            {
                return new ValidationResult("只有草稿狀態的合約可以更新");
            }

            // 檢查房源是否存在
            if (contract.Property == null)
            {
                return new ValidationResult("房源不存在");
            }

            // 檢查租戶是否存在
            if (contract.Tenant == null)
            {
                return new ValidationResult("租戶不存在");
            }

            // 獲取當前請求的 DTO 實例以進行額外的業務邏輯驗證
            var instance = validationContext.ObjectInstance;
            if (instance is ContractUpdateRequestDTO updateRequest)
            {
                // 檢查房源是否為空置狀態（如果房源有變更）
                if (updateRequest.PropertyId != contract.PropertyId)
                {
                    var newProperty = context.Rms_Properties
                        .FirstOrDefault(p => p.PropertyId == updateRequest.PropertyId);
                    
                    if (newProperty == null)
                    {
                        return new ValidationResult("新指定的房源不存在");
                    }
                    
                    if (newProperty.Status != "1") // 不是空置狀態
                    {
                        return new ValidationResult("只能選擇空置狀態的房源");
                    }
                }

                // 檢查租戶是否存在（如果租戶有變更）
                if (updateRequest.TenantId != contract.TenantId)
                {
                    var newTenant = context.Rms_Tenants
                        .FirstOrDefault(t => t.TenantId == updateRequest.TenantId);
                    
                    if (newTenant == null)
                    {
                        return new ValidationResult("新指定的租戶不存在");
                    }
                }

                // 檢查日期邏輯
                if (updateRequest.StartDate >= updateRequest.EndDate)
                {
                    return new ValidationResult("合約起始日必須早於終止日");
                }
            }

            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 合約建立複合驗證屬性（優化 SQL 查詢）
    /// </summary>
    public class ContractCreateValidationAttribute : ValidationAttribute
    {
        public ContractCreateValidationAttribute()
        {
            ErrorMessage = "合約建立驗證失敗";
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success; // 空值由其他驗證處理
            }

            // 嘗試從服務容器中取得 DbContext
            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            var propertyId = value.ToString();
            
            // 一次查詢取得房源資料
            var property = context.Rms_Properties
                .FirstOrDefault(p => p.PropertyId == propertyId);

            if (property == null)
            {
                return new ValidationResult("房源不存在");
            }

            // 檢查房源是否為空置狀態
            if (property.Status != "1")
            {
                return new ValidationResult("只能選擇空置狀態的房源");
            }

            // 獲取當前請求的 DTO 實例以進行額外的業務邏輯驗證
            var instance = validationContext.ObjectInstance;
            if (instance is ContractCreateRequestDTO createRequest)
            {
                // 檢查租戶是否存在
                var tenant = context.Rms_Tenants
                    .FirstOrDefault(t => t.TenantId == createRequest.TenantId);
                
                if (tenant == null)
                {
                    return new ValidationResult("租戶不存在");
                }

                // 檢查日期邏輯
                if (createRequest.StartDate >= createRequest.EndDate)
                {
                    return new ValidationResult("合約起始日必須早於終止日");
                }

                // 檢查是否有日期重疊的合約（僅檢查啟用狀態的合約）
                var hasOverlappingContract = context.Rms_Contracts
                    .Any(c => c.PropertyId == propertyId && 
                              c.Status == "1" && // 啟用狀態
                              c.StartDate < createRequest.EndDate && 
                              c.EndDate > createRequest.StartDate);

                if (hasOverlappingContract)
                {
                    return new ValidationResult("該房源在指定日期範圍內已有啟用的合約");
                }
            }

            return ValidationResult.Success;
        }
    }

} 