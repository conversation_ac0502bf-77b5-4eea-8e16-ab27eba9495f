# 選單權限系統優化文件

## 概述

本文件記錄了選單權限系統的優化方案，主要解決早期版本中「自動添加父節點權限」導致的資料冗餘和顯示不一致問題。

## 問題分析

### 早期版本問題
1. **資料冗餘**：自動添加父節點權限導致資料庫存儲不必要的記錄
2. **邏輯複雜**：需要複雜的父子節點關係處理
3. **顯示不一致**：編輯時過濾父節點，但儲存時又添加父節點

### 當前版本問題
1. **半選狀態丟失**：沒有正確處理 Tree 組件的半選狀態
2. **歷史資料不一致**：可能存在歷史的父節點權限記錄

## 優化方案

### 1. 前端優化

#### Tree 組件處理優化
```typescript
// 優化前
const handleMenuCheck = (checked) => {
  const checkedKeys = Array.isArray(checked) ? checked : checked.checked;
  setSelectedPermissions(checkedKeys as string[]);
};

// 優化後
const handleMenuCheck = (checked) => {
  if (Array.isArray(checked)) {
    setSelectedPermissions(checked as string[]);
  } else {
    const { checked: checkedKeys, halfChecked } = checked;
    // 只儲存完全選中的節點，不包含半選狀態的父節點
    setSelectedPermissions(checkedKeys as string[]);
  }
};
```

#### 編輯載入優化
```typescript
// 新增過濾父節點的邏輯
const filterParentNodes = (permissionIds: string[], tree: SystemMenu[]): string[] => {
  // 過濾掉可能的自動添加父節點
  // 詳細實現見代碼
};

const handleEdit = (role: Role) => {
  const keys = (role.rolesPermissions || []).map((p) => p.systemMenuId);
  const filteredKeys = filterParentNodes(keys, menuTree);
  setSelectedPermissions(filteredKeys);
};
```

### 2. 資料清理

#### 清理腳本功能
- 自動備份現有權限資料
- 分析當前權限結構
- 識別冗餘的父節點權限
- 提供詳細的清理清單
- 安全的清理執行（需手動取消註解）

#### 清理策略
```sql
-- 識別需要清理的父節點權限
-- 如果父節點的所有子節點都有權限，則認為父節點權限是冗餘的
WITH ParentNodesWithAllChildrenPermitted AS (
  SELECT parent.SystemMenuId
  FROM Common_SystemMenu parent
  WHERE 所有子節點都有權限
)
```

### 3. API 契約確認

#### 現有 API 端點
- `GET api/Roles/GetRoles` - 獲取角色列表（包含權限）
- `POST api/Roles/AddRoles` - 新增角色
- `POST api/Roles/EditRoles` - 編輯角色
- `GET api/SystemMenu/GetAllMenu` - 獲取選單樹
- `GET api/SystemMenu/GetMyMenu` - 獲取用戶選單

#### 契約一致性
所有 API 都支援「只傳遞實際選中節點」的策略，不需要修改後端邏輯。

### 4. 後端服務確認

#### SystemMenuService.GetGroupMenuAsync
- 已有自動補父節點邏輯（`GetAllPermittedMenuIdsAsync`）
- 即使權限表不含父節點，側邊選單仍能正確顯示層級結構

#### RolesService.EditRolesAsync
- 使用 systemMenuId 做差異比對
- 不依賴父節點存在與否
- 支援「只存實際選中節點」的策略

## 測試案例

### 前端測試
1. **新增角色**：只勾選子節點，確認只儲存子節點權限
2. **編輯角色**：載入含歷史父節點的角色，確認過濾掉父節點
3. **Tree 顯示**：確認半選狀態正確顯示但不儲存

### 後端測試
1. **側邊選單**：確認只有子節點權限時，側邊選單仍顯示完整層級
2. **權限檢核**：確認頁面權限檢核不受影響
3. **資料一致性**：確認清理後資料庫狀態正確

### API 測試
```json
// 新增角色請求範例
{
  "name": "測試角色",
  "rolesPermissions": [
    {
      "systemMenuId": "child-menu-1"
    },
    {
      "systemMenuId": "child-menu-2"
    }
  ]
}
```

## 部署建議

### 部署順序
1. 部署前端優化代碼
2. 執行資料清理腳本（建議在維護時間）
3. 驗證功能正常
4. 監控系統運行狀況

### 回滾方案
1. 保留資料備份表 `Common_RolesPermissions_Backup`
2. 如需回滾，可從備份表恢復資料
3. 前端代碼可快速回滾到早期版本邏輯

## 監控指標

### 資料指標
- 權限記錄總數變化
- 冗餘父節點權限數量
- 用戶角色權限完整性

### 功能指標
- 側邊選單載入時間
- 權限檢核響應時間
- 角色編輯操作成功率

## 風險評估

### 低風險
- 前端 Tree 組件優化：向下相容
- API 契約：無變更
- 資料清理：有備份機制

### 中風險
- 歷史資料處理：需要仔細驗證
- 用戶體驗變化：需要用戶適應

### 緩解措施
- 充分測試
- 分階段部署
- 保留回滾機制
- 用戶培訓

## 總結

本次優化主要解決了選單權限系統中的資料冗餘和邏輯複雜性問題，採用「只存實際選中節點」的策略，簡化了前端邏輯，提高了資料一致性，同時保持了系統功能的完整性。
