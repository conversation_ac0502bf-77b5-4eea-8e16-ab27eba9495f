/**
 * Contact 相關的共享 Helper 工具類
 * 
 * 提供通用的驗證、格式化、轉換等邏輯
 * 避免在多個 Service 中重複實現相同功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Contact } from '@/services/ims/ContactService';
import { PartnerContact } from '@/services/ims/partner';
import { logger, createContextLogger } from '@/utils/logger';

// 創建上下文日誌器
const contactHelpersLogger = createContextLogger({ module: 'contactHelpers' });

// =========================================================================================
// 數據驗證 Helper
// =========================================================================================

/**
 * 驗證 Contact 數據的完整性
 */
export interface ContactValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 驗證 Contact 基本數據
 */
export const validateContactBasicData = (contact: Partial<Contact>): ContactValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 必填欄位驗證
  if (!contact.name?.trim()) {
    errors.push('聯絡人姓名不能為空');
  }

  if (!contact.contactType?.trim()) {
    errors.push('聯絡人類型不能為空');
  }

  // 格式驗證
  if (contact.email && !isValidEmail(contact.email)) {
    errors.push('電子郵件格式不正確');
  }

  if (contact.phone && !isValidPhone(contact.phone)) {
    warnings.push('電話號碼格式可能不正確');
  }

  // 長度驗證
  if (contact.name && contact.name.length > 100) {
    errors.push('聯絡人姓名不能超過100個字元');
  }

  if (contact.email && contact.email.length > 100) {
    errors.push('電子郵件不能超過100個字元');
  }

  if (contact.phone && contact.phone.length > 20) {
    errors.push('電話號碼不能超過20個字元');
  }

  if (contact.position && contact.position.length > 50) {
    errors.push('職位不能超過50個字元');
  }

  if (contact.department && contact.department.length > 50) {
    errors.push('部門不能超過50個字元');
  }

  if (contact.company && contact.company.length > 100) {
    errors.push('公司名稱不能超過100個字元');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * 驗證 PartnerContact 關聯數據
 */
export const validatePartnerContactData = (partnerContact: Partial<PartnerContact>): ContactValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 必填欄位驗證
  if (!partnerContact.partnerID?.trim()) {
    errors.push('商業夥伴ID不能為空');
  }

  if (!partnerContact.contactID?.trim()) {
    errors.push('聯絡人ID不能為空');
  }

  if (!partnerContact.role?.trim()) {
    errors.push('聯絡人角色不能為空');
  }

  // 優先級驗證
  if (partnerContact.priority !== undefined && 
      (partnerContact.priority < 0 || partnerContact.priority > 999)) {
    errors.push('優先級必須在0-999之間');
  }

  // 長度驗證
  if (partnerContact.role && partnerContact.role.length > 50) {
    errors.push('聯絡人角色不能超過50個字元');
  }

  if (partnerContact.notes && partnerContact.notes.length > 500) {
    errors.push('備註不能超過500個字元');
  }

  if (partnerContact.expertise && partnerContact.expertise.length > 200) {
    errors.push('專業領域不能超過200個字元');
  }

  if (partnerContact.businessScope && partnerContact.businessScope.length > 200) {
    errors.push('業務範圍不能超過200個字元');
  }

  if (partnerContact.workingHours && partnerContact.workingHours.length > 100) {
    errors.push('工作時間不能超過100個字元');
  }

  if (partnerContact.languages && partnerContact.languages.length > 100) {
    errors.push('語言能力不能超過100個字元');
  }

  if (partnerContact.emergencyContact && partnerContact.emergencyContact.length > 100) {
    errors.push('緊急聯絡人不能超過100個字元');
  }

  if (partnerContact.emergencyPhone && partnerContact.emergencyPhone.length > 20) {
    errors.push('緊急聯絡電話不能超過20個字元');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// =========================================================================================
// 格式驗證 Helper
// =========================================================================================

/**
 * 驗證電子郵件格式
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 驗證電話號碼格式（支持多種格式）
 */
export const isValidPhone = (phone: string): boolean => {
  // 移除所有非數字字符
  const cleanPhone = phone.replace(/\D/g, '');
  
  // 檢查長度（7-15位數字）
  if (cleanPhone.length < 7 || cleanPhone.length > 15) {
    return false;
  }
  
  // 基本格式檢查
  const phoneRegex = /^[\d\s\-\+\(\)\.]+$/;
  return phoneRegex.test(phone);
};

// =========================================================================================
// 數據轉換 Helper
// =========================================================================================

/**
 * 清理和正規化 Contact 數據
 */
export const sanitizeContactData = (contact: Partial<Contact>): Partial<Contact> => {
  return {
    ...contact,
    name: contact.name?.trim() || '',
    email: contact.email?.trim().toLowerCase() || '',
    phone: contact.phone?.trim() || '',
    position: contact.position?.trim() || '',
    department: contact.department?.trim() || '',
    company: contact.company?.trim() || '',
    contactType: contact.contactType?.trim() || '客戶',
    isActive: typeof contact.isActive === 'boolean' ? contact.isActive : true
  };
};

/**
 * 清理和正規化 PartnerContact 數據
 */
export const sanitizePartnerContactData = (partnerContact: Partial<PartnerContact>): Partial<PartnerContact> => {
  return {
    ...partnerContact,
    role: partnerContact.role?.trim() || '',
    notes: partnerContact.notes?.trim() || '',
    expertise: partnerContact.expertise?.trim() || '',
    businessScope: partnerContact.businessScope?.trim() || '',
    workingHours: partnerContact.workingHours?.trim() || '',
    languages: partnerContact.languages?.trim() || '',
    emergencyContact: partnerContact.emergencyContact?.trim() || '',
    emergencyPhone: partnerContact.emergencyPhone?.trim() || '',
    priority: typeof partnerContact.priority === 'number' ? partnerContact.priority : 99,
    isPrimary: typeof partnerContact.isPrimary === 'boolean' ? partnerContact.isPrimary : false
  };
};

// =========================================================================================
// 數據比較 Helper
// =========================================================================================

/**
 * 比較兩個 Contact 對象是否相等（忽略時間戳字段）
 */
export const isContactEqual = (contact1: Contact, contact2: Contact): boolean => {
  const fieldsToCompare = [
    'contactID', 'name', 'position', 'email', 'phone', 
    'isActive', 'contactType', 'department', 'company'
  ];
  
  return fieldsToCompare.every(field => 
    contact1[field as keyof Contact] === contact2[field as keyof Contact]
  );
};

/**
 * 獲取 Contact 對象的變更字段
 */
export const getContactChanges = (original: Contact, updated: Contact): Partial<Contact> => {
  const changes: Partial<Contact> = {};
  
  const fieldsToCheck = [
    'name', 'position', 'email', 'phone', 
    'isActive', 'contactType', 'department', 'company'
  ];
  
  fieldsToCheck.forEach(field => {
    const key = field as keyof Contact;
    if (original[key] !== updated[key]) {
      (changes as any)[key] = updated[key];
    }
  });
  
  return changes;
};

// =========================================================================================
// 搜索和篩選 Helper
// =========================================================================================

/**
 * 在 Contact 列表中搜索
 */
export const searchContactsInList = (contacts: Contact[], query: string): Contact[] => {
  if (!query.trim()) {
    return contacts;
  }
  
  const searchTerm = query.toLowerCase().trim();
  
  return contacts.filter(contact => 
    contact.name?.toLowerCase().includes(searchTerm) ||
    contact.email?.toLowerCase().includes(searchTerm) ||
    contact.phone?.includes(searchTerm) ||
    contact.company?.toLowerCase().includes(searchTerm) ||
    contact.department?.toLowerCase().includes(searchTerm) ||
    contact.position?.toLowerCase().includes(searchTerm)
  );
};

/**
 * 按類型篩選 Contact 列表
 */
export const filterContactsByType = (contacts: Contact[], contactType: string): Contact[] => {
  if (!contactType || contactType === 'all') {
    return contacts;
  }
  
  return contacts.filter(contact => contact.contactType === contactType);
};

/**
 * 按狀態篩選 Contact 列表
 */
export const filterContactsByStatus = (contacts: Contact[], isActive?: boolean): Contact[] => {
  if (isActive === undefined) {
    return contacts;
  }
  
  return contacts.filter(contact => contact.isActive === isActive);
};

// =========================================================================================
// 排序 Helper
// =========================================================================================

/**
 * Contact 排序選項
 */
export type ContactSortField = 'name' | 'email' | 'company' | 'contactType' | 'createTime';
export type SortDirection = 'asc' | 'desc';

/**
 * 排序 Contact 列表
 */
export const sortContacts = (
  contacts: Contact[], 
  field: ContactSortField, 
  direction: SortDirection = 'asc'
): Contact[] => {
  return [...contacts].sort((a, b) => {
    let aValue = a[field] || '';
    let bValue = b[field] || '';
    
    // 特殊處理時間字段
    if (field === 'createTime') {
      aValue = a.createTime || 0;
      bValue = b.createTime || 0;
    }
    
    // 字符串比較
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const result = aValue.localeCompare(bValue);
      return direction === 'asc' ? result : -result;
    }
    
    // 數字比較
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      const result = aValue - bValue;
      return direction === 'asc' ? result : -result;
    }
    
    return 0;
  });
};

// =========================================================================================
// 導出 Helper
// =========================================================================================

/**
 * 將 Contact 列表轉換為 CSV 格式
 */
export const contactsToCSV = (contacts: Contact[]): string => {
  const headers = ['姓名', '職位', '電子郵件', '電話', '公司', '部門', '類型', '狀態'];
  const csvContent = [
    headers.join(','),
    ...contacts.map(contact => [
      contact.name || '',
      contact.position || '',
      contact.email || '',
      contact.phone || '',
      contact.company || '',
      contact.department || '',
      contact.contactType || '',
      contact.isActive ? '啟用' : '停用'
    ].map(field => `"${field}"`).join(','))
  ].join('\n');
  
  return csvContent;
};

/**
 * 記錄 Helper 使用情況（用於調試）
 */
export const logHelperUsage = (helperName: string, data?: any) => {
  contactHelpersLogger.info(`使用 Helper: ${helperName}`, data);
};
