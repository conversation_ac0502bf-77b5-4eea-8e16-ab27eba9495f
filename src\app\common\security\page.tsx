"use client";

/* 權限管理系統
  /app/common/security/page.tsx
  雙軌分離架構：選單級權限和功能級權限管理
*/
import React, { useState, useEffect, useCallback } from "react";
import {
  Card,Table,Button,Input,Modal,
  Form,Tree,Space,Typography,message,
  Dropdown,Menu,Descriptions
} from "antd";
import {
  PlusOutlined,EditOutlined,DeleteOutlined,SearchOutlined,SecurityScanOutlined,
  UserOutlined,FileTextOutlined,MoreOutlined,SettingOutlined,TeamOutlined,
  CheckCircleOutlined,SaveOutlined,
} from "@ant-design/icons";
import { createContextLogger } from "@/utils/logger";
import PermissionResourceManagement from "./components/PermissionResourceManagement";
import AggregatePermissionView from "./components/AggregatePermissionView";
import { getRoles,addRole,editRole,deleteRole,Role as ApiRole } from "@/services/common/RolesService";
import { getAllMenuTree,MenuTreeResponse,SystemMenu as ApiSystemMenu } from "@/services/common/systemMenuService";
import { getSystemGroups,SystemGroup } from "@/services/common/systemGroupService";

const { Title } = Typography;

// 日誌記錄器
const logger = createContextLogger("SecurityPage" as any);

// 類型定義 (頁面內部使用)
interface Role {
  rolesId: string;
  name: string;
  rolesPermissions?: {
    rolesPermissionsId?: string;
    rolesId?: string;
    systemMenuId: string;
  }[];
}

interface SystemMenu {
  systemMenuId: string;
  systemGroupId: string;
  label: string;
  children?: SystemMenu[];
  key: React.Key;
  title: string;
}

/**
 * 權限管理系統主頁面
 * 採用雙軌分離架構：選單級權限和功能級權限各司其職
 */
const SecurityPage: React.FC = () => {
  // ==================== 選單級權限狀態 ====================
  const [roles, setRoles] = useState<Role[]>([]);
  const [filteredRoles, setFilteredRoles] = useState<Role[]>([]);
  const [searchText, setSearchText] = useState("");
  const [loading, setLoading] = useState(false);

  // 角色管理狀態
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);

  // 檢核預覽狀態
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewValues, setPreviewValues] = useState<any>(null);

  // 選單權限狀態
  const [menuTree, setMenuTree] = useState<SystemMenu[]>([]);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [systemGroups, setSystemGroups] = useState<SystemGroup[]>([]);

  // 多層級選單狀態
  const [currentView, setCurrentView] = useState("menu-level");
  const [functionPermissionTab, setFunctionPermissionTab] = useState("role-permission-assignment");

  // 表單實例
  const [form] = Form.useForm();

  // ==================== 工具函數 ====================
  /**
   * 過濾自動添加的父節點權限，只保留末節點權限
   *
   * 核心邏輯：
   * 1. 如果一個父節點的所有子節點都被選中，則認為該父節點是「自動添加的」
   * 2. 只保留用戶真正選擇的末節點權限，提升資料儲存效率
   * 3. 後端會在權限檢查時自動補齊父節點，確保選單顯示完整
   *
   * 範例：["系統設定", "權限管理", "用戶管理"] → ["權限管理", "用戶管理"]
   *
   * @param permissionIds 原始權限ID列表
   * @param tree 選單樹結構
   * @returns 過濾後的末節點權限ID列表
   */
  const filterParentNodes = useCallback((permissionIds: string[], tree: SystemMenu[]): string[] => {
    if (!permissionIds || permissionIds.length === 0) {
      return [];
    }

    const permissionSet = new Set(permissionIds);
    const autoAddedParentNodes = new Set<string>();
    const leafNodes = new Set<string>();

    // 建立選單映射表，便於快速查找
    const menuMap = new Map<string, SystemMenu>();
    const buildMenuMap = (nodes: SystemMenu[]) => {
      nodes.forEach(node => {
        menuMap.set(node.systemMenuId, node);
        // 識別末節點（沒有子節點的節點）
        if (!node.children || node.children.length === 0) {
          leafNodes.add(node.systemMenuId);
        }
        if (node.children) {
          buildMenuMap(node.children);
        }
      });
    };
    buildMenuMap(tree);

    // 遞歸檢查自動添加的父節點
    const checkIfAutoAddedParent = (nodeId: string): boolean => {
      const node = menuMap.get(nodeId);

      // 末節點：直接返回是否被選中
      if (!node || !node.children || node.children.length === 0) {
        return permissionSet.has(nodeId);
      }

      // 檢查所有子節點是否都被選中
      const allChildrenSelected = node.children.every(child =>
        checkIfAutoAddedParent(child.systemMenuId)
      );

      // 如果所有子節點都被選中且父節點也被選中，則父節點是自動添加的
      if (allChildrenSelected && permissionSet.has(nodeId)) {
        autoAddedParentNodes.add(nodeId);
        logger.debug("識別自動添加的父節點", {
          nodeId,
          label: node.label,
          childrenCount: node.children.length
        });
        return true;
      }

      return permissionSet.has(nodeId);
    };

    // 檢查所有有權限的節點
    permissionIds.forEach(nodeId => {
      if (!autoAddedParentNodes.has(nodeId)) {
        checkIfAutoAddedParent(nodeId);
      }
    });

    // 過濾掉自動添加的父節點，只保留末節點權限
    const filteredPermissions = permissionIds.filter(id => !autoAddedParentNodes.has(id));

    logger.debug("權限過濾結果", {
      原始權限: permissionIds.length,
      自動添加的父節點: autoAddedParentNodes.size,
      末節點權限: filteredPermissions.length,
      過濾掉的節點: Array.from(autoAddedParentNodes)
    });

    return filteredPermissions;
  }, []);

  // ==================== 資料載入 ====================
  useEffect(() => {
    loadRoles();
    loadMenuTree();
    loadSystemGroups();
  }, []);

  useEffect(() => {
    const filtered = roles.filter((role) =>
      role.name.toLowerCase().includes(searchText.toLowerCase())
    );
    setFilteredRoles(filtered);
  }, [roles, searchText]);

  /**
   * 載入角色列表（改為呼叫服務端 API）
   */
  const loadRoles = useCallback(async () => {
    try {
      setLoading(true);
      const resp = await getRoles();
      if (resp.success && resp.data) {
        const apiRoles = resp.data as ApiRole[];
        const mapped: Role[] = apiRoles.map((r) => ({
          rolesId: r.rolesId,
          name: r.name,
          rolesPermissions:
            r.rolesPermissions?.map((p) => ({
              rolesPermissionsId: p.rolesPermissionsId,
              rolesId: p.rolesId,
              systemMenuId: p.systemMenuId,
            })) || [],
        }));
        setRoles(mapped);
      } else {
        message.error(resp.message || "載入角色失敗");
      }
    } catch (error) {
      logger.error("載入角色失敗", error);
      message.error("載入角色失敗");
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 轉換 API 選單為 Antd Tree 所需結構
   */
  const mapMenu = (m: ApiSystemMenu): SystemMenu => ({
    systemMenuId: m.systemMenuId,
    systemGroupId: m.systemGroupId,
    label: m.label,
    key: m.systemMenuId,
    title: m.label,
    children: m.children ? m.children.map(mapMenu) : undefined,
  });

  /**
   * 載入選單樹結構（呼叫服務端 API）
   */
  const loadMenuTree = useCallback(async () => {
    try {
      const resp = await getAllMenuTree();
      if (resp.success && resp.data) {
        const groups = resp.data as MenuTreeResponse[];
        // 合併所有群組的 menus
        const menus: ApiSystemMenu[] = groups.flatMap((g) => g.menus || []);
        const tree = menus.map(mapMenu);
        setMenuTree(tree);
        // 預設展開所有第一層
        setExpandedKeys(tree.map((n) => n.key));
      } else {
        message.error(resp.message || "載入選單樹失敗");
      }
    } catch (error) {
      logger.error("載入選單樹失敗", error);
      message.error("載入選單樹失敗");
    }
  }, []);

  /**
   * 載入系統群組列表
   */
  const loadSystemGroups = useCallback(async () => {
    try {
      const resp = await getSystemGroups();
      if (resp.success && resp.data) {
        setSystemGroups(resp.data);
      } else {
        message.error(resp.message || "載入系統群組失敗");
      }
    } catch (error) {
      logger.error("載入系統群組失敗", error);
      message.error("載入系統群組失敗");
    }
  }, []);

  // ==================== 事件處理函數 ====================
  /**
   * 處理選單權限選擇
   * 優化：正確處理 Tree 組件的 checked 和 halfChecked 狀態
   */
  const handleMenuCheck = useCallback(
    (
      checked: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }
    ) => {
      if (Array.isArray(checked)) {
        // 簡單陣列格式
        setSelectedPermissions(checked as string[]);
      } else {
        // 物件格式，包含 checked 和 halfChecked
        const { checked: checkedKeys, halfChecked } = checked;
        
        // 策略：只儲存完全選中的節點，不包含半選狀態的父節點
        // 這確保資料庫只存儲用戶明確選擇的權限
        setSelectedPermissions(checkedKeys as string[]);
        
        // 可選：如果需要顯示半選狀態，可以儲存到另一個狀態
        // setHalfCheckedPermissions(halfChecked as string[]);
      }
    },
    []
  );

  /**
   * 處理選單展開
   */
  const handleMenuExpand = useCallback((expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys);
  }, []);

  /**
   * 處理檢核預覽
   * 優化：預覽時顯示過濾後的末節點權限資訊
   */
  const handlePreview = useCallback(async () => {
    try {
      const values = await form.validateFields();

      // 過濾自動添加的父節點，獲取實際要儲存的權限
      const leafNodePermissions = filterParentNodes(selectedPermissions || [], menuTree);

      // 處理選單權限顯示 - 按系統別分組
      const createGroupedMenuMap = (
        menus: SystemMenu[],
        groups: SystemGroup[]
      ): Map<string, { groupName: string; menus: string[] }> => {
        const groupMap = new Map<
          string,
          { groupName: string; menus: string[] }
        >();

        // 初始化系統群組映射
        groups.forEach((group) => {
          groupMap.set(group.systemGroupId, {
            groupName: group.name,
            menus: [],
          });
        });

        // 建立選單到系統群組的映射
        const traverse = (menuList: SystemMenu[]) => {
          for (const menu of menuList) {
            if (menu.label && menu.label.trim()) {
              const group = groupMap.get(menu.systemGroupId);
              if (group) {
                group.menus.push(menu.label);
              }
            }
            if (menu.children && menu.children.length > 0) {
              traverse(menu.children);
            }
          }
        };
        traverse(menus);

        return groupMap;
      };

      const groupMap = createGroupedMenuMap(menuTree, systemGroups);

      // 建立選單 ID 到名稱的映射
      const menuIdToNameMap = new Map<string, string>();
      const traverseForMapping = (menuList: SystemMenu[]) => {
        for (const menu of menuList) {
          if (menu.label && menu.label.trim()) {
            menuIdToNameMap.set(menu.systemMenuId, menu.label);
          }
          if (menu.children && menu.children.length > 0) {
            traverseForMapping(menu.children);
          }
        }
      };
      traverseForMapping(menuTree);

      // 按照系統別分組選取的權限（使用過濾後的末節點權限）
      const groupedPermissions: Array<{ groupName: string; menus: string[] }> =
        [];
      const ungroupedMenus: string[] = [];

      // 對過濾後的權限進行分組
      leafNodePermissions.forEach((menuId) => {
        const menuName = menuIdToNameMap.get(menuId);
        if (!menuName) return;

        // 找到對應的選單項目來獲取 systemGroupId
        let foundGroupId: string | null = null;
        const findMenuGroup = (menuList: SystemMenu[]): boolean => {
          for (const menu of menuList) {
            if (menu.systemMenuId === menuId) {
              foundGroupId = menu.systemGroupId;
              return true;
            }
            if (menu.children && menu.children.length > 0) {
              if (findMenuGroup(menu.children)) {
                return true;
              }
            }
          }
          return false;
        };
        findMenuGroup(menuTree);

        if (foundGroupId) {
          const group = groupMap.get(foundGroupId);
          if (group && group.menus.includes(menuName)) {
            // 查找是否已有這個系統群組
            let existingGroup = groupedPermissions.find(
              (g) => g.groupName === group.groupName
            );
            if (!existingGroup) {
              existingGroup = { groupName: group.groupName, menus: [] };
              groupedPermissions.push(existingGroup);
            }
            existingGroup.menus.push(menuName);
          }
        } else {
          ungroupedMenus.push(menuName);
        }
      });

      setPreviewValues({
        roleName: values.name,
        originalPermissionCount: selectedPermissions?.length || 0,
        menuPermissionCount: leafNodePermissions.length,
        filteredCount: (selectedPermissions?.length || 0) - leafNodePermissions.length,
        groupedPermissions,
        ungroupedMenus,
      });
      setPreviewVisible(true);
    } catch {
      // 驗證失敗，antd 已顯示錯誤
    }
  }, [form, selectedPermissions, menuTree, systemGroups, filterParentNodes]);

  /**
   * 處理角色提交（建立/更新 + 儲存選單權限）
   * 優化：只儲存末節點權限，過濾自動添加的父節點
   */
  const handleSubmit = useCallback(
    async (values: any) => {
      try {
        // 過濾自動添加的父節點，只保留末節點權限
        const leafNodePermissions = filterParentNodes(selectedPermissions || [], menuTree);

        logger.info("權限儲存優化", {
          原始權限數量: selectedPermissions?.length || 0,
          末節點權限數量: leafNodePermissions.length,
          過濾掉的父節點: (selectedPermissions?.length || 0) - leafNodePermissions.length
        });

        const payload: Partial<ApiRole> = {
          rolesId: editingRole?.rolesId,
          name: values.name,
          rolesPermissions: leafNodePermissions.map((menuId) => ({
            rolesPermissionsId: "",
            rolesId: editingRole?.rolesId || "",
            systemMenuId: menuId as string,
          })),
        } as any;

        const resp = editingRole
          ? await editRole(payload)
          : await addRole(payload);
        if (resp.success) {
          message.success(editingRole ? "角色更新成功" : "角色新增成功");
          // 重置檢核預覽狀態
          setPreviewVisible(false);
          setPreviewValues(null);
          setIsModalVisible(false);
          form.resetFields();
          setSelectedPermissions([]);
          loadRoles();
        } else {
          message.error(
            resp.message || (editingRole ? "角色更新失敗" : "角色新增失敗")
          );
        }
      } catch (error) {
        logger.error("提交角色失敗", error);
        message.error("提交角色失敗");
      }
    },
    [editingRole, selectedPermissions, form, loadRoles, filterParentNodes, menuTree]
  );

  /**
   * 處理角色編輯（載入該角色的選單權限）
   */
  const handleEdit = useCallback(
    (role: Role) => {
      setEditingRole(role);
      form.setFieldsValue({ name: role.name });
      
      const keys = (role.rolesPermissions || []).map((p) => p.systemMenuId);
      
      // 只保留實際選擇的權限
      const filteredKeys = filterParentNodes(keys, menuTree);
      setSelectedPermissions(filteredKeys);
      
      // 重置檢核預覽狀態
      setPreviewVisible(false);
      setPreviewValues(null);
      setIsModalVisible(true);
    },
    [form, menuTree]
  );

  /**
   * 處理角色刪除
   */
  const handleDelete = useCallback((role: Role) => {
    setRoleToDelete(role);
    setDeleteConfirmText("");
    setDeleteModalVisible(true);
  }, []);

  /**
   * 執行角色刪除
   */
  const executeDelete = useCallback(async () => {
    if (!roleToDelete) return;

    try {
      const resp = await deleteRole({
        rolesId: roleToDelete.rolesId,
        name: roleToDelete.name,
      });
      if (resp.success) {
        message.success("角色刪除成功");
        setDeleteModalVisible(false);
        setRoleToDelete(null);
        setDeleteConfirmText("");
        loadRoles();
      } else {
        message.error(resp.message || "角色刪除失敗");
      }
    } catch (error) {
      logger.error("刪除角色失敗", error);
      message.error("刪除角色失敗");
    }
  }, [roleToDelete, loadRoles]);

  /**
   * 統一導航處理函數
   */
  const handleNavigation = useCallback((key: string) => {
    if (key === 'menu-level') {
      setCurrentView('menu-level');
      logger.info('切換到選單級權限管理');
    } else {
      setFunctionPermissionTab(key);
      setCurrentView('function-level');
      logger.info('切換到功能級權限', { option: key });
    }
  }, []);

  // ==================== 表格列定義 ====================
  const columns = [
    {
      title: "角色名稱",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: Role) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            刪除
          </Button>
        </Space>
      ),
    },
  ];

  /**
   * 渲染選單級權限管理內容
   */
  const renderMenuPermissionContent = () => (
    <div>
      {/* 角色管理 */}
      <div style={{ marginBottom: 24 }}>
        <Space style={{ marginBottom: 16 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingRole(null);
              form.resetFields();
              setSelectedPermissions([]);
              // 重置檢核預覽狀態
              setPreviewVisible(false);
              setPreviewValues(null);
              setIsModalVisible(true);
            }}
          >
            新增角色
          </Button>
          <Input
            placeholder="搜尋角色名稱"
            prefix={<SearchOutlined />}
            allowClear
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 300 }}
          />
        </Space>

        <Table
          columns={columns}
          dataSource={filteredRoles}
          rowKey="rolesId"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
            showTotal: (total) => `共 ${total} 筆資料`,
          }}
        />
      </div>

      {/* 新增/編輯角色模態框 */}
      <Modal
        title={editingRole ? "編輯角色" : "新增角色"}
        open={isModalVisible}
        onOk={handlePreview}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
          setSelectedPermissions([]);
          // 重置檢核預覽狀態
          setPreviewVisible(false);
          setPreviewValues(null);
        }}
        okText="確認"
        cancelText="取消"
        width={1000}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="name"
            label="角色名稱"
            rules={[{ required: true, message: "請輸入角色名稱" }]}
          >
            <Input placeholder="請輸入角色名稱" />
          </Form.Item>
          <Form.Item label="選單權限設定" required>
            <Tree
              checkable
              checkedKeys={selectedPermissions}
              expandedKeys={expandedKeys}
              onExpand={handleMenuExpand}
              onCheck={handleMenuCheck}
              defaultExpandAll={true}
              treeData={menuTree}
              height={400}
              style={{ overflow: "auto" }}
              fieldNames={{
                title: "label",
                key: "systemMenuId",
                children: "children",
              }}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 檢核預覽 Modal */}
      <Modal
        title="請確認角色資料"
        open={previewVisible}
        onCancel={() => {
          setPreviewVisible(false);
          setPreviewValues(null);
        }}
        onOk={form.submit}
        okText={editingRole ? "確認更新" : "確認新增"}
        okButtonProps={
          editingRole
            ? { icon: <SaveOutlined /> }
            : { icon: <CheckCircleOutlined /> }
        }
        cancelText="返回修改"
        width={800}
      >
        <Descriptions bordered size="small" column={1}>
          <Descriptions.Item label="角色名稱">
            {previewValues?.roleName}
          </Descriptions.Item>
          <Descriptions.Item label="權限儲存優化">
            <div style={{ fontSize: "13px", lineHeight: "1.6" }}>
              <div>• 實際儲存權限：<strong style={{ color: "#52c41a" }}>{previewValues?.menuPermissionCount} 項</strong>（末節點權限）</div>
              {previewValues?.filteredCount > 0 && (
                <div>• 自動過濾父節點：<span style={{ color: "#faad14" }}>{previewValues?.filteredCount} 項</span></div>
              )}
              <div style={{ color: "#666", marginTop: "4px" }}>
                💡 系統會自動補齊父節點顯示，無需重複儲存
              </div>
            </div>
          </Descriptions.Item>

          {/* 按系統別分組顯示權限 */}
          {previewValues?.groupedPermissions?.map(
            (group: { groupName: string; menus: string[] }) => (
              <Descriptions.Item
                key={group.groupName}
                label={`${group.groupName} (${group.menus.length}項)`}
              >
                <div
                  style={{
                    maxHeight: "150px",
                    overflow: "auto",
                    padding: "8px",
                    borderRadius: "4px",
                    fontSize: "13px",
                    lineHeight: "1.6",
                  }}
                >
                  {group.menus.join("、")}
                </div>
              </Descriptions.Item>
            )
          )}

          {/* 未分組的選單 */}
          {previewValues?.ungroupedMenus?.length > 0 && (
            <Descriptions.Item
              label={`其他 (${previewValues.ungroupedMenus.length}項)`}
            >
              <div
                style={{
                  maxHeight: "150px",
                  overflow: "auto",
                  backgroundColor: "#fff2f0",
                  padding: "8px",
                  borderRadius: "4px",
                  fontSize: "13px",
                  lineHeight: "1.6",
                }}
              >
                {previewValues.ungroupedMenus.join("、")}
              </div>
            </Descriptions.Item>
          )}

          {/* 無權限提示 */}
          {previewValues?.menuPermissionCount === 0 && (
            <Descriptions.Item label="選單權限">
              <div
                style={{
                  color: "#999",
                  fontStyle: "italic",
                  padding: "16px",
                  textAlign: "center",
                }}
              >
                無選單權限
              </div>
            </Descriptions.Item>
          )}
        </Descriptions>
      </Modal>
    </div>
  );

  /**
   * 渲染功能級權限管理內容
   */
  const renderFunctionPermissionContent = () => {
    switch (functionPermissionTab) {
      case "role-permission-assignment":
        return <AggregatePermissionView />;
      case "permission-resource":
        return <PermissionResourceManagement />;
      default:
        return <AggregatePermissionView />;
    }
  };

  // 功能配置映射
  const functionConfig: Record<string, string> = {
    'menu-level': '選單級權限管理',
    'role-permission-assignment': '角色權限分配',
    'permission-resource': '權限資源管理'
  };

  /**
   * 獲取當前功能名稱
   */
  const getCurrentFunctionName = () => {
    const key = currentView === 'menu-level' ? 'menu-level' : functionPermissionTab;
    return functionConfig[key] || '選單級權限管理';
  };

  /**
   * 渲染當前內容
   */
  const renderCurrentContent = () => {
    if (currentView === 'menu-level') {
      return renderMenuPermissionContent();
    } else if (currentView === 'function-level') {
      return renderFunctionPermissionContent();
    }
    return renderMenuPermissionContent();
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 頁面標題 */}
      <div style={{ marginBottom: 20 }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: 16
        }}>
          <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '8px' }}>
            <SecurityScanOutlined /> 權限管理
            {currentView !== 'menu-level' && (
              <>
                <span style={{ color: '#d9d9d9', fontSize: '16px' }}>|</span>
                <span style={{ color: '#666', fontSize: '16px', fontWeight: 'normal' }}>
                  {getCurrentFunctionName()}
                </span>
              </>
            )}
            {/* 快速導航下拉選單 */}
            <Dropdown
              overlay={
                <Menu onClick={({ key }) => handleNavigation(key)}>
                  <Menu.Item key="menu-level" icon={<UserOutlined />}>
                    選單級權限管理
                  </Menu.Item>

                  <Menu.SubMenu
                    key="function-level"
                    icon={<FileTextOutlined />}
                    title="功能級權限管理"
                  >
                    <Menu.Item key="role-permission-assignment" icon={<TeamOutlined />}>
                      角色權限分配
                    </Menu.Item>
                    <Menu.Item key="permission-resource" icon={<SettingOutlined />}>
                      權限資源管理
                    </Menu.Item>
                  </Menu.SubMenu>
                </Menu>
              }
              trigger={['click']}
              placement="bottomRight"
            >
              <Button
                type="text"
                icon={<MoreOutlined style={{ fontSize: '20px' }} />}
                style={{
                  color: '#666',
                  border: 'none',
                  boxShadow: 'none',
                  padding: '4px 8px',
                  height: 'auto',
                  marginLeft: '8px'
                }}
              />
            </Dropdown>
          </Title>          
        </div>
      </div>

      {/* 主要內容區域 - 直接顯示，無標籤頁框架 */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        border: '1px solid #f0f0f0',
        padding: '24px'
      }}>
        {/* 顯示當前選中的內容 */}
        {renderCurrentContent()}
      </div>

      {/* 刪除確認對話框 */}
      <Modal
        title="刪除確認"
        open={deleteModalVisible}
        onOk={executeDelete}
        onCancel={() => {
          setDeleteModalVisible(false);
          setRoleToDelete(null);
          setDeleteConfirmText("");
        }}
        okText="刪除"
        cancelText="取消"
        okButtonProps={{
          danger: true,
          disabled: deleteConfirmText !== (roleToDelete?.name || ""),
        }}
      >
        <div>
          <div style={{ marginBottom: 16 }}>
            請輸入
            <strong style={{ color: "red", fontWeight: "bold" }}>
              {roleToDelete?.name}
            </strong>
            以確認刪除：
          </div>
          <Input
            autoFocus
            placeholder="請輸入角色名稱"
            value={deleteConfirmText}
            onChange={(e) => setDeleteConfirmText(e.target.value)}
            onPressEnter={() => {
              if (deleteConfirmText === roleToDelete?.name) {
                executeDelete();
              }
            }}
          />
          {deleteConfirmText && deleteConfirmText !== roleToDelete?.name && (
            <div style={{ color: "#ff4d4f", fontSize: "12px", marginTop: 8 }}>
              輸入的名稱與角色名稱不符
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default SecurityPage;
