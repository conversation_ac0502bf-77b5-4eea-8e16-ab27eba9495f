/**
 * 聯絡人驗證工具
 * 
 * 統一的聯絡人驗證規則，對應後端 ValidationHelper.cs
 * 確保前後端驗證邏輯一致性
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { CONTACT_VALIDATION_RULES } from './contactConstants';

// Ant Design 表單驗證規則類型
export type ValidationRule = {
  required?: boolean;
  type?: 'email' | 'url' | 'number';
  max?: number;
  min?: number;
  pattern?: RegExp;
  message?: string;
  validator?: (rule: any, value: any) => Promise<void>;
};

/**
 * 生成聯絡人姓名驗證規則
 */
export const getNameValidationRules = (): ValidationRule[] => [
  {
    required: CONTACT_VALIDATION_RULES.name.required,
    message: CONTACT_VALIDATION_RULES.name.message.required
  },
  {
    max: CONTACT_VALIDATION_RULES.name.maxLength,
    message: CONTACT_VALIDATION_RULES.name.message.maxLength
  }
];

/**
 * 生成電子郵件驗證規則
 */
export const getEmailValidationRules = (): ValidationRule[] => [
  {
    type: CONTACT_VALIDATION_RULES.email.type,
    message: CONTACT_VALIDATION_RULES.email.message.type
  },
  {
    max: CONTACT_VALIDATION_RULES.email.maxLength,
    message: CONTACT_VALIDATION_RULES.email.message.maxLength
  }
];

/**
 * 生成電話驗證規則
 */
export const getPhoneValidationRules = (): ValidationRule[] => [
  {
    max: CONTACT_VALIDATION_RULES.phone.maxLength,
    message: CONTACT_VALIDATION_RULES.phone.message.maxLength
  }
];

/**
 * 生成職位驗證規則
 */
export const getPositionValidationRules = (): ValidationRule[] => [
  {
    max: CONTACT_VALIDATION_RULES.position.maxLength,
    message: CONTACT_VALIDATION_RULES.position.message.maxLength
  }
];

/**
 * 生成部門驗證規則
 */
export const getDepartmentValidationRules = (): ValidationRule[] => [
  {
    max: CONTACT_VALIDATION_RULES.department.maxLength,
    message: CONTACT_VALIDATION_RULES.department.message.maxLength
  }
];

/**
 * 生成公司驗證規則
 */
export const getCompanyValidationRules = (): ValidationRule[] => [
  {
    max: CONTACT_VALIDATION_RULES.company.maxLength,
    message: CONTACT_VALIDATION_RULES.company.message.maxLength
  }
];

/**
 * 統一的驗證規則獲取器
 * 
 * @param fieldName 欄位名稱
 * @returns 對應的驗證規則陣列
 */
export const getContactFieldValidationRules = (fieldName: string): ValidationRule[] => {
  switch (fieldName) {
    case 'name':
      return getNameValidationRules();
    case 'email':
      return getEmailValidationRules();
    case 'phone':
      return getPhoneValidationRules();
    case 'position':
      return getPositionValidationRules();
    case 'department':
      return getDepartmentValidationRules();
    case 'company':
      return getCompanyValidationRules();
    default:
      return [];
  }
};

/**
 * 驗證聯絡人資料完整性
 * 
 * @param contactData 聯絡人資料
 * @returns 驗證結果
 */
export const validateContactData = (contactData: any): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  // 必填欄位檢查
  if (!contactData.name || contactData.name.trim() === '') {
    errors.push(CONTACT_VALIDATION_RULES.name.message.required);
  }

  // 長度檢查
  if (contactData.name && contactData.name.length > CONTACT_VALIDATION_RULES.name.maxLength) {
    errors.push(CONTACT_VALIDATION_RULES.name.message.maxLength);
  }

  // 電子郵件格式檢查
  if (contactData.email && contactData.email.trim() !== '') {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(contactData.email)) {
      errors.push(CONTACT_VALIDATION_RULES.email.message.type);
    }
    if (contactData.email.length > CONTACT_VALIDATION_RULES.email.maxLength) {
      errors.push(CONTACT_VALIDATION_RULES.email.message.maxLength);
    }
  }

  // 其他欄位長度檢查
  const fieldsToCheck = ['phone', 'position', 'department', 'company'];
  fieldsToCheck.forEach(field => {
    if (contactData[field] && contactData[field].length > CONTACT_VALIDATION_RULES[field as keyof typeof CONTACT_VALIDATION_RULES].maxLength) {
      errors.push(CONTACT_VALIDATION_RULES[field as keyof typeof CONTACT_VALIDATION_RULES].message.maxLength);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 清理和正規化聯絡人資料
 * 對應後端的資料處理邏輯
 *
 * @param contactData 原始聯絡人資料
 * @returns 清理後的聯絡人資料
 */
export const sanitizeContactData = (contactData: any): any => {
  return {
    ...contactData,
    name: contactData.name?.trim() || '',
    email: contactData.email?.trim().toLowerCase() || '',
    phone: contactData.phone?.trim() || '',
    position: contactData.position?.trim() || '',
    department: contactData.department?.trim() || '',
    company: contactData.company?.trim() || '',
    contactType: contactData.contactType || '客戶',
    isActive: typeof contactData.isActive === 'boolean' ? contactData.isActive : true
  };
};
