using FAST_ERP_Backend.Interfaces.Rms;
using FAST_ERP_Backend.Models.Rms;
using FAST_ERP_Backend.Models;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using FAST_ERP_Backend.Models.Rms.Enums;
using System.Text.Encodings.Web;

namespace FAST_ERP_Backend.Services.Rms
{
    /// <summary>
    /// 合約管理服務實作
    /// </summary>
    public class ContractService : IContractService
    {
        private readonly ERPDbContext _context;
        private readonly ILogger<ContractService> _logger;

        /// <summary>
        /// 初始化合約服務
        /// </summary>
        /// <param name="context">資料庫上下文</param>
        /// <param name="logger">日誌服務</param>
        public ContractService(ERPDbContext context, ILogger<ContractService> logger)
        {
            _context = context;
            _logger = logger;
        }

        // 共用的快照序列化設定：保留中文，不跳脫為 \uXXXX
        private static readonly JsonSerializerOptions SnapshotJsonOptions = new()
        {
            Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        };

        /// <summary>
        /// 取得合約列表
        /// </summary>
        public async Task<ApiResponse<List<ContractDTO>>> GetContractListAsync(ContractQueryRequestDTO request)
        {
            try
            {
                var query = _context.Rms_Contracts
                    .Include(c => c.Property)
                    .Include(c => c.Tenant)
                    .AsNoTracking()
                    .AsQueryable();

                // 套用查詢條件
                if (!string.IsNullOrEmpty(request.ContractId))
                    query = query.Where(c => c.ContractId == request.ContractId);

                if (!string.IsNullOrEmpty(request.PropertyId))
                    query = query.Where(c => c.PropertyId == request.PropertyId);

                if (!string.IsNullOrEmpty(request.TenantId))
                    query = query.Where(c => c.TenantId == request.TenantId);

                if (!string.IsNullOrEmpty(request.Status))
                    query = query.Where(c => c.Status == request.Status);

                if (!string.IsNullOrEmpty(request.PropertyName))
                    query = query.Where(c => c.Property.PropertyName.Contains(request.PropertyName));

                if (!string.IsNullOrEmpty(request.TenantName))
                    query = query.Where(c => c.Tenant.TenantName.Contains(request.TenantName));

                // 取得總筆數
                var totalCount = await query.CountAsync();

                // 計算分頁資訊
                var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);
                var currentPage = Math.Max(1, Math.Min(request.Page, totalPages > 0 ? totalPages : 1));

                // 分頁查詢
                var contracts = await query
                    .OrderByDescending(c => c.CreateTime)
                    .Skip((currentPage - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .Select(c => new ContractDTO
                    {
                        ContractId = c.ContractId,
                        PropertyId = c.PropertyId,
                        TenantId = c.TenantId,
                        StartDate = c.StartDate,
                        EndDate = c.EndDate,
                        RentAmount = c.RentAmount,
                        DepositAmount = c.DepositAmount,
                        Status = c.Status,
                        SnapshotData = c.SnapshotData,
                        Note = c.Note,
                        CreateTime = c.CreateTime ?? 0,
                        CreateUserId = c.CreateUserId,
                        UpdateTime = c.UpdateTime ?? 0,
                        UpdateUserId = c.UpdateUserId,
                        // 關聯資料
                        PropertyName = c.Property.PropertyName,
                        TenantName = c.Tenant.TenantName
                    })
                    .ToListAsync();

                return new ApiResponse<List<ContractDTO>>
                {
                    Success = true,
                    Data = contracts,
                    Message = "取得合約列表成功",
                    HttpCode = 200,
                    Paginate = new PaginateInfo
                    {
                        TotalCount = totalCount,
                        CurrentPage = currentPage,
                        PageSize = request.PageSize,
                        TotalPages = totalPages
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得合約列表時發生錯誤");
                return ApiResponse<List<ContractDTO>>.ErrorResult("取得合約列表時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 取得單一合約
        /// </summary>
        public async Task<ApiResponse<ContractDTO>> GetContractByIdAsync(string contractId)
        {
            try
            {
                var contract = await _context.Rms_Contracts
                    .Include(c => c.Property)
                    .Include(c => c.Tenant)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(c => c.ContractId == contractId);

                if (contract == null)
                {
                    return ApiResponse<ContractDTO>.ErrorResult("合約不存在", 404);
                }

                var contractDto = new ContractDTO
                {
                    ContractId = contract.ContractId,
                    PropertyId = contract.PropertyId,
                    TenantId = contract.TenantId,
                    StartDate = contract.StartDate,
                    EndDate = contract.EndDate,
                    RentAmount = contract.RentAmount,
                    DepositAmount = contract.DepositAmount,
                    Status = contract.Status,
                    SnapshotData = contract.SnapshotData,
                    Note = contract.Note,
                    PaymentDueDay = contract.PaymentDueDay,
                    CreateTime = contract.CreateTime ?? 0,
                    CreateUserId = contract.CreateUserId,
                    UpdateTime = contract.UpdateTime ?? 0,
                    UpdateUserId = contract.UpdateUserId,
                    // 關聯資料
                    PropertyName = contract.Property.PropertyName,
                    TenantName = contract.Tenant.TenantName
                };

                return ApiResponse<ContractDTO>.SuccessResult(contractDto, "取得合約成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得合約時發生錯誤");
                return ApiResponse<ContractDTO>.ErrorResult("取得合約時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 建立合約
        /// </summary>
        public async Task<ApiResponse<ContractDTO>> CreateContractAsync(ContractCreateRequestDTO request, string tokenUid)
        {
            try
            {
                var contract = new Contract
                {
                    ContractId = Guid.NewGuid().ToString(),
                    PropertyId = request.PropertyId,
                    TenantId = request.TenantId,
                    StartDate = request.StartDate,
                    EndDate = request.EndDate,
                    RentAmount = request.RentAmount,
                    DepositAmount = request.DepositAmount,
                    Status = ContractStatusInfo.Draft.Code, // 預設為草稿狀態
                    Note = request.Note,
                    PaymentDueDay = request.PaymentDueDay
                };

                _context.Rms_Contracts.Add(contract);
                await _context.SaveChangesAsync();

                // 取得關聯資料
                var property = await _context.Rms_Properties.FirstOrDefaultAsync(p => p.PropertyId == request.PropertyId);
                var tenant = await _context.Rms_Tenants.FirstOrDefaultAsync(t => t.TenantId == request.TenantId);

                var contractDto = new ContractDTO
                {
                    ContractId = contract.ContractId,
                    PropertyId = contract.PropertyId,
                    TenantId = contract.TenantId,
                    StartDate = contract.StartDate,
                    EndDate = contract.EndDate,
                    RentAmount = contract.RentAmount,
                    DepositAmount = contract.DepositAmount,
                    Status = contract.Status,
                    SnapshotData = contract.SnapshotData,
                    Note = contract.Note,
                    PaymentDueDay = contract.PaymentDueDay,
                    // 關聯資料
                    PropertyName = property.PropertyName,
                    TenantName = tenant.TenantName
                };

                return ApiResponse<ContractDTO>.SuccessResult(contractDto, "建立合約成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "建立合約時發生錯誤");
                return ApiResponse<ContractDTO>.ErrorResult("建立合約時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 更新合約
        /// </summary>
        public async Task<ApiResponse<string>> UpdateContractAsync(ContractUpdateRequestDTO request, string tokenUid)
        {
            try
            {
                var contract = await _context.Rms_Contracts
                    .FirstOrDefaultAsync(c => c.ContractId == request.ContractId);

                // 更新資料
                contract.PropertyId = request.PropertyId;
                contract.TenantId = request.TenantId;
                contract.StartDate = request.StartDate;
                contract.EndDate = request.EndDate;
                contract.RentAmount = request.RentAmount;
                contract.DepositAmount = request.DepositAmount;
                contract.Note = request.Note;
                contract.PaymentDueDay = request.PaymentDueDay;

                await _context.SaveChangesAsync();

                return ApiResponse<string>.SuccessResult("更新合約成功", "更新合約成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新合約時發生錯誤");
                return ApiResponse<string>.ErrorResult("更新合約時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 啟用合約
        /// </summary>
        public async Task<ApiResponse<string>> ActivateContractAsync(ContractActivateRequestDTO request, string tokenUid)
        {
            try
            {
                // 啟用與房源狀態更新需具一致性，採用交易
                await using var tx = await _context.Database.BeginTransactionAsync();

                var contract = await _context.Rms_Contracts
                    .Include(c => c.Property)
                    .Include(c => c.Tenant)
                    .FirstOrDefaultAsync(c => c.ContractId == request.ContractId);

                // 建立快照資料
                var snapshotData = new
                {
                    Property = new
                    {
                        PropertyId = contract.Property.PropertyId,
                        PropertyName = contract.Property.PropertyName,
                        Address = contract.Property.Address,
                        Area = contract.Property.Area,
                        Floor = contract.Property.Floor,
                        Type = contract.Property.Type,
                        Owner = contract.Property.Owner,
                        Note = contract.Property.Note
                    },
                    Tenant = new
                    {
                        TenantId = contract.Tenant.TenantId,
                        TenantName = contract.Tenant.TenantName,
                        ContactPerson = contract.Tenant.ContactPerson,
                        ContactPhone = contract.Tenant.ContactPhone,
                        Email = contract.Tenant.Email,
                        Address = contract.Tenant.Address,
                        Note = contract.Tenant.Note
                    },
                    SnapshotTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    Version = "1.0"
                };

                // 更新合約狀態和快照
                contract.Status = ContractStatusInfo.Active.Code; // 啟用
                contract.SnapshotData = JsonSerializer.Serialize(snapshotData, SnapshotJsonOptions);
                contract.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                contract.UpdateUserId = tokenUid;

                // 更新房源狀態
                contract.Property.Status = PropertyStatusInfo.Rented.Code; // 出租
                contract.Property.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                contract.Property.UpdateUserId = tokenUid;

                // 啟用時整期產生租金費用（UTC 年月算術，冪等）
                // 1) 將起訖時間正規化為當月 1 號 00:00 UTC
                var startUtc = DateTimeOffset.FromUnixTimeSeconds(contract.StartDate).UtcDateTime;
                var endUtc = DateTimeOffset.FromUnixTimeSeconds(contract.EndDate).UtcDateTime;
                var startFirst = new DateTime(startUtc.Year, startUtc.Month, 1, 0, 0, 0, DateTimeKind.Utc);
                var endFirst = new DateTime(endUtc.Year, endUtc.Month, 1, 0, 0, 0, DateTimeKind.Utc);

                // 2) 計算總期數（含端點）：以 0-based 月序做加總
                int startTotal = startFirst.Year * 12 + (startFirst.Month - 1);
                int endTotal = endFirst.Year * 12 + (endFirst.Month - 1);

                int dueDay = contract.PaymentDueDay;

                for (int t = startTotal; t <= endTotal; t++)
                {
                    int year = t / 12;
                    int month = (t % 12) + 1;

                    string billingPeriod = $"{year:D4}{month:D2}";

                    int daysInMonth = DateTime.DaysInMonth(year, month);
                    int finalDay = Math.Min(dueDay, daysInMonth);
                    long feeDate = new DateTimeOffset(new DateTime(year, month, finalDay, 0, 0, 0, DateTimeKind.Utc)).ToUnixTimeSeconds();

                    _context.Rms_Fees.Add(new Fee
                    {
                        FeeId = Guid.NewGuid().ToString(),
                        ContractId = contract.ContractId,
                        FeeType = FeeTypeInfo.Rent.Code,
                        BillingPeriod = billingPeriod,
                        Amount = contract.RentAmount,
                        FeeDate = feeDate,
                        PaidAmount = 0,
                        Status = FeeStatusInfo.Unpaid.Code,
                        Note = string.Empty,
                        CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        CreateUserId = tokenUid
                    });
                }

                await _context.SaveChangesAsync();
                await tx.CommitAsync();
                return ApiResponse<string>.SuccessResult("啟用合約成功", "啟用合約成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "啟用合約時發生錯誤");
                return ApiResponse<string>.ErrorResult("啟用合約時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 終止合約
        /// </summary>
        public async Task<ApiResponse<string>> TerminateContractAsync(ContractTerminateRequestDTO request, string tokenUid)
        {
            try
            {
                await using var tx = await _context.Database.BeginTransactionAsync();

                var contract = await _context.Rms_Contracts
                    .Include(c => c.Property)
                    .FirstOrDefaultAsync(c => c.ContractId == request.ContractId);

                // 更新合約狀態
                contract.Status = ContractStatusInfo.Terminated.Code; // 終止
                contract.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                contract.UpdateUserId = tokenUid;

                // 檢查該房源是否還有其他啟用的合約
                var hasOtherActiveContracts = await _context.Rms_Contracts
                    .Where(c => c.PropertyId == contract.PropertyId 
                               && c.Status == ContractStatusInfo.Active.Code // 啟用狀態
                               && c.ContractId != request.ContractId)
                    .AnyAsync();

                // 如果沒有其他啟用合約，則將房源狀態改為空置
                if (!hasOtherActiveContracts)
                {
                    contract.Property.Status = PropertyStatusInfo.Vacant.Code; // 空置
                }

                await _context.SaveChangesAsync();
                await tx.CommitAsync();

                _logger.LogInformation("合約終止成功 ContractId={ContractId}, PropertyId={PropertyId}, UserId={UserId}", contract.ContractId, contract.PropertyId, tokenUid);

                return ApiResponse<string>.SuccessResult("終止合約成功", "終止合約成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "終止合約時發生錯誤");
                return ApiResponse<string>.ErrorResult("終止合約時發生錯誤", 500);
            }
        }

        /// <summary>
        /// 刪除合約
        /// </summary>
        public async Task<ApiResponse<string>> DeleteContractAsync(ContractDeleteRequestDTO request, string tokenUid)
        {
            try
            {
                var contract = await _context.Rms_Contracts
                    .FirstOrDefaultAsync(c => c.ContractId == request.ContractId);

                // 軟刪除
                contract.IsDeleted = true;

                await _context.SaveChangesAsync();

                return ApiResponse<string>.SuccessResult("刪除合約成功", "刪除合約成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刪除合約時發生錯誤");
                return ApiResponse<string>.ErrorResult("刪除合約時發生錯誤", 500);
            }
        }
    }
}