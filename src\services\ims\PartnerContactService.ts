// =========================================================================================
// Service for Partner Contact Management - 商業夥伴聯絡人關聯管理服務
// =========================================================================================

import { PartnerContact } from '@/services/ims/partner';
import { apiEndpoints, ApiResponse } from "@/config/api";
import { httpClient } from "../http";
import { logger, SYMBOLS, createContextLogger } from '@/utils/logger';
import {
  validatePartnerContactData,
  sanitizePartnerContactData,
  logHelperUsage
} from '@/app/ims/utils/contactHelpers';

// 創建上下文日誌器
const partnerContactServiceLogger = createContextLogger({ module: 'PartnerContactService' });

// =========================================================================================
// 工具函數
// =========================================================================================

/**
 * 建立空的聯絡人關聯物件
 */
export const createEmptyPartnerContact = (partnerID: string): Partial<PartnerContact> => ({
    partnerID,
    contactID: '',
    role: '',
    isPrimary: false,
    priority: 99, // 預設優先序為 99
    notes: '',
    expertise: '',
    businessScope: '',
    workingHours: '',
    languages: '',
    emergencyContact: '',
    emergencyPhone: '',
    createTime: null,
    createUserId: null,
    updateTime: null,
    updateUserId: null,
    deleteTime: null,
    deleteUserId: null,
    isDeleted: false
});

// =========================================================================================
// API 服務函數
// =========================================================================================

/**
 * 取得夥伴聯絡人關聯列表
 * @param partnerID 夥伴ID
 * @returns Promise<ApiResponse<PartnerContact[]>>
 */
export async function getPartnerContacts(partnerID: string): Promise<ApiResponse<PartnerContact[]>> {
    try {
        if (!partnerID) {
            return {
                success: false,
                message: "夥伴ID不能為空",
                data: []
            };
        }

        partnerContactServiceLogger.log(SYMBOLS.LOADING, '載入夥伴聯絡人關聯列表', { partnerID });
        const response = await httpClient(`${apiEndpoints.getPartnerContactList}?PartnerID=${partnerID}`, {
            method: "GET",
        });

        if (response.success) {
            // 確保按 Priority 排序
            const sortedData = response.data?.sort((a: PartnerContact, b: PartnerContact) => a.priority - b.priority) || [];
            partnerContactServiceLogger.log(SYMBOLS.SUCCESS, '夥伴聯絡人關聯列表載入成功', { count: sortedData.length });
            return { ...response, data: sortedData };
        } else {
            partnerContactServiceLogger.log(SYMBOLS.WARNING, '夥伴聯絡人關聯列表載入失敗', response.message);
        }

        return response;
    } catch (error: any) {
        partnerContactServiceLogger.log(SYMBOLS.ERROR, '載入夥伴聯絡人關聯列表失敗', error);
        return {
            success: false,
            message: error.message || "載入夥伴聯絡人關聯列表失敗",
            data: []
        };
    }
}

/**
 * 取得特定聯絡人關聯
 * @param partnerID 夥伴ID
 * @param contactID 聯絡人ID
 * @returns Promise<ApiResponse<PartnerContact>>
 */
export async function getPartnerContact(partnerID: string, contactID: string): Promise<ApiResponse<PartnerContact>> {
    try {
        if (!partnerID || !contactID) {
            return {
                success: false,
                message: "夥伴ID和聯絡人ID不能為空",
            };
        }

        partnerContactServiceLogger.log(SYMBOLS.LOADING, '載入夥伴聯絡人關聯', { partnerID, contactID });
        const response = await httpClient(`${apiEndpoints.getPartnerContact}/${partnerID}/${contactID}`, {
            method: "GET",
        });

        if (response.success) {
            partnerContactServiceLogger.log(SYMBOLS.SUCCESS, '夥伴聯絡人關聯載入成功');
        } else {
            partnerContactServiceLogger.log(SYMBOLS.WARNING, '夥伴聯絡人關聯載入失敗', response.message);
        }

        return response;
    } catch (error: any) {
        partnerContactServiceLogger.log(SYMBOLS.ERROR, '載入夥伴聯絡人關聯失敗', error);
        return {
            success: false,
            message: error.message || "載入夥伴聯絡人關聯失敗",
        };
    }
}

/**
 * 新增夥伴聯絡人關聯
 * @param contactData 聯絡人關聯資料
 * @returns Promise<ApiResponse>
 */
export async function addPartnerContact(contactData: Partial<PartnerContact>): Promise<ApiResponse> {
    try {
        // 資料清理和正規化
        const sanitizedData = sanitizePartnerContactData(contactData);
        logHelperUsage('sanitizePartnerContactData', { originalData: contactData, sanitizedData });

        // 使用共享驗證邏輯
        const validation = validatePartnerContactData(sanitizedData);
        if (!validation.isValid) {
            partnerContactServiceLogger.log(SYMBOLS.WARNING, '新增夥伴聯絡人關聯驗證失敗', {
                errors: validation.errors
            });
            return {
                success: false,
                message: validation.errors.join(', ')
            };
        }

        // 如果有警告，記錄但不阻止操作
        if (validation.warnings.length > 0) {
            partnerContactServiceLogger.log(SYMBOLS.WARNING, '夥伴聯絡人關聯驗證警告', {
                warnings: validation.warnings
            });
        }

        partnerContactServiceLogger.log(SYMBOLS.LOADING, '新增夥伴聯絡人關聯', {
            partnerID: sanitizedData.partnerID,
            contactID: sanitizedData.contactID
        });
        
        const response = await httpClient(apiEndpoints.addPartnerContact, {
            method: "POST",
            body: JSON.stringify(sanitizedData),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            partnerContactServiceLogger.log(SYMBOLS.SUCCESS, '夥伴聯絡人關聯新增成功');
        } else {
            partnerContactServiceLogger.log(SYMBOLS.WARNING, '夥伴聯絡人關聯新增失敗', response.message);
        }

        return response;
    } catch (error: any) {
        partnerContactServiceLogger.log(SYMBOLS.ERROR, '新增夥伴聯絡人關聯時發生錯誤', error);
        return {
            success: false,
            message: error.message || "新增夥伴聯絡人關聯失敗",
        };
    }
}

/**
 * 更新夥伴聯絡人關聯（僅允許更新情境欄位，複合鍵不可變）
 * @param contactData 聯絡人關聯資料
 * @returns Promise<ApiResponse>
 */
export async function updatePartnerContact(contactData: Partial<PartnerContact>): Promise<ApiResponse> {
    try {
        // 基本驗證
        if (!contactData.partnerID) {
            return {
                success: false,
                message: "夥伴ID不能為空",
            };
        }

        if (!contactData.contactID) {
            return {
                success: false,
                message: "聯絡人ID不能為空",
            };
        }

        if (!contactData.role) {
            return {
                success: false,
                message: "聯絡人角色不能為空",
            };
        }

        partnerContactServiceLogger.log(SYMBOLS.LOADING, '更新夥伴聯絡人關聯', { 
            partnerID: contactData.partnerID,
            contactID: contactData.contactID
        });
        
        const response = await httpClient(apiEndpoints.editPartnerContact, {
            method: "PUT",
            body: JSON.stringify(contactData),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            partnerContactServiceLogger.log(SYMBOLS.SUCCESS, '夥伴聯絡人關聯更新成功');
        } else {
            partnerContactServiceLogger.log(SYMBOLS.WARNING, '夥伴聯絡人關聯更新失敗', response.message);
        }

        return response;
    } catch (error: any) {
        partnerContactServiceLogger.log(SYMBOLS.ERROR, '更新夥伴聯絡人關聯時發生錯誤', error);
        return {
            success: false,
            message: error.message || "更新夥伴聯絡人關聯失敗",
        };
    }
}

/**
 * 刪除夥伴聯絡人關聯（硬刪除，無法還原）
 * @param partnerID 夥伴ID
 * @param contactID 聯絡人ID
 * @returns Promise<ApiResponse>
 */
export async function deletePartnerContact(partnerID: string, contactID: string): Promise<ApiResponse> {
    try {
        if (!partnerID || !contactID) {
            return {
                success: false,
                message: "夥伴ID和聯絡人ID不能為空",
            };
        }

        partnerContactServiceLogger.log(SYMBOLS.LOADING, '刪除夥伴聯絡人關聯', { partnerID, contactID });
        const response = await httpClient(`${apiEndpoints.deletePartnerContact}/${partnerID}/${contactID}`, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            partnerContactServiceLogger.log(SYMBOLS.SUCCESS, '夥伴聯絡人關聯刪除成功');
        } else {
            partnerContactServiceLogger.log(SYMBOLS.WARNING, '夥伴聯絡人關聯刪除失敗', response.message);
        }

        return response;
    } catch (error: any) {
        partnerContactServiceLogger.log(SYMBOLS.ERROR, '刪除夥伴聯絡人關聯時發生錯誤', error);
        return {
            success: false,
            message: error.message || "刪除夥伴聯絡人關聯失敗",
        };
    }
}

// 注意：PartnerContact 使用硬刪除，不提供還原和查詢已刪除的功能
