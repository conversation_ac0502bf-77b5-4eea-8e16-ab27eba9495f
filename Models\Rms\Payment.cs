using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
using FAST_ERP_Backend.Attributes;

namespace FAST_ERP_Backend.Models.Rms
{
    /// <summary>
    /// 繳費紀錄資料表
    /// </summary>
    public class Payment : ModelBaseEntity
    {
        /// <summary>
        /// 繳費紀錄編號
        /// </summary>
        [Key]
        [Comment("繳費紀錄編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string PaymentId { get; set; }

        /// <summary>
        /// 合約編號 (FK)
        /// </summary>
        [Comment("合約編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string ContractId { get; set; }

        /// <summary>
        /// 繳費日期
        /// </summary>
        [Comment("繳費日期")]
        [Column(TypeName = "bigint")]
        public long PaymentDate { get; set; }

        /// <summary>
        /// 繳費金額
        /// </summary>
        [Comment("繳費金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        /// <summary>
        /// 繳費方式（現金/轉帳/支票等）
        /// </summary>
        [Comment("繳費方式")]
        [Column(TypeName = "nvarchar(50)")]
        public string PaymentType { get; set; }

        /// <summary>
        /// 狀態（待確認(0)/已入帳(1)/作廢(9)）
        /// </summary>
        [Comment("狀態")]
        [Column(TypeName = "nvarchar(50)")]
        public string Status { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Note { get; set; }

        /// <summary>
        /// 合約導航屬性
        /// </summary>
        public virtual Contract Contract { get; set; }

        /// <summary>
        /// 繳費分配集合
        /// </summary>
        public virtual ICollection<PaymentAllocation> PaymentAllocations { get; set; }

        /// <summary>
        /// 初始化繳費紀錄實體
        /// </summary>
        public Payment()
        {
            PaymentAllocations = new HashSet<PaymentAllocation>();
        }
    }
    /// <summary>
    /// 繳費紀錄資料傳輸物件
    /// </summary>
    public class PaymentDTO : ModelBaseEntityDTO
    {
        /// <summary>
        /// 繳費紀錄編號
        /// </summary>
        public string PaymentId { get; set; }
        
        /// <summary>
        /// 合約編號
        /// </summary>
        public string ContractId { get; set; }
        
        /// <summary>
        /// 繳費日期
        /// </summary>
        public long PaymentDate { get; set; }
        
        /// <summary>
        /// 繳費金額
        /// </summary>
        public decimal Amount { get; set; }
        
        /// <summary>
        /// 繳費方式
        /// </summary>
        public string PaymentType { get; set; }
        
        /// <summary>
        /// 狀態（待確認/已入帳/作廢）
        /// </summary>
        public string Status { get; set; }
        
        /// <summary>
        /// 備註
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// 初始化繳費紀錄資料傳輸物件
        /// </summary>
        public PaymentDTO()
        {
        }
    }
}