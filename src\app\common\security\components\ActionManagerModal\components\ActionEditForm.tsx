import React from 'react';
import { Form, Input, InputNumber, Button, Space } from 'antd';
import { SaveOutlined, CloseOutlined } from '@ant-design/icons';
import { createContextLogger, SYMBOLS } from '@/utils/logger';
import { ActionEditFormProps } from '../types/actionManager.types';

// 遵循 FastERP 日誌記錄策略
const actionEditFormLogger = createContextLogger({ module: 'ActionEditForm' });

const ActionEditForm: React.FC<ActionEditFormProps> = ({
  action,
  editValues,
  onUpdateValues,
  onSave,
  onCancel
}) => {
  const handleValuesChange = (changedValues: Partial<{ code: string; name: string; sortCode: number }>) => {
    actionEditFormLogger.log(SYMBOLS.DEBUG, '更新編輯值', { 
      actionId: action.granularPermissionActionID, 
      changedValues 
    });
    onUpdateValues({ ...editValues, ...changedValues });
  };

  const handleSave = () => {
    actionEditFormLogger.log(SYMBOLS.DEBUG, '儲存動作編輯', { 
      actionId: action.granularPermissionActionID,
      editValues 
    });
    onSave();
  };

  const handleCancel = () => {
    actionEditFormLogger.log(SYMBOLS.DEBUG, '取消動作編輯', { 
      actionId: action.granularPermissionActionID 
    });
    onCancel();
  };

  return (
    <Form
      layout="vertical"
      size="small"
      initialValues={editValues}
      onValuesChange={handleValuesChange}
    >
      <Form.Item
        label="動作代碼"
        name="code"
        rules={[
          { required: true, message: '請輸入動作代碼' },
          { max: 50, message: '動作代碼不能超過50個字元' }
        ]}
      >
        <Input 
          placeholder="輸入動作代碼"
          disabled // 代碼不可編輯
          style={{ fontFamily: 'monospace' }}
        />
      </Form.Item>

      <Form.Item
        label="動作名稱"
        name="name"
        rules={[
          { required: true, message: '請輸入動作名稱' },
          { max: 100, message: '動作名稱不能超過100個字元' }
        ]}
      >
        <Input 
          placeholder="輸入動作名稱"
          autoFocus
        />
      </Form.Item>

      <Form.Item
        label="排序代碼"
        name="sortCode"
        rules={[
          { required: true, message: '請輸入排序代碼' },
          { type: 'number', min: 0, message: '排序代碼必須大於等於0' }
        ]}
      >
        <InputNumber 
          placeholder="輸入排序代碼"
          style={{ width: '100%' }}
          min={0}
          precision={0}
        />
      </Form.Item>

      <Form.Item style={{ marginBottom: 0 }}>
        <Space size="small" style={{ width: '100%', justifyContent: 'flex-end' }}>
          <Button
            size="small"
            icon={<CloseOutlined />}
            onClick={handleCancel}
          >
            取消
          </Button>
          <Button
            size="small"
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
          >
            儲存
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default ActionEditForm;
