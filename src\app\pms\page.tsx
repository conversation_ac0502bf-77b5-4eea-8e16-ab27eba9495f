"use client";

import { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Typo<PERSON>,
  Badge,
  Button,
  Flex,
  Space,
  Statistic,
  Progress,
  Tabs,
  Empty,
} from "antd";
import {
  FileTextOutlined,
  FileOutlined,
  SettingOutlined,
  DatabaseOutlined,
  Bar<PERSON><PERSON>Outlined,
  SafetyCertificateOutlined,
  ArrowLeftOutlined,
  ExportOutlined,
  HomeOutlined,
  ToolOutlined,
  ShopOutlined,
  DollarOutlined,
  ReconciliationOutlined,
  ContainerOutlined,
  PrinterOutlined,
  AuditOutlined,
  TeamOutlined,
  PieChartOutlined,
  LineChartOutlined,
  AreaChartOutlined,
  ReloadOutlined,
  UpOutlined,
  DownOutlined,
  BankOutlined,
  DesktopOutlined,
  CarOutlined,
  AppstoreOutlined,
  EnvironmentOutlined,
  BugOutlined,
  SunOutlined,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import {
  LoadingSpinner,
  PageLoadingOverlay,
  SectionLoadingOverlay,
} from "@/app/components/common/Loading";
import {
  getAssetStatisticsOverview,
  getAssetStatisticsByDepartment,
  getAssetStatisticsByStatus,
  getAssetStatisticsByManufacturer,
  getAssetStatisticsByAccount,
  getAssetStatisticsByValueRange,
  AssetStatisticsOverview,
  DepartmentStatistics,
  StatusStatistics,
  ManufacturerStatistics,
  AccountStatistics,
  ValueRangeStatistics,
} from "@/services/pms/assetService";

const { Title, Text } = Typography;

interface SettingCard {
  key: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  path?: string;
  component?: React.ReactNode;
  status?: "success" | "processing" | "default" | "error" | "warning";
}

const PMSOverviewPage = () => {
  const router = useRouter();
  const [selectedSetting, setSelectedSetting] = useState<SettingCard | null>(
    null
  );
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 統計相關狀態
  const [overviewStats, setOverviewStats] =
    useState<AssetStatisticsOverview | null>(null);
  const [departmentStats, setDepartmentStats] = useState<
    DepartmentStatistics[]
  >([]);
  const [statusStats, setStatusStats] = useState<StatusStatistics[]>([]);
  const [manufacturerStats, setManufacturerStats] = useState<
    ManufacturerStatistics[]
  >([]);
  const [accountStats, setAccountStats] = useState<AccountStatistics[]>([]);
  const [valueRangeStats, setValueRangeStats] = useState<
    ValueRangeStatistics[]
  >([]);
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);
  const [manufacturerExpanded, setManufacturerExpanded] = useState(false);
  const [departmentExpanded, setDepartmentExpanded] = useState(false);
  const [accountExpanded, setAccountExpanded] = useState(false);

  // 使用 useEffect 來處理初始載入
  useEffect(() => {
    // 初始載入時設置 loading 為 true
    setIsLoading(true);
    loadStatisticsData();
  }, []);

  // 載入統計資料
  const loadStatisticsData = async () => {
    try {
      setStatsLoading(true);
      setStatsError(null);

      const [
        overviewResponse,
        departmentResponse,
        statusResponse,
        manufacturerResponse,
        accountResponse,
        valueRangeResponse,
      ] = await Promise.allSettled([
        getAssetStatisticsOverview(),
        getAssetStatisticsByDepartment(),
        getAssetStatisticsByStatus(),
        getAssetStatisticsByManufacturer(),
        getAssetStatisticsByAccount(),
        getAssetStatisticsByValueRange(),
      ]);

      // 處理總覽統計
      if (
        overviewResponse.status === "fulfilled" &&
        overviewResponse.value.success &&
        overviewResponse.value.data !== undefined
      ) {
        setOverviewStats(overviewResponse.value.data);
      }

      // 處理部門統計
      if (
        departmentResponse.status === "fulfilled" &&
        departmentResponse.value.success &&
        departmentResponse.value.data !== undefined
      ) {
        setDepartmentStats(departmentResponse.value.data);
      }

      // 處理狀態統計
      if (
        statusResponse.status === "fulfilled" &&
        statusResponse.value.success &&
        statusResponse.value.data !== undefined
      ) {
        setStatusStats(statusResponse.value.data);
      }

      // 處理廠牌統計
      if (
        manufacturerResponse.status === "fulfilled" &&
        manufacturerResponse.value.success &&
        manufacturerResponse.value.data !== undefined
      ) {
        setManufacturerStats(manufacturerResponse.value.data);
      }

      // 處理科目統計
      if (
        accountResponse.status === "fulfilled" &&
        accountResponse.value.success &&
        accountResponse.value.data !== undefined
      ) {
        setAccountStats(accountResponse.value.data);
      }

      // 處理價值區間統計
      if (
        valueRangeResponse.status === "fulfilled" &&
        valueRangeResponse.value.success &&
        valueRangeResponse.value.data !== undefined
      ) {
        setValueRangeStats(valueRangeResponse.value.data);
      }
    } catch (error) {
      console.error("載入統計資料失敗:", error);
      setStatsError("載入統計資料失敗，請稍後再試");
    } finally {
      setStatsLoading(false);
      setIsLoading(false);
    }
  };

  // 重新載入統計資料
  const handleRefreshStats = () => {
    loadStatisticsData();
  };

  const settingCards: SettingCard[] = [
    // 財產管理模組
    {
      key: "fixedAssetMaintenance",
      title: "財產維護單",
      description: "新增、修改、查詢財產基本資料",
      icon: <DatabaseOutlined style={{ fontSize: "24px", color: "#1890ff" }} />,
      path: "/pms/document_maintenance/fixed_asset_maintenance_form",
      status: "processing",
    },
    {
      key: "vendorMaintenance",
      title: "廠商修護單",
      description: "廠商修繕申請、審核、派工管理",
      icon: <ToolOutlined style={{ fontSize: "24px", color: "#52c41a" }} />,
      path: "/pms/document_maintenance/vendor_maintenance_form",
      status: "processing",
    },
    {
      key: "assetCarryOut",
      title: "財產攜出單",
      description: "財產攜出申請、審核、歸還管理",
      icon: <ExportOutlined style={{ fontSize: "24px", color: "#722ed1" }} />,
      path: "/pms/document_maintenance/asset_carryout_form",
      status: "processing",
    },
    {
      key: "assetDepreciation",
      title: "財產折舊單",
      description: "財產折舊計算與管理",
      icon: <DollarOutlined style={{ fontSize: "24px", color: "#fa8c16" }} />,
      path: "/pms/document_maintenance/fixed_asset_depreciation_form",
      status: "default",
    },
    {
      key: "assetLocationChange",
      title: "財產變動單",
      description: "財產變動申請與處理",
      icon: <HomeOutlined style={{ fontSize: "24px", color: "#08979c" }} />,
      path: "/pms/document_maintenance/asset_location_change_form",
      status: "default",
    },
    {
      key: "assetSale",
      title: "財產出售單",
      description: "財產出售申請與處理",
      icon: <ShopOutlined style={{ fontSize: "24px", color: "#eb2f96" }} />,
      path: "/pms/document_maintenance/fixed_asset_sale_form",
      status: "default",
    },
    {
      key: "assetScrap",
      title: "財產報廢單",
      description: "財產報廢申請與處理",
      icon: (
        <ReconciliationOutlined
          style={{ fontSize: "24px", color: "#f5222d" }}
        />
      ),
      path: "/pms/document_maintenance/fixed_asset_scrap_form",
      status: "processing",
    },
    {
      key: "assetInventoryForm",
      title: "財產盤點單",
      description: "建立盤點單、明細盤點與批次/整批盤點",
      icon: <AuditOutlined style={{ fontSize: "24px", color: "#1890ff" }} />,
      path: "/pms/document_maintenance/asset_inventory_form",
      status: "processing",
    },

    // 基本資料模組
    {
      key: "assetCategory",
      title: "財產類別設定",
      description: "設定與管理財產分類項目",
      icon: <SettingOutlined style={{ fontSize: "24px", color: "#1890ff" }} />,
      path: "/pms/parameter_settings/asset_category",
      status: "processing",
    },
    {
      key: "assetAccount",
      title: "財產科目設定",
      description: "設定財產會計科目項目",
      icon: <DatabaseOutlined style={{ fontSize: "24px", color: "#52c41a" }} />,
      path: "/pms/parameter_settings/asset_account",
      status: "processing",
    },
    {
      key: "assetSubAccount",
      title: "財產子目設定",
      description: "設定財產會計子科目項目",
      icon: (
        <ReconciliationOutlined
          style={{ fontSize: "24px", color: "#722ed1" }}
        />
      ),
      path: "/pms/parameter_settings/asset_sub_account",
      status: "processing",
    },
    {
      key: "storageLocation",
      title: "存放地點設定",
      description: "設定資產存放位置資料",
      icon: <HomeOutlined style={{ fontSize: "24px", color: "#08979c" }} />,
      path: "/pms/parameter_settings/storage_location",
      status: "processing",
    },
    {
      key: "manufacturer",
      title: "製造商設定",
      description: "維護製造商基本資料",
      icon: (
        <SafetyCertificateOutlined
          style={{ fontSize: "24px", color: "#eb2f96" }}
        />
      ),
      path: "/pms/parameter_settings/manufacturer",
      status: "processing",
    },
    {
      key: "assetSource",
      title: "財產來源設定",
      description: "設定財產取得來源分類",
      icon: (
        <ContainerOutlined style={{ fontSize: "24px", color: "#fa8c16" }} />
      ),
      path: "/pms/parameter_settings/asset_source",
      status: "processing",
    },
    {
      key: "accessoryEquipment",
      title: "附屬設備設定",
      description: "設定附屬設備項目管理",
      icon: <ToolOutlined style={{ fontSize: "24px", color: "#13c2c2" }} />,
      path: "/pms/parameter_settings/accessory_equipment",
      status: "processing",
    },
    {
      key: "amortizationSource",
      title: "攤提來源設定",
      description: "設定資產攤提來源項目",
      icon: <DollarOutlined style={{ fontSize: "24px", color: "#08979c" }} />,
      path: "/pms/parameter_settings/amortization_source",
      status: "processing",
    },
    {
      key: "userRole",
      title: "保管人&使用人設定",
      description: "設定資產保管人與使用人權限",
      icon: <TeamOutlined style={{ fontSize: "24px", color: "#1890ff" }} />,
      path: "/pms/parameter_settings/user_role",
      status: "processing",
    },
    {
      key: "assetBatchImport",
      title: "財產批次匯入",
      description: "大量財產資料批次匯入系統",
      icon: (
        <ContainerOutlined style={{ fontSize: "24px", color: "#13c2c2" }} />
      ),
      path: "/pms/parameter_settings/asset_batch_import",
      status: "processing",
    },
    {
      key: "systemParameter",
      title: "系統參數設定",
      description: "PMS 系統基本參數與進階設定",
      icon: <SettingOutlined style={{ fontSize: "24px", color: "#f5222d" }} />,
      path: "/pms/parameter_settings/system_parameter_setting",
      status: "processing",
    },

    // 報表列印模組
    /* {
      key: "assetInventoryDetail",
      title: "財產盤點明細表",
      description: "財產盤點作業明細記錄報表",
      icon: <AuditOutlined style={{ fontSize: "24px", color: "#1890ff" }} />,
      path: "/pms/asset_report/asset_inventory_record",
      status: "default",
    }, */
    {
      key: "assetDepreciationReport",
      title: "財產折舊表",
      description: "財產折舊計算與統計報表",
      icon: <DollarOutlined style={{ fontSize: "24px", color: "#52c41a" }} />,
      path: "/pms/asset_report/asset_depreciation",
      status: "processing",
    },
    {
      key: "assetRegister",
      title: "財產清冊",
      description: "完整財產清單與基本資料",
      icon: <DatabaseOutlined style={{ fontSize: "24px", color: "#722ed1" }} />,
      path: "/pms/asset_report/asset_list",
      status: "processing",
    },
    {
      key: "custodianRoster",
      title: "保管人&使用人清冊",
      description: "依保管人/使用人彙總清冊",
      icon: <TeamOutlined style={{ fontSize: "24px", color: "#1890ff" }} />,
      path: "/pms/asset_report/custodian_roster",
      status: "processing",
    },
    {
      key: "assetCard",
      title: "財產卡",
      description: "個別財產詳細資料卡片",
      icon: (
        <ReconciliationOutlined
          style={{ fontSize: "24px", color: "#fa8c16" }}
        />
      ),
      path: "/pms/asset_report/asset_card",
      status: "processing",
    },
    {
      key: "assetScrapReport",
      title: "財產報廢清冊",
      description: "已報廢財產統計清單",
      icon: <ExportOutlined style={{ fontSize: "24px", color: "#f5222d" }} />,
      path: "/pms/asset_report/asset_scrap",
      status: "default",
    },
    {
      key: "assetInventoryRecord",
      title: "固定資產明細表",
      description: "產生與列印固定資產明細資料",
      icon: <FileTextOutlined style={{ fontSize: "24px", color: "#08979c" }} />,
      path: "/pms/asset_report/asset_detail",
      status: "processing",
    },
    {
      key: "assetLabel",
      title: "財產標籤",
      description: "產生與列印財產條碼標籤",
      icon: <PrinterOutlined style={{ fontSize: "24px", color: "#eb2f96" }} />,
      path: "/pms/asset_report/asset_label",
      status: "processing",
    },
    {
      key: "assetInventoryList",
      title: "財產盤點清冊",
      description: "財產盤點作業清冊列表",
      icon: (
        <ContainerOutlined style={{ fontSize: "24px", color: "#13c2c2" }} />
      ),
      path: "/pms/asset_report/asset_inventory_list",
      status: "processing",
    },
    {
      key: "assetIncreaseDecrease",
      title: "財產增減表",
      description: "財產增加與減少異動統計",
      icon: <BarChartOutlined style={{ fontSize: "24px", color: "#52c41a" }} />,
      path: "/pms/asset_report/asset_increase_decrease",
      status: "processing",
    },
  ];

  const handleCardClick = (setting: SettingCard) => {
    if (setting.path) {
      // 如果有路徑，直接導航
      router.push(setting.path);
    } else if (setting.component) {
      // 如果有組件，展開顯示
      setSelectedSetting(setting);
      setIsExpanded(true);
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleBack = () => {
    setIsExpanded(false);
    setTimeout(() => setSelectedSetting(null), 300);
  };

  // 格式化貨幣顯示
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("zh-TW", {
      style: "currency",
      currency: "TWD",
      minimumFractionDigits: 0,
    }).format(value);
  };

  // 根據科目編號獲取對應的圖標
  const getAccountIcon = (accountNo: string) => {
    switch (accountNo) {
      case "1":
        return (
          <EnvironmentOutlined style={{ fontSize: "20px", color: "#1890ff" }} />
        );
      case "2":
        return <BankOutlined style={{ fontSize: "20px", color: "#1890ff" }} />;
      case "3":
        return <ToolOutlined style={{ fontSize: "20px", color: "#52c41a" }} />;
      case "4":
        return (
          <DesktopOutlined style={{ fontSize: "20px", color: "#722ed1" }} />
        );
      case "5":
        return <SunOutlined style={{ fontSize: "20px", color: "#722ed1" }} />;
      case "6":
        return <BugOutlined style={{ fontSize: "20px", color: "#722ed1" }} />;
      case "7":
        return <CarOutlined style={{ fontSize: "20px", color: "#fa8c16" }} />;
      case "8":
        return (
          <AppstoreOutlined style={{ fontSize: "20px", color: "#eb2f96" }} />
        );
      case "9":
        return <AuditOutlined style={{ fontSize: "20px", color: "#722ed1" }} />;
      default:
        return (
          <ContainerOutlined style={{ fontSize: "20px", color: "#13c2c2" }} />
        );
    }
  };

  // 統計總覽卡片組件
  const renderStatisticsOverview = () => {
    if (statsLoading) {
      return (
        <Card className="mb-6">
          <SectionLoadingOverlay text="載入統計資料中..." />
        </Card>
      );
    }

    if (statsError) {
      return (
        <Card className="mb-6">
          <Empty description={statsError} image={Empty.PRESENTED_IMAGE_SIMPLE}>
            <Button icon={<ReloadOutlined />} onClick={handleRefreshStats}>
              重新載入
            </Button>
          </Empty>
        </Card>
      );
    }

    if (!overviewStats) {
      return null;
    }

    return (
      <div className="mb-6">
        <Card
          title={
            <Flex align="center" gap="middle">
              <BarChartOutlined />
              <span>財產統計總覽</span>
            </Flex>
          }
          extra={
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefreshStats}
              loading={statsLoading}
              size="small"
            >
              重新整理
            </Button>
          }
        >
          <Row gutter={[24, 24]}>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="總財產數量"
                value={overviewStats.totalAssets}
                prefix={<DatabaseOutlined />}
                valueStyle={{ color: "#1890ff" }}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="總價值"
                value={overviewStats.totalValue}
                prefix={<DollarOutlined />}
                formatter={(value) => formatCurrency(value as number)}
                valueStyle={{ color: "#52c41a" }}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="淨值"
                value={overviewStats.totalNetValue}
                prefix={<ReconciliationOutlined />}
                formatter={(value) => formatCurrency(value as number)}
                valueStyle={{ color: "#722ed1" }}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="今年新增"
                value={overviewStats.thisYearNewAssets}
                prefix={<ContainerOutlined />}
                valueStyle={{ color: "#fa8c16" }}
              />
            </Col>
          </Row>

          {/* 中間分隔線 */}
          <div
            style={{ margin: "24px 0", borderTop: "1px solid #f0f0f0" }}
          ></div>

          <Row gutter={[24, 24]}>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="本月新增"
                value={overviewStats.thisMonthNewAssets}
                valueStyle={{ color: "#13c2c2" }}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="已報廢數量"
                value={overviewStats.pendingScrapAssets}
                valueStyle={{ color: "#f5222d" }}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="報廢後堪用"
                value={overviewStats.reusableAfterScrapAssets}
                valueStyle={{ color: "#8c8c8c" }}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="維修中數量"
                value={overviewStats.underMaintenanceAssets}
                valueStyle={{ color: "#FFB7DD" }}
              />
            </Col>
          </Row>

          <div className="mt-4 text-right">
            <Text type="secondary" style={{ fontSize: "12px" }}>
              統計時間：{overviewStats.generatedTime}
            </Text>
          </div>
        </Card>
      </div>
    );
  };

  // 統計圖表組件
  const renderStatisticsCharts = () => {
    if (statsLoading || statsError) {
      return null;
    }

    const tabItems = [
      {
        key: "department",
        label: (
          <span>
            <TeamOutlined />
            部門分布
          </span>
        ),
        children: renderDepartmentStats(),
      },
      {
        key: "status",
        label: (
          <span>
            <SettingOutlined />
            狀態分布
          </span>
        ),
        children: renderStatusStats(),
      },
      {
        key: "manufacturer",
        label: (
          <span>
            <SafetyCertificateOutlined />
            廠牌分布
          </span>
        ),
        children: renderManufacturerStats(),
      },
      {
        key: "account",
        label: (
          <span>
            <DatabaseOutlined />
            科目分布
          </span>
        ),
        children: renderAccountStats(),
      },
      {
        key: "valueRange",
        label: (
          <span>
            <DollarOutlined />
            價值區間
          </span>
        ),
        children: renderValueRangeStats(),
      },
    ];

    return (
      <div className="mb-6">
        <Card
          title={
            <Flex align="center" gap="middle">
              <PieChartOutlined />
              <span>統計圖表分析</span>
            </Flex>
          }
        >
          <Tabs defaultActiveKey="department" items={tabItems} />
        </Card>
      </div>
    );
  };

  // 部門統計圖表
  const renderDepartmentStats = () => {
    if (departmentStats.length === 0) {
      return <Empty description="無部門統計資料" />;
    }

    const maxDisplayCount = 6;
    const displayCount = departmentExpanded
      ? departmentStats.length
      : maxDisplayCount;
    const displayedStats = departmentStats.slice(0, displayCount);

    return (
      <div>
        <Row gutter={[16, 16]}>
          {displayedStats.map((dept, index) => (
            <Col xs={24} sm={12} md={8} key={dept.departmentId}>
              <Card size="small">
                <div className="text-center">
                  <div
                    style={{
                      fontSize: "16px",
                      fontWeight: "bold",
                      marginBottom: "8px",
                    }}
                  >
                    {dept.departmentName}
                  </div>
                  <div style={{ marginBottom: "8px" }}>
                    <Progress
                      type="circle"
                      percent={Math.round(dept.percentage)}
                      width={60}
                      strokeColor={{
                        "0%": "#108ee9",
                        "100%": "#87d068",
                      }}
                    />
                  </div>
                  <div style={{ fontSize: "12px", color: "#666" }}>
                    {dept.assetCount} 項財產 ({dept.percentage.toFixed(1)}%)
                  </div>
                  <div
                    style={{
                      fontSize: "12px",
                      color: "#52c41a",
                      fontWeight: "bold",
                    }}
                  >
                    {formatCurrency(dept.totalValue)}
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>

        {/* 更多部門按鈕 */}
        {departmentStats.length > maxDisplayCount && (
          <div style={{ textAlign: "center", marginTop: "16px" }}>
            <Button
              type="link"
              onClick={() => setDepartmentExpanded(!departmentExpanded)}
              icon={departmentExpanded ? <UpOutlined /> : <DownOutlined />}
            >
              {departmentExpanded
                ? "收起部門"
                : `更多部門 (${departmentStats.length - maxDisplayCount} 個)`}
            </Button>
          </div>
        )}
      </div>
    );
  };

  // 狀態統計圖表
  const renderStatusStats = () => {
    if (statusStats.length === 0) {
      return <Empty description="無狀態統計資料" />;
    }

    return (
      <Row gutter={[16, 16]}>
        {statusStats.map((status, index) => (
          <Col xs={24} sm={12} md={8} key={status.statusId}>
            <Card size="small">
              <div className="text-center">
                <div
                  style={{
                    fontSize: "16px",
                    fontWeight: "bold",
                    marginBottom: "8px",
                  }}
                >
                  {status.statusName}
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Progress
                    type="circle"
                    percent={Math.round(status.percentage)}
                    width={60}
                    strokeColor={{
                      "0%": "#108ee9",
                      "100%": "#87d068",
                    }}
                  />
                </div>
                <div style={{ fontSize: "12px", color: "#666" }}>
                  {status.assetCount} 項財產 ({status.percentage.toFixed(1)}%)
                </div>
                <div
                  style={{
                    fontSize: "12px",
                    color: "#52c41a",
                    fontWeight: "bold",
                  }}
                >
                  {formatCurrency(status.totalValue)}
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    );
  };

  // 廠牌統計圖表
  const renderManufacturerStats = () => {
    if (manufacturerStats.length === 0) {
      return <Empty description="無廠牌統計資料" />;
    }

    const maxDisplayCount = 8;
    const displayCount = manufacturerExpanded
      ? manufacturerStats.length
      : maxDisplayCount;
    const displayedStats = manufacturerStats.slice(0, displayCount);

    return (
      <div>
        <Row gutter={[16, 16]}>
          {displayedStats.map((manufacturer, index) => (
            <Col xs={24} sm={12} md={6} key={manufacturer.manufacturerId}>
              <Card size="small">
                <div className="text-center">
                  <div
                    style={{
                      fontSize: "14px",
                      fontWeight: "bold",
                      marginBottom: "8px",
                    }}
                  >
                    {manufacturer.manufacturerName}
                  </div>
                  <div style={{ marginBottom: "8px" }}>
                    <Progress
                      percent={Math.round(manufacturer.percentage)}
                      size="small"
                      strokeColor={{
                        "0%": "#108ee9",
                        "100%": "#87d068",
                      }}
                    />
                  </div>
                  <div style={{ fontSize: "12px", color: "#666" }}>
                    {manufacturer.assetCount} 項 (
                    {manufacturer.percentage.toFixed(1)}%)
                  </div>
                  <div
                    style={{
                      fontSize: "12px",
                      color: "#52c41a",
                      fontWeight: "bold",
                    }}
                  >
                    {formatCurrency(manufacturer.totalValue)}
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>

        {/* 更多廠牌按鈕 */}
        {manufacturerStats.length > maxDisplayCount && (
          <div style={{ textAlign: "center", marginTop: "16px" }}>
            <Button
              type="link"
              onClick={() => setManufacturerExpanded(!manufacturerExpanded)}
              icon={manufacturerExpanded ? <UpOutlined /> : <DownOutlined />}
            >
              {manufacturerExpanded
                ? "收起廠牌"
                : `更多廠牌 (${manufacturerStats.length - maxDisplayCount} 個)`}
            </Button>
          </div>
        )}
      </div>
    );
  };

  // 科目統計圖表
  const renderAccountStats = () => {
    if (accountStats.length === 0) {
      return <Empty description="無科目統計資料" />;
    }

    const maxDisplayCount = 6;
    const displayCount = accountExpanded
      ? accountStats.length
      : maxDisplayCount;
    const displayedStats = accountStats.slice(0, displayCount);

    return (
      <div>
        <Row gutter={[16, 16]}>
          {displayedStats.map((account, index) => (
            <Col xs={24} sm={12} md={8} key={account.accountId}>
              <Card size="small">
                <div className="text-center">
                  <div
                    style={{
                      fontSize: "14px",
                      fontWeight: "bold",
                      marginBottom: "8px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      gap: "8px",
                    }}
                  >
                    {getAccountIcon(account.accountNo)}
                    {account.accountName}
                  </div>
                  <div
                    style={{
                      fontSize: "12px",
                      color: "#666",
                      marginBottom: "4px",
                    }}
                  >
                    科目編號: {account.accountNo}
                  </div>
                  <div style={{ marginBottom: "8px" }}>
                    <Progress
                      percent={Math.round(account.percentage)}
                      size="small"
                      strokeColor={{
                        "0%": "#108ee9",
                        "100%": "#87d068",
                      }}
                    />
                  </div>
                  <div style={{ fontSize: "12px", color: "#666" }}>
                    {account.assetCount} 項財產 ({account.percentage.toFixed(1)}
                    %)
                  </div>
                  <div
                    style={{
                      fontSize: "12px",
                      color: "#52c41a",
                      fontWeight: "bold",
                      marginBottom: "4px",
                    }}
                  >
                    總值: {formatCurrency(account.totalValue)}
                  </div>
                  <div
                    style={{
                      fontSize: "12px",
                      color: "#722ed1",
                      fontWeight: "bold",
                    }}
                  >
                    淨值: {formatCurrency(account.netValue)}
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>

        {/* 更多科目按鈕 */}
        {accountStats.length > maxDisplayCount && (
          <div style={{ textAlign: "center", marginTop: "16px" }}>
            <Button
              type="link"
              onClick={() => setAccountExpanded(!accountExpanded)}
              icon={accountExpanded ? <UpOutlined /> : <DownOutlined />}
            >
              {accountExpanded
                ? "收起科目"
                : `更多科目 (${accountStats.length - maxDisplayCount} 個)`}
            </Button>
          </div>
        )}
      </div>
    );
  };

  // 價值區間統計圖表
  const renderValueRangeStats = () => {
    if (valueRangeStats.length === 0) {
      return <Empty description="無價值區間統計資料" />;
    }

    return (
      <Row gutter={[16, 16]}>
        {valueRangeStats.map((range, index) => (
          <Col xs={24} sm={12} md={6} key={range.valueRange}>
            <Card size="small">
              <div className="text-center">
                <div
                  style={{
                    fontSize: "14px",
                    fontWeight: "bold",
                    marginBottom: "8px",
                  }}
                >
                  {range.valueRange}
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Progress
                    percent={Math.round(range.percentage)}
                    size="small"
                    strokeColor={{
                      "0%": "#108ee9",
                      "100%": "#87d068",
                    }}
                  />
                </div>
                <div style={{ fontSize: "12px", color: "#666" }}>
                  {range.assetCount} 項 ({range.percentage.toFixed(1)}%)
                </div>
                <div
                  style={{
                    fontSize: "12px",
                    color: "#52c41a",
                    fontWeight: "bold",
                  }}
                >
                  {formatCurrency(range.totalValue)}
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    );
  };

  const renderSettingCards = () => (
    <div style={{ overflow: "hidden", width: "100%" }}>
      {/* 基本資料模組 */}
      <div className="mb-8">
        <Title level={3} className="mb-4">
          <SettingOutlined className="mr-2" />
          基本資料
        </Title>
        <Row
          gutter={[24, 24]}
          style={{
            opacity: isExpanded ? 0 : 1,
            transform: `translateY(${isExpanded ? "20px" : "0"})`,
            transition: "all 0.3s ease-in-out",
            display: isExpanded ? "none" : "flex",
            margin: "0 -8px",
          }}
        >
          {settingCards
            .filter((card) =>
              [
                "assetCategory",
                "assetAccount",
                "assetSubAccount",
                "storageLocation",
                "manufacturer",
                "assetSource",
                "accessoryEquipment",
                "amortizationSource",
                "userRole",
                "assetBatchImport",
                "systemParameter",
              ].includes(card.key)
            )
            .map((setting) => (
              <Col
                xs={24}
                sm={12}
                md={8}
                lg={6}
                xl={6}
                key={setting.key}
                style={{ padding: "0 12px" }}
              >
                <Badge.Ribbon
                  text={
                    setting.status === "processing"
                      ? "使用中"
                      : setting.status === "success"
                      ? "已設定"
                      : setting.status === "warning"
                      ? "待設定"
                      : "開發中"
                  }
                  color={
                    setting.status === "processing"
                      ? "blue"
                      : setting.status === "success"
                      ? "green"
                      : setting.status === "warning"
                      ? "orange"
                      : "gray"
                  }
                >
                  <Card
                    hoverable
                    onClick={() => handleCardClick(setting)}
                    className="h-full"
                    style={{
                      height: "100%",
                      transition: "all 0.3s",
                      width: "100%",
                      minHeight: "120px",
                    }}
                  >
                    <div className="flex items-center mb-4">
                      {setting.icon}
                      <Title level={5} className="mb-0 ml-3">
                        {setting.title}
                      </Title>
                    </div>
                    <Text type="secondary" style={{ fontSize: "12px" }}>
                      {setting.description}
                    </Text>
                  </Card>
                </Badge.Ribbon>
              </Col>
            ))}
        </Row>
      </div>

      {/* 財產管理模組 */}
      <div className="mb-8">
        <Title level={3} className="mb-4">
          <FileTextOutlined className="mr-2" />
          財產管理
        </Title>
        <Row
          gutter={[24, 24]}
          style={{
            opacity: isExpanded ? 0 : 1,
            transform: `translateY(${isExpanded ? "20px" : "0"})`,
            transition: "all 0.3s ease-in-out",
            display: isExpanded ? "none" : "flex",
            margin: "0 -8px",
          }}
        >
          {settingCards
            .filter((card) =>
              [
                "fixedAssetMaintenance",
                "vendorMaintenance",
                "assetCarryOut",
                "assetDepreciation",
                "assetLocationChange",
                "assetSale",
                "assetScrap",
                "assetInventoryForm",
              ].includes(card.key)
            )
            .map((setting) => (
              <Col
                xs={24}
                sm={12}
                md={8}
                lg={6}
                xl={6}
                key={setting.key}
                style={{ padding: "0 12px" }}
              >
                <Badge.Ribbon
                  text={
                    setting.status === "processing"
                      ? "使用中"
                      : setting.status === "success"
                      ? "已設定"
                      : setting.status === "warning"
                      ? "待設定"
                      : "開發中"
                  }
                  color={
                    setting.status === "processing"
                      ? "blue"
                      : setting.status === "success"
                      ? "green"
                      : setting.status === "warning"
                      ? "orange"
                      : "gray"
                  }
                >
                  <Card
                    hoverable
                    onClick={() => handleCardClick(setting)}
                    className="h-full"
                    style={{
                      height: "100%",
                      transition: "all 0.3s",
                      width: "100%",
                      minHeight: "120px",
                    }}
                  >
                    <div className="flex items-center mb-4">
                      {setting.icon}
                      <Title level={5} className="mb-0 ml-3">
                        {setting.title}
                      </Title>
                    </div>
                    <Text type="secondary" style={{ fontSize: "12px" }}>
                      {setting.description}
                    </Text>
                  </Card>
                </Badge.Ribbon>
              </Col>
            ))}
        </Row>
      </div>

      {/* 報表列印模組 */}
      <div className="mb-8">
        <Title level={3} className="mb-4">
          <BarChartOutlined className="mr-2" />
          報表列印
        </Title>
        <Row
          gutter={[24, 24]}
          style={{
            opacity: isExpanded ? 0 : 1,
            transform: `translateY(${isExpanded ? "20px" : "0"})`,
            transition: "all 0.3s ease-in-out",
            display: isExpanded ? "none" : "flex",
            margin: "0 -8px",
          }}
        >
          {settingCards
            .filter((card) =>
              [
                "assetInventoryDetail",
                "assetDepreciationReport",
                "assetRegister",
                "custodianRoster",
                "assetCard",
                "assetScrapReport",
                "assetInventoryRecord",
                "assetLabel",
                "assetInventoryList",
                "assetIncreaseDecrease",
              ].includes(card.key)
            )
            .map((setting) => (
              <Col
                xs={24}
                sm={12}
                md={8}
                lg={6}
                xl={6}
                key={setting.key}
                style={{ padding: "0 12px" }}
              >
                <Badge.Ribbon
                  text={
                    setting.status === "processing"
                      ? "使用中"
                      : setting.status === "success"
                      ? "已設定"
                      : setting.status === "warning"
                      ? "待設定"
                      : "開發中"
                  }
                  color={
                    setting.status === "processing"
                      ? "blue"
                      : setting.status === "success"
                      ? "green"
                      : setting.status === "warning"
                      ? "orange"
                      : "gray"
                  }
                >
                  <Card
                    hoverable
                    onClick={() => handleCardClick(setting)}
                    className="h-full"
                    style={{
                      height: "100%",
                      transition: "all 0.3s",
                      width: "100%",
                      minHeight: "120px",
                    }}
                  >
                    <div className="flex items-center mb-4">
                      {setting.icon}
                      <Title level={5} className="mb-0 ml-3">
                        {setting.title}
                      </Title>
                    </div>
                    <Text type="secondary" style={{ fontSize: "12px" }}>
                      {setting.description}
                    </Text>
                  </Card>
                </Badge.Ribbon>
              </Col>
            ))}
        </Row>
      </div>
    </div>
  );

  const renderSettingContent = () => (
    <div
      style={{
        opacity: isExpanded ? 1 : 0,
        transform: `translateY(${isExpanded ? "0" : "-20px"})`,
        transition: "all 0.3s ease-in-out",
        display: !isExpanded ? "none" : "block",
        width: "100%",
        overflow: "hidden",
      }}
    >
      <Flex vertical gap="middle">
        <Space className="py-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            style={{
              display: "flex",
              alignItems: "center",
              fontSize: "15px",
              height: "40px",
              borderRadius: "8px",
              transition: "all 0.3s",
            }}
            className="hover:bg-gray-100 hover:border-gray-300"
          >
            返回功能總覽
          </Button>
        </Space>
        <Card
          title={
            <Flex align="center" gap="middle">
              {selectedSetting?.icon}
              <span>{selectedSetting?.title}</span>
            </Flex>
          }
          style={{ width: "100%" }}
        >
          {selectedSetting?.component}
        </Card>
      </Flex>
    </div>
  );

  return (
    <div className="p-6 overflow-x-hidden" style={{ maxWidth: "100%" }}>
      {/* 頁面載入 Loading 覆蓋層 */}
      {isLoading && <PageLoadingOverlay text="正在載入功能總覽..." />}

      <div className="mb-6">
        <Title level={2}>
          <DatabaseOutlined className="mr-3" />
          財產管理系統功能總覽
        </Title>
        <Text type="secondary">
          {isExpanded
            ? selectedSetting?.description
            : "選擇下方任一功能模組進行操作與管理"}
        </Text>
      </div>

      {/* 統計總覽區域 */}
      {renderStatisticsOverview()}

      {/* 統計圖表區域 */}
      {renderStatisticsCharts()}

      {renderSettingCards()}
      {renderSettingContent()}
    </div>
  );
};

export default PMSOverviewPage;
