/**
 * Partner 聯絡人關聯管理組件
 * 
 * 專門用於 Partner 頁面的聯絡人關聯管理
 * 提供快速新增、選擇現有聯絡人、編輯關聯等功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

"use client";

import React, { useState } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  message, 
  Divider,
  Typography,
  Row,
  Col
} from 'antd';
import { 
  PlusOutlined, 
  UserAddOutlined, 
  LinkOutlined,
  ContactsOutlined
} from '@ant-design/icons';

// Services
import { Contact } from '@/services/ims/ContactService';
import { PartnerContact } from '@/services/ims/partner';

// 共享組件
import ContactFormModal from './ContactFormModal';
import ContactSelector from './ContactSelector';
import ContactManagementTab from '../ContactManagementTab';

// 響應式工具
import { useScreenSize } from './ResponsiveModalConfig';

const { Title, Text } = Typography;

export interface PartnerContactManagerProps {
  /** Partner ID */
  partnerID: string;
  /** Partner 名稱 */
  partnerName?: string;
  /** 現有聯絡人關聯列表 */
  partnerContacts?: PartnerContact[];
  /** 新增聯絡人回調 */
  onAddContact?: (contactData: Contact, partnerContactData?: Partial<PartnerContact>) => Promise<void>;
  /** 關聯現有聯絡人回調 */
  onLinkContact?: (contactId: string, partnerContactData: Partial<PartnerContact>) => Promise<void>;
  /** 更新關聯回調 */
  onUpdatePartnerContact?: (partnerContact: PartnerContact) => Promise<void>;
  /** 移除關聯回調 */
  onRemovePartnerContact?: (partnerContactId: string) => Promise<void>;
  /** 載入狀態 */
  loading?: boolean;
  /** 自定義樣式類名 */
  className?: string;
}

/**
 * Partner 聯絡人關聯管理組件
 * 
 * 特性：
 * - 快速新增聯絡人（包含 PartnerContact 關聯）
 * - 選擇並關聯現有聯絡人
 * - 管理現有的聯絡人關聯
 * - 響應式設計
 */
const PartnerContactManager: React.FC<PartnerContactManagerProps> = ({
  partnerID,
  partnerName,
  partnerContacts = [],
  onAddContact,
  onLinkContact,
  onUpdatePartnerContact,
  onRemovePartnerContact,
  loading = false,
  className = ''
}) => {
  const { isMobile } = useScreenSize();
  
  // 表單狀態
  const [contactFormVisible, setContactFormVisible] = useState(false);
  const [contactFormMode, setContactFormMode] = useState<'create' | 'quick' | 'edit'>('quick');
  const [contactFormLoading, setContactFormLoading] = useState(false);
  
  // 關聯狀態
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [linkingContact, setLinkingContact] = useState(false);

  // 快速新增聯絡人
  const handleQuickAddContact = () => {
    setContactFormMode('quick');
    setContactFormVisible(true);
  };

  // 完整新增聯絡人
  const handleAddContact = () => {
    setContactFormMode('create');
    setContactFormVisible(true);
  };

  // 處理聯絡人表單提交
  const handleContactSubmit = async (contactData: Contact, partnerContactData?: Partial<PartnerContact>) => {
    setContactFormLoading(true);
    try {
      await onAddContact?.(contactData, partnerContactData);
      message.success('聯絡人新增成功');
      setContactFormVisible(false);
    } catch (error) {
      message.error('新增聯絡人失敗');
    } finally {
      setContactFormLoading(false);
    }
  };

  // 關聯現有聯絡人
  const handleLinkExistingContact = async () => {
    if (!selectedContact) {
      message.warning('請先選擇要關聯的聯絡人');
      return;
    }

    setLinkingContact(true);
    try {
      const partnerContactData: Partial<PartnerContact> = {
        partnerID,
        contactID: selectedContact.contactID,
        priority: 99,
        role: '',
        isPrimary: false
      };
      
      await onLinkContact?.(selectedContact.contactID, partnerContactData);
      message.success(`已成功關聯聯絡人：${selectedContact.name}`);
      setSelectedContact(null);
    } catch (error) {
      message.error('關聯聯絡人失敗');
    } finally {
      setLinkingContact(false);
    }
  };

  return (
    <div className={`partner-contact-manager ${className}`}>
      {/* 標題區域 */}
      <Card size="small">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <ContactsOutlined style={{ color: '#1890ff' }} />
            <Title level={5} style={{ margin: 0 }}>
              聯絡人管理
            </Title>
            {partnerName && (
              <Text type="secondary">- {partnerName}</Text>
            )}
          </div>
          
          <Space wrap size="small">
            <Button
              type="primary"
              icon={<UserAddOutlined />}
              onClick={handleQuickAddContact}
              size={isMobile ? 'small' : 'middle'}
              loading={loading}
            >
              {isMobile ? '快速新增' : '快速新增聯絡人'}
            </Button>
            
            <Button
              icon={<PlusOutlined />}
              onClick={handleAddContact}
              size={isMobile ? 'small' : 'middle'}
              loading={loading}
            >
              {isMobile ? '完整新增' : '完整新增聯絡人'}
            </Button>
          </Space>
        </div>
      </Card>

      {/* 關聯現有聯絡人區域 */}
      <Card 
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <LinkOutlined style={{ color: '#52c41a' }} />
            <span>關聯現有聯絡人</span>
          </div>
        }
        size="small"
        style={{ marginTop: 16 }}
      >
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={16} md={18}>
            <ContactSelector
              value={selectedContact}
              onChange={setSelectedContact}
              placeholder="搜尋並選擇要關聯的聯絡人"
              showAddButton={false}
              allowClear={true}
            />
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Button
              type="primary"
              icon={<LinkOutlined />}
              onClick={handleLinkExistingContact}
              loading={linkingContact}
              disabled={!selectedContact}
              style={{ width: '100%' }}
            >
              建立關聯
            </Button>
          </Col>
        </Row>
      </Card>

      <Divider />

      {/* 聯絡人關聯管理表格 */}
      <ContactManagementTab
        partnerID={partnerID}
        partnerContacts={partnerContacts}
        onChange={(updatedContacts) => {
          // 處理聯絡人關聯變更
          console.log('聯絡人關聯已更新:', updatedContacts);
        }}
        readonly={false}
      />

      {/* 聯絡人表單模態框 */}
      <ContactFormModal
        visible={contactFormVisible}
        onClose={() => {
          setContactFormVisible(false);
          setContactFormMode('quick');
        }}
        onSubmit={handleContactSubmit}
        loading={contactFormLoading}
        mode={contactFormMode}
        partnerID={partnerID}
        showPartnerContactFields={true}
      />
    </div>
  );
};

export default PartnerContactManager;
