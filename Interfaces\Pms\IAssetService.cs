using FAST_ERP_Backend.Models.Pms;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IAssetService
    {
        /// <summary>
        /// 取得新的財產編號
        /// </summary>
        /// <param name="subject">科目</param>
        /// <param name="subSubject">子目</param>
        /// <param name="category">類別</param>
        /// <returns>新的財產編號</returns>
        Task<(bool success, string assetNo, string message)> GetNewAssetNoAsync(string subject, string subSubject, string category);

        /// <summary>
        /// 取得資產資料列表
        /// </summary>
        /// <returns>資產資料列表</returns>
        Task<List<AssetWithAccessoriesDTO>> GetAssetAsync();

        /// <summary>
        /// 新增資產資料
        /// </summary>
        /// <param name="_data">資產資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddAssetAsync(AssetWithAccessoriesDTO _data);

        /// <summary>
        /// 編輯資產資料
        /// </summary>
        /// <param name="_data">資產資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditAssetAsync(AssetWithAccessoriesDTO _data);

        /// <summary>
        /// 刪除資產資料
        /// </summary>
        /// <param name="_data">資產資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteAssetAsync(AssetWithAccessoriesDTO _data);

        /// <summary>
        /// 取得資產詳細資料
        /// </summary>
        /// <param name="_assetId">資產編號</param>
        /// <returns>資產詳細資料</returns>
        Task<AssetWithAccessoriesDTO> GetAssetDetailAsync(string _assetId);

        /// <summary>
        /// 驗證Excel檔案格式和內容
        /// </summary>
        /// <param name="fileStream">Excel檔案流</param>
        /// <returns>驗證結果</returns>
        Task<BatchValidationResultDTO> ValidateExcelFileAsync(Stream fileStream);

        /// <summary>
        /// 批次匯入資產資料
        /// </summary>
        /// <param name="fileStream">Excel檔案流</param>
        /// <param name="userId">操作使用者ID</param>
        /// <returns>匯入結果</returns>
        Task<BatchImportResultDTO> BatchImportAssetsAsync(Stream fileStream, string userId);

        /// <summary>
        /// 下載批次轉檔範本
        /// </summary>
        /// <returns>Excel範本檔案</returns>
        Task<(byte[] fileBytes, string fileName)> DownloadBatchTemplateAsync();

        /// <summary>
        /// 取消目前使用者的批次匯入作業
        /// </summary>
        /// <param name="userId">操作使用者ID</param>
        /// <returns>是否成功發出取消請求</returns>
        Task<bool> CancelBatchImportAsync(string userId);

        /// <summary>
        /// 取得財產統計資料
        /// </summary>
        /// <param name="request">統計查詢參數</param>
        /// <returns>完整統計結果</returns>
        Task<AssetStatisticsResultDTO> GetAssetStatisticsAsync(AssetStatisticsRequestDTO? request = null);

        /// <summary>
        /// 取得財產統計總覽
        /// </summary>
        /// <returns>統計總覽資料</returns>
        Task<AssetStatisticsOverviewDTO> GetAssetStatisticsOverviewAsync();

        /// <summary>
        /// 按部門取得財產統計
        /// </summary>
        /// <param name="departmentIds">部門ID篩選</param>
        /// <returns>部門統計資料</returns>
        Task<List<AssetStatisticsByDepartmentDTO>> GetAssetStatisticsByDepartmentAsync(List<string>? departmentIds = null);

        /// <summary>
        /// 按狀態取得財產統計
        /// </summary>
        /// <param name="statusIds">狀態ID篩選</param>
        /// <returns>狀態統計資料</returns>
        Task<List<AssetStatisticsByStatusDTO>> GetAssetStatisticsByStatusAsync(List<Guid>? statusIds = null);

        /// <summary>
        /// 按科目取得財產統計
        /// </summary>
        /// <param name="accountIds">科目ID篩選</param>
        /// <returns>科目統計資料</returns>
        Task<List<AssetStatisticsByAccountDTO>> GetAssetStatisticsByAccountAsync(List<Guid>? accountIds = null);

        /// <summary>
        /// 按年度取得財產統計
        /// </summary>
        /// <param name="startYear">起始年度</param>
        /// <param name="endYear">結束年度</param>
        /// <returns>年度統計資料</returns>
        Task<List<AssetStatisticsByYearDTO>> GetAssetStatisticsByYearAsync(int? startYear = null, int? endYear = null);

        /// <summary>
        /// 按月度取得財產統計
        /// </summary>
        /// <param name="year">年度</param>
        /// <param name="months">月份數量（預設12個月）</param>
        /// <returns>月度統計資料</returns>
        Task<List<AssetStatisticsByMonthDTO>> GetAssetStatisticsByMonthAsync(int? year = null, int months = 12);

        /// <summary>
        /// 按廠牌取得財產統計
        /// </summary>
        /// <returns>廠牌統計資料</returns>
        Task<List<AssetStatisticsByManufacturerDTO>> GetAssetStatisticsByManufacturerAsync();

        /// <summary>
        /// 按設備類型取得財產統計
        /// </summary>
        /// <returns>設備類型統計資料</returns>
        Task<List<AssetStatisticsByEquipmentTypeDTO>> GetAssetStatisticsByEquipmentTypeAsync();

        /// <summary>
        /// 按使用年限取得財產統計
        /// </summary>
        /// <returns>使用年限統計資料</returns>
        Task<List<AssetStatisticsByAgeDTO>> GetAssetStatisticsByAgeAsync();

        /// <summary>
        /// 取得折舊統計資料
        /// </summary>
        /// <param name="year">年度</param>
        /// <param name="months">月份數量（預設12個月）</param>
        /// <returns>折舊統計資料</returns>
        Task<List<AssetDepreciationStatisticsDTO>> GetAssetDepreciationStatisticsAsync(int? year = null, int months = 12);

        /// <summary>
        /// 按價值區間取得財產統計
        /// </summary>
        /// <returns>價值區間統計資料</returns>
        Task<List<AssetStatisticsByValueRangeDTO>> GetAssetStatisticsByValueRangeAsync();
    }
}