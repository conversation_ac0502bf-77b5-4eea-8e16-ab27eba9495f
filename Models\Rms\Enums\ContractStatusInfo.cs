using System.Collections.Generic;
using System.Linq;

namespace FAST_ERP_Backend.Models.Rms.Enums
{
    /// <summary>
    /// 合約狀態（物件化定義）：草稿(0)、啟用(1)、終止(2)
    /// </summary>
    public sealed class ContractStatusInfo
    {
        public string Code { get; }
        public string Name { get; }

        private ContractStatusInfo(string code, string name)
        {
            Code = code;
            Name = name;
        }

        public static readonly ContractStatusInfo Draft = new("0", "草稿");
        public static readonly ContractStatusInfo Active = new("1", "啟用");
        public static readonly ContractStatusInfo Terminated = new("2", "終止");

        public static IEnumerable<ContractStatusInfo> All => new[] { Draft, Active, Terminated };

        public static ContractStatusInfo FromCode(string code) => All.First(x => x.Code == code);
    }
}


