import { createContextLogger, SYMBOLS } from '@/utils/logger';
import { granularPermissionService, GranularPermissionAction, ActionDefinition } from '@/services/common/granularPermissionService';

// 遵循 FastERP 日誌記錄策略
const actionManagerLogger = createContextLogger({ module: 'ActionManagerService' });

export class ActionManagerService {
  // 載入權限動作資料
  static async loadPermissionActions(permissionId: string): Promise<GranularPermissionAction[]> {
    try {
      actionManagerLogger.log(SYMBOLS.DEBUG, '開始載入權限動作資料', { permissionId });
      
      const response = await granularPermissionService.getPermissionActions(permissionId);
      
      if (response.success && response.data) {
        actionManagerLogger.log(SYMBOLS.SUCCESS, '權限動作資料載入成功', { 
          permissionId, 
          actionCount: response.data.length 
        });
        return response.data;
      } else {
        actionManagerLogger.log(SYMBOLS.WARNING, '權限動作資料載入失敗', { 
          permissionId, 
          error: response.message 
        });
        return [];
      }
    } catch (error) {
      actionManagerLogger.log(SYMBOLS.ERROR, '權限動作資料載入異常', { permissionId, error });
      throw error;
    }
  }

  // 載入可用的標準動作定義
  static async loadValidActions(): Promise<ActionDefinition[]> {
    try {
      actionManagerLogger.log(SYMBOLS.DEBUG, '開始載入標準動作定義');
      
      const response = await granularPermissionService.getValidActions();
      
      if (response.success && response.data) {
        actionManagerLogger.log(SYMBOLS.SUCCESS, '標準動作定義載入成功', { 
          actionCount: response.data.length 
        });
        return response.data;
      } else {
        actionManagerLogger.log(SYMBOLS.WARNING, '標準動作定義載入失敗', { 
          error: response.message 
        });
        return [];
      }
    } catch (error) {
      actionManagerLogger.log(SYMBOLS.ERROR, '標準動作定義載入異常', { error });
      throw error;
    }
  }

  // 新增權限動作
  static async addPermissionAction(actionData: {
    granularPermissionID: string;
    code: string;
    name: string;
    sortCode: number;
  }): Promise<{ success: boolean; message: string }> {
    try {
      actionManagerLogger.log(SYMBOLS.DEBUG, '開始新增權限動作', { actionData });
      
      const response = await granularPermissionService.createPermissionAction(actionData);
      
      if (response.success) {
        actionManagerLogger.log(SYMBOLS.SUCCESS, '權限動作新增成功', { 
          actionCode: actionData.code,
          actionName: actionData.name 
        });
        return { success: true, message: '新增成功' };
      } else {
        actionManagerLogger.log(SYMBOLS.WARNING, '權限動作新增失敗', { 
          actionData, 
          error: response.message 
        });
        return { success: false, message: response.message || '新增失敗' };
      }
    } catch (error) {
      actionManagerLogger.log(SYMBOLS.ERROR, '權限動作新增異常', { actionData, error });
      return { success: false, message: '新增失敗' };
    }
  }

  // 更新權限動作
  static async updatePermissionAction(actionData: GranularPermissionAction): Promise<{ success: boolean; message: string }> {
    try {
      actionManagerLogger.log(SYMBOLS.DEBUG, '開始更新權限動作', { actionData });
      
      const response = await granularPermissionService.updatePermissionAction(actionData);
      
      if (response.success) {
        actionManagerLogger.log(SYMBOLS.SUCCESS, '權限動作更新成功', { 
          actionId: actionData.granularPermissionActionID,
          actionName: actionData.name 
        });
        return { success: true, message: '更新成功' };
      } else {
        actionManagerLogger.log(SYMBOLS.WARNING, '權限動作更新失敗', { 
          actionData, 
          error: response.message 
        });
        return { success: false, message: response.message || '更新失敗' };
      }
    } catch (error) {
      actionManagerLogger.log(SYMBOLS.ERROR, '權限動作更新異常', { actionData, error });
      return { success: false, message: '更新失敗' };
    }
  }

  // 批次新增權限動作
  static async batchAddPermissionActions(
    permissionId: string, 
    actionCodes: string[]
  ): Promise<{ success: boolean; message: string; addedCount: number }> {
    try {
      actionManagerLogger.log(SYMBOLS.DEBUG, '開始批次新增權限動作', { 
        permissionId, 
        actionCodes 
      });
      
      let addedCount = 0;
      const errors: string[] = [];
      
      for (const code of actionCodes) {
        try {
          const result = await this.addPermissionAction({
            granularPermissionID: permissionId,
            code,
            name: code, // 預設使用 code 作為 name
            sortCode: 0
          });
          
          if (result.success) {
            addedCount++;
          } else {
            errors.push(`${code}: ${result.message}`);
          }
        } catch (error) {
          errors.push(`${code}: 新增失敗`);
          actionManagerLogger.log(SYMBOLS.ERROR, '批次新增單個動作失敗', { code, error });
        }
      }
      
      if (addedCount > 0) {
        actionManagerLogger.log(SYMBOLS.SUCCESS, '批次新增權限動作完成', { 
          permissionId, 
          addedCount, 
          totalCount: actionCodes.length 
        });
        return { 
          success: true, 
          message: `成功新增 ${addedCount} 個動作${errors.length > 0 ? `，${errors.length} 個失敗` : ''}`,
          addedCount 
        };
      } else {
        actionManagerLogger.log(SYMBOLS.WARNING, '批次新增權限動作全部失敗', { 
          permissionId, 
          errors 
        });
        return { 
          success: false, 
          message: `全部新增失敗: ${errors.join(', ')}`,
          addedCount: 0 
        };
      }
    } catch (error) {
      actionManagerLogger.log(SYMBOLS.ERROR, '批次新增權限動作異常', { 
        permissionId, 
        actionCodes, 
        error 
      });
      return { 
        success: false, 
        message: '批次新增失敗', 
        addedCount: 0 
      };
    }
  }
}
