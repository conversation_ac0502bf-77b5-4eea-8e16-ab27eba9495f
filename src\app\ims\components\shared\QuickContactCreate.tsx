/**
 * 快速創建 Contact 組件
 * 
 * 提供簡化的聯絡人創建流程，包括：
 * - 最小化必填欄位
 * - 快速驗證和提交
 * - 智能預填充
 * - 一鍵創建並關聯到商業夥伴
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

"use client";

import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Form, 
  Input, 
  Select, 
  Button, 
  Space, 
  Row, 
  Col,
  Divider,
  Typography,
  Alert
} from 'antd';
import { 
  UserOutlined, 
  MailOutlined, 
  PhoneOutlined,
  PlusOutlined,
  CheckOutlined
} from '@ant-design/icons';
import { Contact } from '@/services/ims/ContactService';
import { PartnerContact } from '@/services/ims/partner';
import { CONTACT_TYPES } from '@/app/ims/components/contact/shared/contactConstants';
import { useContactManagement } from '@/app/ims/hooks/useContactManagement';
import { useScreenSize } from './ResponsiveModalConfig';

const { Option } = Select;
const { Text } = Typography;

export interface QuickContactCreateProps {
  // 顯示控制
  visible: boolean;
  onClose: () => void;
  
  // 預填充數據
  defaultValues?: Partial<Contact>;
  
  // 商業夥伴關聯
  partnerID?: string;
  partnerName?: string;
  autoAssociateToPartner?: boolean;
  
  // 回調函數
  onContactCreated?: (contact: Contact) => void;
  onPartnerContactCreated?: (partnerContact: PartnerContact) => void;
  
  // 配置選項
  showPartnerAssociation?: boolean;
  allowMultipleCreate?: boolean;
}

const QuickContactCreate: React.FC<QuickContactCreateProps> = ({
  visible,
  onClose,
  defaultValues,
  partnerID,
  partnerName,
  autoAssociateToPartner = false,
  onContactCreated,
  onPartnerContactCreated,
  showPartnerAssociation = true,
  allowMultipleCreate = true
}) => {
  const { isMobile } = useScreenSize();
  const [form] = Form.useForm();
  const [createAnother, setCreateAnother] = useState(false);
  const [associateToPartner, setAssociateToPartner] = useState(autoAssociateToPartner);

  // Contact 管理 Hook
  const [contactState, contactActions] = useContactManagement({
    autoLoad: false,
    onContactCreated: (contact) => {
      onContactCreated?.(contact);
      
      // 如果需要關聯到商業夥伴
      if (associateToPartner && partnerID) {
        handlePartnerAssociation(contact);
      }
      
      // 處理連續創建
      if (createAnother) {
        form.resetFields();
        // 保留一些預設值
        if (defaultValues) {
          form.setFieldsValue({
            contactType: defaultValues.contactType || '客戶',
            company: defaultValues.company || '',
            department: defaultValues.department || ''
          });
        }
      } else {
        handleClose();
      }
    }
  });

  // 處理商業夥伴關聯
  const handlePartnerAssociation = async (contact: Contact) => {
    if (!partnerID || !contact.contactID) return;
    
    try {
      // TODO: 實現 PartnerContact 關聯邏輯
      // const partnerContactData: Partial<PartnerContact> = {
      //   partnerID,
      //   contactID: contact.contactID,
      //   role: '聯絡人',
      //   isPrimary: false,
      //   priority: 99
      // };
      // 
      // const response = await addPartnerContact(partnerContactData);
      // if (response.success) {
      //   onPartnerContactCreated?.(response.data);
      // }
      
      console.log('商業夥伴關聯功能開發中...', { partnerID, contactID: contact.contactID });
    } catch (error) {
      console.error('商業夥伴關聯失敗:', error);
    }
  };

  // 處理表單提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 合併預設值
      const contactData = {
        ...defaultValues,
        ...values,
        isActive: true
      };
      
      await contactActions.createContact(contactData);
    } catch (error) {
      console.error('快速創建聯絡人失敗:', error);
    }
  };

  // 處理關閉
  const handleClose = () => {
    form.resetFields();
    setCreateAnother(false);
    setAssociateToPartner(autoAssociateToPartner);
    onClose();
  };

  // 初始化表單
  useEffect(() => {
    if (visible && defaultValues) {
      form.setFieldsValue(defaultValues);
    }
  }, [visible, defaultValues, form]);

  return (
    <Modal
      title={
        <Space>
          <PlusOutlined />
          快速創建聯絡人
          {partnerName && (
            <Text type="secondary">
              - {partnerName}
            </Text>
          )}
        </Space>
      }
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={isMobile ? '95%' : 600}
      style={{ top: isMobile ? 20 : undefined }}
    >
      {/* 商業夥伴關聯提示 */}
      {showPartnerAssociation && partnerID && (
        <Alert
          message="商業夥伴關聯"
          description={`創建後將自動關聯到商業夥伴：${partnerName || partnerID}`}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
          action={
            <Button
              size="small"
              type={associateToPartner ? 'primary' : 'default'}
              onClick={() => setAssociateToPartner(!associateToPartner)}
            >
              {associateToPartner ? '已啟用' : '已停用'}
            </Button>
          }
        />
      )}

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        preserve={false}
      >
        <Row gutter={[16, 0]}>
          {/* 基本資訊 */}
          <Col xs={24} sm={12}>
            <Form.Item
              name="name"
              label="姓名"
              rules={[
                { required: true, message: '請輸入聯絡人姓名' },
                { max: 100, message: '姓名不能超過100個字元' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="請輸入聯絡人姓名"
                maxLength={100}
                showCount
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12}>
            <Form.Item
              name="contactType"
              label="聯絡人類型"
              rules={[{ required: true, message: '請選擇聯絡人類型' }]}
              initialValue="客戶"
            >
              <Select placeholder="請選擇聯絡人類型">
                {CONTACT_TYPES.map(type => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          {/* 聯絡方式 */}
          <Col xs={24} sm={12}>
            <Form.Item
              name="email"
              label="電子郵件"
              rules={[
                { type: 'email', message: '請輸入有效的電子郵件格式' },
                { max: 100, message: '電子郵件不能超過100個字元' }
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="請輸入電子郵件"
                maxLength={100}
                showCount
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12}>
            <Form.Item
              name="phone"
              label="電話"
              rules={[
                { max: 20, message: '電話號碼不能超過20個字元' }
              ]}
            >
              <Input
                prefix={<PhoneOutlined />}
                placeholder="請輸入電話號碼"
                maxLength={20}
                showCount
              />
            </Form.Item>
          </Col>

          {/* 可選資訊 */}
          <Col xs={24} sm={12}>
            <Form.Item
              name="position"
              label="職位"
              rules={[
                { max: 50, message: '職位不能超過50個字元' }
              ]}
            >
              <Input
                placeholder="請輸入職位"
                maxLength={50}
                showCount
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12}>
            <Form.Item
              name="company"
              label="公司"
              rules={[
                { max: 100, message: '公司名稱不能超過100個字元' }
              ]}
            >
              <Input
                placeholder="請輸入公司名稱"
                maxLength={100}
                showCount
              />
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        {/* 操作按鈕 */}
        <Row justify="space-between" align="middle">
          <Col>
            {allowMultipleCreate && (
              <Space>
                <Button
                  type={createAnother ? 'primary' : 'default'}
                  size="small"
                  onClick={() => setCreateAnother(!createAnother)}
                >
                  {createAnother ? <CheckOutlined /> : <PlusOutlined />}
                  連續創建
                </Button>
              </Space>
            )}
          </Col>
          
          <Col>
            <Space>
              <Button onClick={handleClose}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={contactState.formLoading}
              >
                {createAnother ? '創建並繼續' : '創建聯絡人'}
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default QuickContactCreate;
