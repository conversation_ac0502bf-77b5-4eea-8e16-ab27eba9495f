using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Interfaces.Rms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Rms;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace FAST_ERP_Backend.Controllers.Rms
{
    /// <summary>
    /// 房源管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class PropertyController : ControllerBase
    {
        private readonly IPropertyService _propertyService;
        private readonly ISystemParametersItemService _systemParametersItemService;
        private readonly ILogger<PropertyController> _logger;

        /// <summary>
        /// 建構式
        /// </summary>
        public PropertyController(IPropertyService propertyService, ISystemParametersItemService systemParametersItemService, ILogger<PropertyController> logger)
        {
            _propertyService = propertyService;
            _systemParametersItemService = systemParametersItemService;
            _logger = logger;
        }

        //取得登入者token資訊,在middleware時就會將資訊存入
        private ClaimsPrincipal LoginUser => HttpContext.User;

        /// <summary>
        /// 取得房源列表
        /// </summary>
        /// <param name="request">查詢條件</param>
        /// <returns>房源列表</returns>
        [HttpGet]
        [ProducesResponseType(typeof(ApiResponse<List<PropertyDTO>>), 200)]
        [ProducesResponseType(typeof(ApiResponse<string>), 400)]
        [ProducesResponseType(typeof(ApiResponse<string>), 500)]
        public async Task<IActionResult> GetPropertyList([FromQuery] PropertyQueryRequestDTO request)
        {
            var result = await _propertyService.GetPropertyListAsync(request);
            return result.Success ? Ok(result) : StatusCode(result.HttpCode, result);
        }

        /// <summary>
        /// 取得房源詳細資料
        /// </summary>
        /// <param name="propertyId">房源編號</param>
        /// <returns>房源詳細資料</returns>
        [HttpGet("{propertyId}")]
        [ProducesResponseType(typeof(ApiResponse<PropertyDTO>), 200)]
        [ProducesResponseType(typeof(ApiResponse<string>), 404)]
        [ProducesResponseType(typeof(ApiResponse<string>), 500)]
        public async Task<IActionResult> GetPropertyById([Required] string propertyId)
        {
            var result = await _propertyService.GetPropertyByIdAsync(propertyId);
            return result.Success ? Ok(result) : StatusCode(result.HttpCode, result);
        }

        /// <summary>
        /// 新增房源
        /// </summary>
        /// <param name="request">新增請求</param>
        /// <returns>新增結果</returns>
        [HttpPost]
        [ProducesResponseType(typeof(ApiResponse<PropertyDTO>), 201)]
        [ProducesResponseType(typeof(ApiResponse<string>), 400)]
        [ProducesResponseType(typeof(ApiResponse<string>), 500)]
        public async Task<IActionResult> CreateProperty([FromBody] PropertyCreateRequestDTO request)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier)!.Value;
            var result = await _propertyService.CreatePropertyAsync(request, tokenUid);
            
            return result.Success 
                ? CreatedAtAction(nameof(GetPropertyById), new { propertyId = result.Data.PropertyId }, result)
                : StatusCode(result.HttpCode, result);
        }

        /// <summary>
        /// 更新房源
        /// </summary>
        /// <param name="request">更新請求</param>
        /// <returns>更新結果</returns>
        [HttpPut]
        [ProducesResponseType(typeof(ApiResponse<string>), 200)]
        [ProducesResponseType(typeof(ApiResponse<string>), 400)]
        [ProducesResponseType(typeof(ApiResponse<string>), 404)]
        [ProducesResponseType(typeof(ApiResponse<string>), 500)]
        public async Task<IActionResult> UpdateProperty([FromBody] PropertyUpdateRequestDTO request)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier)!.Value;
            var result = await _propertyService.UpdatePropertyAsync(request, tokenUid);
            return result.Success ? Ok(result) : StatusCode(result.HttpCode, result);
        }

        /// <summary>
        /// 刪除房源
        /// </summary>
        /// <param name="request">刪除請求</param>
        /// <returns>刪除結果</returns>
        [HttpDelete]
        [ProducesResponseType(typeof(ApiResponse<string>), 200)]
        [ProducesResponseType(typeof(ApiResponse<string>), 400)]
        [ProducesResponseType(typeof(ApiResponse<string>), 404)]
        [ProducesResponseType(typeof(ApiResponse<string>), 500)]
        public async Task<IActionResult> DeleteProperty([FromBody] PropertyDeleteRequestDTO request)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier)!.Value;
            var result = await _propertyService.DeletePropertyAsync(request, tokenUid);
            return result.Success ? Ok(result) : StatusCode(result.HttpCode, result);
        }

        /// <summary>
        /// 取得房源狀態列表
        /// </summary>
        /// <returns>房源狀態列表</returns>
        [HttpGet("status-list")]
        [ProducesResponseType(typeof(ApiResponse<object>), 200)]
        [ProducesResponseType(typeof(ApiResponse<string>), 500)]
        public async Task<IActionResult> GetPropertyStatusList()
        {
            var result = await _systemParametersItemService.GetSystemParametersItemAsync("", "PropertyStatus");
            return result.Success ? Ok(result) : StatusCode(result.HttpCode, result);
        }

        /// <summary>
        /// 取得房型列表
        /// </summary>
        /// <returns>房型列表</returns>
        [HttpGet("type-list")]
        [ProducesResponseType(typeof(ApiResponse<object>), 200)]
        [ProducesResponseType(typeof(ApiResponse<string>), 500)]
        public async Task<IActionResult> GetPropertyTypeList()
        {
             var result = await _systemParametersItemService.GetSystemParametersItemAsync("", "PropertyType");
            return result.Success ? Ok(result) : StatusCode(result.HttpCode, result);
        }
    }
} 