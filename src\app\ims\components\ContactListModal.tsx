"use client";

import React, { useState, useEffect } from "react";
import {
  Modal,
  message,
} from "antd";
import {
  UserOutlined,
} from "@ant-design/icons";

// Services
import { getContactList, deleteContact, Contact } from "@/services/ims/ContactService";

// 共享組件
import ContactTable from "./shared/ContactTable";
import UnifiedContactFilters from "./contact/UnifiedContactFilters";

// 響應式工具
import { useScreenSize } from "./shared/ResponsiveModalConfig";

// Types
interface ContactListModalProps {
  visible: boolean;
  onClose: () => void;
  onEdit: (contact: Contact) => void;
  onSelect?: (contact: Contact) => void;
  selectMode?: boolean;
}

const ContactListModal: React.FC<ContactListModalProps> = ({
  visible,
  onClose,
  onEdit,
  onSelect,
  selectMode = false,
}) => {
  const { isMobile } = useScreenSize();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);

  // 載入聯絡人資料
  const loadContacts = async () => {
    setLoading(true);
    try {
      const response = await getContactList();
      if (response.success && response.data) {
        setContacts(response.data);
        setFilteredContacts(response.data);
      } else {
        message.error("載入聯絡人資料失敗");
      }
    } catch (error) {
      console.error("載入聯絡人資料失敗:", error);
      message.error("載入聯絡人資料失敗");
    } finally {
      setLoading(false);
    }
  };

  // 處理篩選結果
  const handleFilterResult = (filteredContacts: Contact[]) => {
    setFilteredContacts(filteredContacts);
  };

  // 處理刪除
  const handleDelete = async (contactId: string) => {
    try {
      const response = await deleteContact(contactId);
      if (response.success) {
        message.success("刪除成功");
        await loadContacts();
      } else {
        message.error(response.message || "刪除失敗");
      }
    } catch (error) {
      console.error("刪除聯絡人失敗:", error);
      message.error("刪除失敗");
    }
  };

  // 處理選擇
  const handleSelect = (contact: Contact) => {
    if (onSelect) {
      onSelect(contact);
      onClose();
    }
  };

  useEffect(() => {
    if (visible) {
      loadContacts();
    }
  }, [visible]);

  return (
    <Modal
      title={
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <UserOutlined style={{ color: "#1890ff" }} />
          <span>{selectMode ? "選擇聯絡人" : "聯絡人列表"}</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={isMobile ? "95%" : "90%"}
      style={{ top: 20 }}
      destroyOnClose
    >
      {/* 篩選器 */}
      <div style={{ marginBottom: 16 }}>
        <UnifiedContactFilters
          contacts={contacts}
          onFilterResult={handleFilterResult}
          showStats={true}
          compact={isMobile}
        />
      </div>

      {/* 聯絡人表格 */}
      <ContactTable
        contacts={filteredContacts}
        loading={loading}
        selectMode={selectMode}
        onEdit={onEdit}
        onDelete={handleDelete}
        onSelect={handleSelect}
        showPagination={true}
        pageSize={10}
      />
    </Modal>
  );
};

export default ContactListModal;
