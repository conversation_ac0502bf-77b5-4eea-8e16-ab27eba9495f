# FastERP 架構參考指南

## 技術棧標準

### 前端技術棧
- **Next.js 14** - 最新版本，支援 App Router
- **React 18** - 現代化 React 特性
- **TypeScript** - 強型別支援，提升開發品質
- **Ant Design 5.x** - 成熟的 UI 組件庫
- **Tailwind CSS** - 實用優先的 CSS 框架

### 後端技術棧
- **.NET 8** - 最新 LTS 版本
- **EF Core 9.0** - 最新 ORM 框架
- **SQL Server** - 企業級資料庫
- **MongoDB** - 日誌系統專用
- **SignalR** - 即時通訊支援

## 模組架構標準

### 模組組織結構
```
Controllers/
├── Common/        # 共用控制器
├── Ims/          # 進銷存控制器
├── Pas/          # 人事控制器
├── Pms/          # 專案控制器
├── Rms/          # 報表控制器
└── Sms/          # 系統控制器

Services/
├── Common/        # 共用服務
├── Ims/          # 進銷存服務
└── ...           # 其他模組服務

Models/
├── Common/        # 共用模型
├── Ims/          # 進銷存模型
└── ...           # 其他模組模型
```

### 前端模組結構
```
src/app/
├── ims/           # 進銷存管理系統
├── pas/           # 人事管理系統
├── pms/           # 專案管理系統
├── rms/           # 報表管理系統
├── sms/           # 系統管理
└── common/        # 共用組件
```

## API 設計標準

### 路由模式
**標準格式：** `api/{module_abbreviation}/{controller_name}`

**模組前綴對照：**
- **IMS：** `/api/ims/` (進銷存管理系統)
- **Common：** `/api/common/` (共用功能)
- **Pas：** `/api/pas/` (人事管理系統)
- **Pms：** `/api/pms/` (專案管理系統)
- **Rms：** `/api/rms/` (報表管理系統)
- **Sms：** `/api/sms/` (系統管理)

### 統一回應格式
**ApiResponse<T> 標準格式：**
```json
{
  "success": true,
  "message": "操作成功",
  "data": [...],
  "paginate": {
    "currentPage": 1,
    "pageSize": 10,
    "totalCount": 100,
    "totalPages": 10
  }
}
```

**錯誤回應格式：**
```json
{
  "success": false,
  "message": "錯誤訊息",
  "data": null,
  "paginate": null
}
```

## 泛型架構標準

### GenericController<T> 模式
**優點：**
- 標準化 CRUD 操作
- 減少重複程式碼
- 統一錯誤處理
- 自動 API 文件生成

**使用方式：**
```csharp
public class PartnerController : GenericController<Partner, PartnerDTO>
{
    public PartnerController(IPartnerService service) : base(service) { }
    
    // 特殊業務邏輯可以覆寫或新增
}
```

### GenericService<T> 模式
**優點：**
- 統一業務邏輯處理
- 標準化資料驗證
- 一致的事務管理
- 可擴展的服務層

## 身份驗證標準

### JWT Token 機制
- 使用 JWT Token 進行身份驗證
- Token 不需要 "Bearer" 前綴
- 自動處理 Authorization header
- 程式碼：`var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last() ?? string.Empty;`

### 測試帳號
- **帳號：** FastAdmin
- **密碼：** fast!234
- **TestLogin API：** `POST /api/common/Login/TestLogin?acc=FastAdmin&pw=fast%21234`

## 資料庫設計標準

### 主鍵命名規範
- 使用描述性名稱：`PartnerID`、`ContactID`、`ItemID`
- 避免通用名稱：`Id`
- 資料庫儲存為 `nvarchar(100)`，模型定義為 `Guid`

### 關聯設計原則
- 適當的外鍵約束
- 合理的索引策略
- 正規化設計
- 資料完整性保護

## 驗證規則標準

### 前後端同步原則
- 後端 `[Required]` 屬性為準
- 前端驗證規則必須與後端一致
- 統一錯誤訊息格式
- 預設值設定同步

### 常見驗證模式
```csharp
// 後端模型
public class Contact
{
    [Required]
    public string Name { get; set; }
    
    public int Priority { get; set; } = 99; // 預設值
}
```

```typescript
// 前端驗證
export const getContactValidationRules = () => ({
  name: [{ required: true, message: '請輸入姓名' }],
  priority: [{ type: 'number', min: 0, max: 999 }] // 非必填
});
```

## 效能優化標準

### 前端優化
- Next.js SSR/SSG 優化
- 組件懶載入
- 圖片優化
- 程式碼分割

### 後端優化
- EF Core 查詢優化
- 資料庫連接池
- 適當的快取策略
- 分頁查詢優化

## 開發準則

### 程式碼品質
- TypeScript 強型別使用
- ESLint 規則遵循
- 程式碼審查機制
- 單元測試覆蓋

### 命名規範
- 英文程式碼，繁體中文註解
- 描述性變數和函數名稱
- 一致的命名風格
- 清晰的檔案組織

### 錯誤處理
- 統一的錯誤處理機制
- 清晰的錯誤訊息
- 適當的日誌記錄
- 使用者友善的錯誤提示
