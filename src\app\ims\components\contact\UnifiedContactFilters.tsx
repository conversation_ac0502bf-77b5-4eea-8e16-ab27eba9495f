/**
 * 統一的聯絡人篩選器組件
 * 
 * 完全使用 FilterSearchContainer，提供標準化的聯絡人篩選和搜尋體驗
 * 取代原有的 ContactFilters 和 ContactSearchForm 組件
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

"use client";

import React, { forwardRef, useImperativeHandle } from 'react';

// 共享組件
import FilterSearchContainer, { 
  FilterSearchContainerRef 
} from '../shared/FilterSearchContainer';

// 聯絡人相關
import { Contact } from '@/services/ims/ContactService';
import { CONTACT_FILTER_OPTIONS } from './shared/contactConstants';
import { applyContactFilters, getContactStats } from '../shared/contactUtils';

// 響應式工具
import { useScreenSize } from '../shared/ResponsiveModalConfig';

export interface UnifiedContactFiltersProps {
  /** 聯絡人資料 */
  contacts: Contact[];
  /** 篩選結果回調 */
  onFilterResult: (filteredContacts: Contact[], filterState: {
    searchText: string;
    activeFilters: string[];
    filterValues: Record<string, any>;
    hasActiveFilters: boolean;
    filterCount: number;
  }) => void;
  /** 是否顯示統計資訊 */
  showStats?: boolean;
  /** 標題 */
  title?: string;
  /** 搜尋框佔位符 */
  searchPlaceholder?: string;
  /** 是否緊湊模式 */
  compact?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定義樣式類名 */
  className?: string;
}

export interface UnifiedContactFiltersRef {
  /** 清除所有篩選條件 */
  clearAll: () => void;
}

/**
 * 統一的聯絡人篩選器組件
 * 
 * 特性：
 * - 使用標準化的 CONTACT_FILTER_OPTIONS 配置
 * - 整合 applyContactFilters 工具函數
 * - 自動計算統計資訊
 * - 支援響應式設計
 * - 提供 ref 介面供外部控制
 * 
 * @example
 * ```tsx
 * const filterRef = useRef<UnifiedContactFiltersRef>(null);
 * 
 * <UnifiedContactFilters
 *   ref={filterRef}
 *   contacts={contacts}
 *   onFilterResult={(filtered, state) => {
 *     setFilteredContacts(filtered);
 *   }}
 *   showStats={true}
 * />
 * ```
 */
const UnifiedContactFilters = forwardRef<UnifiedContactFiltersRef, UnifiedContactFiltersProps>(({
  contacts = [],
  onFilterResult,
  showStats = true,
  title = "聯絡人篩選與搜尋",
  searchPlaceholder,
  compact,
  disabled = false,
  className = ''
}, ref) => {
  const { isMobile } = useScreenSize();
  const filterSearchRef = React.useRef<FilterSearchContainerRef>(null);

  // 自動設置搜尋佔位符
  const defaultSearchPlaceholder = isMobile 
    ? "搜尋聯絡人" 
    : "搜尋聯絡人姓名、電子郵件、電話、公司...";

  // 處理篩選結果
  const handleFilterResult = React.useCallback((filterState: {
    searchText: string;
    activeFilters: string[];
    filterValues: Record<string, any>;
    hasActiveFilters: boolean;
    filterCount: number;
  }) => {
    // 使用統一的 applyContactFilters 工具函數
    const filteredContacts = applyContactFilters(
      contacts,
      filterState.searchText,
      filterState.filterValues
    );

    // 回調篩選結果
    onFilterResult(filteredContacts, filterState);
  }, [contacts, onFilterResult]);

  // 計算統計資訊
  const stats = React.useMemo(() => {
    if (!showStats) return undefined;
    
    const contactStats = getContactStats(contacts);
    return {
      total: contactStats.total,
      filtered: contacts.length // 這會被 handleFilterResult 更新
    };
  }, [contacts, showStats]);

  // 暴露 ref 介面
  useImperativeHandle(ref, () => ({
    clearAll: () => {
      filterSearchRef.current?.clearAll();
    }
  }), []);

  return (
    <FilterSearchContainer
      ref={filterSearchRef}
      title={title}
      searchPlaceholder={searchPlaceholder || defaultSearchPlaceholder}
      filterOptions={[...CONTACT_FILTER_OPTIONS] as any}
      onFilterResult={handleFilterResult}
      showStats={showStats}
      stats={stats}
      compact={compact || isMobile}
      disabled={disabled}
      className={className}
    />
  );
});

UnifiedContactFilters.displayName = 'UnifiedContactFilters';

export default UnifiedContactFilters;
