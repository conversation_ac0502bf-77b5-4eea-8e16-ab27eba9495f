import axios, { AxiosInstance, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';
import { getCookie, removeCookie } from '@/utils/cookies';
import { apiConfig, ApiResponse } from '@/config/api';
import { notifySuc<PERSON>, notifyError } from '@/utils/notification';
import router from 'next/router';
import { logger, SYMBOLS, createContextLogger } from '@/utils/logger';

// 創建上下文日誌器
const httpLogger = createContextLogger({ module: 'HttpService' });

// 創建 axios 實例
const http: AxiosInstance = axios.create({
    //baseURL: apiConfig.baseURL,
    timeout: 1200000,//20分鐘
    // 不設定 Content-Type，讓 axios 自動決定（尤其是處理 FormData 時）
});

// 請求攔截器
http.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        const token = getCookie('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error: AxiosError) => {
        return Promise.reject(error);
    }
);

// 響應攔截器
http.interceptors.response.use(
    (response: AxiosResponse) => {
        // 檢查響應數據格式
        const data = response.data;

        // 處理字符串響應；
        if (typeof data === 'string') {
            // 檢查是否包含成功或失敗的關鍵字
            const isSuccess = data.includes('成功');
            return {
                data: {
                    success: isSuccess,
                    message: data,
                    data: null
                },
            } as AxiosResponse;
        }

        if (data && typeof data === 'object') {
            // 處理登入 API 的特殊響應
            if (response.config.url?.includes('/Login/')) {
                const loginResponse = {
                    data: {
                        success: data.result,
                        message: data.msg,
                        data: data,
                    },
                } as AxiosResponse;
                return loginResponse;
            }

            // 處理一般 API 響應 - 直接返回後端的回應格式
            if (data.success !== undefined && data.message !== undefined && data.data !== undefined) {
                // 後端已經返回標準格式，直接使用
                const apiResponse = {
                    data: data,
                } as AxiosResponse;
                return apiResponse;
            } else {
                // 後端返回非標準格式，包裝成標準格式
                const apiResponse = {
                    data: {
                        success: true,
                        message: '',
                        data: data,
                    },
                } as AxiosResponse;
                return apiResponse;
            }
        }
        return response;
    },
    (error: AxiosError) => {
        // 處理網路錯誤或連接失敗
        if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
            httpLogger.log(SYMBOLS.ERROR, '網路連線失敗', {
                url: error.config?.url,
                method: error.config?.method
            });

            // 清除所有相關的 cookies
            removeCookie('token');
            removeCookie('userId');
            removeCookie('userName');

            // 顯示錯誤訊息
            notifyError('連線已斷開', '請重新登入');

            // 確保在瀏覽器環境下執行跳轉
            if (typeof window !== 'undefined') {
                router.push('/login');
            }
            return Promise.reject(error);
        }

        // 處理其他錯誤
        if (error.response) {
            const status = error.response.status;
            // 檢查是否為未授權或 token 相關錯誤
            if (status === 401 ||
                (error.response.data &&
                    typeof error.response.data === 'object' &&
                    'msg' in error.response.data &&
                    (error.response.data.msg as string).includes('token'))) {

                httpLogger.log(SYMBOLS.WARNING, '認證失敗，清除登入狀態', { status });

                // 清除所有相關的 cookies
                removeCookie('token');
                removeCookie('userId');
                removeCookie('userName');
                // 顯示錯誤訊息
                notifyError('登入已過期', '請重新登入');
                // 確保在瀏覽器環境下執行跳轉
                if (typeof window !== 'undefined') {
                    router.push('/login');
                }
            } else {
                httpLogger.log(SYMBOLS.ERROR, 'HTTP 錯誤回應', {
                    status,
                    url: error.config?.url,
                    method: error.config?.method
                });

                switch (status) {
                    case 403:
                        notifyError('權限不足', '您沒有權限執行此操作');
                        break;
                    case 404:
                        notifyError('找不到資源', '請求的資源不存在');
                        break;
                    case 500:
                        notifyError('伺服器錯誤', '請稍後再試');
                        break;
                    default:
                        notifyError('發生錯誤', '請稍後再試');
                }
            }
        } else if (error.request) {
            // 請求已發出但沒有收到響應
            notifyError('無法連接到伺服器', '請重新登入');
            // 清除所有相關的 cookies
            removeCookie('token');
            removeCookie('userId');
            removeCookie('userName');
            // 跳轉到登入頁面
            if (typeof window !== 'undefined') {
                router.push('/login');
            }
        } else {
            // 請求配置有誤
            notifyError('請求發生錯誤', '請稍後再試');
            console.error('API 請求錯誤:', error);
        }
        return Promise.reject(error);
    }
);

// 通用 API 請求函數
export async function httpClient<T = any>(
    url: string,
    options: RequestInit = {}
): Promise<ApiResponse<T>> {
    try {
        const method = options.method || 'GET';
        const isFormData = typeof FormData !== 'undefined' && options.body instanceof FormData;

        const headers = isFormData
            ? undefined // ✅ 不設定 header，讓 axios 自動加 boundary
            : { 'Content-Type': 'application/json' };

        const response = await http.request({
            url,
            method,
            data: options.body,
            headers,
        });

        return response.data;
    } catch (error) {
        if (axios.isAxiosError(error)) {
            httpLogger.log(SYMBOLS.ERROR, 'Axios 請求失敗', {
                url,
                method: options.method || 'GET',
                error: error.response?.data || error.message
            });
            return {
                success: false,
                message: error.response?.data?.message || '請求失敗',
            };
        }

        httpLogger.log(SYMBOLS.ERROR, '未知請求錯誤', { url, method: options.method || 'GET', error });
        return {
            success: false,
            message: '網路連線異常',
        };
    }
}



// ===== Raw HTTP (keep AxiosResponse with headers/status) =====
const httpRaw: AxiosInstance = axios.create({
    baseURL: apiConfig.baseURL,
    timeout: 100000,
});

// Reuse the same Authorization behavior
httpRaw.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        const token = getCookie('token');
        if (token) {
            (config.headers as any).Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error: AxiosError) => Promise.reject(error)
);

// Do NOT attach the response wrapper interceptor on httpRaw
export async function httpRawRequest<T = any>(args: {
    url: string;
    method?: string;
    headers?: Record<string, any>;
    data?: any;
    params?: any;
    validateStatus?: (status: number) => boolean;
}): Promise<AxiosResponse<T>> {
    return httpRaw.request<T>({
        url: args.url,
        method: args.method || 'GET',
        headers: args.headers,
        data: args.data,
        params: args.params,
        validateStatus: args.validateStatus,
    });
}
